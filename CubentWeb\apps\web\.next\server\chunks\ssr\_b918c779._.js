module.exports = {

"[project]/packages/cms/.basehub/next-toolbar/chunk-YSQDPG26.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk
/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ __turbopack_context__.s({
    "__commonJS": (()=>__commonJS),
    "__toESM": (()=>__toESM)
});
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __commonJS = (cb, mod)=>function __require() {
        return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = {
            exports: {}
        }).exports, mod), mod.exports;
    };
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
;
}}),
"[project]/packages/cms/.basehub/runtime/_error.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk
/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ // @ts-nocheck
__turbopack_context__.s({
    "GenqlError": (()=>GenqlError)
});
class GenqlError extends Error {
    errors = [];
    /**
     * Partial data returned by the server
     */ data;
    constructor(errors, data){
        let message = Array.isArray(errors) ? errors.map((x)=>x?.message || '').join('\n') : '';
        if (!message) {
            message = 'GraphQL error';
        }
        super(message);
        this.errors = errors;
        this.data = data;
    }
}
}}),
"[project]/packages/cms/.basehub/runtime/_batcher.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk
/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ // @ts-nocheck
__turbopack_context__.s({
    "QueryBatcher": (()=>QueryBatcher)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_error.ts [app-rsc] (ecmascript)");
;
/**
 * takes a list of requests (queue) and batches them into a single server request.
 * It will then resolve each individual requests promise with the appropriate data.
 * @private
 * @param {QueryBatcher}   client - the client to use
 * @param {Queue} queue  - the list of requests to batch
 */ function dispatchQueueBatch(client, queue) {
    let batchedQuery = queue.map((item)=>item.request);
    if (batchedQuery.length === 1) {
        batchedQuery = batchedQuery[0];
    }
    ;
    (()=>{
        try {
            return client.fetcher(batchedQuery);
        } catch (e) {
            return Promise.reject(e);
        }
    })().then((responses)=>{
        if (queue.length === 1 && !Array.isArray(responses)) {
            if (responses.errors && responses.errors.length) {
                queue[0].reject(new __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GenqlError"](responses.errors, responses.data));
                return;
            }
            queue[0].resolve(responses);
            return;
        } else if (responses.length !== queue.length) {
            throw new Error('response length did not match query length');
        }
        for(let i = 0; i < queue.length; i++){
            if (responses[i].errors && responses[i].errors.length) {
                queue[i].reject(new __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GenqlError"](responses[i].errors, responses[i].data));
            } else {
                queue[i].resolve(responses[i]);
            }
        }
    }).catch((e)=>{
        for(let i = 0; i < queue.length; i++){
            queue[i].reject(e);
        }
    });
}
/**
 * creates a list of requests to batch according to max batch size.
 * @private
 * @param {QueryBatcher} client - the client to create list of requests from from
 * @param {Options} options - the options for the batch
 */ function dispatchQueue(client, options) {
    const queue = client._queue;
    const maxBatchSize = options.maxBatchSize || 0;
    client._queue = [];
    if (maxBatchSize > 0 && maxBatchSize < queue.length) {
        for(let i = 0; i < queue.length / maxBatchSize; i++){
            dispatchQueueBatch(client, queue.slice(i * maxBatchSize, (i + 1) * maxBatchSize));
        }
    } else {
        dispatchQueueBatch(client, queue);
    }
}
class QueryBatcher {
    fetcher;
    _options;
    _queue;
    constructor(fetcher, { batchInterval = 16, shouldBatch = true, maxBatchSize = 0 } = {}){
        this.fetcher = fetcher;
        this._options = {
            batchInterval,
            shouldBatch,
            maxBatchSize
        };
        this._queue = [];
    }
    /**
     * Fetch will send a graphql request and return the parsed json.
     * @param {string}      query          - the graphql query.
     * @param {Variables}   variables      - any variables you wish to inject as key/value pairs.
     * @param {[string]}    operationName  - the graphql operationName.
     * @param {Options}     overrides      - the client options overrides.
     *
     * @return {promise} resolves to parsed json of server response
     *
     * @example
     * client.fetch(`
     *    query getHuman($id: ID!) {
     *      human(id: $id) {
     *        name
     *        height
     *      }
     *    }
     * `, { id: "1001" }, 'getHuman')
     *    .then(human => {
     *      // do something with human
     *      console.log(human);
     *    });
     */ fetch(query, variables, operationName, overrides = {}) {
        const request = {
            query
        };
        const options = Object.assign({}, this._options, overrides);
        if (variables) {
            request.variables = variables;
        }
        if (operationName) {
            request.operationName = operationName;
        }
        const promise = new Promise((resolve, reject)=>{
            this._queue.push({
                request,
                resolve,
                reject
            });
            if (this._queue.length === 1) {
                if (options.shouldBatch) {
                    setTimeout(()=>dispatchQueue(this, options), options.batchInterval);
                } else {
                    dispatchQueue(this, options);
                }
            }
        });
        return promise;
    }
    /**
     * Fetch will send a graphql request and return the parsed json.
     * @param {string}      query          - the graphql query.
     * @param {Variables}   variables      - any variables you wish to inject as key/value pairs.
     * @param {[string]}    operationName  - the graphql operationName.
     * @param {Options}     overrides      - the client options overrides.
     *
     * @return {Promise<Array<Result>>} resolves to parsed json of server response
     *
     * @example
     * client.forceFetch(`
     *    query getHuman($id: ID!) {
     *      human(id: $id) {
     *        name
     *        height
     *      }
     *    }
     * `, { id: "1001" }, 'getHuman')
     *    .then(human => {
     *      // do something with human
     *      console.log(human);
     *    });
     */ forceFetch(query, variables, operationName, overrides = {}) {
        const request = {
            query
        };
        const options = Object.assign({}, this._options, overrides, {
            shouldBatch: false
        });
        if (variables) {
            request.variables = variables;
        }
        if (operationName) {
            request.operationName = operationName;
        }
        const promise = new Promise((resolve, reject)=>{
            const client = new QueryBatcher(this.fetcher, this._options);
            client._queue = [
                {
                    request,
                    resolve,
                    reject
                }
            ];
            dispatchQueue(client, options);
        });
        return promise;
    }
}
}}),
"[project]/packages/cms/.basehub/runtime/_fetcher.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk
/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ // @ts-nocheck
__turbopack_context__.s({
    "createFetcher": (()=>createFetcher)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_batcher$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_batcher.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_error.ts [app-rsc] (ecmascript)");
;
;
const DEFAULT_BATCH_OPTIONS = {
    maxBatchSize: 10,
    batchInterval: 40
};
const createFetcher = ({ url, headers = {}, fetcher, fetch: _fetch, batch = false, ...rest })=>{
    if (!url && !fetcher) {
        throw new Error('url or fetcher is required');
    }
    fetcher = fetcher || (async (body, extraFetchOptions)=>{
        let headersObject = typeof headers == 'function' ? await headers() : headers;
        headersObject = headersObject || {};
        if (typeof fetch === 'undefined' && !_fetch) {
            throw new Error('Global `fetch` function is not available, pass a fetch polyfill to Genql `createClient`');
        }
        let fetchImpl = _fetch || fetch;
        if (extraFetchOptions?.headers) {
            headersObject = {
                ...headersObject,
                ...extraFetchOptions.headers
            };
            delete extraFetchOptions.headers;
        }
        const res = await fetchImpl(url, {
            headers: {
                'Content-Type': 'application/json',
                ...headersObject
            },
            method: 'POST',
            body: JSON.stringify(body),
            ...rest,
            ...extraFetchOptions
        });
        if (!res.ok) {
            throw new Error(`${res.statusText}: ${await res.text()}`);
        }
        const json = await res.json();
        return json;
    });
    if (!batch) {
        return async (body, extraFetchOptions)=>{
            const json = await fetcher(body, extraFetchOptions);
            if (Array.isArray(json)) {
                return json.map((json)=>{
                    if (json?.errors?.length) {
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GenqlError"](json.errors || [], json.data);
                    }
                    return json.data;
                });
            } else {
                if (json?.errors?.length) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GenqlError"](json.errors || [], json.data);
                }
                return json.data;
            }
        };
    }
    const batcher = new __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_batcher$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["QueryBatcher"](async (batchedQuery, extraFetchOptions)=>{
        // console.log(batchedQuery) // [{ query: 'query{user{age}}', variables: {} }, ...]
        const json = await fetcher(batchedQuery, extraFetchOptions);
        return json;
    }, batch === true ? DEFAULT_BATCH_OPTIONS : batch);
    return async ({ query, variables })=>{
        const json = await batcher.fetch(query, variables);
        if (json?.data) {
            return json.data;
        }
        throw new Error('Genql batch fetcher returned unexpected result ' + JSON.stringify(json));
    };
};
}}),
"[project]/packages/cms/.basehub/runtime/_aliasing.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk
/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ // @ts-nocheck
__turbopack_context__.s({
    "aliasSeparator": (()=>aliasSeparator),
    "replaceSystemAliases": (()=>replaceSystemAliases)
});
const aliasSeparator = '__alias__';
function replaceSystemAliases(obj) {
    if (typeof obj !== 'object' || obj === null) {
        return obj;
    }
    if (Array.isArray(obj)) {
        return obj.map((item)=>replaceSystemAliases(item));
    }
    const newObj = {};
    for (const [key, value] of Object.entries(obj)){
        if (key.includes(aliasSeparator)) {
            const [_prefix, ...rest] = key.split(aliasSeparator);
            const newKey = rest.join(aliasSeparator) // In case there are multiple __alias__ in the key
            ;
            newObj[newKey] = replaceSystemAliases(value);
        } else {
            newObj[key] = replaceSystemAliases(value);
        }
    }
    return newObj;
}
}}),
"[project]/packages/cms/.basehub/runtime/_generate-graphql-operation.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk
/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ // @ts-nocheck
__turbopack_context__.s({
    "generateGraphqlOperation": (()=>generateGraphqlOperation),
    "getFieldFromPath": (()=>getFieldFromPath)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_aliasing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_aliasing.js [app-rsc] (ecmascript)");
;
const parseRequest = (request, ctx, path, options)=>{
    if (typeof request === 'object' && '__args' in request) {
        const args = request.__args;
        let fields = {
            ...request
        };
        delete fields.__args;
        const argNames = Object.keys(args);
        if (argNames.length === 0) {
            return parseRequest(fields, ctx, path, options);
        }
        const field = getFieldFromPath(ctx.root, path);
        const argStrings = argNames.map((argName)=>{
            ctx.varCounter++;
            const varName = `v${ctx.varCounter}`;
            const typing = field.args && field.args[argName] // typeMap used here, .args
            ;
            if (!typing) {
                throw new Error(`no typing defined for argument \`${argName}\` in path \`${path.join('.')}\``);
            }
            const shouldStringifyValue = [
                'String',
                'String!'
            ].includes(typing[1]);
            let value = args[argName];
            if (shouldStringifyValue) {
                if (typeof value === 'object') {
                    // stringify the object
                    value = JSON.stringify(value);
                }
            }
            ctx.variables[varName] = {
                value,
                typing
            };
            return `${argName}:$${varName}`;
        });
        return `(${argStrings})${parseRequest(fields, ctx, path, options)}`;
    } else if (typeof request === 'object' && Object.keys(request).length > 0) {
        const fields = request;
        const fieldNames = Object.keys(fields).filter((k)=>Boolean(fields[k]));
        const type = path.length > 0 ? getFieldFromPath(ctx.root, path).type : ctx.root;
        const scalarFields = type.scalar;
        let scalarFieldsFragment;
        const validFieldNames = fieldNames.filter((f)=>{
            if ([
                '__scalar',
                '__name',
                '__fragmentOn'
            ].includes(f)) return true;
            if (f.startsWith('on_')) return true;
            return type.fields && f in type.fields;
        });
        if (validFieldNames.length === 0) {
            return '';
        }
        if (fieldNames.includes('__scalar')) {
            const falsyFieldNames = new Set(Object.keys(fields).filter((k)=>!Boolean(fields[k])));
            if (scalarFields?.length) {
                ctx.fragmentCounter++;
                scalarFieldsFragment = `f${ctx.fragmentCounter}`;
                ctx.fragments.push(`fragment ${scalarFieldsFragment} on ${type.name}{${scalarFields.filter((f)=>!falsyFieldNames.has(f)).map((f)=>`${options?.aliasPrefix ? `${options.aliasPrefix}${__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_aliasing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["aliasSeparator"]}${f}: ` : ''}${f}`).join(',')}}`);
            }
        }
        const fieldsSelection = validFieldNames.filter((f)=>![
                '__scalar',
                '__name',
                '__fragmentOn'
            ].includes(f)).map((f)=>{
            if (f.startsWith('on_')) {
                ctx.fragmentCounter++;
                const implementationFragment = `f${ctx.fragmentCounter}`;
                const parsed = parseRequest(fields[f], ctx, [
                    ...path,
                    f
                ], {
                    ...options,
                    aliasPrefix: implementationFragment
                });
                const typeMatch = f.match(/^on_(.+)/);
                if (!typeMatch || !typeMatch[1]) throw new Error('match failed');
                ctx.fragments.push(`fragment ${implementationFragment} on ${typeMatch[1]}${parsed}`);
                return `...${implementationFragment}`;
            } else {
                const field = type.fields?.[f];
                if (!field) return '';
                // For scalar fields or fields without subfields, just return the field name
                if (!field.type.fields) {
                    return `${options?.aliasPrefix ? `${options.aliasPrefix}${__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_aliasing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["aliasSeparator"]}${f}: ` : ''}${f}`;
                }
                // For object fields, parse recursively
                const parsed = parseRequest(fields[f], ctx, [
                    ...path,
                    f
                ], options);
                // If the parsed result is empty and this is an object type,
                // we should include at least one field or it will be invalid GraphQL
                if (!parsed && field.type.fields) {
                    // Get the first scalar field as a default selection
                    const firstScalar = field.type.scalar?.[0];
                    if (firstScalar) {
                        return `${options?.aliasPrefix ? `${options.aliasPrefix}${__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_aliasing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["aliasSeparator"]}${f}: ` : ''}${f}{${firstScalar}}`;
                    }
                }
                return `${options?.aliasPrefix ? `${options.aliasPrefix}${__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_aliasing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["aliasSeparator"]}${f}: ` : ''}${f}${parsed}`;
            }
        }).filter(Boolean).concat(scalarFieldsFragment ? [
            `...${scalarFieldsFragment}`
        ] : []).join(',');
        return fieldsSelection ? `{${fieldsSelection}}` : '';
    } else {
        return '';
    }
};
const generateGraphqlOperation = (operation, root, fields)=>{
    const ctx = {
        root: root,
        varCounter: 0,
        variables: {},
        fragmentCounter: 0,
        fragments: []
    };
    const result = parseRequest(fields, ctx, []);
    const varNames = Object.keys(ctx.variables);
    const varsString = varNames.length > 0 ? `(${varNames.map((v)=>{
        const variableType = ctx.variables[v].typing[1];
        return `$${v}:${variableType}`;
    })})` : '';
    const operationName = fields?.__name || '';
    return {
        query: [
            `${operation} ${operationName}${varsString}${result}`,
            ...ctx.fragments
        ].join(','),
        variables: Object.keys(ctx.variables).reduce((r, v)=>{
            r[v] = ctx.variables[v].value;
            return r;
        }, {}),
        ...operationName ? {
            operationName: operationName.toString()
        } : {}
    };
};
const getFieldFromPath = (root, path)=>{
    let current;
    if (!root) throw new Error('root type is not provided');
    if (path.length === 0) throw new Error(`path is empty`);
    path.forEach((f)=>{
        const type = current ? current.type : root;
        if (!type.fields) throw new Error(`type \`${type.name}\` does not have fields`);
        const possibleTypes = Object.keys(type.fields).filter((i)=>i.startsWith('on_')).reduce((types, fieldName)=>{
            const field = type.fields && type.fields[fieldName];
            if (field) types.push(field.type);
            return types;
        }, [
            type
        ]);
        let field = null;
        possibleTypes.forEach((type)=>{
            const found = type.fields && type.fields[f];
            if (found) field = found;
        });
        if (!field) throw new Error(`type \`${type.name}\` does not have a field \`${f}\``);
        current = field;
    });
    return current;
};
}}),
"[project]/packages/cms/.basehub/runtime/_create-client.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk
/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ // @ts-nocheck
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_fetcher$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_fetcher.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_generate$2d$graphql$2d$operation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_generate-graphql-operation.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_aliasing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_aliasing.js [app-rsc] (ecmascript)");
;
;
;
const createClient = ({ queryRoot, mutationRoot, subscriptionRoot, getExtraFetchOptions, ...options })=>{
    const fetcher = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_fetcher$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createFetcher"])(options);
    const client = {};
    if (queryRoot) {
        client.query = async (request)=>{
            if (!queryRoot) throw new Error('queryRoot argument is missing');
            const body = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_generate$2d$graphql$2d$operation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateGraphqlOperation"])('query', queryRoot, request);
            const extraFetchOptions = await getExtraFetchOptions?.('query', body, request);
            return await fetcher(body, extraFetchOptions).then((result)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_aliasing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["replaceSystemAliases"])(result));
        };
    }
    if (mutationRoot) {
        client.mutation = async (request)=>{
            if (!mutationRoot) throw new Error('mutationRoot argument is missing');
            const body = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_generate$2d$graphql$2d$operation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateGraphqlOperation"])('mutation', mutationRoot, request);
            const extraFetchOptions = await getExtraFetchOptions?.('mutation', body, request);
            return await fetcher((0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_generate$2d$graphql$2d$operation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateGraphqlOperation"])('mutation', mutationRoot, request), extraFetchOptions);
        };
    }
    return client;
};
createClient.replaceSystemAliases = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_aliasing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["replaceSystemAliases"];
}}),
"[project]/packages/cms/.basehub/runtime/_link-type-map.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk
/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ // @ts-nocheck
__turbopack_context__.s({
    "linkTypeMap": (()=>linkTypeMap),
    "resolveConcreteTypes": (()=>resolveConcreteTypes)
});
const linkTypeMap = (typeMap)=>{
    const indexToName = Object.assign({}, ...Object.keys(typeMap.types).map((k, i)=>({
            [i]: k
        })));
    let intermediaryTypeMap = Object.assign({}, ...Object.keys(typeMap.types || {}).map((k)=>{
        const type = typeMap.types[k];
        const fields = type || {};
        return {
            [k]: {
                name: k,
                // type scalar properties
                scalar: Object.keys(fields).filter((f)=>{
                    const [type] = fields[f] || [];
                    const isScalar = type && typeMap.scalars.includes(type);
                    if (!isScalar) {
                        return false;
                    }
                    const args = fields[f]?.[1];
                    const argTypes = Object.values(args || {}).map((x)=>x?.[1]).filter(Boolean);
                    const hasRequiredArgs = argTypes.some((str)=>str && str.endsWith('!'));
                    if (hasRequiredArgs) {
                        return false;
                    }
                    return true;
                }),
                // fields with corresponding `type` and `args`
                fields: Object.assign({}, ...Object.keys(fields).map((f)=>{
                    const [typeIndex, args] = fields[f] || [];
                    if (typeIndex == null) {
                        return {};
                    }
                    return {
                        [f]: {
                            // replace index with type name
                            type: indexToName[typeIndex],
                            args: Object.assign({}, ...Object.keys(args || {}).map((k)=>{
                                // if argTypeString == argTypeName, argTypeString is missing, need to readd it
                                if (!args || !args[k]) {
                                    return;
                                }
                                const [argTypeName, argTypeString] = args[k];
                                return {
                                    [k]: [
                                        indexToName[argTypeName],
                                        argTypeString || indexToName[argTypeName]
                                    ]
                                };
                            }))
                        }
                    };
                }))
            }
        };
    }));
    const res = resolveConcreteTypes(intermediaryTypeMap);
    return res;
};
const resolveConcreteTypes = (linkedTypeMap)=>{
    Object.keys(linkedTypeMap).forEach((typeNameFromKey)=>{
        const type = linkedTypeMap[typeNameFromKey];
        // type.name = typeNameFromKey
        if (!type.fields) {
            return;
        }
        const fields = type.fields;
        Object.keys(fields).forEach((f)=>{
            const field = fields[f];
            if (field.args) {
                const args = field.args;
                Object.keys(args).forEach((key)=>{
                    const arg = args[key];
                    if (arg) {
                        const [typeName] = arg;
                        if (typeof typeName === 'string') {
                            if (!linkedTypeMap[typeName]) {
                                linkedTypeMap[typeName] = {
                                    name: typeName
                                };
                            }
                            arg[0] = linkedTypeMap[typeName];
                        }
                    }
                });
            }
            const typeName = field.type;
            if (typeof typeName === 'string') {
                if (!linkedTypeMap[typeName]) {
                    linkedTypeMap[typeName] = {
                        name: typeName
                    };
                }
                field.type = linkedTypeMap[typeName];
            }
        });
    });
    return linkedTypeMap;
};
}}),
"[project]/packages/cms/.basehub/runtime/index.ts [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk
/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ // @ts-nocheck
__turbopack_context__.s({
    "everything": (()=>everything)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_create$2d$client$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_create-client.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_generate$2d$graphql$2d$operation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_generate-graphql-operation.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_link$2d$type$2d$map$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_link-type-map.ts [app-rsc] (ecmascript)");
// export { Observable } from 'zen-observable-ts'
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_fetcher$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_fetcher.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_error.ts [app-rsc] (ecmascript)");
;
;
;
;
;
const everything = {
    __scalar: true
};
}}),
"[project]/packages/cms/.basehub/runtime/index.ts [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_create$2d$client$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_create-client.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_generate$2d$graphql$2d$operation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_generate-graphql-operation.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_link$2d$type$2d$map$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_link-type-map.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_fetcher$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_fetcher.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_error.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/index.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/packages/cms/.basehub/types.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk
/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ // @ts-nocheck
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const __TURBOPACK__default__export__ = {
    "scalars": [
        0,
        4,
        5,
        6,
        7,
        25,
        29,
        30,
        32,
        33,
        35,
        36,
        37,
        41,
        53,
        58,
        63,
        71,
        72
    ],
    "types": {
        "AnalyticsKeyScope": {},
        "Authors": {
            "_analyticsKey": [
                58,
                {
                    "scope": [
                        0
                    ]
                }
            ],
            "_dashboardUrl": [
                58
            ],
            "_id": [
                58
            ],
            "_idPath": [
                58
            ],
            "_meta": [
                43
            ],
            "_searchKey": [
                58
            ],
            "_slug": [
                58
            ],
            "_slugPath": [
                58
            ],
            "_sys": [
                13
            ],
            "_title": [
                58
            ],
            "item": [
                2
            ],
            "items": [
                2
            ],
            "__typename": [
                58
            ]
        },
        "AuthorsItem": {
            "_analyticsKey": [
                58,
                {
                    "scope": [
                        0
                    ]
                }
            ],
            "_dashboardUrl": [
                58
            ],
            "_id": [
                58
            ],
            "_idPath": [
                58
            ],
            "_slug": [
                58
            ],
            "_slugPath": [
                58
            ],
            "_sys": [
                13
            ],
            "_title": [
                58
            ],
            "avatar": [
                15
            ],
            "xUrl": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "AuthorsItemFilterInput": {
            "AND": [
                3
            ],
            "OR": [
                3
            ],
            "_id": [
                59
            ],
            "_slug": [
                59
            ],
            "_sys_apiNamePath": [
                59
            ],
            "_sys_createdAt": [
                31
            ],
            "_sys_hash": [
                59
            ],
            "_sys_id": [
                59
            ],
            "_sys_idPath": [
                59
            ],
            "_sys_lastModifiedAt": [
                31
            ],
            "_sys_slug": [
                59
            ],
            "_sys_slugPath": [
                59
            ],
            "_sys_title": [
                59
            ],
            "_title": [
                59
            ],
            "xUrl": [
                59
            ],
            "__typename": [
                58
            ]
        },
        "AuthorsItemOrderByEnum": {},
        "BSHBEventSchema": {},
        "BSHBRichTextContentSchema": {},
        "BSHBRichTextTOCSchema": {},
        "BaseRichTextJson": {
            "blocks": [
                58
            ],
            "content": [
                6
            ],
            "toc": [
                7
            ],
            "__typename": [
                58
            ]
        },
        "BlockAudio": {
            "duration": [
                33
            ],
            "fileName": [
                58
            ],
            "fileSize": [
                36
            ],
            "lastModified": [
                33
            ],
            "mimeType": [
                58
            ],
            "url": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "BlockCodeSnippet": {
            "allowedLanguages": [
                30
            ],
            "code": [
                58
            ],
            "html": [
                58,
                {
                    "theme": [
                        58
                    ]
                }
            ],
            "language": [
                30
            ],
            "__typename": [
                58
            ]
        },
        "BlockColor": {
            "b": [
                36
            ],
            "g": [
                36
            ],
            "hex": [
                58
            ],
            "hsl": [
                58
            ],
            "r": [
                36
            ],
            "rgb": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "BlockDocument": {
            "_analyticsKey": [
                58,
                {
                    "scope": [
                        0
                    ]
                }
            ],
            "_dashboardUrl": [
                58
            ],
            "_id": [
                58
            ],
            "_idPath": [
                58
            ],
            "_slug": [
                58
            ],
            "_slugPath": [
                58
            ],
            "_sys": [
                13
            ],
            "_title": [
                58
            ],
            "on_Authors": [
                1
            ],
            "on_AuthorsItem": [
                2
            ],
            "on_Blog": [
                20
            ],
            "on_Categories": [
                26
            ],
            "on_CategoriesItem": [
                27
            ],
            "on_LegalPages": [
                38
            ],
            "on_LegalPagesItem": [
                39
            ],
            "on_Posts": [
                48
            ],
            "on_PostsItem": [
                49
            ],
            "on__AgentSTART": [
                65
            ],
            "on_authorsItem_AsList": [
                75
            ],
            "on_categoriesItem_AsList": [
                76
            ],
            "on_legalPagesItem_AsList": [
                77
            ],
            "on_postsItem_AsList": [
                78
            ],
            "__typename": [
                58
            ]
        },
        "BlockDocumentSys": {
            "apiNamePath": [
                58
            ],
            "createdAt": [
                58
            ],
            "hash": [
                58
            ],
            "id": [
                35
            ],
            "idPath": [
                58
            ],
            "lastModifiedAt": [
                58
            ],
            "slug": [
                58
            ],
            "slugPath": [
                58
            ],
            "title": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "BlockFile": {
            "fileName": [
                58
            ],
            "fileSize": [
                36
            ],
            "lastModified": [
                33
            ],
            "mimeType": [
                58
            ],
            "url": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "BlockImage": {
            "alt": [
                58
            ],
            "aspectRatio": [
                58
            ],
            "blurDataURL": [
                58
            ],
            "fileName": [
                58
            ],
            "fileSize": [
                36
            ],
            "height": [
                36
            ],
            "lastModified": [
                33
            ],
            "mimeType": [
                58
            ],
            "placeholderURL": [
                58
            ],
            "rawUrl": [
                58
            ],
            "thumbhash": [
                58
            ],
            "url": [
                58,
                {
                    "anim": [
                        58
                    ],
                    "background": [
                        58
                    ],
                    "blur": [
                        36
                    ],
                    "border": [
                        58
                    ],
                    "brightness": [
                        36
                    ],
                    "compression": [
                        58
                    ],
                    "contrast": [
                        36
                    ],
                    "dpr": [
                        36
                    ],
                    "fit": [
                        58
                    ],
                    "format": [
                        58
                    ],
                    "gamma": [
                        58
                    ],
                    "gravity": [
                        58
                    ],
                    "height": [
                        36
                    ],
                    "metadata": [
                        58
                    ],
                    "quality": [
                        36
                    ],
                    "rotate": [
                        58
                    ],
                    "sharpen": [
                        58
                    ],
                    "trim": [
                        58
                    ],
                    "width": [
                        36
                    ]
                }
            ],
            "width": [
                36
            ],
            "__typename": [
                58
            ]
        },
        "BlockList": {
            "_analyticsKey": [
                58,
                {
                    "scope": [
                        0
                    ]
                }
            ],
            "_dashboardUrl": [
                58
            ],
            "_id": [
                58
            ],
            "_idPath": [
                58
            ],
            "_meta": [
                43
            ],
            "_searchKey": [
                58
            ],
            "_slug": [
                58
            ],
            "_slugPath": [
                58
            ],
            "_sys": [
                13
            ],
            "_title": [
                58
            ],
            "on_Authors": [
                1
            ],
            "on_Categories": [
                26
            ],
            "on_LegalPages": [
                38
            ],
            "on_Posts": [
                48
            ],
            "on_authorsItem_AsList": [
                75
            ],
            "on_categoriesItem_AsList": [
                76
            ],
            "on_legalPagesItem_AsList": [
                77
            ],
            "on_postsItem_AsList": [
                78
            ],
            "__typename": [
                58
            ]
        },
        "BlockOgImage": {
            "height": [
                36
            ],
            "url": [
                58
            ],
            "width": [
                36
            ],
            "__typename": [
                58
            ]
        },
        "BlockRichText": {
            "html": [
                58,
                {
                    "slugs": [
                        25
                    ],
                    "toc": [
                        25
                    ]
                }
            ],
            "json": [
                56
            ],
            "markdown": [
                58
            ],
            "plainText": [
                58
            ],
            "readingTime": [
                36,
                {
                    "wpm": [
                        36
                    ]
                }
            ],
            "on_Body": [
                21
            ],
            "on_Body_1": [
                23
            ],
            "__typename": [
                58
            ]
        },
        "BlockVideo": {
            "aspectRatio": [
                58
            ],
            "duration": [
                33
            ],
            "fileName": [
                58
            ],
            "fileSize": [
                36
            ],
            "height": [
                36
            ],
            "lastModified": [
                33
            ],
            "mimeType": [
                58
            ],
            "url": [
                58
            ],
            "width": [
                36
            ],
            "__typename": [
                58
            ]
        },
        "Blog": {
            "_analyticsKey": [
                58,
                {
                    "scope": [
                        0
                    ]
                }
            ],
            "_dashboardUrl": [
                58
            ],
            "_id": [
                58
            ],
            "_idPath": [
                58
            ],
            "_slug": [
                58
            ],
            "_slugPath": [
                58
            ],
            "_sys": [
                13
            ],
            "_title": [
                58
            ],
            "authors": [
                1,
                {
                    "filter": [
                        3
                    ],
                    "first": [
                        36
                    ],
                    "orderBy": [
                        4
                    ],
                    "skip": [
                        36
                    ]
                }
            ],
            "categories": [
                26,
                {
                    "filter": [
                        28
                    ],
                    "first": [
                        36
                    ],
                    "orderBy": [
                        29
                    ],
                    "skip": [
                        36
                    ]
                }
            ],
            "posts": [
                48,
                {
                    "filter": [
                        50
                    ],
                    "first": [
                        36
                    ],
                    "orderBy": [
                        53
                    ],
                    "skip": [
                        36
                    ]
                }
            ],
            "__typename": [
                58
            ]
        },
        "Body": {
            "html": [
                58,
                {
                    "slugs": [
                        25
                    ],
                    "toc": [
                        25
                    ]
                }
            ],
            "json": [
                22
            ],
            "markdown": [
                58
            ],
            "plainText": [
                58
            ],
            "readingTime": [
                36,
                {
                    "wpm": [
                        36
                    ]
                }
            ],
            "__typename": [
                58
            ]
        },
        "BodyRichText": {
            "content": [
                6
            ],
            "toc": [
                7
            ],
            "__typename": [
                58
            ]
        },
        "Body_1": {
            "html": [
                58,
                {
                    "slugs": [
                        25
                    ],
                    "toc": [
                        25
                    ]
                }
            ],
            "json": [
                24
            ],
            "markdown": [
                58
            ],
            "plainText": [
                58
            ],
            "readingTime": [
                36,
                {
                    "wpm": [
                        36
                    ]
                }
            ],
            "__typename": [
                58
            ]
        },
        "Body_1RichText": {
            "content": [
                6
            ],
            "toc": [
                7
            ],
            "__typename": [
                58
            ]
        },
        "Boolean": {},
        "Categories": {
            "_analyticsKey": [
                58,
                {
                    "scope": [
                        0
                    ]
                }
            ],
            "_dashboardUrl": [
                58
            ],
            "_id": [
                58
            ],
            "_idPath": [
                58
            ],
            "_meta": [
                43
            ],
            "_searchKey": [
                58
            ],
            "_slug": [
                58
            ],
            "_slugPath": [
                58
            ],
            "_sys": [
                13
            ],
            "_title": [
                58
            ],
            "item": [
                27
            ],
            "items": [
                27
            ],
            "__typename": [
                58
            ]
        },
        "CategoriesItem": {
            "_analyticsKey": [
                58,
                {
                    "scope": [
                        0
                    ]
                }
            ],
            "_dashboardUrl": [
                58
            ],
            "_id": [
                58
            ],
            "_idPath": [
                58
            ],
            "_slug": [
                58
            ],
            "_slugPath": [
                58
            ],
            "_sys": [
                13
            ],
            "_title": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "CategoriesItemFilterInput": {
            "AND": [
                28
            ],
            "OR": [
                28
            ],
            "_id": [
                59
            ],
            "_slug": [
                59
            ],
            "_sys_apiNamePath": [
                59
            ],
            "_sys_createdAt": [
                31
            ],
            "_sys_hash": [
                59
            ],
            "_sys_id": [
                59
            ],
            "_sys_idPath": [
                59
            ],
            "_sys_lastModifiedAt": [
                31
            ],
            "_sys_slug": [
                59
            ],
            "_sys_slugPath": [
                59
            ],
            "_sys_title": [
                59
            ],
            "_title": [
                59
            ],
            "__typename": [
                58
            ]
        },
        "CategoriesItemOrderByEnum": {},
        "CodeSnippetLanguage": {},
        "DateFilter": {
            "eq": [
                32
            ],
            "isAfter": [
                32
            ],
            "isBefore": [
                32
            ],
            "isNull": [
                25
            ],
            "neq": [
                32
            ],
            "onOrAfter": [
                32
            ],
            "onOrBefore": [
                32
            ],
            "__typename": [
                58
            ]
        },
        "DateTime": {},
        "Float": {},
        "GetUploadSignedURL": {
            "signedURL": [
                58
            ],
            "uploadURL": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "ID": {},
        "Int": {},
        "JSON": {},
        "LegalPages": {
            "_analyticsKey": [
                58,
                {
                    "scope": [
                        0
                    ]
                }
            ],
            "_dashboardUrl": [
                58
            ],
            "_id": [
                58
            ],
            "_idPath": [
                58
            ],
            "_meta": [
                43
            ],
            "_searchKey": [
                58
            ],
            "_slug": [
                58
            ],
            "_slugPath": [
                58
            ],
            "_sys": [
                13
            ],
            "_title": [
                58
            ],
            "item": [
                39
            ],
            "items": [
                39
            ],
            "__typename": [
                58
            ]
        },
        "LegalPagesItem": {
            "_analyticsKey": [
                58,
                {
                    "scope": [
                        0
                    ]
                }
            ],
            "_dashboardUrl": [
                58
            ],
            "_id": [
                58
            ],
            "_idPath": [
                58
            ],
            "_slug": [
                58
            ],
            "_slugPath": [
                58
            ],
            "_sys": [
                13
            ],
            "_title": [
                58
            ],
            "body": [
                23
            ],
            "description": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "LegalPagesItemFilterInput": {
            "AND": [
                40
            ],
            "OR": [
                40
            ],
            "_id": [
                59
            ],
            "_slug": [
                59
            ],
            "_sys_apiNamePath": [
                59
            ],
            "_sys_createdAt": [
                31
            ],
            "_sys_hash": [
                59
            ],
            "_sys_id": [
                59
            ],
            "_sys_idPath": [
                59
            ],
            "_sys_lastModifiedAt": [
                31
            ],
            "_sys_slug": [
                59
            ],
            "_sys_slugPath": [
                59
            ],
            "_sys_title": [
                59
            ],
            "_title": [
                59
            ],
            "description": [
                59
            ],
            "__typename": [
                58
            ]
        },
        "LegalPagesItemOrderByEnum": {},
        "ListFilter": {
            "isEmpty": [
                25
            ],
            "length": [
                36
            ],
            "__typename": [
                58
            ]
        },
        "ListMeta": {
            "filteredCount": [
                36
            ],
            "totalCount": [
                36
            ],
            "__typename": [
                58
            ]
        },
        "MediaBlock": {
            "fileName": [
                58
            ],
            "fileSize": [
                36
            ],
            "lastModified": [
                33
            ],
            "mimeType": [
                58
            ],
            "url": [
                58
            ],
            "on_BlockAudio": [
                9
            ],
            "on_BlockFile": [
                14
            ],
            "on_BlockImage": [
                15
            ],
            "on_BlockVideo": [
                19
            ],
            "__typename": [
                58
            ]
        },
        "MediaBlockUnion": {
            "on_BlockAudio": [
                9
            ],
            "on_BlockFile": [
                14
            ],
            "on_BlockImage": [
                15
            ],
            "on_BlockVideo": [
                19
            ],
            "on_MediaBlock": [
                44
            ],
            "__typename": [
                58
            ]
        },
        "Mutation": {
            "getUploadSignedURL": [
                34,
                {
                    "fileHash": [
                        58
                    ],
                    "fileName": [
                        58,
                        "String!"
                    ]
                }
            ],
            "transaction": [
                62,
                {
                    "authorId": [
                        58
                    ],
                    "autoCommit": [
                        58
                    ],
                    "data": [
                        58,
                        "String!"
                    ],
                    "skipWorkflows": [
                        25
                    ],
                    "timeout": [
                        36
                    ]
                }
            ],
            "transactionAsync": [
                58,
                {
                    "authorId": [
                        58
                    ],
                    "autoCommit": [
                        58
                    ],
                    "data": [
                        58,
                        "String!"
                    ],
                    "skipWorkflows": [
                        25
                    ]
                }
            ],
            "transactionStatus": [
                62,
                {
                    "id": [
                        58,
                        "String!"
                    ]
                }
            ],
            "__typename": [
                58
            ]
        },
        "NumberFilter": {
            "eq": [
                33
            ],
            "gt": [
                33
            ],
            "gte": [
                33
            ],
            "isNull": [
                25
            ],
            "lt": [
                33
            ],
            "lte": [
                33
            ],
            "neq": [
                33
            ],
            "__typename": [
                58
            ]
        },
        "Posts": {
            "_analyticsKey": [
                58,
                {
                    "scope": [
                        0
                    ]
                }
            ],
            "_dashboardUrl": [
                58
            ],
            "_id": [
                58
            ],
            "_idPath": [
                58
            ],
            "_meta": [
                43
            ],
            "_searchKey": [
                58
            ],
            "_slug": [
                58
            ],
            "_slugPath": [
                58
            ],
            "_sys": [
                13
            ],
            "_title": [
                58
            ],
            "item": [
                49
            ],
            "items": [
                49
            ],
            "__typename": [
                58
            ]
        },
        "PostsItem": {
            "_analyticsKey": [
                58,
                {
                    "scope": [
                        0
                    ]
                }
            ],
            "_dashboardUrl": [
                58
            ],
            "_id": [
                58
            ],
            "_idPath": [
                58
            ],
            "_slug": [
                58
            ],
            "_slugPath": [
                58
            ],
            "_sys": [
                13
            ],
            "_title": [
                58
            ],
            "authors": [
                2
            ],
            "body": [
                21
            ],
            "categories": [
                27
            ],
            "date": [
                58
            ],
            "description": [
                58
            ],
            "image": [
                15
            ],
            "__typename": [
                58
            ]
        },
        "PostsItemFilterInput": {
            "AND": [
                50
            ],
            "OR": [
                50
            ],
            "_id": [
                59
            ],
            "_slug": [
                59
            ],
            "_sys_apiNamePath": [
                59
            ],
            "_sys_createdAt": [
                31
            ],
            "_sys_hash": [
                59
            ],
            "_sys_id": [
                59
            ],
            "_sys_idPath": [
                59
            ],
            "_sys_lastModifiedAt": [
                31
            ],
            "_sys_slug": [
                59
            ],
            "_sys_slugPath": [
                59
            ],
            "_sys_title": [
                59
            ],
            "_title": [
                59
            ],
            "authors": [
                51
            ],
            "categories": [
                52
            ],
            "date": [
                31
            ],
            "description": [
                59
            ],
            "__typename": [
                58
            ]
        },
        "PostsItemFilterInput__authors_0___untitled": {
            "_id": [
                59
            ],
            "_slug": [
                59
            ],
            "_sys_apiNamePath": [
                59
            ],
            "_sys_createdAt": [
                31
            ],
            "_sys_hash": [
                59
            ],
            "_sys_id": [
                59
            ],
            "_sys_idPath": [
                59
            ],
            "_sys_lastModifiedAt": [
                31
            ],
            "_sys_slug": [
                59
            ],
            "_sys_slugPath": [
                59
            ],
            "_sys_title": [
                59
            ],
            "_title": [
                59
            ],
            "xUrl": [
                59
            ],
            "__typename": [
                58
            ]
        },
        "PostsItemFilterInput__categories_0___untitled": {
            "_id": [
                59
            ],
            "_slug": [
                59
            ],
            "_sys_apiNamePath": [
                59
            ],
            "_sys_createdAt": [
                31
            ],
            "_sys_hash": [
                59
            ],
            "_sys_id": [
                59
            ],
            "_sys_idPath": [
                59
            ],
            "_sys_lastModifiedAt": [
                31
            ],
            "_sys_slug": [
                59
            ],
            "_sys_slugPath": [
                59
            ],
            "_sys_title": [
                59
            ],
            "_title": [
                59
            ],
            "__typename": [
                58
            ]
        },
        "PostsItemOrderByEnum": {},
        "Query": {
            "_agent": [
                65,
                {
                    "id": [
                        58,
                        "String!"
                    ]
                }
            ],
            "_agents": [
                73
            ],
            "_componentInstances": [
                74
            ],
            "_structure": [
                37,
                {
                    "format": [
                        72
                    ],
                    "resolveTargetsWith": [
                        71
                    ],
                    "targetBlock": [
                        61
                    ],
                    "withConstraints": [
                        25
                    ],
                    "withIDs": [
                        25
                    ],
                    "withTypeOptions": [
                        25
                    ]
                }
            ],
            "_sys": [
                55
            ],
            "blog": [
                20
            ],
            "legalPages": [
                38,
                {
                    "filter": [
                        40
                    ],
                    "first": [
                        36
                    ],
                    "orderBy": [
                        41
                    ],
                    "skip": [
                        36
                    ]
                }
            ],
            "__typename": [
                58
            ]
        },
        "RepoSys": {
            "branches": [
                67,
                {
                    "limit": [
                        36
                    ],
                    "offset": [
                        36
                    ]
                }
            ],
            "hash": [
                58
            ],
            "id": [
                35
            ],
            "playgroundInfo": [
                70
            ],
            "slug": [
                58
            ],
            "title": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "RichTextJson": {
            "content": [
                6
            ],
            "toc": [
                7
            ],
            "on_BaseRichTextJson": [
                8
            ],
            "on_BodyRichText": [
                22
            ],
            "on_Body_1RichText": [
                24
            ],
            "__typename": [
                58
            ]
        },
        "SelectFilter": {
            "excludes": [
                58
            ],
            "excludesAll": [
                58
            ],
            "includes": [
                58
            ],
            "includesAll": [
                58
            ],
            "includesAny": [
                58
            ],
            "isEmpty": [
                25
            ],
            "__typename": [
                58
            ]
        },
        "String": {},
        "StringFilter": {
            "contains": [
                58
            ],
            "endsWith": [
                58
            ],
            "eq": [
                58
            ],
            "in": [
                58
            ],
            "isNull": [
                25
            ],
            "matches": [
                60
            ],
            "notEq": [
                58
            ],
            "notIn": [
                58
            ],
            "startsWith": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "StringMatchesFilter": {
            "caseSensitive": [
                25
            ],
            "pattern": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "TargetBlock": {
            "focus": [
                25
            ],
            "id": [
                58
            ],
            "label": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "TransactionStatus": {
            "duration": [
                36
            ],
            "endedAt": [
                58
            ],
            "id": [
                58
            ],
            "message": [
                58
            ],
            "startedAt": [
                58
            ],
            "status": [
                63
            ],
            "__typename": [
                58
            ]
        },
        "TransactionStatusEnum": {},
        "Variant": {
            "apiName": [
                58
            ],
            "color": [
                58
            ],
            "id": [
                58
            ],
            "isDefault": [
                25
            ],
            "label": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "_AgentSTART": {
            "_agentKey": [
                58
            ],
            "_analyticsKey": [
                58,
                {
                    "scope": [
                        0
                    ]
                }
            ],
            "_dashboardUrl": [
                58
            ],
            "_id": [
                58
            ],
            "_idPath": [
                58
            ],
            "_slug": [
                58
            ],
            "_slugPath": [
                58
            ],
            "_sys": [
                13
            ],
            "_title": [
                58
            ],
            "accent": [
                58
            ],
            "avatar": [
                58
            ],
            "chatUrl": [
                58
            ],
            "commit": [
                25
            ],
            "description": [
                58
            ],
            "edit": [
                25
            ],
            "embedUrl": [
                58
            ],
            "getUserInfo": [
                25
            ],
            "grayscale": [
                58
            ],
            "manageBranches": [
                25
            ],
            "mcpUrl": [
                58
            ],
            "model": [
                58
            ],
            "searchTheWeb": [
                25
            ],
            "slackInstallUrl": [
                58
            ],
            "systemPrompt": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "_BranchInfo": {
            "archivedAt": [
                58
            ],
            "archivedBy": [
                58
            ],
            "authorId": [
                58
            ],
            "contributors": [
                58
            ],
            "createdAt": [
                58
            ],
            "description": [
                58
            ],
            "git": [
                69
            ],
            "headCommit": [
                68
            ],
            "headCommitId": [
                58
            ],
            "id": [
                35
            ],
            "inlineSuggestionAppliedAt": [
                58
            ],
            "isDefault": [
                25
            ],
            "isInlineSuggestion": [
                25
            ],
            "name": [
                58
            ],
            "playgroundId": [
                58
            ],
            "rollbackCommitId": [
                58
            ],
            "rollbackIsoDate": [
                58
            ],
            "sourceBranchId": [
                58
            ],
            "updatedAt": [
                58
            ],
            "workingRootBlockId": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "_Branches": {
            "_meta": [
                43
            ],
            "items": [
                66
            ],
            "__typename": [
                58
            ]
        },
        "_CommitInfo": {
            "authorId": [
                58
            ],
            "branchId": [
                58
            ],
            "contributors": [
                58
            ],
            "createdAt": [
                58
            ],
            "hash": [
                58
            ],
            "id": [
                58
            ],
            "mergeParentCommitId": [
                58
            ],
            "message": [
                58
            ],
            "parentCommitId": [
                58
            ],
            "playgroundId": [
                58
            ],
            "repoId": [
                58
            ],
            "rootBlockId": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "_GitInfo": {
            "branch": [
                58
            ],
            "deploymentUrl": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "_PlaygroundInfo": {
            "claimUrl": [
                58
            ],
            "editUrl": [
                58
            ],
            "expiresAt": [
                58
            ],
            "id": [
                58
            ],
            "__typename": [
                58
            ]
        },
        "_ResolveTargetsWithEnum": {},
        "_StructureFormatEnum": {},
        "_agents": {
            "start": [
                65
            ],
            "__typename": [
                58
            ]
        },
        "_components": {
            "authorsItem": [
                75,
                {
                    "filter": [
                        3
                    ],
                    "first": [
                        36
                    ],
                    "orderBy": [
                        4
                    ],
                    "skip": [
                        36
                    ]
                }
            ],
            "categoriesItem": [
                76,
                {
                    "filter": [
                        28
                    ],
                    "first": [
                        36
                    ],
                    "orderBy": [
                        29
                    ],
                    "skip": [
                        36
                    ]
                }
            ],
            "legalPagesItem": [
                77,
                {
                    "filter": [
                        40
                    ],
                    "first": [
                        36
                    ],
                    "orderBy": [
                        41
                    ],
                    "skip": [
                        36
                    ]
                }
            ],
            "postsItem": [
                78,
                {
                    "filter": [
                        50
                    ],
                    "first": [
                        36
                    ],
                    "orderBy": [
                        53
                    ],
                    "skip": [
                        36
                    ]
                }
            ],
            "__typename": [
                58
            ]
        },
        "authorsItem_AsList": {
            "_analyticsKey": [
                58,
                {
                    "scope": [
                        0
                    ]
                }
            ],
            "_dashboardUrl": [
                58
            ],
            "_id": [
                58
            ],
            "_idPath": [
                58
            ],
            "_meta": [
                43
            ],
            "_searchKey": [
                58
            ],
            "_slug": [
                58
            ],
            "_slugPath": [
                58
            ],
            "_sys": [
                13
            ],
            "_title": [
                58
            ],
            "item": [
                2
            ],
            "items": [
                2
            ],
            "__typename": [
                58
            ]
        },
        "categoriesItem_AsList": {
            "_analyticsKey": [
                58,
                {
                    "scope": [
                        0
                    ]
                }
            ],
            "_dashboardUrl": [
                58
            ],
            "_id": [
                58
            ],
            "_idPath": [
                58
            ],
            "_meta": [
                43
            ],
            "_searchKey": [
                58
            ],
            "_slug": [
                58
            ],
            "_slugPath": [
                58
            ],
            "_sys": [
                13
            ],
            "_title": [
                58
            ],
            "item": [
                27
            ],
            "items": [
                27
            ],
            "__typename": [
                58
            ]
        },
        "legalPagesItem_AsList": {
            "_analyticsKey": [
                58,
                {
                    "scope": [
                        0
                    ]
                }
            ],
            "_dashboardUrl": [
                58
            ],
            "_id": [
                58
            ],
            "_idPath": [
                58
            ],
            "_meta": [
                43
            ],
            "_searchKey": [
                58
            ],
            "_slug": [
                58
            ],
            "_slugPath": [
                58
            ],
            "_sys": [
                13
            ],
            "_title": [
                58
            ],
            "item": [
                39
            ],
            "items": [
                39
            ],
            "__typename": [
                58
            ]
        },
        "postsItem_AsList": {
            "_analyticsKey": [
                58,
                {
                    "scope": [
                        0
                    ]
                }
            ],
            "_dashboardUrl": [
                58
            ],
            "_id": [
                58
            ],
            "_idPath": [
                58
            ],
            "_meta": [
                43
            ],
            "_searchKey": [
                58
            ],
            "_slug": [
                58
            ],
            "_slugPath": [
                58
            ],
            "_sys": [
                13
            ],
            "_title": [
                58
            ],
            "item": [
                49
            ],
            "items": [
                49
            ],
            "__typename": [
                58
            ]
        }
    }
};
}}),
"[project]/packages/cms/.basehub/schema.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk
/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ // @ts-nocheck
/* istanbul ignore file */ /* tslint:disable */ /* eslint-disable */ __turbopack_context__.s({
    "enumAnalyticsKeyScope": (()=>enumAnalyticsKeyScope),
    "enumAuthorsItemOrderByEnum": (()=>enumAuthorsItemOrderByEnum),
    "enumCategoriesItemOrderByEnum": (()=>enumCategoriesItemOrderByEnum),
    "enumLegalPagesItemOrderByEnum": (()=>enumLegalPagesItemOrderByEnum),
    "enumPostsItemOrderByEnum": (()=>enumPostsItemOrderByEnum),
    "enumTransactionStatusEnum": (()=>enumTransactionStatusEnum),
    "enum_resolveTargetsWithEnum": (()=>enum_resolveTargetsWithEnum),
    "enum_structureFormatEnum": (()=>enum_structureFormatEnum),
    "fragmentOn": (()=>fragmentOn),
    "fragmentOnRecursiveCollection": (()=>fragmentOnRecursiveCollection),
    "isAuthors": (()=>isAuthors),
    "isAuthorsItem": (()=>isAuthorsItem),
    "isBaseRichTextJson": (()=>isBaseRichTextJson),
    "isBlockAudio": (()=>isBlockAudio),
    "isBlockCodeSnippet": (()=>isBlockCodeSnippet),
    "isBlockColor": (()=>isBlockColor),
    "isBlockDocument": (()=>isBlockDocument),
    "isBlockDocumentSys": (()=>isBlockDocumentSys),
    "isBlockFile": (()=>isBlockFile),
    "isBlockImage": (()=>isBlockImage),
    "isBlockList": (()=>isBlockList),
    "isBlockOgImage": (()=>isBlockOgImage),
    "isBlockRichText": (()=>isBlockRichText),
    "isBlockVideo": (()=>isBlockVideo),
    "isBlog": (()=>isBlog),
    "isBody": (()=>isBody),
    "isBodyRichText": (()=>isBodyRichText),
    "isBody_1": (()=>isBody_1),
    "isBody_1RichText": (()=>isBody_1RichText),
    "isCategories": (()=>isCategories),
    "isCategoriesItem": (()=>isCategoriesItem),
    "isGetUploadSignedURL": (()=>isGetUploadSignedURL),
    "isLegalPages": (()=>isLegalPages),
    "isLegalPagesItem": (()=>isLegalPagesItem),
    "isListMeta": (()=>isListMeta),
    "isMediaBlock": (()=>isMediaBlock),
    "isMediaBlockUnion": (()=>isMediaBlockUnion),
    "isMutation": (()=>isMutation),
    "isPosts": (()=>isPosts),
    "isPostsItem": (()=>isPostsItem),
    "isQuery": (()=>isQuery),
    "isRepoSys": (()=>isRepoSys),
    "isRichTextJson": (()=>isRichTextJson),
    "isTransactionStatus": (()=>isTransactionStatus),
    "isVariant": (()=>isVariant),
    "is_AgentSTART": (()=>is_AgentSTART),
    "is_BranchInfo": (()=>is_BranchInfo),
    "is_Branches": (()=>is_Branches),
    "is_CommitInfo": (()=>is_CommitInfo),
    "is_GitInfo": (()=>is_GitInfo),
    "is_PlaygroundInfo": (()=>is_PlaygroundInfo),
    "is_agents": (()=>is_agents),
    "is_components": (()=>is_components),
    "isauthorsItem_AsList": (()=>isauthorsItem_AsList),
    "iscategoriesItem_AsList": (()=>iscategoriesItem_AsList),
    "islegalPagesItem_AsList": (()=>islegalPagesItem_AsList),
    "ispostsItem_AsList": (()=>ispostsItem_AsList)
});
function fragmentOn(name, fields) {
    return {
        __fragmentOn: name,
        ...fields
    };
}
function fragmentOnRecursiveCollection(name, fields, options) {
    let current = {
        ...fields
    };
    if (options.levels > 0) {
        current[options.recursiveKey] = {
            ...options.getLevelArgs ? {
                __args: options.getLevelArgs(options.levels)
            } : {},
            items: fragmentOnRecursiveCollection(name, fields, {
                ...options,
                levels: options.levels - 1
            })
        };
    }
    return current;
}
const Authors_possibleTypes = [
    'Authors'
];
const isAuthors = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isAuthors"');
    return Authors_possibleTypes.includes(obj.__typename);
};
const AuthorsItem_possibleTypes = [
    'AuthorsItem'
];
const isAuthorsItem = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isAuthorsItem"');
    return AuthorsItem_possibleTypes.includes(obj.__typename);
};
const BaseRichTextJson_possibleTypes = [
    'BaseRichTextJson'
];
const isBaseRichTextJson = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isBaseRichTextJson"');
    return BaseRichTextJson_possibleTypes.includes(obj.__typename);
};
const BlockAudio_possibleTypes = [
    'BlockAudio'
];
const isBlockAudio = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isBlockAudio"');
    return BlockAudio_possibleTypes.includes(obj.__typename);
};
const BlockCodeSnippet_possibleTypes = [
    'BlockCodeSnippet'
];
const isBlockCodeSnippet = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isBlockCodeSnippet"');
    return BlockCodeSnippet_possibleTypes.includes(obj.__typename);
};
const BlockColor_possibleTypes = [
    'BlockColor'
];
const isBlockColor = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isBlockColor"');
    return BlockColor_possibleTypes.includes(obj.__typename);
};
const BlockDocument_possibleTypes = [
    'Authors',
    'AuthorsItem',
    'Blog',
    'Categories',
    'CategoriesItem',
    'LegalPages',
    'LegalPagesItem',
    'Posts',
    'PostsItem',
    '_AgentSTART',
    'authorsItem_AsList',
    'categoriesItem_AsList',
    'legalPagesItem_AsList',
    'postsItem_AsList'
];
const isBlockDocument = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isBlockDocument"');
    return BlockDocument_possibleTypes.includes(obj.__typename);
};
const BlockDocumentSys_possibleTypes = [
    'BlockDocumentSys'
];
const isBlockDocumentSys = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isBlockDocumentSys"');
    return BlockDocumentSys_possibleTypes.includes(obj.__typename);
};
const BlockFile_possibleTypes = [
    'BlockFile'
];
const isBlockFile = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isBlockFile"');
    return BlockFile_possibleTypes.includes(obj.__typename);
};
const BlockImage_possibleTypes = [
    'BlockImage'
];
const isBlockImage = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isBlockImage"');
    return BlockImage_possibleTypes.includes(obj.__typename);
};
const BlockList_possibleTypes = [
    'Authors',
    'Categories',
    'LegalPages',
    'Posts',
    'authorsItem_AsList',
    'categoriesItem_AsList',
    'legalPagesItem_AsList',
    'postsItem_AsList'
];
const isBlockList = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isBlockList"');
    return BlockList_possibleTypes.includes(obj.__typename);
};
const BlockOgImage_possibleTypes = [
    'BlockOgImage'
];
const isBlockOgImage = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isBlockOgImage"');
    return BlockOgImage_possibleTypes.includes(obj.__typename);
};
const BlockRichText_possibleTypes = [
    'Body',
    'Body_1'
];
const isBlockRichText = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isBlockRichText"');
    return BlockRichText_possibleTypes.includes(obj.__typename);
};
const BlockVideo_possibleTypes = [
    'BlockVideo'
];
const isBlockVideo = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isBlockVideo"');
    return BlockVideo_possibleTypes.includes(obj.__typename);
};
const Blog_possibleTypes = [
    'Blog'
];
const isBlog = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isBlog"');
    return Blog_possibleTypes.includes(obj.__typename);
};
const Body_possibleTypes = [
    'Body'
];
const isBody = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isBody"');
    return Body_possibleTypes.includes(obj.__typename);
};
const BodyRichText_possibleTypes = [
    'BodyRichText'
];
const isBodyRichText = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isBodyRichText"');
    return BodyRichText_possibleTypes.includes(obj.__typename);
};
const Body_1_possibleTypes = [
    'Body_1'
];
const isBody_1 = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isBody_1"');
    return Body_1_possibleTypes.includes(obj.__typename);
};
const Body_1RichText_possibleTypes = [
    'Body_1RichText'
];
const isBody_1RichText = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isBody_1RichText"');
    return Body_1RichText_possibleTypes.includes(obj.__typename);
};
const Categories_possibleTypes = [
    'Categories'
];
const isCategories = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isCategories"');
    return Categories_possibleTypes.includes(obj.__typename);
};
const CategoriesItem_possibleTypes = [
    'CategoriesItem'
];
const isCategoriesItem = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isCategoriesItem"');
    return CategoriesItem_possibleTypes.includes(obj.__typename);
};
const GetUploadSignedURL_possibleTypes = [
    'GetUploadSignedURL'
];
const isGetUploadSignedURL = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isGetUploadSignedURL"');
    return GetUploadSignedURL_possibleTypes.includes(obj.__typename);
};
const LegalPages_possibleTypes = [
    'LegalPages'
];
const isLegalPages = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isLegalPages"');
    return LegalPages_possibleTypes.includes(obj.__typename);
};
const LegalPagesItem_possibleTypes = [
    'LegalPagesItem'
];
const isLegalPagesItem = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isLegalPagesItem"');
    return LegalPagesItem_possibleTypes.includes(obj.__typename);
};
const ListMeta_possibleTypes = [
    'ListMeta'
];
const isListMeta = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isListMeta"');
    return ListMeta_possibleTypes.includes(obj.__typename);
};
const MediaBlock_possibleTypes = [
    'BlockAudio',
    'BlockFile',
    'BlockImage',
    'BlockVideo'
];
const isMediaBlock = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isMediaBlock"');
    return MediaBlock_possibleTypes.includes(obj.__typename);
};
const MediaBlockUnion_possibleTypes = [
    'BlockAudio',
    'BlockFile',
    'BlockImage',
    'BlockVideo'
];
const isMediaBlockUnion = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isMediaBlockUnion"');
    return MediaBlockUnion_possibleTypes.includes(obj.__typename);
};
const Mutation_possibleTypes = [
    'Mutation'
];
const isMutation = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isMutation"');
    return Mutation_possibleTypes.includes(obj.__typename);
};
const Posts_possibleTypes = [
    'Posts'
];
const isPosts = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isPosts"');
    return Posts_possibleTypes.includes(obj.__typename);
};
const PostsItem_possibleTypes = [
    'PostsItem'
];
const isPostsItem = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isPostsItem"');
    return PostsItem_possibleTypes.includes(obj.__typename);
};
const Query_possibleTypes = [
    'Query'
];
const isQuery = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isQuery"');
    return Query_possibleTypes.includes(obj.__typename);
};
const RepoSys_possibleTypes = [
    'RepoSys'
];
const isRepoSys = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isRepoSys"');
    return RepoSys_possibleTypes.includes(obj.__typename);
};
const RichTextJson_possibleTypes = [
    'BaseRichTextJson',
    'BodyRichText',
    'Body_1RichText'
];
const isRichTextJson = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isRichTextJson"');
    return RichTextJson_possibleTypes.includes(obj.__typename);
};
const TransactionStatus_possibleTypes = [
    'TransactionStatus'
];
const isTransactionStatus = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isTransactionStatus"');
    return TransactionStatus_possibleTypes.includes(obj.__typename);
};
const Variant_possibleTypes = [
    'Variant'
];
const isVariant = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isVariant"');
    return Variant_possibleTypes.includes(obj.__typename);
};
const _AgentSTART_possibleTypes = [
    '_AgentSTART'
];
const is_AgentSTART = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "is_AgentSTART"');
    return _AgentSTART_possibleTypes.includes(obj.__typename);
};
const _BranchInfo_possibleTypes = [
    '_BranchInfo'
];
const is_BranchInfo = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "is_BranchInfo"');
    return _BranchInfo_possibleTypes.includes(obj.__typename);
};
const _Branches_possibleTypes = [
    '_Branches'
];
const is_Branches = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "is_Branches"');
    return _Branches_possibleTypes.includes(obj.__typename);
};
const _CommitInfo_possibleTypes = [
    '_CommitInfo'
];
const is_CommitInfo = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "is_CommitInfo"');
    return _CommitInfo_possibleTypes.includes(obj.__typename);
};
const _GitInfo_possibleTypes = [
    '_GitInfo'
];
const is_GitInfo = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "is_GitInfo"');
    return _GitInfo_possibleTypes.includes(obj.__typename);
};
const _PlaygroundInfo_possibleTypes = [
    '_PlaygroundInfo'
];
const is_PlaygroundInfo = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "is_PlaygroundInfo"');
    return _PlaygroundInfo_possibleTypes.includes(obj.__typename);
};
const _agents_possibleTypes = [
    '_agents'
];
const is_agents = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "is_agents"');
    return _agents_possibleTypes.includes(obj.__typename);
};
const _components_possibleTypes = [
    '_components'
];
const is_components = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "is_components"');
    return _components_possibleTypes.includes(obj.__typename);
};
const authorsItem_AsList_possibleTypes = [
    'authorsItem_AsList'
];
const isauthorsItem_AsList = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "isauthorsItem_AsList"');
    return authorsItem_AsList_possibleTypes.includes(obj.__typename);
};
const categoriesItem_AsList_possibleTypes = [
    'categoriesItem_AsList'
];
const iscategoriesItem_AsList = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "iscategoriesItem_AsList"');
    return categoriesItem_AsList_possibleTypes.includes(obj.__typename);
};
const legalPagesItem_AsList_possibleTypes = [
    'legalPagesItem_AsList'
];
const islegalPagesItem_AsList = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "islegalPagesItem_AsList"');
    return legalPagesItem_AsList_possibleTypes.includes(obj.__typename);
};
const postsItem_AsList_possibleTypes = [
    'postsItem_AsList'
];
const ispostsItem_AsList = (obj)=>{
    if (!obj?.__typename) throw new Error('__typename is missing in "ispostsItem_AsList"');
    return postsItem_AsList_possibleTypes.includes(obj.__typename);
};
const enumAnalyticsKeyScope = {
    query: 'query',
    send: 'send'
};
const enumAuthorsItemOrderByEnum = {
    _sys_createdAt__ASC: '_sys_createdAt__ASC',
    _sys_createdAt__DESC: '_sys_createdAt__DESC',
    _sys_hash__ASC: '_sys_hash__ASC',
    _sys_hash__DESC: '_sys_hash__DESC',
    _sys_id__ASC: '_sys_id__ASC',
    _sys_id__DESC: '_sys_id__DESC',
    _sys_lastModifiedAt__ASC: '_sys_lastModifiedAt__ASC',
    _sys_lastModifiedAt__DESC: '_sys_lastModifiedAt__DESC',
    _sys_slug__ASC: '_sys_slug__ASC',
    _sys_slug__DESC: '_sys_slug__DESC',
    _sys_title__ASC: '_sys_title__ASC',
    _sys_title__DESC: '_sys_title__DESC',
    avatar__ASC: 'avatar__ASC',
    avatar__DESC: 'avatar__DESC',
    xUrl__ASC: 'xUrl__ASC',
    xUrl__DESC: 'xUrl__DESC'
};
const enumCategoriesItemOrderByEnum = {
    _sys_createdAt__ASC: '_sys_createdAt__ASC',
    _sys_createdAt__DESC: '_sys_createdAt__DESC',
    _sys_hash__ASC: '_sys_hash__ASC',
    _sys_hash__DESC: '_sys_hash__DESC',
    _sys_id__ASC: '_sys_id__ASC',
    _sys_id__DESC: '_sys_id__DESC',
    _sys_lastModifiedAt__ASC: '_sys_lastModifiedAt__ASC',
    _sys_lastModifiedAt__DESC: '_sys_lastModifiedAt__DESC',
    _sys_slug__ASC: '_sys_slug__ASC',
    _sys_slug__DESC: '_sys_slug__DESC',
    _sys_title__ASC: '_sys_title__ASC',
    _sys_title__DESC: '_sys_title__DESC'
};
const enumLegalPagesItemOrderByEnum = {
    _sys_createdAt__ASC: '_sys_createdAt__ASC',
    _sys_createdAt__DESC: '_sys_createdAt__DESC',
    _sys_hash__ASC: '_sys_hash__ASC',
    _sys_hash__DESC: '_sys_hash__DESC',
    _sys_id__ASC: '_sys_id__ASC',
    _sys_id__DESC: '_sys_id__DESC',
    _sys_lastModifiedAt__ASC: '_sys_lastModifiedAt__ASC',
    _sys_lastModifiedAt__DESC: '_sys_lastModifiedAt__DESC',
    _sys_slug__ASC: '_sys_slug__ASC',
    _sys_slug__DESC: '_sys_slug__DESC',
    _sys_title__ASC: '_sys_title__ASC',
    _sys_title__DESC: '_sys_title__DESC',
    body__ASC: 'body__ASC',
    body__DESC: 'body__DESC',
    description__ASC: 'description__ASC',
    description__DESC: 'description__DESC'
};
const enumPostsItemOrderByEnum = {
    _sys_createdAt__ASC: '_sys_createdAt__ASC',
    _sys_createdAt__DESC: '_sys_createdAt__DESC',
    _sys_hash__ASC: '_sys_hash__ASC',
    _sys_hash__DESC: '_sys_hash__DESC',
    _sys_id__ASC: '_sys_id__ASC',
    _sys_id__DESC: '_sys_id__DESC',
    _sys_lastModifiedAt__ASC: '_sys_lastModifiedAt__ASC',
    _sys_lastModifiedAt__DESC: '_sys_lastModifiedAt__DESC',
    _sys_slug__ASC: '_sys_slug__ASC',
    _sys_slug__DESC: '_sys_slug__DESC',
    _sys_title__ASC: '_sys_title__ASC',
    _sys_title__DESC: '_sys_title__DESC',
    authors__ASC: 'authors__ASC',
    authors__DESC: 'authors__DESC',
    body__ASC: 'body__ASC',
    body__DESC: 'body__DESC',
    categories__ASC: 'categories__ASC',
    categories__DESC: 'categories__DESC',
    date__ASC: 'date__ASC',
    date__DESC: 'date__DESC',
    description__ASC: 'description__ASC',
    description__DESC: 'description__DESC',
    image__ASC: 'image__ASC',
    image__DESC: 'image__DESC'
};
const enumTransactionStatusEnum = {
    Cancelled: 'Cancelled',
    Completed: 'Completed',
    Failed: 'Failed',
    Running: 'Running',
    Scheduled: 'Scheduled'
};
const enum_resolveTargetsWithEnum = {
    id: 'id',
    objectName: 'objectName'
};
const enum_structureFormatEnum = {
    json: 'json',
    xml: 'xml'
};
}}),
"[project]/packages/cms/.basehub/index.ts [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk
/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ // @ts-nocheck
__turbopack_context__.s({
    "basehub": (()=>basehub),
    "cacheTagFromQuery": (()=>cacheTagFromQuery),
    "everything": (()=>everything),
    "generateMutationOp": (()=>generateMutationOp),
    "generateQueryOp": (()=>generateQueryOp),
    "getStuffFromEnv": (()=>getStuffFromEnv),
    "gitBranchDeploymentURL": (()=>gitBranchDeploymentURL),
    "isNextjs": (()=>isNextjs),
    "productionDeploymentURL": (()=>productionDeploymentURL),
    "resolvedRef": (()=>resolvedRef),
    "sdkBuildId": (()=>sdkBuildId)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/index.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_link$2d$type$2d$map$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_link-type-map.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_create$2d$client$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_create-client.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_generate$2d$graphql$2d$operation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_generate-graphql-operation.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/types.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/schema.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_fetcher$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/_fetcher.ts [app-rsc] (ecmascript)");
;
;
;
;
const typeMap = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_link$2d$type$2d$map$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["linkTypeMap"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"]);
const createClient = function(options) {
    const { url, headers } = getStuffFromEnv(options);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_create$2d$client$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])({
        url: url.toString(),
        ...options,
        headers: {
            ...options?.headers,
            ...headers
        },
        queryRoot: typeMap.Query,
        mutationRoot: typeMap.Mutation,
        subscriptionRoot: typeMap.Subscription
    });
};
const everything = {
    __scalar: true
};
const generateQueryOp = function(fields) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_generate$2d$graphql$2d$operation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateGraphqlOperation"])('query', typeMap.Query, fields);
};
const generateMutationOp = function(fields) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_generate$2d$graphql$2d$operation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateGraphqlOperation"])('mutation', typeMap.Mutation, fields);
};
const getStuffFromEnv = (options)=>{
    const defaultEnvVarPrefix = "BASEHUB";
    options = options || {};
    if (options.token === undefined) {
        options.token = undefined || null;
    }
    if (options.prefix === undefined) {
        options.prefix = undefined || null;
    }
    // we'll use the draft from .env if available
    if (!options.draft && true) {
        options.draft = true;
    }
    const buildEnvVarName = (name)=>{
        let prefix = defaultEnvVarPrefix;
        if (options.prefix) {
            if (options.prefix.endsWith("_")) {
                options.prefix = options.prefix.slice(0, -1); // we remove the trailing _
            }
            if (options.prefix.endsWith(name)) {
                // remove the name from the prefix
                options.prefix = options.prefix.slice(0, -name.length);
            }
            // the user may include BASEHUB in their prefix...
            if (options.prefix.endsWith(defaultEnvVarPrefix)) {
                prefix = options.prefix;
            } else {
                // ... if they don't, we'll add it ourselves.
                prefix = `${options.prefix}_${defaultEnvVarPrefix}`;
            }
        }
        // this should result in something like <prefix>_BASEHUB_{TOKEN,REF,DRAFT} or BASEHUB_{TOKEN,REF,DRAFT}
        return `${prefix}_${name}`;
    };
    const getEnvVar = (name)=>{
        if (typeof process === 'undefined') {
            return undefined;
        }
        return process?.env?.[buildEnvVarName(name)];
    };
    const parsedDebugForcedURL = getEnvVar("DEBUG_FORCED_URL");
    const parsedBackwardsCompatURL = getEnvVar("URL");
    const backwardsCompatURL = parsedBackwardsCompatURL ? new URL(parsedBackwardsCompatURL) : undefined;
    const basehubUrl = new URL(parsedDebugForcedURL ? parsedDebugForcedURL : `https://api.basehub.com/graphql`);
    // These params can either come disambiguated, or in the URL.
    // Params that come from the URL take precedence.
    const parsedBasehubTokenEnv = getEnvVar("TOKEN");
    const parsedBasehubRefEnv = getEnvVar("REF");
    const parsedBasehubDraftEnv = getEnvVar("DRAFT");
    const parsedBasehubApiVersionEnv = getEnvVar("API_VERSION");
    let tokenNotFoundErrorMessage = `🔴 Token not found. Make sure to include the ${buildEnvVarName("TOKEN")} env var.`;
    const resolveTokenParam = (token)=>{
        if (!token) return null;
        const isRaw = token.startsWith("bshb_");
        if (isRaw) return token;
        tokenNotFoundErrorMessage = `🔴 Token not found. Make sure to include the ${token} env var.`;
        if (typeof process === 'undefined') {
            return undefined;
        }
        return process?.env?.[token] ?? ''; // empty string to prevent fallback
    };
    const resolvedToken = resolveTokenParam(options?.token ?? null);
    const token = resolvedToken ?? basehubUrl.searchParams.get("token") ?? parsedBasehubTokenEnv ?? (backwardsCompatURL ? backwardsCompatURL.searchParams.get("token") : undefined) ?? null;
    if (!token) {
        throw new Error(tokenNotFoundErrorMessage);
    }
    let draft = basehubUrl.searchParams.get("draft") ?? parsedBasehubDraftEnv ?? (backwardsCompatURL ? backwardsCompatURL.searchParams.get("draft") : undefined) ?? false;
    if (options?.draft !== undefined) {
        draft = options.draft;
    }
    let apiVersion = basehubUrl.searchParams.get("api-version") ?? parsedBasehubApiVersionEnv ?? (backwardsCompatURL ? backwardsCompatURL.searchParams.get("api-version") : undefined) ?? "4";
    if (options?.apiVersion !== undefined) {
        apiVersion = options.apiVersion;
    }
    // 2. let's validate the URL
    if (basehubUrl.pathname.split("/")[1] !== "graphql") {
        throw new Error(`🔴 Invalid URL. The URL needs to point your repo's GraphQL endpoint, so the pathname should end with /graphql.`);
    }
    // we'll pass these via headers
    basehubUrl.searchParams.delete("token");
    basehubUrl.searchParams.delete("ref");
    basehubUrl.searchParams.delete("draft");
    basehubUrl.searchParams.delete("api-version");
    // 3.
    const gitBranch = "main";
    const gitCommitSHA = "ceca9cdc63d7a1a1878e4d8bb407439b741c24b6";
    const sdkBuildId = "bshb_sdk_f998162618e2d";
    return {
        isForcedDraft: true,
        draft,
        url: basehubUrl,
        sdkBuildId,
        headers: {
            "x-basehub-token": token,
            "x-basehub-ref": options?.ref ?? resolvedRef.ref,
            "x-basehub-sdk-build-id": sdkBuildId,
            ...("TURBOPACK compile-time truthy", 1) ? {
                "x-basehub-git-branch": gitBranch
            } : ("TURBOPACK unreachable", undefined),
            ...("TURBOPACK compile-time truthy", 1) ? {
                "x-basehub-git-commit-sha": gitCommitSHA
            } : ("TURBOPACK unreachable", undefined),
            ...("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : {},
            ...("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : {},
            ...draft ? {
                "x-basehub-draft": "true"
            } : {},
            ...apiVersion ? {
                "x-basehub-api-version": apiVersion
            } : {}
        }
    };
};
;
const sdkBuildId = "bshb_sdk_f998162618e2d";
const resolvedRef = {
    "repoHash": "57ec52db",
    "type": "branch",
    "ref": "main",
    "createSuggestedBranchLink": null,
    "id": "KluwvFPvKCxusUOmSQG4q",
    "name": "main",
    "git": null,
    "createdAt": "2025-06-16T00:30:26.760Z",
    "archivedAt": null,
    "archivedBy": null,
    "headCommitId": "HijQ0sLasCsht2mw0eLWG",
    "isDefault": true,
    "deletedAt": null,
    "workingRootBlockId": "a8Oul5Re6jsffvG4Ab5XZ"
};
const gitBranchDeploymentURL = null;
const productionDeploymentURL = null;
const isNextjs = true;
/**
 * Returns a hash code from an object
 * @param  {Object} obj The object to hash.
 * @return {String}    A hash string
 */ function hashObject(obj) {
    const sortObjectKeys = (obj)=>{
        if (!isObjectAsWeCommonlyCallIt(obj)) return obj;
        return Object.keys(obj).sort().reduce((acc, key)=>{
            acc[key] = obj[key];
            return acc;
        }, {});
    };
    const recursiveSortObjectKeys = (obj)=>{
        const sortedObj = sortObjectKeys(obj);
        if (!isObjectAsWeCommonlyCallIt(sortedObj)) return sortedObj;
        Object.keys(sortedObj).forEach((key)=>{
            if (isObjectAsWeCommonlyCallIt(sortedObj[key])) {
                sortedObj[key] = recursiveSortObjectKeys(sortedObj[key]);
            } else if (Array.isArray(sortedObj[key])) {
                sortedObj[key] = sortedObj[key].map((item)=>{
                    if (isObjectAsWeCommonlyCallIt(item)) {
                        return recursiveSortObjectKeys(item);
                    } else {
                        return item;
                    }
                });
            }
        });
        return sortedObj;
    };
    const isObjectAsWeCommonlyCallIt = (obj)=>{
        return Object.prototype.toString.call(obj) === '[object Object]';
    };
    const sortedObj = recursiveSortObjectKeys(obj);
    const str = JSON.stringify(sortedObj);
    let hash = 0;
    for(let i = 0, len = str.length; i < len; i++){
        let chr = str.charCodeAt(i);
        hash = (hash << 5) - hash + chr;
        hash |= 0; // Convert to 32bit integer
    }
    return Math.abs(hash).toString();
}
function cacheTagFromQuery(query, apiVersion) {
    const now = performance.now();
    const result = "basehub-" + hashObject({
        ...query,
        refId: resolvedRef.id,
        ...apiVersion ? {
            apiVersion
        } : {}
    });
    return result;
}
const basehub = (options)=>{
    const { url, headers } = getStuffFromEnv(options);
    if (!options) {
        options = {};
    }
    options.getExtraFetchOptions = async (op, _body, originalRequest)=>{
        if (op !== 'query') return {};
        let extra = {
            headers: {
                "x-basehub-sdk-build-id": sdkBuildId
            }
        };
        let isNextjsDraftMode = false;
        if (options.draft === undefined) {
            // try to auto-detect (only if draft is not explicitly set by the user)
            try {
                const { draftMode } = await __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
                isNextjsDraftMode = (await draftMode()).isEnabled;
            } catch (error) {
            // noop, not using nextjs
            }
        }
        const isDraftResolved = true || isNextjsDraftMode || options.draft === true;
        if ("TURBOPACK compile-time truthy", 1) {
            extra.headers = {
                ...extra.headers,
                "x-basehub-draft": "true"
            };
            // get rid of automatic nextjs caching
            extra.next = {
                revalidate: undefined
            };
            extra.cache = "no-store";
            // try to get ref from cookies
            try {
                const { cookies } = await __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
                const cookieStore = await cookies();
                const ref = cookieStore.get("bshb-preview-ref-" + resolvedRef.repoHash)?.value;
                if (ref) {
                    extra.headers = {
                        ...extra.headers,
                        "x-basehub-ref": ref
                    };
                }
            } catch (error) {
            // noop 
            }
        }
        if ("TURBOPACK compile-time truthy", 1) return extra;
        "TURBOPACK unreachable";
    };
    return {
        ...createClient(options),
        raw: (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_fetcher$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createFetcher"])({
            ...options,
            url,
            headers
        })
    };
};
basehub.replaceSystemAliases = __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$_create$2d$client$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"].replaceSystemAliases;
}}),
"[project]/packages/cms/.basehub/index.ts [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$runtime$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/runtime/index.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/types.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/schema.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/index.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/packages/cms/.basehub/next-toolbar/index.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk
/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ /* __next_internal_action_entry_do_not_use__ [{"000b2ff4143fed18480598c3e642376619afe6aee7":"$$RSC_SERVER_ACTION_2","60731820c97062f3beaf2f0a990b8a063944ca076d":"$$RSC_SERVER_ACTION_0","60a2760dfaa22049980d9c07fc3162c2da14f05ff9":"$$RSC_SERVER_ACTION_1","60affb05dd27939552a9e835c36ec78df1dbcfc16e":"$$RSC_SERVER_ACTION_3"},"",""] */ __turbopack_context__.s({
    "$$RSC_SERVER_ACTION_0": (()=>$$RSC_SERVER_ACTION_0),
    "$$RSC_SERVER_ACTION_1": (()=>$$RSC_SERVER_ACTION_1),
    "$$RSC_SERVER_ACTION_2": (()=>$$RSC_SERVER_ACTION_2),
    "$$RSC_SERVER_ACTION_3": (()=>$$RSC_SERVER_ACTION_3),
    "Toolbar": (()=>ServerToolbar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$next$2d$toolbar$2f$chunk$2d$YSQDPG26$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/next-toolbar/chunk-YSQDPG26.js [app-rsc] (ecmascript)");
// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/server-toolbar.tsx
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/headers.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/index.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/index.ts [app-rsc] (ecmascript) <locals>");
;
;
;
;
;
;
;
var LazyClientConditionalRenderer = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["lazy"])(()=>__turbopack_context__.r("[project]/packages/cms/.basehub/next-toolbar/client-conditional-renderer-KQINRCBN.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i).then((mod)=>({
            default: mod.ClientConditionalRenderer
        })));
const $$RSC_SERVER_ACTION_0 = async function enableDraftMode_unbound(basehubProps2, { bshbPreviewToken }) {
    try {
        const { headers, url } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getStuffFromEnv"])(basehubProps2);
        const appApiEndpoint = getBaseHubAppApiEndpoint(url, "/api/nextjs/preview-auth");
        const res = await fetch(appApiEndpoint, {
            cache: "no-store",
            method: "POST",
            headers: {
                "content-type": "application/json",
                "x-basehub-token": headers["x-basehub-token"]
            },
            body: JSON.stringify({
                bshbPreview: bshbPreviewToken
            })
        });
        const responseIsJson = res.headers.get("content-type")?.includes("json");
        if (!responseIsJson) {
            return {
                status: 400,
                response: {
                    error: "Bad request"
                }
            };
        }
        const response = await res.json();
        if (res.status === 200) (await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["draftMode"])()).enable();
        return {
            status: res.status,
            response
        };
    } catch (error) {
        return {
            status: 500,
            response: {
                error: "Something went wrong"
            }
        };
    }
};
const $$RSC_SERVER_ACTION_1 = async function getLatestBranches_unbound(basehubProps2, { bshbPreviewToken }) {
    try {
        const { headers, url, isForcedDraft: isForcedDraft2 } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getStuffFromEnv"])(basehubProps2);
        if ((await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["draftMode"])()).isEnabled === false && !isForcedDraft2 && !bshbPreviewToken) {
            return {
                status: 403,
                response: {
                    error: "Unauthorized"
                }
            };
        }
        const appApiEndpoint = getBaseHubAppApiEndpoint(url, "/api/nextjs/latest-branches");
        const res = await fetch(appApiEndpoint, {
            cache: "no-store",
            method: "GET",
            headers: {
                "content-type": "application/json",
                "x-basehub-token": headers["x-basehub-token"],
                ...bshbPreviewToken && {
                    "x-basehub-preview-token": bshbPreviewToken
                },
                ...isForcedDraft2 && {
                    "x-basehub-forced-draft": "true"
                }
            }
        });
        const responseIsJson = res.headers.get("content-type")?.includes("json");
        if (!responseIsJson) {
            return {
                status: 400,
                response: {
                    error: "Bad request"
                }
            };
        }
        const response = await res.json();
        return {
            status: res.status,
            response
        };
    } catch (error) {
        return {
            status: 500,
            response: {
                error: "Something went wrong"
            }
        };
    }
};
const $$RSC_SERVER_ACTION_2 = async function disableDraftMode() {
    (await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["draftMode"])()).disable();
};
const $$RSC_SERVER_ACTION_3 = async function revalidateTags_unbound(basehubProps2, { bshbPreviewToken, ref }) {
    const { headers, url } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getStuffFromEnv"])(basehubProps2);
    const appApiEndpoint = getBaseHubAppApiEndpoint(url, "/api/nextjs/pending-tags");
    if (!bshbPreviewToken) {
        return {
            success: false,
            error: "Unauthorized"
        };
    }
    const res = await fetch(appApiEndpoint, {
        cache: "no-store",
        method: "GET",
        headers: {
            "content-type": "application/json",
            "x-basehub-token": headers["x-basehub-token"],
            "x-basehub-ref": ref || headers["x-basehub-ref"],
            "x-basehub-preview-token": bshbPreviewToken,
            "x-basehub-sdk-build-id": headers["x-basehub-sdk-build-id"]
        }
    });
    if (res.status !== 200) {
        return {
            success: false,
            message: `Received status ${res.status} from server`
        };
    }
    const response = await res.json();
    try {
        const { tags } = response;
        if (!tags || !Array.isArray(tags) || tags.length === 0) {
            return {
                success: true,
                message: "No tags to revalidate"
            };
        }
        await Promise.all(tags.map(async (_tag)=>{
            const tag = _tag.startsWith("basehub-") ? _tag : `basehub-${_tag}`;
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(tag);
        }));
        return {
            success: true,
            message: `Revalidated ${tags.length} tags`
        };
    } catch (error) {
        console.log(response);
        console.error(error);
        return {
            success: false,
            message: "Something went wrong while revalidating tags"
        };
    }
};
var ServerToolbar = async ({ ...basehubProps })=>{
    const { isForcedDraft } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getStuffFromEnv"])(basehubProps);
    const enableDraftMode_unbound = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])($$RSC_SERVER_ACTION_0, "60731820c97062f3beaf2f0a990b8a063944ca076d", null);
    const getLatestBranches_unbound = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])($$RSC_SERVER_ACTION_1, "60a2760dfaa22049980d9c07fc3162c2da14f05ff9", null);
    const disableDraftMode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])($$RSC_SERVER_ACTION_2, "000b2ff4143fed18480598c3e642376619afe6aee7", null);
    const revalidateTags_unbound = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])($$RSC_SERVER_ACTION_3, "60affb05dd27939552a9e835c36ec78df1dbcfc16e", null);
    const enableDraftMode = enableDraftMode_unbound.bind(null, basehubProps);
    const getLatestBranches = getLatestBranches_unbound.bind(null, basehubProps);
    const revalidateTags = revalidateTags_unbound.bind(null, basehubProps);
    return /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createElement"])(LazyClientConditionalRenderer, {
        draft: (await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["draftMode"])()).isEnabled,
        isForcedDraft,
        enableDraftMode,
        disableDraftMode,
        revalidateTags,
        getLatestBranches,
        resolvedRef: __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["resolvedRef"]
    });
};
function getBaseHubAppApiEndpoint(url, pathname) {
    let origin;
    switch(true){
        case url.origin.includes("api.basehub.com"):
            origin = "https://basehub.com" + pathname + url.search + url.hash;
            break;
        case url.origin.includes("api.bshb.dev"):
            origin = "https://basehub.dev" + pathname + url.search + url.hash;
            break;
        case url.origin.includes("localhost:3001"):
            origin = "http://localhost:3000" + pathname + url.search + url.hash;
            break;
        default:
            origin = url.origin + pathname + url.search + url.hash;
    }
    return origin;
}
;
}}),
"[project]/packages/cms/components/toolbar.tsx [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$next$2d$toolbar$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/next-toolbar/index.js [app-rsc] (ecmascript)");
;
}}),
"[project]/packages/cms/components/toolbar.tsx [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$next$2d$toolbar$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/next-toolbar/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f$components$2f$toolbar$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/packages/cms/components/toolbar.tsx [app-rsc] (ecmascript) <locals>");
}}),
"[project]/packages/feature-flags/keys.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "keys": (()=>keys)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$nextjs$40$0$2e$13$2e$4_a_5f24d1d288682fc49fcfce0396a9018e$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@t3-oss+env-nextjs@0.13.4_a_5f24d1d288682fc49fcfce0396a9018e/node_modules/@t3-oss/env-nextjs/dist/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
const keys = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$nextjs$40$0$2e$13$2e$4_a_5f24d1d288682fc49fcfce0396a9018e$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createEnv"])({
        server: {
            FLAGS_SECRET: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
        },
        runtimeEnv: {
            FLAGS_SECRET: process.env.FLAGS_SECRET
        }
    });
}}),
"[project]/packages/feature-flags/components/toolbar.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Toolbar": (()=>Toolbar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$vercel$2b$toolbar$40$0$2e$1$2e$37_$40$ver_bda1ef661f09be058a55068699771d3b$2f$node_modules$2f40$vercel$2f$toolbar$2f$dist$2f$next$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@vercel+toolbar@0.1.37_@ver_bda1ef661f09be058a55068699771d3b/node_modules/@vercel/toolbar/dist/next/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$feature$2d$flags$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/feature-flags/keys.ts [app-rsc] (ecmascript)");
;
;
;
const Toolbar = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$feature$2d$flags$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["keys"])().FLAGS_SECRET ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$vercel$2b$toolbar$40$0$2e$1$2e$37_$40$ver_bda1ef661f09be058a55068699771d3b$2f$node_modules$2f40$vercel$2f$toolbar$2f$dist$2f$next$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["VercelToolbar"], {}, void 0, false, {
        fileName: "[project]/packages/feature-flags/components/toolbar.tsx",
        lineNumber: 4,
        columnNumber: 53
    }, this) : null;
}}),
"[project]/packages/internationalization/languine.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"locale\":{\"source\":\"en\",\"targets\":[]},\"files\":{\"json\":{\"include\":[\"dictionaries/[locale].json\"]}}}"));}}),
"[project]/packages/internationalization/index.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getDictionary": (()=>getDictionary),
    "locales": (()=>locales)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$server$2d$only$2f$empty$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/server-only/empty.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$internationalization$2f$languine$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/packages/internationalization/languine.json (json)");
;
;
const locales = [
    __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$internationalization$2f$languine$2e$json__$28$json$29$__["default"].locale.source,
    ...__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$internationalization$2f$languine$2e$json__$28$json$29$__["default"].locale.targets
];
const dictionaries = Object.fromEntries(locales.map((locale)=>[
        locale,
        ()=>__turbopack_context__.r("[project]/packages/internationalization/dictionaries/en.json (json, async loader)")(__turbopack_context__.i).then((mod)=>mod.default).catch((err)=>{
                console.error(`Failed to load dictionary for locale: ${locale}`, err);
                return __turbopack_context__.r("[project]/packages/internationalization/dictionaries/en.json (json, async loader)")(__turbopack_context__.i).then((mod)=>mod.default);
            })
    ]));
const getDictionary = async (locale)=>{
    const normalizedLocale = locale.split('-')[0];
    if (!locales.includes(normalizedLocale)) {
        console.warn(`Locale "${locale}" is not supported, defaulting to "en"`);
        return dictionaries['en']();
    }
    try {
        return await dictionaries[normalizedLocale]();
    } catch (error) {
        console.error(`Error loading dictionary for locale "${normalizedLocale}", falling back to "en"`, error);
        return dictionaries['en']();
    }
};
}}),
"[project]/packages/cms/keys.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "keys": (()=>keys)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$nextjs$40$0$2e$13$2e$4_a_5f24d1d288682fc49fcfce0396a9018e$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@t3-oss+env-nextjs@0.13.4_a_5f24d1d288682fc49fcfce0396a9018e/node_modules/@t3-oss/env-nextjs/dist/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
const keys = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$nextjs$40$0$2e$13$2e$4_a_5f24d1d288682fc49fcfce0396a9018e$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createEnv"])({
        server: {
            BASEHUB_TOKEN: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().startsWith('bshb_pk_')
        },
        runtimeEnv: {
            BASEHUB_TOKEN: process.env.BASEHUB_TOKEN
        }
    });
}}),
"[project]/packages/email/keys.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "keys": (()=>keys)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$nextjs$40$0$2e$13$2e$4_a_5f24d1d288682fc49fcfce0396a9018e$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@t3-oss+env-nextjs@0.13.4_a_5f24d1d288682fc49fcfce0396a9018e/node_modules/@t3-oss/env-nextjs/dist/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
const keys = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$nextjs$40$0$2e$13$2e$4_a_5f24d1d288682fc49fcfce0396a9018e$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createEnv"])({
        server: {
            RESEND_FROM: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().email(),
            RESEND_TOKEN: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().startsWith('re_')
        },
        runtimeEnv: {
            RESEND_FROM: process.env.RESEND_FROM,
            RESEND_TOKEN: process.env.RESEND_TOKEN
        }
    });
}}),
"[project]/packages/next-config/keys.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "keys": (()=>keys)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$presets$2d$zod$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@t3-oss+env-core@0.13.4_ark_5791aa5b9cbdf911b905d1920b6c6d1f/node_modules/@t3-oss/env-core/dist/presets-zod.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$nextjs$40$0$2e$13$2e$4_a_5f24d1d288682fc49fcfce0396a9018e$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@t3-oss+env-nextjs@0.13.4_a_5f24d1d288682fc49fcfce0396a9018e/node_modules/@t3-oss/env-nextjs/dist/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
;
const keys = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$nextjs$40$0$2e$13$2e$4_a_5f24d1d288682fc49fcfce0396a9018e$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createEnv"])({
        extends: [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$presets$2d$zod$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["vercel"])()
        ],
        server: {
            ANALYZE: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            // Added by Vercel
            NEXT_RUNTIME: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
                'nodejs',
                'edge'
            ]).optional()
        },
        client: {
            NEXT_PUBLIC_APP_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url(),
            NEXT_PUBLIC_WEB_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url(),
            NEXT_PUBLIC_API_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url().optional(),
            NEXT_PUBLIC_DOCS_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url().optional()
        },
        runtimeEnv: {
            ANALYZE: process.env.ANALYZE,
            NEXT_RUNTIME: ("TURBOPACK compile-time value", "nodejs"),
            NEXT_PUBLIC_APP_URL: ("TURBOPACK compile-time value", "http://localhost:3000"),
            NEXT_PUBLIC_WEB_URL: ("TURBOPACK compile-time value", "http://localhost:3001"),
            NEXT_PUBLIC_API_URL: ("TURBOPACK compile-time value", "http://localhost:3002"),
            NEXT_PUBLIC_DOCS_URL: ("TURBOPACK compile-time value", "https://docs.cubent.dev/")
        }
    });
}}),
"[project]/packages/observability/keys.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "keys": (()=>keys)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$nextjs$40$0$2e$13$2e$4_a_5f24d1d288682fc49fcfce0396a9018e$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@t3-oss+env-nextjs@0.13.4_a_5f24d1d288682fc49fcfce0396a9018e/node_modules/@t3-oss/env-nextjs/dist/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
const keys = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$nextjs$40$0$2e$13$2e$4_a_5f24d1d288682fc49fcfce0396a9018e$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createEnv"])({
        server: {
            BETTERSTACK_API_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            BETTERSTACK_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            // Added by Sentry Integration, Vercel Marketplace
            SENTRY_ORG: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            SENTRY_PROJECT: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
        },
        client: {
            // Added by Sentry Integration, Vercel Marketplace
            NEXT_PUBLIC_SENTRY_DSN: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url().optional()
        },
        runtimeEnv: {
            BETTERSTACK_API_KEY: process.env.BETTERSTACK_API_KEY,
            BETTERSTACK_URL: process.env.BETTERSTACK_URL,
            SENTRY_ORG: process.env.SENTRY_ORG,
            SENTRY_PROJECT: process.env.SENTRY_PROJECT,
            NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN
        }
    });
}}),
"[project]/packages/rate-limit/keys.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "keys": (()=>keys)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$nextjs$40$0$2e$13$2e$4_a_5f24d1d288682fc49fcfce0396a9018e$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@t3-oss+env-nextjs@0.13.4_a_5f24d1d288682fc49fcfce0396a9018e/node_modules/@t3-oss/env-nextjs/dist/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
const keys = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$nextjs$40$0$2e$13$2e$4_a_5f24d1d288682fc49fcfce0396a9018e$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createEnv"])({
        server: {
            UPSTASH_REDIS_REST_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url().optional(),
            UPSTASH_REDIS_REST_TOKEN: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
        },
        runtimeEnv: {
            UPSTASH_REDIS_REST_URL: process.env.UPSTASH_REDIS_REST_URL,
            UPSTASH_REDIS_REST_TOKEN: process.env.UPSTASH_REDIS_REST_TOKEN
        }
    });
}}),
"[project]/packages/security/keys.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "keys": (()=>keys)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$nextjs$40$0$2e$13$2e$4_a_5f24d1d288682fc49fcfce0396a9018e$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@t3-oss+env-nextjs@0.13.4_a_5f24d1d288682fc49fcfce0396a9018e/node_modules/@t3-oss/env-nextjs/dist/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
const keys = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$nextjs$40$0$2e$13$2e$4_a_5f24d1d288682fc49fcfce0396a9018e$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createEnv"])({
        server: {
            ARCJET_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().startsWith('ajkey_').optional()
        },
        runtimeEnv: {
            ARCJET_KEY: process.env.ARCJET_KEY
        }
    });
}}),
"[project]/apps/web/env.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "env": (()=>env)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/keys.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$email$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/email/keys.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$feature$2d$flags$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/feature-flags/keys.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$next$2d$config$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/next-config/keys.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$observability$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/observability/keys.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$rate$2d$limit$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/rate-limit/keys.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$security$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/security/keys.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$nextjs$40$0$2e$13$2e$4_a_5f24d1d288682fc49fcfce0396a9018e$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@t3-oss+env-nextjs@0.13.4_a_5f24d1d288682fc49fcfce0396a9018e/node_modules/@t3-oss/env-nextjs/dist/index.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
const env = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$nextjs$40$0$2e$13$2e$4_a_5f24d1d288682fc49fcfce0396a9018e$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createEnv"])({
    extends: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["keys"])(),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$next$2d$config$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["keys"])(),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$email$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["keys"])(),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$observability$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["keys"])(),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$feature$2d$flags$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["keys"])(),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$security$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["keys"])(),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$rate$2d$limit$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["keys"])()
    ],
    server: {},
    client: {},
    runtimeEnv: {}
});
}}),
"[project]/packages/cms/index.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "blog": (()=>blog),
    "legal": (()=>legal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/index.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/index.ts [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/schema.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/keys.ts [app-rsc] (ecmascript)");
;
;
const basehub = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["basehub"])({
    token: (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["keys"])().BASEHUB_TOKEN
});
/* -------------------------------------------------------------------------------------------------
 * Common Fragments
 * -----------------------------------------------------------------------------------------------*/ const imageFragment = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fragmentOn"])('BlockImage', {
    url: true,
    width: true,
    height: true,
    alt: true,
    blurDataURL: true
});
/* -------------------------------------------------------------------------------------------------
 * Blog Fragments & Queries
 * -----------------------------------------------------------------------------------------------*/ const postMetaFragment = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fragmentOn"])('PostsItem', {
    _slug: true,
    _title: true,
    authors: {
        _title: true,
        avatar: imageFragment,
        xUrl: true
    },
    categories: {
        _title: true
    },
    date: true,
    description: true,
    image: imageFragment
});
const postFragment = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fragmentOn"])('PostsItem', {
    ...postMetaFragment,
    body: {
        plainText: true,
        json: {
            content: true,
            toc: true
        },
        readingTime: true
    }
});
const blog = {
    postsQuery: (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fragmentOn"])('Query', {
        blog: {
            posts: {
                items: postMetaFragment
            }
        }
    }),
    latestPostQuery: (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fragmentOn"])('Query', {
        blog: {
            posts: {
                __args: {
                    orderBy: '_sys_createdAt__DESC'
                },
                item: postFragment
            }
        }
    }),
    postQuery: (slug)=>({
            blog: {
                posts: {
                    __args: {
                        filter: {
                            _sys_slug: {
                                eq: slug
                            }
                        }
                    },
                    item: postFragment
                }
            }
        }),
    getPosts: async ()=>{
        const data = await basehub.query(blog.postsQuery);
        return data.blog.posts.items;
    },
    getLatestPost: async ()=>{
        const data = await basehub.query(blog.latestPostQuery);
        return data.blog.posts.item;
    },
    getPost: async (slug)=>{
        const query = blog.postQuery(slug);
        const data = await basehub.query(query);
        return data.blog.posts.item;
    }
};
/* -------------------------------------------------------------------------------------------------
 * Legal Fragments & Queries
 * -----------------------------------------------------------------------------------------------*/ const legalPostMetaFragment = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fragmentOn"])('LegalPagesItem', {
    _slug: true,
    _title: true,
    description: true
});
const legalPostFragment = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fragmentOn"])('LegalPagesItem', {
    ...legalPostMetaFragment,
    body: {
        plainText: true,
        json: {
            content: true,
            toc: true
        },
        readingTime: true
    }
});
const legal = {
    postsQuery: (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fragmentOn"])('Query', {
        legalPages: {
            items: legalPostFragment
        }
    }),
    latestPostQuery: (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fragmentOn"])('Query', {
        legalPages: {
            __args: {
                orderBy: '_sys_createdAt__DESC'
            },
            item: legalPostFragment
        }
    }),
    postQuery: (slug)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fragmentOn"])('Query', {
            legalPages: {
                __args: {
                    filter: {
                        _sys_slug: {
                            eq: slug
                        }
                    }
                },
                item: legalPostFragment
            }
        }),
    getPosts: async ()=>{
        const data = await basehub.query(legal.postsQuery);
        return data.legalPages.items;
    },
    getLatestPost: async ()=>{
        const data = await basehub.query(legal.latestPostQuery);
        return data.legalPages.item;
    },
    getPost: async (slug)=>{
        const query = legal.postQuery(slug);
        const data = await basehub.query(query);
        return data.legalPages.item;
    }
};
}}),
"[project]/packages/cms/.basehub/react-pump/chunk-F5PHAOMO.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk
/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ __turbopack_context__.s({
    "__commonJS": (()=>__commonJS)
});
var __getOwnPropNames = Object.getOwnPropertyNames;
var __commonJS = (cb, mod)=>function __require() {
        return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = {
            exports: {}
        }).exports, mod), mod.exports;
    };
;
}}),
"[project]/packages/cms/.basehub/react-pump/index.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk
/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ __turbopack_context__.s({
    "Pump": (()=>Pump),
    "createPump": (()=>createPump)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$react$2d$pump$2f$chunk$2d$F5PHAOMO$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/react-pump/chunk-F5PHAOMO.js [app-rsc] (ecmascript)");
// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/react/pump/server-pump.tsx
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/index.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/index.ts [app-rsc] (ecmascript) <locals>");
;
;
;
var LazyClientPump = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["lazy"])(()=>__turbopack_context__.r("[project]/packages/cms/.basehub/react-pump/client-pump-WYUPTPKD.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i).then((mod)=>({
            default: mod.ClientPump
        })));
var cache = /* @__PURE__ */ new Map();
var pumpToken = null;
var spaceID = null;
var pusherData = null;
var DEDUPE_TIME_MS = 32;
var Pump = async ({ children, queries, bind, ...basehubProps })=>{
    const errors = [];
    const responseHashes = [];
    if (__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isNextjs"]) {
        let isNextjsDraftMode = false;
        if (basehubProps.draft === void 0) {
            try {
                const { draftMode } = await __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
                isNextjsDraftMode = (await draftMode()).isEnabled;
            } catch (error) {}
        }
        if (isNextjsDraftMode && basehubProps.draft === void 0) {
            basehubProps.draft = true;
        }
    }
    const { headers, draft } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getStuffFromEnv"])(basehubProps);
    const token = headers["x-basehub-token"];
    const apiVersion = headers["x-basehub-api-version"];
    const pumpEndpoint = "https://aws.basehub.com/pump";
    const noQueries = queries.length === 0;
    const queriesWithFallback = draft && noQueries ? [
        {
            _sys: {
                id: true
            }
        }
    ] : queries;
    if (draft) {
        if (__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isNextjs"]) {
            try {
                const { cookies } = await __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
                const cookieStore = await cookies();
                const ref = cookieStore.get("bshb-preview-ref-" + __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["resolvedRef"].repoHash)?.value;
                if (ref) {
                    headers["x-basehub-ref"] = ref;
                }
            } catch (error) {}
        }
    }
    const results = await Promise.all(// @ts-ignore
    queriesWithFallback.map(async (singleQuery, index)=>{
        const rawQueryOp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["generateQueryOp"])(singleQuery);
        const cacheKey = JSON.stringify({
            ...rawQueryOp,
            ...headers
        }) + (draft ? "_draft" : "_prod");
        let data = void 0;
        if (cache.has(cacheKey)) {
            const cached = cache.get(cacheKey);
            if (performance.now() - cached.start < DEDUPE_TIME_MS) {
                data = await cached.data;
            }
        }
        if (!data) {
            const dataPromise = draft ? fetch(pumpEndpoint, {
                ...__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isNextjs"] ? {
                    cache: "no-store"
                } : {},
                method: "POST",
                headers: {
                    ...headers,
                    "content-type": "application/json",
                    "x-basehub-token": token,
                    "x-basehub-api-version": apiVersion
                },
                body: JSON.stringify(rawQueryOp)
            }).then(async (response)=>{
                const { data: data2 = null, newPumpToken, errors: _errors = null, spaceID: _spaceID, pusherData: _pusherData, responseHash: _responseHash } = await response.json();
                pumpToken = newPumpToken;
                pusherData = _pusherData;
                spaceID = _spaceID;
                errors.push(_errors);
                responseHashes[index] = _responseHash;
                return __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["basehub"].replaceSystemAliases(data2);
            }) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["basehub"])(basehubProps).query(singleQuery);
            cache.set(cacheKey, {
                start: performance.now(),
                data: dataPromise
            });
            data = await dataPromise;
        }
        return {
            data,
            rawQueryOp
        };
    }));
    if (bind) {
        children = children.bind(null, bind);
    }
    let resolvedChildren;
    const childrenPromise = children(results.map((r)=>r.data));
    if (childrenPromise instanceof Promise) {
        resolvedChildren = await childrenPromise?.catch((e)=>{
            if (draft) {
                console.error("Error in Pump children function", e);
                return null;
            } else throw e;
        });
    } else {
        resolvedChildren = childrenPromise;
    }
    if (draft) {
        if (!pumpToken || !spaceID || !pusherData) {
            console.log("Results (length):", results?.length);
            console.log("Errors:", JSON.stringify(errors, null, 2));
            console.log("Pump Endpoint:", pumpEndpoint);
            console.log("Pump Token:", pumpToken);
            console.log("Space ID:", spaceID);
            console.log("Pusher Data:", pusherData);
            console.log("Response Hashes:", JSON.stringify(responseHashes, null, 2));
            throw new Error("Pump did not return the necessary data. Look at the logs to see what's missing.");
        }
        return /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createElement"])(LazyClientPump, {
            rawQueries: results.map((r)=>r.rawQueryOp),
            initialState: {
                // @ts-ignore
                data: !noQueries ? results.map((r)=>r.data ?? null) : [],
                errors,
                responseHashes,
                pusherData,
                spaceID
            },
            pumpEndpoint,
            pumpToken: pumpToken ?? void 0,
            initialResolvedChildren: resolvedChildren,
            apiVersion,
            previewRef: headers["x-basehub-ref"] || __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["resolvedRef"].ref
        }, children);
    }
    return resolvedChildren;
};
var createPump = (queries)=>{
    return (props)=>{
        const queryResult = typeof queries === "function" ? queries(props.params) : queries;
        return /* @__PURE__ */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createElement"])(Pump, {
            ...props,
            queries: queryResult
        });
    };
};
;
}}),
"[project]/packages/cms/components/feed.tsx [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$react$2d$pump$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/react-pump/index.js [app-rsc] (ecmascript)");
;
}}),
"[project]/packages/cms/components/feed.tsx [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$react$2d$pump$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/react-pump/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f$components$2f$feed$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/packages/cms/components/feed.tsx [app-rsc] (ecmascript) <locals>");
}}),
"[project]/packages/cms/.basehub/react-pump/index.js [app-rsc] (ecmascript) <export Pump as Feed>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Feed": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$react$2d$pump$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Pump"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$react$2d$pump$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/react-pump/index.js [app-rsc] (ecmascript)");
}}),
"[project]/packages/observability/status/index.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Status": (()=>Status)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$server$2d$only$2f$empty$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/server-only/empty.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$observability$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/observability/keys.ts [app-rsc] (ecmascript)");
;
;
;
const apiKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$observability$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["keys"])().BETTERSTACK_API_KEY;
const url = (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$observability$2f$keys$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["keys"])().BETTERSTACK_URL;
const Status = async ()=>{
    if (!apiKey || !url) {
        return null;
    }
    let statusColor = 'bg-muted-foreground';
    let statusLabel = 'Unable to fetch status';
    try {
        const response = await fetch('https://uptime.betterstack.com/api/v2/monitors', {
            headers: {
                Authorization: `Bearer ${apiKey}`
            }
        });
        if (!response.ok) {
            throw new Error('Failed to fetch status');
        }
        const { data } = await response.json();
        const status = data.filter((monitor)=>monitor.attributes.status === 'up').length / data.length;
        if (status === 0) {
            statusColor = 'bg-destructive';
            statusLabel = 'Degraded performance';
        } else if (status < 1) {
            statusColor = 'bg-warning';
            statusLabel = 'Partial outage';
        } else {
            statusColor = 'bg-success';
            statusLabel = 'All systems normal';
        }
    } catch  {
        statusColor = 'bg-muted-foreground';
        statusLabel = 'Unable to fetch status';
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
        className: "flex items-center gap-3 font-medium text-sm",
        target: "_blank",
        rel: "noreferrer",
        href: url,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "relative flex h-2 w-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: `absolute inline-flex h-full w-full animate-ping rounded-full opacity-75 ${statusColor}`
                    }, void 0, false, {
                        fileName: "[project]/packages/observability/status/index.tsx",
                        lineNumber: 59,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: `relative inline-flex h-2 w-2 rounded-full ${statusColor}`
                    }, void 0, false, {
                        fileName: "[project]/packages/observability/status/index.tsx",
                        lineNumber: 62,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/packages/observability/status/index.tsx",
                lineNumber: 58,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "text-muted-foreground",
                children: statusLabel
            }, void 0, false, {
                fileName: "[project]/packages/observability/status/index.tsx",
                lineNumber: 66,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/packages/observability/status/index.tsx",
        lineNumber: 52,
        columnNumber: 5
    }, this);
};
}}),
"[project]/apps/web/app/[locale]/components/footer.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40a3b4c3f810b46a59ae87461736b0849b87acd154":"$$RSC_SERVER_ACTION_0"},"",""] */ __turbopack_context__.s({
    "$$RSC_SERVER_ACTION_0": (()=>$$RSC_SERVER_ACTION_0),
    "Footer": (()=>Footer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$env$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/env.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/index.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f$components$2f$feed$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/cms/components/feed.tsx [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$react$2d$pump$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__Pump__as__Feed$3e$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/react-pump/index.js [app-rsc] (ecmascript) <export Pump as Feed>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$observability$2f$status$2f$index$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/observability/status/index.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/client/app-dir/link.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
const $$RSC_SERVER_ACTION_0 = async function children([data]) {
    const navigationItems = [
        {
            title: 'Product',
            items: [
                {
                    title: 'Home',
                    href: '/'
                },
                {
                    title: 'Pricing',
                    href: '/pricing'
                },
                {
                    title: 'Features',
                    href: '/features'
                },
                {
                    title: 'Enterprise',
                    href: '/enterprise'
                }
            ]
        },
        {
            title: 'Resources',
            items: [
                {
                    title: 'Docs',
                    href: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$env$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["env"].NEXT_PUBLIC_DOCS_URL || '/docs'
                },
                {
                    title: 'Blog',
                    href: '/blog'
                },
                {
                    title: 'Forum',
                    href: '/forum'
                },
                {
                    title: 'Changelog',
                    href: '/changelog'
                }
            ]
        },
        {
            title: 'Company',
            items: [
                {
                    title: 'Contact us',
                    href: '/contact'
                },
                {
                    title: 'Community',
                    href: '/community'
                },
                {
                    title: 'Customers',
                    href: '/customers'
                }
            ]
        },
        {
            title: 'Legal',
            items: data.legalPages.items.map((post)=>({
                    title: post._title,
                    href: `/legal/${post._slug}`
                }))
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
        className: "relative border-t border-border overflow-hidden",
        style: {
            backgroundColor: '#161616'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "relative z-10 mx-auto max-w-7xl px-6 py-12 lg:px-8 lg:py-16",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col items-center gap-4 mb-12",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                            href: "mailto:<EMAIL>",
                            className: "text-foreground hover:text-muted-foreground transition-colors text-lg font-medium",
                            children: "<EMAIL>"
                        }, void 0, false, {
                            fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                            lineNumber: 54,
                            columnNumber: 15
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex gap-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                    href: "https://x.com/cubent",
                                    target: "_blank",
                                    rel: "noopener noreferrer",
                                    className: "bg-gradient-to-r from-blue-500 to-green-500 bg-clip-text text-transparent hover:from-blue-400 hover:to-green-400 transition-all",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        className: "w-5 h-5 fill-current",
                                        viewBox: "0 0 24 24",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("linearGradient", {
                                                    id: "gradient-x",
                                                    x1: "0%",
                                                    y1: "0%",
                                                    x2: "100%",
                                                    y2: "0%",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                                            offset: "0%",
                                                            stopColor: "rgb(59 130 246)"
                                                        }, void 0, false, {
                                                            fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                            lineNumber: 72,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                                            offset: "100%",
                                                            stopColor: "rgb(34 197 94)"
                                                        }, void 0, false, {
                                                            fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                            lineNumber: 73,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                    lineNumber: 71,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                lineNumber: 70,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                fill: "url(#gradient-x)",
                                                d: "M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                lineNumber: 76,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                        lineNumber: 69,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                    lineNumber: 63,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                    href: "https://github.com/cubent",
                                    target: "_blank",
                                    rel: "noopener noreferrer",
                                    className: "bg-gradient-to-r from-blue-500 to-green-500 bg-clip-text text-transparent hover:from-blue-400 hover:to-green-400 transition-all",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        className: "w-5 h-5 fill-current",
                                        viewBox: "0 0 24 24",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("linearGradient", {
                                                    id: "gradient-github",
                                                    x1: "0%",
                                                    y1: "0%",
                                                    x2: "100%",
                                                    y2: "0%",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                                            offset: "0%",
                                                            stopColor: "rgb(59 130 246)"
                                                        }, void 0, false, {
                                                            fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                            lineNumber: 88,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                                            offset: "100%",
                                                            stopColor: "rgb(34 197 94)"
                                                        }, void 0, false, {
                                                            fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                            lineNumber: 89,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                    lineNumber: 87,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                lineNumber: 86,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                fill: "url(#gradient-github)",
                                                d: "M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                lineNumber: 92,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                        lineNumber: 85,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                    lineNumber: 79,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                    href: "https://discord.gg/cubent",
                                    target: "_blank",
                                    rel: "noopener noreferrer",
                                    className: "bg-gradient-to-r from-blue-500 to-green-500 bg-clip-text text-transparent hover:from-blue-400 hover:to-green-400 transition-all",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        className: "w-5 h-5 fill-current",
                                        viewBox: "0 0 24 24",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("linearGradient", {
                                                    id: "gradient-discord",
                                                    x1: "0%",
                                                    y1: "0%",
                                                    x2: "100%",
                                                    y2: "0%",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                                            offset: "0%",
                                                            stopColor: "rgb(59 130 246)"
                                                        }, void 0, false, {
                                                            fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                            lineNumber: 104,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                                            offset: "100%",
                                                            stopColor: "rgb(34 197 94)"
                                                        }, void 0, false, {
                                                            fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                            lineNumber: 105,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                    lineNumber: 103,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                lineNumber: 102,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                fill: "url(#gradient-discord)",
                                                d: "M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0189 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1568 2.4189Z"
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                lineNumber: 108,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                        lineNumber: 101,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                    lineNumber: 95,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                    href: "https://youtube.com/@cubent",
                                    target: "_blank",
                                    rel: "noopener noreferrer",
                                    className: "bg-gradient-to-r from-blue-500 to-green-500 bg-clip-text text-transparent hover:from-blue-400 hover:to-green-400 transition-all",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        className: "w-5 h-5 fill-current",
                                        viewBox: "0 0 24 24",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("linearGradient", {
                                                    id: "gradient-youtube",
                                                    x1: "0%",
                                                    y1: "0%",
                                                    x2: "100%",
                                                    y2: "0%",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                                            offset: "0%",
                                                            stopColor: "rgb(59 130 246)"
                                                        }, void 0, false, {
                                                            fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                            lineNumber: 120,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                                            offset: "100%",
                                                            stopColor: "rgb(34 197 94)"
                                                        }, void 0, false, {
                                                            fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                            lineNumber: 121,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                    lineNumber: 119,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                lineNumber: 118,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                fill: "url(#gradient-youtube)",
                                                d: "M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                lineNumber: 124,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                        lineNumber: 117,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                    lineNumber: 111,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                            lineNumber: 62,
                            columnNumber: 15
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                    lineNumber: 53,
                    columnNumber: 13
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-center mb-12",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-2 gap-8 sm:grid-cols-4 max-w-4xl",
                        children: navigationItems.map((section)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-col gap-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-sm font-medium text-foreground",
                                        children: section.title
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                        lineNumber: 135,
                                        columnNumber: 21
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                        className: "flex flex-col gap-3",
                                        children: section.items?.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                                    href: item.href,
                                                    className: "text-sm text-muted-foreground hover:text-foreground transition-colors",
                                                    target: item.href.includes('http') ? '_blank' : undefined,
                                                    rel: item.href.includes('http') ? 'noopener noreferrer' : undefined,
                                                    children: item.title
                                                }, void 0, false, {
                                                    fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                    lineNumber: 141,
                                                    columnNumber: 27
                                                }, this)
                                            }, item.title, false, {
                                                fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                                lineNumber: 140,
                                                columnNumber: 25
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                        lineNumber: 138,
                                        columnNumber: 21
                                    }, this)
                                ]
                            }, section.title, true, {
                                fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                                lineNumber: 134,
                                columnNumber: 19
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                        lineNumber: 132,
                        columnNumber: 15
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                    lineNumber: 131,
                    columnNumber: 13
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col items-center gap-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$observability$2f$status$2f$index$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Status"], {}, void 0, false, {
                            fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                            lineNumber: 165,
                            columnNumber: 15
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-muted-foreground",
                            children: [
                                "© ",
                                new Date().getFullYear(),
                                " Made by Logicent Ltd"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                            lineNumber: 166,
                            columnNumber: 15
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
                    lineNumber: 164,
                    columnNumber: 13
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
            lineNumber: 51,
            columnNumber: 11
        }, this)
    }, void 0, false, {
        fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
        lineNumber: 49,
        columnNumber: 9
    }, this);
};
const Footer = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$react$2d$pump$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__Pump__as__Feed$3e$__["Feed"], {
        queries: [
            __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["legal"].postsQuery
        ],
        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])($$RSC_SERVER_ACTION_0, "40a3b4c3f810b46a59ae87461736b0849b87acd154", null)
    }, void 0, false, {
        fileName: "[project]/apps/web/app/[locale]/components/footer.tsx",
        lineNumber: 8,
        columnNumber: 3
    }, this);
}}),
"[project]/apps/web/app/[locale]/components/header/index.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Header": (()=>Header)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const Header = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/apps/web/app/[locale]/components/header/index.tsx <module evaluation>", "Header");
}}),
"[project]/apps/web/app/[locale]/components/header/index.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Header": (()=>Header)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const Header = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/apps/web/app/[locale]/components/header/index.tsx", "Header");
}}),
"[project]/apps/web/app/[locale]/components/header/index.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$app$2f5b$locale$5d2f$components$2f$header$2f$index$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/app/[locale]/components/header/index.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$app$2f5b$locale$5d2f$components$2f$header$2f$index$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/apps/web/app/[locale]/components/header/index.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$app$2f5b$locale$5d2f$components$2f$header$2f$index$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/apps/web/components/performance-optimizer.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PerformanceOptimizer": (()=>PerformanceOptimizer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const PerformanceOptimizer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call PerformanceOptimizer() from the server but PerformanceOptimizer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/apps/web/components/performance-optimizer.tsx <module evaluation>", "PerformanceOptimizer");
}}),
"[project]/apps/web/components/performance-optimizer.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PerformanceOptimizer": (()=>PerformanceOptimizer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const PerformanceOptimizer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call PerformanceOptimizer() from the server but PerformanceOptimizer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/apps/web/components/performance-optimizer.tsx", "PerformanceOptimizer");
}}),
"[project]/apps/web/components/performance-optimizer.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$components$2f$performance$2d$optimizer$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/components/performance-optimizer.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$components$2f$performance$2d$optimizer$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/apps/web/components/performance-optimizer.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$components$2f$performance$2d$optimizer$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/apps/web/components/seo-optimizer.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PerformanceHints": (()=>PerformanceHints),
    "StructuredData": (()=>StructuredData),
    "generateSEOMetadata": (()=>generateSEOMetadata),
    "generateStructuredData": (()=>generateStructuredData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
;
const generateSEOMetadata = ({ title = 'Cubent – Lightning-Fast AI Code Assistant', description = 'Cubent is your AI-native coding assistant, purpose-built for developers. From full-code generation to codebase-aware autocomplete and terminal-ready actions.', keywords = [
    'AI code assistant',
    'code generation',
    'autocomplete',
    'developer tools',
    'artificial intelligence',
    'programming assistant',
    'VS Code extension'
], canonicalUrl = 'https://cubent.dev', ogImage = 'https://cubent.dev/og-image.png', structuredData } = {})=>{
    const baseMetadata = {
        title,
        description,
        keywords: keywords.join(', '),
        authors: [
            {
                name: 'Cubent Team'
            }
        ],
        creator: 'Cubent',
        publisher: 'Cubent',
        formatDetection: {
            email: false,
            address: false,
            telephone: false
        },
        metadataBase: new URL('https://cubent.dev'),
        alternates: {
            canonical: canonicalUrl
        },
        openGraph: {
            title,
            description,
            url: canonicalUrl,
            siteName: 'Cubent',
            images: [
                {
                    url: ogImage,
                    width: 1200,
                    height: 630,
                    alt: title
                }
            ],
            locale: 'en_US',
            type: 'website'
        },
        twitter: {
            card: 'summary_large_image',
            title,
            description,
            creator: '@cubent',
            images: [
                ogImage
            ]
        },
        robots: {
            index: true,
            follow: true,
            googleBot: {
                index: true,
                follow: true,
                'max-video-preview': -1,
                'max-image-preview': 'large',
                'max-snippet': -1
            }
        },
        verification: {
            google: process.env.GOOGLE_SITE_VERIFICATION
        }
    };
    return baseMetadata;
};
const generateStructuredData = (type, data)=>{
    const baseStructuredData = {
        '@context': 'https://schema.org',
        '@type': type
    };
    switch(type){
        case 'Organization':
            return {
                ...baseStructuredData,
                name: 'Cubent',
                description: 'Lightning-Fast AI Code Assistant for developers',
                url: 'https://cubent.dev',
                logo: 'https://cubent.dev/favicon.svg',
                sameAs: [
                    'https://twitter.com/cubent',
                    'https://github.com/cubent'
                ],
                contactPoint: {
                    '@type': 'ContactPoint',
                    contactType: 'customer service',
                    url: 'https://cubent.dev/contact'
                },
                foundingDate: '2024',
                industry: 'Software Development Tools',
                keywords: 'AI code assistant, code generation, autocomplete, developer tools',
                ...data
            };
        case 'WebSite':
            return {
                ...baseStructuredData,
                name: 'Cubent',
                url: 'https://cubent.dev',
                potentialAction: {
                    '@type': 'SearchAction',
                    target: 'https://cubent.dev/search?q={search_term_string}',
                    'query-input': 'required name=search_term_string'
                },
                ...data
            };
        case 'Article':
            return {
                ...baseStructuredData,
                headline: data.title,
                description: data.description,
                author: {
                    '@type': 'Organization',
                    name: 'Cubent Team'
                },
                publisher: {
                    '@type': 'Organization',
                    name: 'Cubent',
                    logo: {
                        '@type': 'ImageObject',
                        url: 'https://cubent.dev/favicon.svg'
                    }
                },
                datePublished: data.publishedAt,
                dateModified: data.updatedAt || data.publishedAt,
                ...data
            };
        case 'Product':
            return {
                ...baseStructuredData,
                name: 'Cubent AI Code Assistant',
                description: 'AI-native coding assistant for developers with full-code generation and autocomplete',
                brand: {
                    '@type': 'Brand',
                    name: 'Cubent'
                },
                category: 'Software Development Tools',
                offers: {
                    '@type': 'Offer',
                    url: 'https://cubent.dev/pricing',
                    priceCurrency: 'USD',
                    availability: 'https://schema.org/InStock'
                },
                ...data
            };
        default:
            return {
                ...baseStructuredData,
                ...data
            };
    }
};
const StructuredData = ({ data })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
        type: "application/ld+json",
        dangerouslySetInnerHTML: {
            __html: JSON.stringify(data)
        }
    }, void 0, false, {
        fileName: "[project]/apps/web/components/seo-optimizer.tsx",
        lineNumber: 169,
        columnNumber: 5
    }, this);
};
const PerformanceHints = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                rel: "dns-prefetch",
                href: "//fonts.googleapis.com"
            }, void 0, false, {
                fileName: "[project]/apps/web/components/seo-optimizer.tsx",
                lineNumber: 183,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                rel: "dns-prefetch",
                href: "//www.google-analytics.com"
            }, void 0, false, {
                fileName: "[project]/apps/web/components/seo-optimizer.tsx",
                lineNumber: 184,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                rel: "dns-prefetch",
                href: "//images.unsplash.com"
            }, void 0, false, {
                fileName: "[project]/apps/web/components/seo-optimizer.tsx",
                lineNumber: 185,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                rel: "preconnect",
                href: "https://fonts.gstatic.com",
                crossOrigin: "anonymous"
            }, void 0, false, {
                fileName: "[project]/apps/web/components/seo-optimizer.tsx",
                lineNumber: 188,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                rel: "prefetch",
                href: "/api/health"
            }, void 0, false, {
                fileName: "[project]/apps/web/components/seo-optimizer.tsx",
                lineNumber: 191,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
}}),
"[project]/apps/web/components/error-boundary.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ConsoleErrorSuppressor": (()=>ConsoleErrorSuppressor),
    "ErrorBoundary": (()=>ErrorBoundary)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const ConsoleErrorSuppressor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ConsoleErrorSuppressor() from the server but ConsoleErrorSuppressor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/apps/web/components/error-boundary.tsx <module evaluation>", "ConsoleErrorSuppressor");
const ErrorBoundary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ErrorBoundary() from the server but ErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/apps/web/components/error-boundary.tsx <module evaluation>", "ErrorBoundary");
}}),
"[project]/apps/web/components/error-boundary.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ConsoleErrorSuppressor": (()=>ConsoleErrorSuppressor),
    "ErrorBoundary": (()=>ErrorBoundary)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const ConsoleErrorSuppressor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ConsoleErrorSuppressor() from the server but ConsoleErrorSuppressor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/apps/web/components/error-boundary.tsx", "ConsoleErrorSuppressor");
const ErrorBoundary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ErrorBoundary() from the server but ErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/apps/web/components/error-boundary.tsx", "ErrorBoundary");
}}),
"[project]/apps/web/components/error-boundary.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$components$2f$error$2d$boundary$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/components/error-boundary.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$components$2f$error$2d$boundary$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/apps/web/components/error-boundary.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$components$2f$error$2d$boundary$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/apps/web/app/[locale]/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Main layout component for the Cubent website
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$analytics$2f$index$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/analytics/index.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f$components$2f$toolbar$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/cms/components/toolbar.tsx [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$next$2d$toolbar$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/cms/.basehub/next-toolbar/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$index$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/design-system/index.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$lib$2f$fonts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/design-system/lib/fonts.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/design-system/lib/utils.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$feature$2d$flags$2f$components$2f$toolbar$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/feature-flags/components/toolbar.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$internationalization$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/packages/internationalization/index.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$app$2f5b$locale$5d2f$components$2f$footer$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/app/[locale]/components/footer.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$app$2f5b$locale$5d2f$components$2f$header$2f$index$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/app/[locale]/components/header/index.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$components$2f$performance$2d$optimizer$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/components/performance-optimizer.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$components$2f$seo$2d$optimizer$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/components/seo-optimizer.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$components$2f$error$2d$boundary$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/components/error-boundary.tsx [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const RootLayout = async ({ children, params })=>{
    const { locale } = await params;
    const dictionary = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$internationalization$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getDictionary"])(locale);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        lang: locale,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cn"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$lib$2f$fonts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fonts"], 'scroll-smooth'),
        suppressHydrationWarning: true,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("head", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$components$2f$seo$2d$optimizer$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PerformanceHints"], {}, void 0, false, {
                        fileName: "[project]/apps/web/app/[locale]/layout.tsx",
                        lineNumber: 35,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                        rel: "canonical",
                        href: `https://cubent.dev/${locale === 'en' ? '' : locale}`
                    }, void 0, false, {
                        fileName: "[project]/apps/web/app/[locale]/layout.tsx",
                        lineNumber: 36,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/apps/web/app/[locale]/layout.tsx",
                lineNumber: 34,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$components$2f$error$2d$boundary$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ConsoleErrorSuppressor"], {}, void 0, false, {
                        fileName: "[project]/apps/web/app/[locale]/layout.tsx",
                        lineNumber: 39,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
                        type: "application/ld+json",
                        dangerouslySetInnerHTML: {
                            __html: JSON.stringify({
                                "@context": "https://schema.org",
                                "@type": "Organization",
                                "name": "Cubent",
                                "description": "Lightning-Fast AI Code Assistant for developers. Purpose-built for full-code generation, codebase-aware autocomplete and terminal-ready actions.",
                                "url": "https://cubent.dev",
                                "logo": "https://cubent.dev/favicon.svg",
                                "sameAs": [
                                    "https://twitter.com/cubent",
                                    "https://github.com/cubent"
                                ],
                                "contactPoint": {
                                    "@type": "ContactPoint",
                                    "contactType": "customer service",
                                    "url": "https://cubent.dev/contact"
                                },
                                "founder": {
                                    "@type": "Organization",
                                    "name": "Cubent Team"
                                },
                                "foundingDate": "2024",
                                "industry": "Software Development Tools",
                                "keywords": "AI code assistant, code generation, autocomplete, developer tools, artificial intelligence, programming assistant"
                            })
                        }
                    }, void 0, false, {
                        fileName: "[project]/apps/web/app/[locale]/layout.tsx",
                        lineNumber: 41,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$components$2f$error$2d$boundary$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ErrorBoundary"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$analytics$2f$index$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["AnalyticsProvider"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$design$2d$system$2f$index$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DesignSystemProvider"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$components$2f$performance$2d$optimizer$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PerformanceOptimizer"], {}, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/layout.tsx",
                                            lineNumber: 73,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$app$2f5b$locale$5d2f$components$2f$header$2f$index$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Header"], {
                                            dictionary: dictionary
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/layout.tsx",
                                            lineNumber: 74,
                                            columnNumber: 15
                                        }, this),
                                        children,
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$app$2f5b$locale$5d2f$components$2f$footer$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Footer"], {}, void 0, false, {
                                            fileName: "[project]/apps/web/app/[locale]/layout.tsx",
                                            lineNumber: 76,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/apps/web/app/[locale]/layout.tsx",
                                    lineNumber: 72,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$feature$2d$flags$2f$components$2f$toolbar$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Toolbar"], {}, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/layout.tsx",
                                    lineNumber: 78,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$cms$2f2e$basehub$2f$next$2d$toolbar$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Toolbar"], {}, void 0, false, {
                                    fileName: "[project]/apps/web/app/[locale]/layout.tsx",
                                    lineNumber: 79,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/app/[locale]/layout.tsx",
                            lineNumber: 71,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/apps/web/app/[locale]/layout.tsx",
                        lineNumber: 70,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/apps/web/app/[locale]/layout.tsx",
                lineNumber: 38,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/apps/web/app/[locale]/layout.tsx",
        lineNumber: 29,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = RootLayout;
}}),

};

//# sourceMappingURL=_b918c779._.js.map