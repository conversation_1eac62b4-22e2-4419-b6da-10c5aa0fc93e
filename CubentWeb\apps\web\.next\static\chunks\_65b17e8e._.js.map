{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/cms/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      BASEHUB_TOKEN: z.string().startsWith('bshb_pk_'),\n    },\n    runtimeEnv: {\n      BASEHUB_TOKEN: process.env.BASEHUB_TOKEN,\n    },\n  });\n"], "names": [], "mappings": ";;;AASqB;AATrB;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,eAAe,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC;QACvC;QACA,YAAY;YACV,eAAe,+QAAA,CAAA,UAAO,CAAC,GAAG,CAAC,aAAa;QAC1C;IACF", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/email/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      RESEND_FROM: z.string().email(),\n      RESEND_TOKEN: z.string().startsWith('re_'),\n    },\n    runtimeEnv: {\n      RESEND_FROM: process.env.RESEND_FROM,\n      RESEND_TOKEN: process.env.RESEND_TOKEN,\n    },\n  });\n"], "names": [], "mappings": ";;;AAUmB;AAVnB;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,aAAa,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK;YAC7B,cAAc,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC;QACtC;QACA,YAAY;YACV,aAAa,+QAAA,CAAA,UAAO,CAAC,GAAG,CAAC,WAAW;YACpC,cAAc,+QAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY;QACxC;IACF", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/feature-flags/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      FLAGS_SECRET: z.string().optional(),\n    },\n    runtimeEnv: {\n      FLAGS_SECRET: process.env.FLAGS_SECRET,\n    },\n  });\n"], "names": [], "mappings": ";;;AASoB;AATpB;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,cAAc,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC;QACA,YAAY;YACV,cAAc,+QAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY;QACxC;IACF", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/next-config/keys.ts"], "sourcesContent": ["import { vercel } from '@t3-oss/env-core/presets-zod';\nimport { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    extends: [vercel()],\n    server: {\n      ANALYZE: z.string().optional(),\n\n      // Added by Vercel\n      NEXT_RUNTIME: z.enum(['nodejs', 'edge']).optional(),\n    },\n    client: {\n      NEXT_PUBLIC_APP_URL: z.string().url(),\n      NEXT_PUBLIC_WEB_URL: z.string().url(),\n      NEXT_PUBLIC_API_URL: z.string().url().optional(),\n      NEXT_PUBLIC_DOCS_URL: z.string().url().optional(),\n    },\n    runtimeEnv: {\n      ANALYZE: process.env.ANALYZE,\n      NEXT_RUNTIME: process.env.NEXT_RUNTIME,\n      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,\n      NEXT_PUBLIC_WEB_URL: process.env.NEXT_PUBLIC_WEB_URL,\n      NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,\n      NEXT_PUBLIC_DOCS_URL: process.env.NEXT_PUBLIC_DOCS_URL,\n    },\n  });\n"], "names": [], "mappings": ";;;AAoBe;AApBf;AACA;AACA;AAAA;;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;YAAC,CAAA,GAAA,oRAAA,CAAA,SAAM,AAAD;SAAI;QACnB,QAAQ;YACN,SAAS,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAE5B,kBAAkB;YAClB,cAAc,sOAAA,CAAA,IAAC,CAAC,IAAI,CAAC;gBAAC;gBAAU;aAAO,EAAE,QAAQ;QACnD;QACA,QAAQ;YACN,qBAAqB,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;YACnC,qBAAqB,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;YACnC,qBAAqB,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;YAC9C,sBAAsB,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;QACjD;QACA,YAAY;YACV,SAAS,+QAAA,CAAA,UAAO,CAAC,GAAG,CAAC,OAAO;YAC5B,YAAY;YACZ,mBAAmB;YACnB,mBAAmB;YACnB,mBAAmB;YACnB,oBAAoB;QACtB;IACF", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/observability/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      BETTERSTACK_API_KEY: z.string().optional(),\n      BETTERSTACK_URL: z.string().optional(),\n\n      // Added by Sentry Integration, Vercel Marketplace\n      SENTRY_ORG: z.string().optional(),\n      SENTRY_PROJECT: z.string().optional(),\n    },\n    client: {\n      // Added by Sentry Integration, Vercel Marketplace\n      NEXT_PUBLIC_SENTRY_DSN: z.string().url().optional(),\n    },\n    runtimeEnv: {\n      BETTERSTACK_API_KEY: process.env.BETTERSTACK_API_KEY,\n      BETTERSTACK_URL: process.env.BETTERSTACK_URL,\n      SENTRY_ORG: process.env.SENTRY_ORG,\n      SENTRY_PROJECT: process.env.SENTRY_PROJECT,\n      NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,\n    },\n  });\n"], "names": [], "mappings": ";;;AAkB2B;AAlB3B;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,qBAAqB,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YACxC,iBAAiB,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAEpC,kDAAkD;YAClD,YAAY,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC/B,gBAAgB,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACrC;QACA,QAAQ;YACN,kDAAkD;YAClD,wBAAwB,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;QACnD;QACA,YAAY;YACV,qBAAqB,+QAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB;YACpD,iBAAiB,+QAAA,CAAA,UAAO,CAAC,GAAG,CAAC,eAAe;YAC5C,YAAY,+QAAA,CAAA,UAAO,CAAC,GAAG,CAAC,UAAU;YAClC,gBAAgB,+QAAA,CAAA,UAAO,CAAC,GAAG,CAAC,cAAc;YAC1C,wBAAwB,+QAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB;QAC5D;IACF", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/rate-limit/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      UPSTASH_REDIS_REST_URL: z.string().url().optional(),\n      UPSTASH_REDIS_REST_TOKEN: z.string().optional(),\n    },\n    runtimeEnv: {\n      UPSTASH_REDIS_REST_URL: process.env.UPSTASH_REDIS_REST_URL,\n      UPSTASH_REDIS_REST_TOKEN: process.env.UPSTASH_REDIS_REST_TOKEN,\n    },\n  });\n"], "names": [], "mappings": ";;;AAU8B;AAV9B;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,wBAAwB,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;YACjD,0BAA0B,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC/C;QACA,YAAY;YACV,wBAAwB,+QAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB;YAC1D,0BAA0B,+QAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB;QAChE;IACF", "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/security/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      ARCJET_KEY: z.string().startsWith('ajkey_').optional(),\n    },\n    runtimeEnv: {\n      ARCJET_KEY: process.env.ARCJET_KEY,\n    },\n  });\n"], "names": [], "mappings": ";;;AASkB;AATlB;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,YAAY,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC,UAAU,QAAQ;QACtD;QACA,YAAY;YACV,YAAY,+QAAA,CAAA,UAAO,CAAC,GAAG,CAAC,UAAU;QACpC;IACF", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/env.ts"], "sourcesContent": ["import { keys as cms } from '@repo/cms/keys';\nimport { keys as email } from '@repo/email/keys';\nimport { keys as flags } from '@repo/feature-flags/keys';\nimport { keys as core } from '@repo/next-config/keys';\nimport { keys as observability } from '@repo/observability/keys';\nimport { keys as rateLimit } from '@repo/rate-limit/keys';\nimport { keys as security } from '@repo/security/keys';\nimport { createEnv } from '@t3-oss/env-nextjs';\n\nexport const env = createEnv({\n  extends: [\n    cms(),\n    core(),\n    email(),\n    observability(),\n    flags(),\n    security(),\n    rateLimit(),\n  ],\n  server: {},\n  client: {},\n  runtimeEnv: {},\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEO,MAAM,MAAM,CAAA,GAAA,6QAAA,CAAA,YAAS,AAAD,EAAE;IAC3B,SAAS;QACP,CAAA,GAAA,0HAAA,CAAA,OAAG,AAAD;QACF,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD;QACH,CAAA,GAAA,4HAAA,CAAA,OAAK,AAAD;QACJ,CAAA,GAAA,oIAAA,CAAA,OAAa,AAAD;QACZ,CAAA,GAAA,uIAAA,CAAA,OAAK,AAAD;QACJ,CAAA,GAAA,+HAAA,CAAA,OAAQ,AAAD;QACP,CAAA,GAAA,oIAAA,CAAA,OAAS,AAAD;KACT;IACD,QAAQ,CAAC;IACT,QAAQ,CAAC;IACT,YAAY,CAAC;AACf", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/pricing/page.tsx"], "sourcesContent": ["'use client';\n\nimport { env } from '@/env';\nimport { Button } from '@repo/design-system/components/ui/button';\nimport { Check, MoveRight, Plus } from 'lucide-react';\nimport Link from 'next/link';\nimport { useState } from 'react';\n\nconst PricingClient = () => {\n  const [openFAQ, setOpenFAQ] = useState<number | null>(null);\n\n  const toggleFAQ = (index: number) => {\n    setOpenFAQ(openFAQ === index ? null : index);\n  };\n  const handleStartForFree = () => {\n    // Redirect to sign-up - free trial will be automatically created via webhook\n    window.location.href = 'https://app.cubent.dev/sign-up';\n  };\n\n  const faqData = [\n    {\n      question: \"What happens when I run out of Cubent Units?\",\n      answer: \"You can upgrade your plan, wait for the monthly reset, or switch to BYOK models with your own API keys.\"\n    },\n    {\n      question: \"Can I mix built-in and BYOK models?\",\n      answer: \"Yes! You can use both built-in models (with Cubent Units) and BYOK models (with your API keys) in the same workspace.\"\n    },\n    {\n      question: \"Can I change plans anytime?\",\n      answer: \"Yes, you can upgrade or downgrade your plan at any time. Changes take effect at the next billing cycle.\"\n    },\n    {\n      question: \"Are there any usage limits?\",\n      answer: \"Each plan has monthly Cubent Unit allocations. Enterprise plans can have custom limits based on your needs.\"\n    },\n    {\n      question: \"What happens if I upgrade to a higher subscription tier?\",\n      answer: \"Upgrades take effect immediately. You'll be charged the prorated difference for the current billing period and receive the new tier's benefits right away.\"\n    }\n  ];\n\n  return (\n  <div className=\"w-full min-h-screen relative -mt-20 pt-20\" style={{ backgroundColor: '#161616', paddingLeft: '1.5rem', paddingRight: '1.5rem' }}>\n    <div\n      className=\"max-w-7xl mx-auto relative md:border md:border-white/8 md:bg-transparent md:px-12 md:py-20 px-0 py-8\"\n      style={{\n        backgroundColor: 'transparent'\n      }}\n    >\n      <div className=\"flex flex-col items-center justify-center gap-6 text-center\">\n        {/* Header Section */}\n        <div className=\"flex flex-col gap-4\">\n          <h1 className=\"max-w-4xl text-center font-regular text-5xl tracking-tighter md:text-5xl\">\n            Simple pricing for powerful AI\n          </h1>\n          <p className=\"max-w-2xl text-center text-lg text-muted-foreground leading-relaxed tracking-tight\">\n            Start coding with AI assistance for free, then scale as your projects grow\n          </p>\n\n\n        </div>\n\n        {/* Pricing Plans */}\n        <div className=\"w-full pt-8\">\n          <div className=\"relative\">\n            <div className=\"flex flex-col lg:flex-row bg-gray-900/30 backdrop-blur-sm overflow-hidden\">\n              {/* BYOK Plan */}\n              <div className=\"relative flex-1 border border-white/10\" style={{\n                backgroundColor: '#161616'\n              }}>\n                <div className=\"absolute top-0 left-0 right-0 z-20\">\n                  <div className=\"bg-gradient-to-r from-blue-600 via-orange-500 to-blue-400 text-black px-4 py-1.5 text-xs font-medium text-center relative\">\n                    {/* Grain overlay */}\n                    <div\n                      className=\"absolute inset-0 opacity-70\"\n                      style={{\n                        backgroundImage: `url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilterEarlyAccess'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='8.5' numOctaves='20' stitchTiles='stitch'/%3E%3CfeColorMatrix values='0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilterEarlyAccess)'/%3E%3C/svg%3E\")`,\n                        mixBlendMode: 'overlay'\n                      }}\n                    />\n                    <span className=\"relative z-10\">Early access: 7-day free trial (no credit card)</span>\n                  </div>\n                </div>\n                <div className=\"p-8 pt-12 h-full flex flex-col text-left\">\n                <div className=\"mb-6\">\n                  <h3 className=\"text-lg font-semibold mb-2\">Byok</h3>\n                  <div className=\"mb-4\">\n                    <p className=\"text-sm text-muted-foreground mb-1\">Pricing:</p>\n                    <div className=\"flex items-baseline gap-1\">\n                      <span className=\"text-3xl font-bold\">$5</span>\n                      <span className=\"text-sm text-muted-foreground\">/ month</span>\n                    </div>\n                    <div className=\"text-xs text-white mt-1\">\n                      7-day free trial included\n                    </div>\n                  </div>\n                  <Button variant=\"outline\" onClick={handleStartForFree} className=\"w-full bg-white/5 hover:bg-white/10 h-10 flex items-center border border-white/20 px-6 mb-4\">\n                    <Link href={env.NEXT_PUBLIC_APP_URL} className=\"bg-gradient-to-r from-blue-600 via-orange-500 to-blue-400 bg-clip-text text-transparent hover:text-transparent\">\n                      Start for Free\n                    </Link>\n                  </Button>\n                  <p className=\"text-sm text-muted-foreground mb-4\">\n                    Perfect for passion projects & simple websites.\n                  </p>\n                </div>\n\n                <div className=\"mb-4\">\n                  <p className=\"text-sm text-muted-foreground mb-3\">Get started with:</p>\n                </div>\n\n                <ul className=\"space-y-2 flex-grow text-sm\">\n                  <li className=\"flex items-start gap-2\">\n                    <Check className=\"h-4 w-4 text-white flex-shrink-0 mt-0.5\" />\n                    <span>VS Code extension with full AI coding assistance</span>\n                  </li>\n                  <li className=\"flex items-start gap-2\">\n                    <Check className=\"h-4 w-4 text-white flex-shrink-0 mt-0.5\" />\n                    <span>Infinite messages & conversations</span>\n                  </li>\n                  <li className=\"flex items-start gap-2\">\n                    <Check className=\"h-4 w-4 text-white flex-shrink-0 mt-0.5\" />\n                    <span>Use your own API keys (OpenAI, Anthropic, etc.)</span>\n                  </li>\n                  <li className=\"flex items-start gap-2\">\n                    <Check className=\"h-4 w-4 text-white flex-shrink-0 mt-0.5\" />\n                    <span>Chat Mode & Agent Mode with tool calls</span>\n                  </li>\n                  <li className=\"flex items-start gap-2\">\n                    <Check className=\"h-4 w-4 text-white flex-shrink-0 mt-0.5\" />\n                    <span>Terminal integration & custom modes</span>\n                  </li>\n                  <li className=\"flex items-start gap-2\">\n                    <Check className=\"h-4 w-4 text-white flex-shrink-0 mt-0.5\" />\n                    <span>Access to 23+ AI models</span>\n                  </li>\n                </ul>\n              </div>\n            </div>\n\n              {/* Pro Plan */}\n              <div className=\"relative flex-1 border border-white/10\" style={{ backgroundColor: '#161616' }}>\n                <div className=\"p-8 pt-12 h-full flex flex-col text-left\">\n                  <div className=\"mb-6\">\n                    <h3 className=\"text-lg font-semibold mb-2 text-white/70\">Pro</h3>\n                    <div className=\"mb-4\">\n                      <p className=\"text-sm text-white/60 mb-1\">Pricing:</p>\n                      <div className=\"flex items-baseline gap-1\">\n                        <span className=\"text-3xl font-bold text-white/70\">-</span>\n                        <span className=\"text-sm text-white/60\">/ month</span>\n                      </div>\n                      <div className=\"text-xs text-transparent mt-1\">\n                        &nbsp;\n                      </div>\n                    </div>\n                    <Button variant=\"outline\" className=\"w-full text-gray-400 h-10 flex items-center border border-white/10 px-6 mb-4\" disabled>\n                      Coming Soon\n                    </Button>\n                    <p className=\"text-sm text-white/60 mb-4\">\n                      For production applications with the power to scale.\n                    </p>\n                  </div>\n\n                  <div className=\"mb-4\">\n                    <p className=\"text-sm text-white/60 mb-3\">Everything in Byok, plus:</p>\n                  </div>\n\n                  <ul className=\"space-y-2 flex-grow text-sm\">\n                    <li className=\"flex items-start gap-2\">\n                      <Check className=\"h-4 w-4 text-white/60 flex-shrink-0 mt-0.5\" />\n                      <span className=\"text-white/60\">Generous Cubent Units allocation (no API keys needed)</span>\n                    </li>\n                    <li className=\"flex items-start gap-2\">\n                      <Check className=\"h-4 w-4 text-white/60 flex-shrink-0 mt-0.5\" />\n                      <span className=\"text-white/60\">Advanced code generation & refactoring tools</span>\n                    </li>\n                    <li className=\"flex items-start gap-2\">\n                      <Check className=\"h-4 w-4 text-white/60 flex-shrink-0 mt-0.5\" />\n                      <span className=\"text-white/60\">Enhanced debugging & error analysis</span>\n                    </li>\n                    <li className=\"flex items-start gap-2\">\n                      <Check className=\"h-4 w-4 text-white/60 flex-shrink-0 mt-0.5\" />\n                      <span className=\"text-white/60\">Priority support & faster response times</span>\n                    </li>\n                  </ul>\n                </div>\n              </div>\n\n              {/* Team Plan */}\n              <div className=\"relative flex-1 border border-white/10\" style={{ backgroundColor: '#161616' }}>\n                <div className=\"p-8 pt-12 h-full flex flex-col text-left\">\n                  <div className=\"mb-6\">\n                    <h3 className=\"text-lg font-semibold mb-2 text-white/70\">Team</h3>\n                    <div className=\"mb-4\">\n                      <p className=\"text-sm text-white/60 mb-1\">Pricing:</p>\n                      <div className=\"flex items-baseline gap-1\">\n                        <span className=\"text-3xl font-bold text-white/70\">-</span>\n                        <span className=\"text-sm text-white/60\">/ month</span>\n                      </div>\n                      <div className=\"text-xs text-transparent mt-1\">\n                        &nbsp;\n                      </div>\n                    </div>\n                    <Button variant=\"outline\" className=\"w-full text-gray-400 h-10 flex items-center border border-white/10 px-6 mb-4\" disabled>\n                      Coming Soon\n                    </Button>\n                    <p className=\"text-sm text-white/60 mb-4\">\n                      SSO, control over backups, and industry certifications.\n                    </p>\n                  </div>\n\n                  <div className=\"mb-4\">\n                    <p className=\"text-sm text-white/60 mb-3\">Everything in the Pro Plan, plus:</p>\n                  </div>\n\n                  <ul className=\"space-y-2 flex-grow text-sm\">\n                    <li className=\"flex items-start gap-2\">\n                      <Check className=\"h-4 w-4 text-white/60 flex-shrink-0 mt-0.5\" />\n                      <span className=\"text-white/60\">Team workspace & shared configurations</span>\n                    </li>\n                    <li className=\"flex items-start gap-2\">\n                      <Check className=\"h-4 w-4 text-white/60 flex-shrink-0 mt-0.5\" />\n                      <span className=\"text-white/60\">Code review assistance & team insights</span>\n                    </li>\n                    <li className=\"flex items-start gap-2\">\n                      <Check className=\"h-4 w-4 text-white/60 flex-shrink-0 mt-0.5\" />\n                      <span className=\"text-white/60\">Advanced security & compliance features</span>\n                    </li>\n                    <li className=\"flex items-start gap-2\">\n                      <Check className=\"h-4 w-4 text-white/60 flex-shrink-0 mt-0.5\" />\n                      <span className=\"text-white/60\">Priority email support & training</span>\n                    </li>\n                  </ul>\n                </div>\n              </div>\n\n\n            </div>\n          </div>\n        </div>\n\n        {/* Model Providers Section - MOVED INSIDE SAME CONTAINER */}\n        <div className=\"w-full max-w-6xl pt-16 text-center\">\n          {/* Header */}\n          <div className=\"flex flex-col gap-4 text-center max-w-4xl mx-auto mb-12\">\n            <h2 className=\"font-regular text-3xl tracking-tighter md:text-4xl\">\n              First-class support for every major model provider\n            </h2>\n            <p className=\"max-w-2xl mx-auto text-lg text-muted-foreground leading-relaxed tracking-tight\">\n              Connect with the AI models you trust. Cubent works seamlessly with all leading providers.\n            </p>\n          </div>\n\n\n\n          {/* Model Provider Logos */}\n          <div className=\"w-full max-w-6xl mx-auto\">\n            <div className=\"flex flex-wrap items-center justify-center gap-8 md:gap-12 lg:gap-16\">\n\n              {/* OpenAI */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/openai.png\"\n                    alt=\"OpenAI\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">OpenAI</span>\n              </div>\n\n              {/* Anthropic */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/anthropic.png\"\n                    alt=\"Anthropic\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Anthropic</span>\n              </div>\n\n              {/* Google */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/google.png\"\n                    alt=\"Google\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Google</span>\n              </div>\n\n              {/* Cohere */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/cohere-color.png\"\n                    alt=\"Cohere\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Cohere</span>\n              </div>\n\n              {/* Mistral */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/mistral.png\"\n                    alt=\"Mistral\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Mistral</span>\n              </div>\n\n              {/* OpenRouter */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/openrouter.png\"\n                    alt=\"OpenRouter\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">OpenRouter</span>\n              </div>\n\n            </div>\n          </div>\n\n\n\n        </div>\n\n        {/* FAQ Section - MOVED INSIDE SAME CONTAINER */}\n        <div className=\"w-full max-w-6xl pt-20\">\n          <div className=\"flex flex-col lg:flex-row gap-8 lg:gap-12\">\n            {/* Left side - Title and description */}\n            <div className=\"lg:w-1/3 text-left\">\n              <h2 className=\"text-3xl font-bold mb-4 text-left\">Frequently asked questions</h2>\n              <p className=\"text-muted-foreground mb-6 text-left\">\n                If your question is not covered here, you can contact our team.\n              </p>\n              <Button variant=\"outline\" className=\"border-white/20 text-white hover:bg-white/10\">\n                Contact us\n              </Button>\n            </div>\n\n\n\n            {/* Right side - FAQ items */}\n            <div className=\"lg:w-2/3 space-y-0\">\n              {faqData.map((faq, index) => (\n                <div key={index} className=\"border-b border-white/10 last:border-b-0\">\n                  <button\n                    onClick={() => toggleFAQ(index)}\n                    className=\"w-full flex items-center justify-between py-6 text-left\"\n                  >\n                    <span className=\"text-white font-medium pr-4 text-left\">{faq.question}</span>\n                    <Plus className=\"h-4 w-4 text-white flex-shrink-0\" />\n                  </button>\n                  {openFAQ === index && (\n                    <div className=\"pb-8 -mt-2 text-muted-foreground text-left pt-2\">\n                      {faq.answer}\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Let's Code Section - MOVED INSIDE SAME CONTAINER */}\n          <div className=\"w-full pt-20\">\n            <div\n              className=\"relative overflow-hidden\"\n              style={{\n                background: 'linear-gradient(135deg, #1e3a8a 0%, #f97316 50%, #1e40af 100%)',\n                borderRadius: '12px',\n                padding: '4rem 2rem'\n              }}\n            >\n              {/* Grain overlay */}\n              <div\n                className=\"absolute inset-0 opacity-30\"\n                style={{\n                  backgroundImage: `url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")`,\n                }}\n              />\n              <div className=\"relative z-10\">\n                <div className=\"flex flex-col items-center gap-8 max-w-4xl mx-auto text-center\">\n                  {/* Header */}\n                  <div className=\"space-y-4\">\n                    <div className=\"bg-gradient-to-r from-blue-400 via-blue-500 via-orange-400 to-blue-300 bg-clip-text text-transparent text-sm font-medium tracking-wider uppercase\">\n                      — Let's Code\n                    </div>\n                    <h2 className=\"text-white font-regular text-3xl tracking-tighter md:text-5xl leading-tight\">\n                      Install our extension and<br />\n                      start coding today\n                    </h2>\n                    <p className=\"text-white/70 text-lg leading-relaxed max-w-2xl mx-auto\">\n                      Get started with AI-powered development. Choose your preferred editor and experience the future of coding.\n                    </p>\n                  </div>\n\n                  {/* Download Button */}\n                  <div className=\"flex flex-col sm:flex-row gap-4\">\n                    <Button size=\"lg\" asChild className=\"bg-white text-black hover:bg-gray-100 h-12 px-8\">\n                      <Link href=\"https://marketplace.visualstudio.com/items?itemName=cubent.cubent\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"flex items-center gap-3\">\n                        <div className=\"flex items-center gap-2\">\n                          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                            <path d=\"M23.15 2.587L18.21.21a1.494 1.494 0 0 0-1.705.29l-9.46 8.63-4.12-3.128a.999.999 0 0 0-1.276.057L.327 7.261A1 1 0 0 0 .326 8.74L3.899 12 .326 15.26a1 1 0 0 0 .001 1.479L1.65 17.94a.999.999 0 0 0 1.276.057l4.12-3.128 9.46 8.63a1.492 1.492 0 0 0 1.704.29l4.942-2.377A1.5 1.5 0 0 0 24 20.06V3.939a1.5 1.5 0 0 0-.85-1.352zm-5.146 14.861L10.826 12l7.178-5.448v10.896z\"/>\n                          </svg>\n                          VS Code\n                        </div>\n                      </Link>\n                    </Button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n  );\n};\n\nexport default PricingClient;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;;;AANA;;;;;;AAQA,MAAM,gBAAgB;;IACpB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,MAAM,YAAY,CAAC;QACjB,WAAW,YAAY,QAAQ,OAAO;IACxC;IACA,MAAM,qBAAqB;QACzB,6EAA6E;QAC7E,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,UAAU;QACd;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;KACD;IAED,qBACA,4SAAC;QAAI,WAAU;QAA4C,OAAO;YAAE,iBAAiB;YAAW,aAAa;YAAU,cAAc;QAAS;kBAC5I,cAAA,4SAAC;YACC,WAAU;YACV,OAAO;gBACL,iBAAiB;YACnB;sBAEA,cAAA,4SAAC;gBAAI,WAAU;;kCAEb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAG,WAAU;0CAA2E;;;;;;0CAGzF,4SAAC;gCAAE,WAAU;0CAAqF;;;;;;;;;;;;kCAQpG,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC;gCAAI,WAAU;;kDAEb,4SAAC;wCAAI,WAAU;wCAAyC,OAAO;4CAC7D,iBAAiB;wCACnB;;0DACE,4SAAC;gDAAI,WAAU;0DACb,cAAA,4SAAC;oDAAI,WAAU;;sEAEb,4SAAC;4DACC,WAAU;4DACV,OAAO;gEACL,iBAAiB,CAAC,kZAAkZ,CAAC;gEACra,cAAc;4DAChB;;;;;;sEAEF,4SAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;0DAGpC,4SAAC;gDAAI,WAAU;;kEACf,4SAAC;wDAAI,WAAU;;0EACb,4SAAC;gEAAG,WAAU;0EAA6B;;;;;;0EAC3C,4SAAC;gEAAI,WAAU;;kFACb,4SAAC;wEAAE,WAAU;kFAAqC;;;;;;kFAClD,4SAAC;wEAAI,WAAU;;0FACb,4SAAC;gFAAK,WAAU;0FAAqB;;;;;;0FACrC,4SAAC;gFAAK,WAAU;0FAAgC;;;;;;;;;;;;kFAElD,4SAAC;wEAAI,WAAU;kFAA0B;;;;;;;;;;;;0EAI3C,4SAAC,8JAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,SAAS;gEAAoB,WAAU;0EAC/D,cAAA,4SAAC,8QAAA,CAAA,UAAI;oEAAC,MAAM,qHAAA,CAAA,MAAG,CAAC,mBAAmB;oEAAE,WAAU;8EAAiH;;;;;;;;;;;0EAIlK,4SAAC;gEAAE,WAAU;0EAAqC;;;;;;;;;;;;kEAKpD,4SAAC;wDAAI,WAAU;kEACb,cAAA,4SAAC;4DAAE,WAAU;sEAAqC;;;;;;;;;;;kEAGpD,4SAAC;wDAAG,WAAU;;0EACZ,4SAAC;gEAAG,WAAU;;kFACZ,4SAAC,2RAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,4SAAC;kFAAK;;;;;;;;;;;;0EAER,4SAAC;gEAAG,WAAU;;kFACZ,4SAAC,2RAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,4SAAC;kFAAK;;;;;;;;;;;;0EAER,4SAAC;gEAAG,WAAU;;kFACZ,4SAAC,2RAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,4SAAC;kFAAK;;;;;;;;;;;;0EAER,4SAAC;gEAAG,WAAU;;kFACZ,4SAAC,2RAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,4SAAC;kFAAK;;;;;;;;;;;;0EAER,4SAAC;gEAAG,WAAU;;kFACZ,4SAAC,2RAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,4SAAC;kFAAK;;;;;;;;;;;;0EAER,4SAAC;gEAAG,WAAU;;kFACZ,4SAAC,2RAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,4SAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOZ,4SAAC;wCAAI,WAAU;wCAAyC,OAAO;4CAAE,iBAAiB;wCAAU;kDAC1F,cAAA,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;;sEACb,4SAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,4SAAC;4DAAI,WAAU;;8EACb,4SAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAC1C,4SAAC;oEAAI,WAAU;;sFACb,4SAAC;4EAAK,WAAU;sFAAmC;;;;;;sFACnD,4SAAC;4EAAK,WAAU;sFAAwB;;;;;;;;;;;;8EAE1C,4SAAC;oEAAI,WAAU;8EAAgC;;;;;;;;;;;;sEAIjD,4SAAC,8JAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,WAAU;4DAA+E,QAAQ;sEAAC;;;;;;sEAG5H,4SAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;8DAK5C,4SAAC;oDAAI,WAAU;8DACb,cAAA,4SAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;8DAG5C,4SAAC;oDAAG,WAAU;;sEACZ,4SAAC;4DAAG,WAAU;;8EACZ,4SAAC,2RAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,4SAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAElC,4SAAC;4DAAG,WAAU;;8EACZ,4SAAC,2RAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,4SAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAElC,4SAAC;4DAAG,WAAU;;8EACZ,4SAAC,2RAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,4SAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAElC,4SAAC;4DAAG,WAAU;;8EACZ,4SAAC,2RAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,4SAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOxC,4SAAC;wCAAI,WAAU;wCAAyC,OAAO;4CAAE,iBAAiB;wCAAU;kDAC1F,cAAA,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;;sEACb,4SAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,4SAAC;4DAAI,WAAU;;8EACb,4SAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAC1C,4SAAC;oEAAI,WAAU;;sFACb,4SAAC;4EAAK,WAAU;sFAAmC;;;;;;sFACnD,4SAAC;4EAAK,WAAU;sFAAwB;;;;;;;;;;;;8EAE1C,4SAAC;oEAAI,WAAU;8EAAgC;;;;;;;;;;;;sEAIjD,4SAAC,8JAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,WAAU;4DAA+E,QAAQ;sEAAC;;;;;;sEAG5H,4SAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;8DAK5C,4SAAC;oDAAI,WAAU;8DACb,cAAA,4SAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;8DAG5C,4SAAC;oDAAG,WAAU;;sEACZ,4SAAC;4DAAG,WAAU;;8EACZ,4SAAC,2RAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,4SAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAElC,4SAAC;4DAAG,WAAU;;8EACZ,4SAAC,2RAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,4SAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAElC,4SAAC;4DAAG,WAAU;;8EACZ,4SAAC,2RAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,4SAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAElC,4SAAC;4DAAG,WAAU;;8EACZ,4SAAC,2RAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,4SAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAY9C,4SAAC;wBAAI,WAAU;;0CAEb,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAG,WAAU;kDAAqD;;;;;;kDAGnE,4SAAC;wCAAE,WAAU;kDAAiF;;;;;;;;;;;;0CAQhG,4SAAC;gCAAI,WAAU;0CACb,cAAA,4SAAC;oCAAI,WAAU;;sDAGb,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;8DACb,cAAA,4SAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;8DAGd,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAI9D,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;8DACb,cAAA,4SAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;8DAGd,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAI9D,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;8DACb,cAAA,4SAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;8DAGd,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAI9D,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;8DACb,cAAA,4SAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;8DAGd,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAI9D,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;8DACb,cAAA,4SAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;8DAGd,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAI9D,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;8DACb,cAAA,4SAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;8DAGd,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWpE,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAI,WAAU;;kDAEb,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,4SAAC;gDAAE,WAAU;0DAAuC;;;;;;0DAGpD,4SAAC,8JAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,WAAU;0DAA+C;;;;;;;;;;;;kDAQrF,4SAAC;wCAAI,WAAU;kDACZ,QAAQ,GAAG,CAAC,CAAC,KAAK,sBACjB,4SAAC;gDAAgB,WAAU;;kEACzB,4SAAC;wDACC,SAAS,IAAM,UAAU;wDACzB,WAAU;;0EAEV,4SAAC;gEAAK,WAAU;0EAAyC,IAAI,QAAQ;;;;;;0EACrE,4SAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;oDAEjB,YAAY,uBACX,4SAAC;wDAAI,WAAU;kEACZ,IAAI,MAAM;;;;;;;+CAVP;;;;;;;;;;;;;;;;0CAmBhB,4SAAC;gCAAI,WAAU;0CACb,cAAA,4SAAC;oCACC,WAAU;oCACV,OAAO;wCACL,YAAY;wCACZ,cAAc;wCACd,SAAS;oCACX;;sDAGA,4SAAC;4CACC,WAAU;4CACV,OAAO;gDACL,iBAAiB,CAAC,sTAAsT,CAAC;4CAC3U;;;;;;sDAEF,4SAAC;4CAAI,WAAU;sDACb,cAAA,4SAAC;gDAAI,WAAU;;kEAEb,4SAAC;wDAAI,WAAU;;0EACb,4SAAC;gEAAI,WAAU;0EAAoJ;;;;;;0EAGnK,4SAAC;gEAAG,WAAU;;oEAA8E;kFACjE,4SAAC;;;;;oEAAK;;;;;;;0EAGjC,4SAAC;gEAAE,WAAU;0EAA0D;;;;;;;;;;;;kEAMzE,4SAAC;wDAAI,WAAU;kEACb,cAAA,4SAAC,8JAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,OAAO;4DAAC,WAAU;sEAClC,cAAA,4SAAC,8QAAA,CAAA,UAAI;gEAAC,MAAK;gEAAoE,QAAO;gEAAS,KAAI;gEAAsB,WAAU;0EACjI,cAAA,4SAAC;oEAAI,WAAU;;sFACb,4SAAC;4EAAI,OAAM;4EAAK,QAAO;4EAAK,SAAQ;4EAAY,MAAK;sFACnD,cAAA,4SAAC;gFAAK,GAAE;;;;;;;;;;;wEACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAehC;GAtaM;KAAA;uCAwaS", "debugId": null}}]}