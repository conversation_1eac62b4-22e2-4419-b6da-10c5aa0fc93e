{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/not-found.tsx"], "sourcesContent": ["import { Button } from '@repo/design-system/components/ui/button';\nimport Link from 'next/link';\n\nexport default function NotFound() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\" style={{ backgroundColor: '#161616' }}>\n      <div className=\"max-w-md mx-auto text-center px-6\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-9xl font-bold text-gray-200 dark:text-gray-700\">404</h1>\n          <div className=\"relative -mt-16\">\n            <h2 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-4\">\n              Page Not Found\n            </h2>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n              Sorry, we couldn't find the page you're looking for. The page might have been moved, deleted, or you entered the wrong URL.\n            </p>\n          </div>\n        </div>\n\n        <div className=\"space-y-4\">\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button asChild className=\"bg-white hover:bg-gray-100 text-black\">\n              <Link href=\"/\">\n                Go Home\n              </Link>\n            </Button>\n            <Button asChild variant=\"outline\" className=\"border-white text-white hover:bg-white hover:text-black\">\n              <Link href=\"/contact\">\n                Contact Support\n              </Link>\n            </Button>\n          </div>\n\n          <div className=\"pt-6 border-t border-gray-700\">\n            <p className=\"text-sm text-gray-400 mb-4\">\n              Popular pages:\n            </p>\n            <div className=\"flex flex-wrap gap-2 justify-center\">\n              <Link\n                href=\"/pricing\"\n                className=\"text-sm text-white hover:text-gray-300 underline\"\n              >\n                Pricing\n              </Link>\n              <span className=\"text-gray-600\">•</span>\n              <Link\n                href=\"/blog\"\n                className=\"text-sm text-white hover:text-gray-300 underline\"\n              >\n                Blog\n              </Link>\n              <span className=\"text-gray-600\">•</span>\n              <Link\n                href=\"/legal/privacy\"\n                className=\"text-sm text-white hover:text-gray-300 underline\"\n              >\n                Privacy Policy\n              </Link>\n              <span className=\"text-gray-600\">•</span>\n              <Link\n                href=\"/legal/terms\"\n                className=\"text-sm text-white hover:text-gray-300 underline\"\n              >\n                Terms of Service\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"mt-12 text-xs text-gray-400 dark:text-gray-500\">\n          <p>Error Code: 404 | Page Not Found</p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,6VAAC;QAAI,WAAU;QAAgD,OAAO;YAAE,iBAAiB;QAAU;kBACjG,cAAA,6VAAC;YAAI,WAAU;;8BACb,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAG,WAAU;sCAAsD;;;;;;sCACpE,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAGtE,6VAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;;;;;;;8BAMzD,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAU;;8CACb,6VAAC,2JAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,WAAU;8CACxB,cAAA,6VAAC,2QAAA,CAAA,UAAI;wCAAC,MAAK;kDAAI;;;;;;;;;;;8CAIjB,6VAAC,2JAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,SAAQ;oCAAU,WAAU;8CAC1C,cAAA,6VAAC,2QAAA,CAAA,UAAI;wCAAC,MAAK;kDAAW;;;;;;;;;;;;;;;;;sCAM1B,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,6VAAC;oCAAI,WAAU;;sDACb,6VAAC,2QAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6VAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6VAAC,2QAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6VAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6VAAC,2QAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6VAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6VAAC,2QAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;8BAOP,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}]}