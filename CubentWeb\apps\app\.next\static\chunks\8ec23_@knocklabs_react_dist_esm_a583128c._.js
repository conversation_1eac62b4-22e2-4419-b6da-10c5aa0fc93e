(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Spinner/Spinner.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Spinner": (()=>l)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function s(t) {
    return t === "fast" ? 600 : t === "slow" ? 900 : 750;
}
const l = ({ color: t = "rgba(0,0,0,0.4)", speed: r = "medium", gap: a = 4, thickness: n = 4, size: i = "1em", ...o })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("svg", {
        height: i,
        width: i,
        ...o,
        style: {
            animationDuration: `${s(r)}ms`
        },
        className: "__react-svg-spinner_circle",
        role: "img",
        "aria-labelledby": "title desc",
        viewBox: "0 0 32 32"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("title", {
        id: "title"
    }, "Circle loading spinner"), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("desc", {
        id: "desc"
    }, 'Image of a partial circle indicating "loading."'), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("style", {
        dangerouslySetInnerHTML: {
            __html: `
      .__react-svg-spinner_circle{
          transition-property: transform;
          animation-name: __react-svg-spinner_infinite-spin;
          animation-iteration-count: infinite;
          animation-timing-function: linear;
      }
      @keyframes __react-svg-spinner_infinite-spin {
          from {transform: rotate(0deg)}
          to {transform: rotate(360deg)}
      }
    `
        }
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("circle", {
        role: "presentation",
        cx: 16,
        cy: 16,
        r: 14 - n / 2,
        stroke: t,
        fill: "none",
        strokeWidth: n,
        strokeDasharray: Math.PI * 2 * (11 - a),
        strokeLinecap: "round"
    }));
;
 //# sourceMappingURL=Spinner.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Button/ButtonSpinner.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ButtonSpinner": (()=>l)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Spinner$2f$Spinner$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Spinner/Spinner.mjs [app-client] (ecmascript)");
;
;
/* empty css            */ const l = ({ hasLabel: e })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: `rnf-button-spinner rnf-button-spinner--${e ? "with-label" : "without-label"}`
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Spinner$2f$Spinner$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Spinner"], null));
;
 //# sourceMappingURL=ButtonSpinner.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Button/Button.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": (()=>_)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/feed/context/KnockFeedProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Button$2f$ButtonSpinner$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Button/ButtonSpinner.mjs [app-client] (ecmascript)");
;
;
;
/* empty css            */ const _ = ({ variant: r = "primary", loadingText: n, isLoading: t = !1, isDisabled: a = !1, isFullWidth: s = !1, onClick: l, children: o })=>{
    const { colorMode: u } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockFeed"])(), f = [
        "rnf-button",
        `rnf-button--${r}`,
        s ? "rnf-button--full-width" : "",
        t ? "rnf-button--is-loading" : "",
        `rnf-button--${u}`
    ].join(" "), m = n || /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", {
        className: "rnf-button__button-text-hidden"
    }, o);
    return /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        onClick: l,
        className: f,
        disabled: t || a,
        type: "button"
    }, t && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Button$2f$ButtonSpinner$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ButtonSpinner"], {
        hasLabel: !!n
    }), t ? m : o);
};
;
 //# sourceMappingURL=Button.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Button/ButtonGroup.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ButtonGroup": (()=>a)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
/* empty css            */ const a = ({ children: t })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rnf-button-group"
    }, t);
;
 //# sourceMappingURL=ButtonGroup.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Icons/Bell.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BellIcon": (()=>C)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
const C = ({ width: r = 24, height: t = 24, "aria-hidden": o })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("svg", {
        width: r,
        viewBox: "0 0 24 24",
        fill: "none",
        height: t,
        "aria-hidden": o
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M20.0474 16.4728C18.8436 14.9996 17.9938 14.2496 17.9938 10.1879C17.9938 6.46832 16.0944 5.14317 14.5311 4.49957C14.3235 4.41426 14.128 4.21832 14.0647 4.00504C13.7905 3.07176 13.0217 2.24957 11.9999 2.24957C10.978 2.24957 10.2088 3.07223 9.93736 4.00598C9.87408 4.2216 9.67861 4.41426 9.47096 4.49957C7.9058 5.1441 6.0083 6.46457 6.0083 10.1879C6.00596 14.2496 5.15611 14.9996 3.95237 16.4728C3.45362 17.0832 3.89049 17.9996 4.76283 17.9996H19.2416C20.1092 17.9996 20.5433 17.0803 20.0474 16.4728Z",
        stroke: "currentColor",
        strokeWidth: "1.5",
        strokeLinecap: "round",
        strokeLinejoin: "round"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M14.9999 17.9988V18.7488C14.9999 19.5445 14.6838 20.3075 14.1212 20.8701C13.5586 21.4327 12.7955 21.7488 11.9999 21.7488C11.2042 21.7488 10.4412 21.4327 9.87856 20.8701C9.31595 20.3075 8.99988 19.5445 8.99988 18.7488V17.9988",
        stroke: "currentColor",
        strokeWidth: "1.5",
        strokeLinecap: "round",
        strokeLinejoin: "round"
    }));
;
 //# sourceMappingURL=Bell.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Icons/CheckmarkCircle.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CheckmarkCircle": (()=>n)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
const n = ({ width: t = 16, height: r = 16, "aria-hidden": o })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("svg", {
        width: t,
        height: r,
        viewBox: "0 0 16 16",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        "aria-hidden": o
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M14 8.00012C14 4.68762 11.3125 2.00012 7.99997 2.00012C4.68747 2.00012 1.99997 4.68762 1.99997 8.00012C1.99997 11.3126 4.68747 14.0001 7.99997 14.0001C11.3125 14.0001 14 11.3126 14 8.00012Z",
        stroke: "currentColor",
        strokeWidth: "1.5",
        strokeMiterlimit: "10"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M10.9999 5.5004L6.79994 10.5004L4.99994 8.5004",
        stroke: "currentColor",
        strokeWidth: "1.5",
        strokeLinecap: "round",
        strokeLinejoin: "round"
    }));
;
 //# sourceMappingURL=CheckmarkCircle.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Icons/ChevronDown.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ChevronDown": (()=>i)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
const i = ({ width: o = 8, height: r = 6, "aria-hidden": t })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("svg", {
        width: o,
        height: r,
        viewBox: "0 0 8 6",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        "aria-hidden": t
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M1.74994 1.87512L3.99994 4.12512L6.24994 1.87512",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
    }));
;
 //# sourceMappingURL=ChevronDown.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Icons/CloseCircle.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CloseCircle": (()=>o)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
const o = ({ width: e = 14, height: t = 14, "aria-hidden": r })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("svg", {
        width: e,
        height: t,
        viewBox: "0 0 14 14",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        "aria-hidden": r
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M7.00012 0.499939C3.41606 0.499939 0.500122 3.41588 0.500122 6.99994C0.500122 10.584 3.41606 13.4999 7.00012 13.4999C10.5842 13.4999 13.5001 10.584 13.5001 6.99994C13.5001 3.41588 10.5842 0.499939 7.00012 0.499939ZM9.35356 8.6465C9.40194 8.69247 9.44063 8.74766 9.46735 8.80881C9.49407 8.86997 9.50828 8.93585 9.50913 9.00259C9.50999 9.06932 9.49747 9.13555 9.47233 9.19737C9.44718 9.25919 9.40992 9.31535 9.36273 9.36254C9.31553 9.40973 9.25937 9.447 9.19755 9.47214C9.13573 9.49729 9.0695 9.5098 9.00277 9.50895C8.93604 9.50809 8.87015 9.49389 8.809 9.46717C8.74784 9.44045 8.69265 9.40176 8.64668 9.35337L7.00012 7.70712L5.35356 9.35337C5.25903 9.44318 5.13315 9.49251 5.00277 9.49084C4.87239 9.48918 4.74782 9.43664 4.65562 9.34444C4.56342 9.25224 4.51088 9.12767 4.50921 8.99729C4.50755 8.86691 4.55687 8.74103 4.64668 8.6465L6.29293 6.99994L4.64668 5.35338C4.55687 5.25884 4.50755 5.13297 4.50921 5.00259C4.51088 4.87221 4.56342 4.74764 4.65562 4.65544C4.74782 4.56324 4.87239 4.5107 5.00277 4.50903C5.13315 4.50736 5.25903 4.55669 5.35356 4.6465L7.00012 6.29275L8.64668 4.6465C8.74121 4.55669 8.86709 4.50736 8.99747 4.50903C9.12785 4.5107 9.25242 4.56324 9.34462 4.65544C9.43682 4.74764 9.48936 4.87221 9.49103 5.00259C9.4927 5.13297 9.44337 5.25884 9.35356 5.35338L7.70731 6.99994L9.35356 8.6465Z",
        fill: "currentColor"
    }));
;
 //# sourceMappingURL=CloseCircle.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/hooks/useOnBottomScroll.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>k)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lodash$2e$debounce$40$4$2e$0$2e$8$2f$node_modules$2f$lodash$2e$debounce$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
const d = ()=>{};
function k(t) {
    const r = t.callback ?? d, e = t.ref, l = t.offset ?? 0, n = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lodash$2e$debounce$40$4$2e$0$2e$8$2f$node_modules$2f$lodash$2e$debounce$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(r, 200), [
        r
    ]), c = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (e.current) {
            const o = e.current, s = Math.round(o.scrollTop + o.clientHeight);
            Math.round(o.scrollHeight - l) <= s && n();
        }
    }, [
        n
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        let o;
        return e.current && (o = e.current, e.current.addEventListener("scroll", c)), ()=>{
            o && o.removeEventListener("scroll", c);
        };
    }, [
        c
    ]);
}
;
 //# sourceMappingURL=useOnBottomScroll.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/EmptyFeed/EmptyFeed.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "EmptyFeed": (()=>s)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/feed/context/KnockFeedProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/i18n/hooks/useTranslations.mjs [app-client] (ecmascript)");
;
;
/* empty css            */ const s = ()=>{
    const { colorMode: m } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockFeed"])(), { t } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"])();
    return /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: `rnf-empty-feed rnf-empty-feed--${m}`
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rnf-empty-feed__inner"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("h2", {
        className: "rnf-empty-feed__header"
    }, t("emptyFeedTitle")), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("p", {
        className: "rnf-empty-feed__body"
    }, t("emptyFeedBody"))));
};
;
 //# sourceMappingURL=EmptyFeed.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationCell/ArchiveButton.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ArchiveButton": (()=>g)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/feed/context/KnockFeedProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/i18n/hooks/useTranslations.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$popperjs$2b$core$40$2$2e$11$2e$8$2f$node_modules$2f40$popperjs$2f$core$2f$lib$2f$popper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/popper.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Icons$2f$CloseCircle$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Icons/CloseCircle.mjs [app-client] (ecmascript)");
;
;
;
;
const g = ({ item: i })=>{
    const { colorMode: a, feedClient: l } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockFeed"])(), { t: c } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"])(), [t, s] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1), o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null), r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null), u = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])((e)=>{
        e.preventDefault(), e.stopPropagation(), l.markAsArchived(i);
    }, // TODO: Check if we can remove this disable
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
        i
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (o.current && r.current && t) {
            const e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$popperjs$2b$core$40$2$2e$11$2e$8$2f$node_modules$2f40$popperjs$2f$core$2f$lib$2f$popper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createPopper"])(o.current, r.current, {
                placement: "top-end",
                modifiers: [
                    {
                        name: "offset",
                        options: {
                            offset: [
                                0,
                                8
                            ]
                        }
                    }
                ]
            });
            return ()=>{
                e.destroy();
            };
        }
    }, [
        t
    ]), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        ref: o,
        onClick: u,
        onMouseEnter: ()=>s(!0),
        onMouseLeave: ()=>s(!1),
        type: "button",
        "aria-label": c("archiveNotification"),
        className: `rnf-archive-notification-btn rnf-archive-notification-btn--${a}`
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Icons$2f$CloseCircle$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CloseCircle"], {
        "aria-hidden": !0
    }), t && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        ref: r,
        className: `rnf-tooltip rnf-tooltip--${a}`
    }, c("archiveNotification")));
};
;
 //# sourceMappingURL=ArchiveButton.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationCell/Avatar.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Avatar": (()=>m)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
/* empty css            */ const m = ({ name: e, src: r })=>{
    function i(s) {
        const [a, n] = s.split(" ");
        return a && n ? `${a.charAt(0)}${n.charAt(0)}` : a ? a.charAt(0) : "";
    }
    return /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rnf-avatar"
    }, r ? /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("img", {
        src: r,
        alt: e,
        className: "rnf-avatar__image"
    }) : /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", {
        className: "rnf-avatar__initials"
    }, i(e)));
};
;
 //# sourceMappingURL=Avatar.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationCell/NotificationCell.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NotificationCell": (()=>L)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/feed/context/KnockFeedProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/i18n/hooks/useTranslations.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$core$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/core/utils.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Button$2f$Button$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Button/Button.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Button$2f$ButtonGroup$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Button/ButtonGroup.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lodash$2e$debounce$40$4$2e$0$2e$8$2f$node_modules$2f$lodash$2e$debounce$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationCell$2f$ArchiveButton$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationCell/ArchiveButton.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationCell$2f$Avatar$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationCell/Avatar.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
;
/* empty css            */ function p(e) {
    e && e !== "" && setTimeout(()=>window.location.assign(e), 200);
}
const L = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef(({ item: e, onItemClick: l, onButtonClick: i, avatar: k, children: f, archiveButton: y }, E)=>{
    var _;
    const { feedClient: o, colorMode: N } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockFeed"])(), { locale: b } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"])(), c = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>e.blocks.reduce((t, a)=>({
                ...t,
                [a.name]: a
            }), {}), [
        e
    ]), s = (_ = c.action_url) == null ? void 0 : _.rendered, d = c.actions, m = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useCallback({
        "L.useCallback[m]": ()=>(o.markAsInteracted(e, {
                type: "cell_click",
                action: s
            }), l ? l(e) : p(s))
    }["L.useCallback[m]"], [
        e,
        s,
        l,
        o
    ]), v = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useCallback({
        "L.useCallback[v]": (t, a)=>(o.markAsInteracted(e, {
                type: "button_click",
                name: a.name,
                label: a.label,
                action: a.action
            }), i ? i(e, a) : p(a.action))
    }["L.useCallback[v]"], [
        i,
        o,
        e
    ]), C = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useCallback({
        "L.useCallback[C]": (t)=>{
            switch(t.key){
                case "Enter":
                    {
                        t.stopPropagation(), m();
                        break;
                    }
            }
        }
    }["L.useCallback[C]"], [
        m
    ]), r = e.actors[0];
    return(// eslint-disable-next-line jsx-a11y/no-static-element-interactions
    /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        ref: E,
        className: `rnf-notification-cell rnf-notification-cell--${N}`,
        onClick: m,
        onKeyDown: C,
        tabIndex: 0
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rnf-notification-cell__inner"
    }, !e.read_at && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rnf-notification-cell__unread-dot"
    }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$core$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["renderNodeOrFallback"])(k, r && "name" in r && r.name && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationCell$2f$Avatar$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
        name: r.name,
        src: r.avatar
    })), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rnf-notification-cell__content-outer"
    }, c.body && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rnf-notification-cell__content",
        dangerouslySetInnerHTML: {
            __html: c.body.rendered
        }
    }), d && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rnf-notification-cell__button-group"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Button$2f$ButtonGroup$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ButtonGroup"], null, d.buttons.map((t, a)=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Button$2f$Button$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
            variant: a === 0 ? "primary" : "secondary",
            key: t.name,
            onClick: (g)=>v(g, t)
        }, t.label)))), f && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rnf-notification-cell__child-content"
    }, f), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", {
        className: "rnf-notification-cell__timestamp"
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$core$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatTimestamp"])(e.inserted_at, {
        locale: b
    }))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$core$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["renderNodeOrFallback"])(y, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationCell$2f$ArchiveButton$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ArchiveButton"], {
        item: e
    })))));
});
;
 //# sourceMappingURL=NotificationCell.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeed/Dropdown.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Dropdown": (()=>f)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/feed/context/KnockFeedProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Icons$2f$ChevronDown$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Icons/ChevronDown.mjs [app-client] (ecmascript)");
;
;
;
/* empty css            */ const f = ({ children: o, value: r, onChange: t })=>{
    const { colorMode: n } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockFeed"])();
    return /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: `rnf-dropdown rnf-dropdown--${n}`
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("select", {
        "aria-label": "Select notification filter",
        value: r,
        onChange: t
    }, o), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Icons$2f$ChevronDown$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChevronDown"], {
        "aria-hidden": !0
    }));
};
;
 //# sourceMappingURL=Dropdown.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeed/MarkAsRead.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MarkAsRead": (()=>C)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/feed/context/KnockFeedProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/i18n/hooks/useTranslations.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Icons$2f$CheckmarkCircle$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Icons/CheckmarkCircle.mjs [app-client] (ecmascript)");
;
;
;
/* empty css            */ const C = ({ onClick: a })=>{
    const { useFeedStore: r, feedClient: n, colorMode: s } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockFeed"])(), { t: l } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"])(), o = r((e)=>e.items.filter((c)=>!c.read_at)), d = r((e)=>e.metadata.unread_count), m = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "C.useCallback[m]": (e)=>{
            n.markAllAsRead(), a && a(e, o);
        }
    }["C.useCallback[m]"], [
        n,
        o,
        a
    ]);
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("button", {
        className: `rnf-mark-all-as-read rnf-mark-all-as-read--${s}`,
        disabled: d === 0,
        onClick: m,
        type: "button"
    }, l("markAllAsRead"), /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Icons$2f$CheckmarkCircle$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CheckmarkCircle"], {
        "aria-hidden": !0
    }));
};
;
 //# sourceMappingURL=MarkAsRead.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeed/NotificationFeedHeader.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NotificationFeedHeader": (()=>u)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$core$2f$constants$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/core/constants.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/i18n/hooks/useTranslations.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeed$2f$Dropdown$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeed/Dropdown.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeed$2f$MarkAsRead$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeed/MarkAsRead.mjs [app-client] (ecmascript)");
;
;
;
;
const s = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$core$2f$constants$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterStatus"].All,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$core$2f$constants$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterStatus"].Unread,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$core$2f$constants$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterStatus"].Read
], u = ({ onMarkAllAsReadClick: r, filterStatus: o, setFilterStatus: i })=>{
    const { t: n } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"])();
    return /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("header", {
        className: "rnf-notification-feed__header"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rnf-notification-feed__selector"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", {
        className: "rnf-notification-feed__type"
    }, n("notifications")), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeed$2f$Dropdown$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Dropdown"], {
        value: o,
        onChange: (t)=>i(t.target.value)
    }, s.map((t)=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("option", {
            key: t,
            value: t
        }, n(t))))), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeed$2f$MarkAsRead$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MarkAsRead"], {
        onClick: r
    }));
};
;
 //# sourceMappingURL=NotificationFeedHeader.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeed/NotificationFeed.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NotificationFeed": (()=>Z)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$client$40$0$2e$14$2e$8_$40$types$2b$react$40$19$2e$1$2e$5_react$40$19$2e$1$2e$0$2f$node_modules$2f40$knocklabs$2f$client$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+client@0.14.8_@types+react@19.1.5_react@19.1.0/node_modules/@knocklabs/client/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$client$40$0$2e$14$2e$8_$40$types$2b$react$40$19$2e$1$2e$5_react$40$19$2e$1$2e$0$2f$node_modules$2f40$knocklabs$2f$client$2f$dist$2f$esm$2f$networkStatus$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+client@0.14.8_@types+react@19.1.5_react@19.1.0/node_modules/@knocklabs/client/dist/esm/networkStatus.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$core$2f$constants$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/core/constants.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/feed/context/KnockFeedProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$hooks$2f$useFeedSettings$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useFeedSettings$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/feed/hooks/useFeedSettings.mjs [app-client] (ecmascript) <export default as useFeedSettings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/i18n/hooks/useTranslations.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Spinner$2f$Spinner$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Spinner/Spinner.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$hooks$2f$useOnBottomScroll$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/hooks/useOnBottomScroll.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$EmptyFeed$2f$EmptyFeed$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/EmptyFeed/EmptyFeed.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationCell$2f$NotificationCell$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationCell/NotificationCell.mjs [app-client] (ecmascript)");
/* empty css                              */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeed$2f$NotificationFeedHeader$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeed/NotificationFeedHeader.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
/* empty css            */ const O = (t)=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationCell$2f$NotificationCell$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NotificationCell"], {
        key: t.item.id,
        ...t
    }), T = (t)=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeed$2f$NotificationFeedHeader$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NotificationFeedHeader"], {
        ...t
    }), k = ({ colorMode: t })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rnf-notification-feed__spinner-container"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Spinner$2f$Spinner$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Spinner"], {
        thickness: 3,
        size: "16px",
        color: t === "dark" ? "rgba(255, 255, 255, 0.65)" : void 0
    })), U = "https://knock.app?utm_source=powered-by-knock&utm_medium=referral&utm_campaign=knock-branding-feed", Z = ({ EmptyComponent: t = /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$EmptyFeed$2f$EmptyFeed$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EmptyFeed"], null), renderItem: _ = O, onNotificationClick: g, onNotificationButtonClick: E, onMarkAllAsReadClick: b, initialFilterStatus: r = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$core$2f$constants$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterStatus"].All, header: N, renderHeader: S = T })=>{
    const [a, m] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(r), { feedClient: n, useFeedStore: h, colorMode: c } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockFeed"])(), { settings: i } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$hooks$2f$useFeedSettings$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useFeedSettings$3e$__["useFeedSettings"])(n), { t: F } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"])(), { pageInfo: l, items: d, networkStatus: o } = h(), u = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        m(r);
    }, [
        r
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        n.fetch({
            status: a
        });
    }, [
        n,
        a
    ]);
    const v = d.length === 0, s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$client$40$0$2e$14$2e$8_$40$types$2b$react$40$19$2e$1$2e$5_react$40$19$2e$1$2e$0$2f$node_modules$2f40$knocklabs$2f$client$2f$dist$2f$esm$2f$networkStatus$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isRequestInFlight"])(o), w = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        !s && l.after && n.fetchNextPage();
    }, [
        s,
        l,
        n
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$hooks$2f$useOnBottomScroll$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        ref: u,
        callback: w,
        offset: 70
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: `rnf-notification-feed rnf-notification-feed--${c}`
    }, N || S({
        setFilterStatus: m,
        filterStatus: a,
        onMarkAllAsReadClick: b
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rnf-notification-feed__container",
        ref: u
    }, o === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$client$40$0$2e$14$2e$8_$40$types$2b$react$40$19$2e$1$2e$5_react$40$19$2e$1$2e$0$2f$node_modules$2f40$knocklabs$2f$client$2f$dist$2f$esm$2f$networkStatus$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NetworkStatus"].loading && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(k, {
        colorMode: c
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rnf-notification-feed__feed-items-container"
    }, o !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$client$40$0$2e$14$2e$8_$40$types$2b$react$40$19$2e$1$2e$5_react$40$19$2e$1$2e$0$2f$node_modules$2f40$knocklabs$2f$client$2f$dist$2f$esm$2f$networkStatus$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NetworkStatus"].loading && d.map((y)=>_({
            item: y,
            onItemClick: g,
            onButtonClick: E
        }))), o === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$client$40$0$2e$14$2e$8_$40$types$2b$react$40$19$2e$1$2e$5_react$40$19$2e$1$2e$0$2f$node_modules$2f40$knocklabs$2f$client$2f$dist$2f$esm$2f$networkStatus$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NetworkStatus"].fetchMore && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(k, {
        colorMode: c
    }), !s && v && t), (i == null ? void 0 : i.features.branding_required) && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rnf-notification-feed__knock-branding"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("a", {
        href: U,
        target: "_blank"
    }, F("poweredBy") || "Powered by Knock")));
};
;
 //# sourceMappingURL=NotificationFeed.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeedContainer/NotificationFeedContainer.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NotificationFeedContainer": (()=>i)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
/* empty css            */ const i = ({ children: e })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rnf-feed-provider"
    }, e);
;
 //# sourceMappingURL=NotificationFeedContainer.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/hooks/useComponentVisible.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>a)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function d(e, t) {
    return e ? e === t || e.contains(t) : !1;
}
function a(e, t, o) {
    const r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null), u = (n)=>{
        n.key === "Escape" && t(n);
    }, c = (n)=>{
        o.closeOnClickOutside && !d(r.current, n.target) && t(n);
    };
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>(e && (document.addEventListener("keydown", u, !0), document.addEventListener("click", c, !0)), ()=>{
            document.removeEventListener("keydown", u, !0), document.removeEventListener("click", c, !0);
        }), [
        e
    ]), {
        ref: r
    };
}
;
 //# sourceMappingURL=useComponentVisible.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeedPopover/NotificationFeedPopover.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NotificationFeedPopover": (()=>K)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/i18n/hooks/useTranslations.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/feed/context/KnockFeedProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$popperjs$2b$core$40$2$2e$11$2e$8$2f$node_modules$2f40$popperjs$2f$core$2f$lib$2f$popper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/popper.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$hooks$2f$useComponentVisible$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/hooks/useComponentVisible.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeed$2f$NotificationFeed$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeed/NotificationFeed.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeed$2f$NotificationFeedHeader$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeed/NotificationFeedHeader.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
/* empty css                              */ /* empty css            */ const x = ({ store: e, feedClient: t })=>{
    e.metadata.unseen_count > 0 && t.markAllAsSeen();
}, K = ({ isVisible: e, onOpen: t = x, onClose: p, buttonRef: r, closeOnClickOutside: s = !0, placement: i = "bottom-end", ...m })=>{
    const { t: d } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"])(), { colorMode: l, feedClient: f, useFeedStore: u } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockFeed"])(), a = u(), { ref: o } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$hooks$2f$useComponentVisible$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(e, p, {
        closeOnClickOutside: s
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        e && t && t({
            store: a,
            feedClient: f
        });
    }, [
        e,
        t,
        a,
        f
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (r.current && o.current) {
            const v = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$popperjs$2b$core$40$2$2e$11$2e$8$2f$node_modules$2f40$popperjs$2f$core$2f$lib$2f$popper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createPopper"])(r.current, o.current, {
                strategy: "fixed",
                placement: i,
                modifiers: [
                    {
                        name: "offset",
                        options: {
                            offset: [
                                0,
                                8
                            ]
                        }
                    }
                ]
            });
            return ()=>{
                v.destroy();
            };
        }
    }, [
        r,
        o,
        i
    ]), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: `rnf-notification-feed-popover rnf-notification-feed-popover--${l}`,
        style: {
            visibility: e ? "visible" : "hidden",
            opacity: e ? 1 : 0
        },
        ref: o,
        role: "dialog",
        "aria-label": d("notifications"),
        tabIndex: -1
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rnf-notification-feed-popover__inner"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeed$2f$NotificationFeed$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NotificationFeed"], {
        ...m
    })));
};
;
 //# sourceMappingURL=NotificationFeedPopover.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/UnseenBadge/UnseenBadge.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "UnseenBadge": (()=>m)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/feed/context/KnockFeedProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$core$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/core/utils.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
/* empty css            */ function s(n, e) {
    switch(n){
        case "all":
            return e.total_count;
        case "unread":
            return e.unread_count;
        case "unseen":
            return e.unseen_count;
    }
}
const m = ({ badgeCountType: n = "unseen" })=>{
    const { useFeedStore: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockFeed"])(), t = e((r)=>s(n, r.metadata));
    return t !== 0 ? /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rnf-unseen-badge"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", {
        className: "rnf-unseen-badge__count"
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$core$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatBadgeCount"])(t))) : null;
};
;
 //# sourceMappingURL=UnseenBadge.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationIconButton/NotificationIconButton.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NotificationIconButton": (()=>p)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/feed/context/KnockFeedProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Icons$2f$Bell$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Icons/Bell.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$UnseenBadge$2f$UnseenBadge$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/UnseenBadge/UnseenBadge.mjs [app-client] (ecmascript)");
;
;
;
;
/* empty css            */ const p = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef(({ onClick: o, badgeCountType: e }, n)=>{
    const { colorMode: r } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$feed$2f$context$2f$KnockFeedProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockFeed"])();
    return /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        className: `rnf-notification-icon-button rnf-notification-icon-button--${r}`,
        "aria-label": "Open notification feed",
        ref: n,
        onClick: o
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Icons$2f$Bell$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BellIcon"], {
        "aria-hidden": !0
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$UnseenBadge$2f$UnseenBadge$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UnseenBadge"], {
        badgeCountType: e
    }));
});
;
 //# sourceMappingURL=NotificationIconButton.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/guide/components/helpers.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isValidHttpUrl": (()=>r),
    "maybeNavigateToUrlWithDelay": (()=>i)
});
const r = (o)=>{
    let t;
    try {
        t = new URL(o);
    } catch  {
        return !1;
    }
    return t.protocol === "http:" || t.protocol === "https:";
}, i = (o, t = 200)=>{
    window != null && window.location && r(o) && setTimeout(()=>window.location.assign(o), t);
};
;
 //# sourceMappingURL=helpers.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/guide/components/Banner/Banner.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Banner": (()=>B),
    "BannerView": (()=>w)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$guide$2f$hooks$2f$useGuide$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/guide/hooks/useGuide.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$guide$2f$components$2f$helpers$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/guide/components/helpers.mjs [app-client] (ecmascript)");
;
;
;
;
/* empty css            */ const f = "banner", s = ({ children: e, className: n, ...a })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-banner", n),
        ...a
    }, e);
s.displayName = "BannerView.Root";
const m = ({ children: e, className: n, ...a })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-banner__message", n),
        ...a
    }, e);
m.displayName = "BannerView.Content";
const d = ({ title: e, className: n, ...a })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-banner__title", n),
        ...a
    }, e);
d.displayName = "BannerView.Title";
const u = ({ body: e, className: n, ...a })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-banner__body", n),
        dangerouslySetInnerHTML: {
            __html: e
        },
        ...a
    });
u.displayName = "BannerView.Body";
const y = ({ children: e, className: n, ...a })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-banner__actions", n),
        ...a
    }, e);
y.displayName = "BannerView.Actions";
const _ = ({ text: e, action: n, className: a, ...r })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-banner__action", a),
        ...r
    }, e);
_.displayName = "BannerView.PrimaryButton";
const b = ({ text: e, action: n, className: a, ...r })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-banner__action knock-guide-banner__action--secondary", a),
        ...r
    }, e);
b.displayName = "BannerView.SecondaryButton";
const p = ({ className: e, ...n })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-banner__close", e),
        ...n
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "18",
        height: "18",
        fill: "none"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("g", {
        fill: "#60646C",
        fillRule: "evenodd",
        clipRule: "evenodd"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z"
    }))));
p.displayName = "BannerView.DismissButton";
const k = ({ content: e, colorMode: n = "light", onDismiss: a, onButtonClick: r })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(s, {
        "data-knock-color-mode": n
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(m, null, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(d, {
        title: e.title
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(u, {
        body: e.body
    })), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(y, null, e.secondary_button && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(b, {
        text: e.secondary_button.text,
        action: e.secondary_button.action,
        onClick: (c)=>{
            if (r) {
                const { text: l, action: o } = e.secondary_button;
                r(c, {
                    name: "secondary_button",
                    text: l,
                    action: o
                });
            }
        }
    }), e.primary_button && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(_, {
        text: e.primary_button.text,
        action: e.primary_button.action,
        onClick: (c)=>{
            if (r) {
                const { text: l, action: o } = e.primary_button;
                r(c, {
                    name: "primary_button",
                    text: l,
                    action: o
                });
            }
        }
    }), e.dismissible && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(p, {
        onClick: a
    })));
k.displayName = "BannerView.Default";
const B = ({ guideKey: e, onButtonClick: n })=>{
    const { guide: a, step: r, colorMode: c } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$guide$2f$hooks$2f$useGuide$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGuide"])({
        key: e,
        type: f
    });
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "B.useEffect": ()=>{
            r && r.markAsSeen();
        }
    }["B.useEffect"], [
        r
    ]), !a || !r ? null : /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(k, {
        content: r.content,
        colorMode: c,
        onDismiss: ()=>r.markAsArchived(),
        onButtonClick: (l, o)=>{
            const E = {
                ...o,
                type: "button_click"
            };
            return r.markAsInteracted({
                metadata: E
            }), n ? n(l, {
                button: o,
                step: r,
                guide: a
            }) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$guide$2f$components$2f$helpers$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["maybeNavigateToUrlWithDelay"])(o.action);
        }
    });
};
B.displayName = "Banner";
const w = {};
Object.assign(w, {
    Default: k,
    Root: s,
    Content: m,
    Title: d,
    Body: u,
    Actions: y,
    PrimaryButton: _,
    SecondaryButton: b,
    DismissButton: p
});
;
 //# sourceMappingURL=Banner.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/guide/components/Card/Card.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Card": (()=>x),
    "CardView": (()=>A)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$guide$2f$hooks$2f$useGuide$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/guide/hooks/useGuide.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$guide$2f$components$2f$helpers$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/guide/components/helpers.mjs [app-client] (ecmascript)");
;
;
;
;
/* empty css            */ const v = "card", o = ({ children: e, className: t, ...r })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-card", t),
        ...r
    }, e);
o.displayName = "CardView.Root";
const u = ({ children: e, className: t, ...r })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-card__message", t),
        ...r
    }, e);
u.displayName = "CardView.Content";
const f = ({ children: e, className: t, ...r })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-card__header", t),
        ...r
    }, e);
f.displayName = "CardView.Header";
const w = ({ headline: e, className: t, ...r })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-card__headline", t),
        ...r
    }, e);
w.displayName = "CardView.Headline";
const y = ({ title: e, className: t, ...r })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-card__title", t),
        ...r
    }, e);
y.displayName = "CardView.Title";
const _ = ({ body: e, className: t, ...r })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-card__body", t),
        dangerouslySetInnerHTML: {
            __html: e
        },
        ...r
    });
_.displayName = "CardView.Body";
const k = ({ children: e, className: t, alt: r, ...i })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("img", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-card__img", t),
        alt: r || "",
        ...i
    }, e);
k.displayName = "CardView.Img";
const p = ({ children: e, className: t, ...r })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-card__actions", t),
        ...r
    }, e);
p.displayName = "CardView.Actions";
const g = ({ text: e, action: t, className: r, ...i })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-card__action", r),
        ...i
    }, e);
g.displayName = "CardView.PrimaryButton";
const E = ({ text: e, action: t, className: r, ...i })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-card__action knock-guide-card__action--secondary", r),
        ...i
    }, e);
E.displayName = "CardView.SecondaryButton";
const N = ({ className: e, ...t })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-card__close", e),
        ...t
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "18",
        height: "18",
        fill: "none"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("g", {
        fill: "#60646C",
        fillRule: "evenodd",
        clipRule: "evenodd"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z"
    }))));
N.displayName = "CardView.DismissButton";
const b = ({ content: e, colorMode: t = "light", onDismiss: r, onButtonClick: i, onImageClick: n })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(o, {
        "data-knock-color-mode": t
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(u, null, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(f, null, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(w, {
        headline: e.headline
    }), e.dismissible && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(N, {
        onClick: r
    })), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(y, {
        title: e.title
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(_, {
        body: e.body
    })), e.image && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("a", {
        href: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$guide$2f$components$2f$helpers$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidHttpUrl"])(e.image.action) ? e.image.action : void 0,
        target: "_blank"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(k, {
        src: e.image.url,
        alt: e.image.alt,
        onClick: (d)=>{
            n && n(d, e.image);
        }
    })), (e.primary_button || e.secondary_button) && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(p, null, e.primary_button && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(g, {
        text: e.primary_button.text,
        action: e.primary_button.action,
        onClick: (d)=>{
            if (i) {
                const { text: s, action: l } = e.primary_button;
                i(d, {
                    name: "primary_button",
                    text: s,
                    action: l
                });
            }
        }
    }), e.secondary_button && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(E, {
        text: e.secondary_button.text,
        action: e.secondary_button.action,
        onClick: (d)=>{
            if (i) {
                const { text: s, action: l } = e.secondary_button;
                i(d, {
                    name: "secondary_button",
                    text: s,
                    action: l
                });
            }
        }
    })));
b.displayName = "CardView.Default";
const x = ({ guideKey: e, onButtonClick: t, onImageClick: r })=>{
    const { guide: i, step: n, colorMode: d } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$guide$2f$hooks$2f$useGuide$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGuide"])({
        key: e,
        type: v
    });
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "x.useEffect": ()=>{
            n && n.markAsSeen();
        }
    }["x.useEffect"], [
        n
    ]), !i || !n ? null : /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(b, {
        content: n.content,
        colorMode: d,
        onDismiss: ()=>n.markAsArchived(),
        onButtonClick: (s, l)=>{
            const m = {
                ...l,
                type: "button_click"
            };
            return n.markAsInteracted({
                metadata: m
            }), t ? t(s, {
                button: l,
                step: n,
                guide: i
            }) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$guide$2f$components$2f$helpers$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["maybeNavigateToUrlWithDelay"])(l.action);
        },
        onImageClick: (s, l)=>{
            const m = {
                ...l,
                type: "image_click"
            };
            if (n.markAsInteracted({
                metadata: m
            }), r) return r(s, {
                image: l,
                step: n,
                guide: i
            });
        }
    });
};
x.displayName = "Card";
const A = {};
Object.assign(A, {
    Default: b,
    Root: o,
    Content: u,
    Title: y,
    Body: _,
    Img: k,
    Actions: p,
    PrimaryButton: g,
    SecondaryButton: E,
    DismissButton: N
});
;
 //# sourceMappingURL=Card.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/guide/components/Modal/Modal.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Modal": (()=>A),
    "ModalView": (()=>R)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$guide$2f$hooks$2f$useGuide$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/guide/hooks/useGuide.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$_13a38cfc595b5034cf5be7e9c0d8ba6b$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@radix-ui+react-dialog@1.1._13a38cfc595b5034cf5be7e9c0d8ba6b/node_modules/@radix-ui/react-dialog/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$guide$2f$components$2f$helpers$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/guide/components/helpers.mjs [app-client] (ecmascript)");
;
;
;
;
;
/* empty css            */ const x = "modal", u = ({ children: e, onOpenChange: t, ...l })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$_13a38cfc595b5034cf5be7e9c0d8ba6b$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        defaultOpen: !0,
        onOpenChange: t,
        ...l
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$_13a38cfc595b5034cf5be7e9c0d8ba6b$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Portal"], null, e));
u.displayName = "ModalView.Root";
const y = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef(({ className: e, ...t }, l)=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$_13a38cfc595b5034cf5be7e9c0d8ba6b$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Overlay"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-modal__overlay", e),
        ref: l,
        ...t
    }));
y.displayName = "ModalView.Overlay";
const _ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef(({ children: e, className: t, ...l }, r)=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$_13a38cfc595b5034cf5be7e9c0d8ba6b$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Content"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-modal", t),
        ref: r,
        ...l
    }, e));
_.displayName = "ModalView.Content";
const M = ({ children: e, className: t, ...l })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-modal__header", t),
        ...l
    }, e);
M.displayName = "ModalView.Header";
const p = ({ title: e, className: t, ...l })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$_13a38cfc595b5034cf5be7e9c0d8ba6b$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Title"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-modal__title", t),
        ...l
    }, e);
p.displayName = "ModalView.Title";
const E = ({ body: e, className: t, ...l })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$_13a38cfc595b5034cf5be7e9c0d8ba6b$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Description"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-modal__body", t),
        dangerouslySetInnerHTML: {
            __html: e
        },
        ...l
    });
E.displayName = "ModalView.Body";
const g = ({ children: e, className: t, alt: l, ...r })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("img", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-modal__img", t),
        alt: l || "",
        ...r
    }, e);
g.displayName = "ModalView.Img";
const k = ({ children: e, className: t, ...l })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-modal__actions", t),
        ...l
    }, e);
k.displayName = "ModalView.Actions";
const f = ({ text: e, action: t, className: l, ...r })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-modal__action", l),
        ...r
    }, e);
f.displayName = "ModalView.PrimaryButton";
const N = ({ text: e, action: t, className: l, ...r })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-modal__action knock-guide-modal__action--secondary", l),
        ...r
    }, e);
N.displayName = "ModalView.SecondaryButton";
const w = ({ className: e, ...t })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$_13a38cfc595b5034cf5be7e9c0d8ba6b$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Close"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("knock-guide-modal__close", e),
        ...t
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "18",
        height: "18",
        fill: "none"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("g", {
        fill: "#60646C",
        fillRule: "evenodd",
        clipRule: "evenodd"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z"
    }))));
w.displayName = "ModalView.Close";
const b = ({ content: e, colorMode: t = "light", onOpenChange: l, onDismiss: r, onButtonClick: o, onImageClick: s })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(u, {
        onOpenChange: l
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(y, null), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(_, {
        "data-knock-color-mode": t,
        onPointerDownOutside: r
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(M, null, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(p, {
        title: e.title
    }), e.dismissible && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(w, {
        onClick: r
    })), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(E, {
        body: e.body
    }), e.image && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("a", {
        href: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$guide$2f$components$2f$helpers$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidHttpUrl"])(e.image.action) ? e.image.action : void 0,
        target: "_blank"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(g, {
        src: e.image.url,
        alt: e.image.alt,
        onClick: (m)=>{
            s && s(m, e.image);
        }
    })), (e.primary_button || e.secondary_button) && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(k, null, e.secondary_button && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(N, {
        text: e.secondary_button.text,
        action: e.secondary_button.action,
        onClick: (m)=>{
            if (o) {
                const { text: i, action: c } = e.secondary_button;
                o(m, {
                    name: "secondary_button",
                    text: i,
                    action: c
                });
            }
        }
    }), e.primary_button && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(f, {
        text: e.primary_button.text,
        action: e.primary_button.action,
        onClick: (m)=>{
            if (o) {
                const { text: i, action: c } = e.primary_button;
                o(m, {
                    name: "primary_button",
                    text: i,
                    action: c
                });
            }
        }
    }))));
b.displayName = "ModalView.Default";
const A = ({ guideKey: e, onButtonClick: t, onImageClick: l })=>{
    const { guide: r, step: o, colorMode: s } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$guide$2f$hooks$2f$useGuide$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGuide"])({
        key: e,
        type: x
    });
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "A.useEffect": ()=>{
            o && o.markAsSeen();
        }
    }["A.useEffect"], [
        o
    ]), !r || !o ? null : /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(b, {
        content: o.content,
        colorMode: s,
        onDismiss: ()=>o.markAsArchived(),
        onButtonClick: (m, i)=>{
            const c = {
                ...i,
                type: "button_click"
            };
            return o.markAsInteracted({
                metadata: c
            }), t ? t(m, {
                button: i,
                step: o,
                guide: r
            }) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$guide$2f$components$2f$helpers$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["maybeNavigateToUrlWithDelay"])(i.action);
        },
        onImageClick: (m, i)=>{
            const c = {
                ...i,
                type: "image_click"
            };
            if (o.markAsInteracted({
                metadata: c
            }), l) return l(m, {
                image: i,
                step: o,
                guide: r
            });
        }
    });
};
A.displayName = "Modal";
const R = {};
Object.assign(R, {
    Default: b,
    Root: u,
    Overlay: y,
    Content: _,
    Title: p,
    Body: E,
    Img: g,
    Actions: k,
    PrimaryButton: f,
    SecondaryButton: N,
    Close: w
});
;
 //# sourceMappingURL=Modal.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/utils.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "openPopupWindow": (()=>r)
});
const r = (e)=>{
    const n = window.screenLeft ?? window.screenX, t = window.screenTop ?? window.screenY, o = window.innerWidth ?? document.documentElement.clientWidth ?? screen.width, i = window.innerHeight ?? document.documentElement.clientHeight ?? screen.height, c = o / 2 - 600 / 2 + n, h = `width=600,height=800,top=${i / 2 - 800 / 2 + t},left=${c}`;
    window.open(e, "_blank", h);
};
;
 //# sourceMappingURL=utils.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsIcon/MsTeamsIcon.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MsTeamsIcon": (()=>l)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
const l = ({ height: t, width: c })=>// Source: https://commons.wikimedia.org/wiki/File:Microsoft_Office_Teams_(2018%E2%80%93present).svg
    /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        style: {
            height: t,
            width: c
        },
        viewBox: "0 0 2228.833 2073.333"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        fill: "#5059C9",
        d: "M1554.637,777.5h575.713c54.391,0,98.483,44.092,98.483,98.483c0,0,0,0,0,0v524.398 c0,199.901-162.051,361.952-361.952,361.952h0h-1.711c-199.901,0.028-361.975-162-362.004-361.901c0-0.017,0-0.034,0-0.052V828.971 C1503.167,800.544,1526.211,777.5,1554.637,777.5L1554.637,777.5z"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("circle", {
        fill: "#5059C9",
        cx: "1943.75",
        cy: "440.583",
        r: "233.25"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("circle", {
        fill: "#7B83EB",
        cx: "1218.083",
        cy: "336.917",
        r: "336.917"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        fill: "#7B83EB",
        d: "M1667.323,777.5H717.01c-53.743,1.33-96.257,45.931-95.01,99.676v598.105 c-7.505,322.519,247.657,590.16,570.167,598.053c322.51-7.893,577.671-275.534,570.167-598.053V877.176 C1763.579,823.431,1721.066,778.83,1667.323,777.5z"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        opacity: ".1",
        d: "M1244,777.5v838.145c-0.258,38.435-23.549,72.964-59.09,87.598 c-11.316,4.787-23.478,7.254-35.765,7.257H667.613c-6.738-17.105-12.958-34.21-18.142-51.833 c-18.144-59.477-27.402-121.307-27.472-183.49V877.02c-1.246-53.659,41.198-98.19,94.855-99.52H1244z"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        opacity: ".2",
        d: "M1192.167,777.5v889.978c-0.002,12.287-2.47,24.449-7.257,35.765 c-14.634,35.541-49.163,58.833-87.598,59.09H691.975c-8.812-17.105-17.105-34.21-24.362-51.833 c-7.257-17.623-12.958-34.21-18.142-51.833c-18.144-59.476-27.402-121.307-27.472-183.49V877.02 c-1.246-53.659,41.198-98.19,94.855-99.52H1192.167z"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        opacity: ".2",
        d: "M1192.167,777.5v786.312c-0.395,52.223-42.632,94.46-94.855,94.855h-447.84 c-18.144-59.476-27.402-121.307-27.472-183.49V877.02c-1.246-53.659,41.198-98.19,94.855-99.52H1192.167z"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        opacity: ".2",
        d: "M1140.333,777.5v786.312c-0.395,52.223-42.632,94.46-94.855,94.855H649.472 c-18.144-59.476-27.402-121.307-27.472-183.49V877.02c-1.246-53.659,41.198-98.19,94.855-99.52H1140.333z"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        opacity: ".1",
        d: "M1244,509.522v163.275c-8.812,0.518-17.105,1.037-25.917,1.037 c-8.812,0-17.105-0.518-25.917-1.037c-17.496-1.161-34.848-3.937-51.833-8.293c-104.963-24.857-191.679-98.469-233.25-198.003 c-7.153-16.715-12.706-34.071-16.587-51.833h258.648C1201.449,414.866,1243.801,457.217,1244,509.522z"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        opacity: ".2",
        d: "M1192.167,561.355v111.442c-17.496-1.161-34.848-3.937-51.833-8.293 c-104.963-24.857-191.679-98.469-233.25-198.003h190.228C1149.616,466.699,1191.968,509.051,1192.167,561.355z"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        opacity: ".2",
        d: "M1192.167,561.355v111.442c-17.496-1.161-34.848-3.937-51.833-8.293 c-104.963-24.857-191.679-98.469-233.25-198.003h190.228C1149.616,466.699,1191.968,509.051,1192.167,561.355z"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        opacity: ".2",
        d: "M1140.333,561.355v103.148c-104.963-24.857-191.679-98.469-233.25-198.003 h138.395C1097.783,466.699,1140.134,509.051,1140.333,561.355z"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("linearGradient", {
        id: "a",
        gradientUnits: "userSpaceOnUse",
        x1: "198.099",
        y1: "1683.0726",
        x2: "942.2344",
        y2: "394.2607",
        gradientTransform: "matrix(1 0 0 -1 0 2075.3333)"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("stop", {
        offset: "0",
        stopColor: "#5a62c3"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("stop", {
        offset: ".5",
        stopColor: "#4d55bd"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("stop", {
        offset: "1",
        stopColor: "#3940ab"
    })), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        fill: "url(#a)",
        d: "M95.01,466.5h950.312c52.473,0,95.01,42.538,95.01,95.01v950.312c0,52.473-42.538,95.01-95.01,95.01 H95.01c-52.473,0-95.01-42.538-95.01-95.01V561.51C0,509.038,42.538,466.5,95.01,466.5z"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        fill: "#FFF",
        d: "M820.211,828.193H630.241v517.297H509.211V828.193H320.123V727.844h500.088V828.193z"
    }));
;
 //# sourceMappingURL=MsTeamsIcon.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsAuthButton/MsTeamsAuthButton.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MsTeamsAuthButton": (()=>K)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/i18n/hooks/useTranslations.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$core$2f$context$2f$KnockProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/core/context/KnockProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$context$2f$KnockMsTeamsProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/ms-teams/context/KnockMsTeamsProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$hooks$2f$useMsTeamsAuth$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMsTeamsAuth$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/ms-teams/hooks/useMsTeamsAuth.mjs [app-client] (ecmascript) <export default as useMsTeamsAuth>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/utils.mjs [app-client] (ecmascript)");
/* empty css               */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsIcon$2f$MsTeamsIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsIcon/MsTeamsIcon.mjs [app-client] (ecmascript)");
;
;
;
;
/* empty css            */ const K = ({ msTeamsBotId: _, redirectUrl: b, onAuthenticationComplete: c })=>{
    const { t } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"])(), m = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$core$2f$context$2f$KnockProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockClient"])(), { setConnectionStatus: o, connectionStatus: n, setActionLabel: s, actionLabel: i, errorLabel: p } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$context$2f$KnockMsTeamsProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockMsTeamsClient"])(), { buildMsTeamsAuthUrl: l, disconnectFromMsTeams: E } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$hooks$2f$useMsTeamsAuth$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMsTeamsAuth$3e$__["useMsTeamsAuth"])(_, b);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const u = (r)=>{
            if (r.origin === m.host) try {
                r.data === "authComplete" && o("connected"), r.data === "authFailed" && o("error"), c == null || c(r.data);
            } catch  {
                o("error");
            }
        };
        return window.addEventListener("message", u, !1), ()=>{
            window.removeEventListener("message", u);
        };
    }, [
        m.host,
        c,
        o
    ]);
    const k = t("msTeamsDisconnect") || null, h = t("msTeamsReconnect") || null;
    return n === "connecting" || n === "disconnecting" ? /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rtk-connect__button rtk-connect__button--loading"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsIcon$2f$MsTeamsIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MsTeamsIcon"], {
        height: "16px",
        width: "16px"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", null, t(n === "connecting" ? "msTeamsConnecting" : "msTeamsDisconnecting"))) : n === "error" ? /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        onClick: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["openPopupWindow"])(l()),
        className: "rtk-connect__button rtk-connect__button--error",
        onMouseEnter: ()=>s(h),
        onMouseLeave: ()=>s(null)
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsIcon$2f$MsTeamsIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MsTeamsIcon"], {
        height: "16px",
        width: "16px"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", {
        className: "rtk-connect__button__text--error"
    }, i || p || t("msTeamsError"))) : n === "disconnected" ? /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        onClick: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["openPopupWindow"])(l()),
        className: "rtk-connect__button rtk-connect__button--disconnected"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsIcon$2f$MsTeamsIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MsTeamsIcon"], {
        height: "16px",
        width: "16px"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", null, t("msTeamsConnect"))) : /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        onClick: E,
        className: "rtk-connect__button rtk-connect__button--connected",
        onMouseEnter: ()=>s(k),
        onMouseLeave: ()=>s(null)
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsIcon$2f$MsTeamsIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MsTeamsIcon"], {
        height: "16px",
        width: "16px"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", {
        className: "rtk-connect__button__text--connected"
    }, i || t("msTeamsConnected")));
};
;
 //# sourceMappingURL=MsTeamsAuthButton.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsAuthContainer/MsTeamsAuthContainer.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MsTeamsAuthContainer": (()=>l)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/i18n/hooks/useTranslations.mjs [app-client] (ecmascript)");
/* empty css               */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsIcon$2f$MsTeamsIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsIcon/MsTeamsIcon.mjs [app-client] (ecmascript)");
;
;
;
/* empty css            */ const l = ({ actionButton: t })=>{
    const { t: a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"])();
    return /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rtk-auth"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rtk-auth__header"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsIcon$2f$MsTeamsIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MsTeamsIcon"], {
        height: "32px",
        width: "32px"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", null, t)), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rtk-auth__title"
    }, "Microsoft Teams"), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rtk-auth__description"
    }, a("msTeamsConnectContainerDescription")));
};
;
 //# sourceMappingURL=MsTeamsAuthContainer.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/utils.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "sortByDisplayName": (()=>s)
});
const s = (a)=>a.sort((e, o)=>e.displayName.toLowerCase().localeCompare(o.displayName.toLowerCase()));
;
 //# sourceMappingURL=utils.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsErrorMessage.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>n)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$icon$40$0$2e$0$2e$50_reac_8190e300309f1a2a8df538acb48e4c5d$2f$node_modules$2f40$telegraph$2f$icon$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+icon@0.0.50_reac_8190e300309f1a2a8df538acb48e4c5d/node_modules/@telegraph/icon/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$icon$40$0$2e$0$2e$50_reac_8190e300309f1a2a8df538acb48e4c5d$2f$node_modules$2f40$telegraph$2f$icon$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+icon@0.0.50_reac_8190e300309f1a2a8df538acb48e4c5d/node_modules/@telegraph/icon/dist/esm/index.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$436$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$lucide$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__Lucide$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.436.0_react@19.1.0/node_modules/lucide-react/dist/esm/lucide-react.js [app-client] (ecmascript) <export * as Lucide>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$typography$40$0$2e$1$2e$1_81bd34c438eb5bcf5d175e262063666f$2f$node_modules$2f40$telegraph$2f$typography$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+typography@0.1.1_81bd34c438eb5bcf5d175e262063666f/node_modules/@telegraph/typography/dist/esm/index.mjs [app-client] (ecmascript)");
;
;
;
const n = ({ message: r })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rtk-combobox__error"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", null, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$icon$40$0$2e$0$2e$50_reac_8190e300309f1a2a8df538acb48e4c5d$2f$node_modules$2f40$telegraph$2f$icon$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Icon"], {
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$436$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$lucide$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__Lucide$3e$__["Lucide"].Info,
        color: "black",
        size: "1",
        "aria-hidden": !0
    })), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$typography$40$0$2e$1$2e$1_81bd34c438eb5bcf5d175e262063666f$2f$node_modules$2f40$telegraph$2f$typography$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text"], {
        as: "div",
        color: "black",
        size: "1"
    }, r));
;
 //# sourceMappingURL=MsTeamsErrorMessage.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsChannelInTeamCombobox.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MsTeamsChannelInTeamCombobox": (()=>V)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$context$2f$KnockMsTeamsProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/ms-teams/context/KnockMsTeamsProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$hooks$2f$useMsTeamsChannels$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMsTeamsChannels$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/ms-teams/hooks/useMsTeamsChannels.mjs [app-client] (ecmascript) <export default as useMsTeamsChannels>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$hooks$2f$useConnectedMsTeamsChannels$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useConnectedMsTeamsChannels$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/ms-teams/hooks/useConnectedMsTeamsChannels.mjs [app-client] (ecmascript) <export default as useConnectedMsTeamsChannels>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+combobox@0.0.81__********************************/node_modules/@telegraph/combobox/dist/esm/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$layout$40$0$2e$1$2e$16_re_5659a1ede1b28748b983bae01168197c$2f$node_modules$2f40$telegraph$2f$layout$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+layout@0.1.16_re_5659a1ede1b28748b983bae01168197c/node_modules/@telegraph/layout/dist/esm/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/utils.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsChannelCombobox$2f$MsTeamsErrorMessage$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsErrorMessage.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
const V = ({ teamId: r, msTeamsChannelsRecipientObject: h, queryOptions: _ })=>{
    const { connectionStatus: s } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$context$2f$KnockMsTeamsProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockMsTeamsClient"])(), { data: o = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$hooks$2f$useMsTeamsChannels$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMsTeamsChannels$3e$__["useMsTeamsChannels"])({
        teamId: r,
        queryOptions: _
    }), p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sortByDisplayName"])(o), [
        o
    ]), { data: a, updateConnectedChannels: u, error: l } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$hooks$2f$useConnectedMsTeamsChannels$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useConnectedMsTeamsChannels$3e$__["useConnectedMsTeamsChannels"])({
        msTeamsChannelsRecipientObject: h
    }), C = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>s === "disconnected" || s === "error" || !!l, [
        s,
        l
    ]), E = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>s === "connecting" || s === "disconnecting", [
        s
    ]), i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])((e)=>o.some((d)=>d.id === e), [
        o
    ]), T = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>a == null ? void 0 : a.filter((e)=>e.ms_teams_channel_id && i(e.ms_teams_channel_id)).map((e)=>e.ms_teams_channel_id), [
        a,
        i
    ]);
    return /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Fragment, null, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$layout$40$0$2e$1$2e$16_re_5659a1ede1b28748b983bae01168197c$2f$node_modules$2f40$telegraph$2f$layout$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Box"], {
        w: "full",
        minW: "0"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Root, {
        value: T,
        onValueChange: (e)=>{
            const d = e.map((m)=>({
                    ms_teams_team_id: r,
                    ms_teams_channel_id: m
                })), f = [
                ...(a == null ? void 0 : a.filter((m)=>!m.ms_teams_channel_id || !i(m.ms_teams_channel_id))) ?? [],
                ...d
            ];
            u(f).catch(console.error);
        },
        placeholder: "Select channels",
        disabled: r === void 0 || C || E || o.length === 0,
        closeOnSelect: !1,
        layout: "wrap",
        modal: // Modal comboboxes cause page layout to shift when body has padding. See KNO-7854.
        !1
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Trigger, null), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Content, null, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Search, {
        className: "rtk-combobox__search"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Options, {
        maxHeight: "36"
    }, p.map((e)=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Option, {
            key: e.id,
            value: e.id
        }, e.displayName))), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Empty, null)))), !!l && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsChannelCombobox$2f$MsTeamsErrorMessage$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        message: l
    }));
};
;
 //# sourceMappingURL=MsTeamsChannelInTeamCombobox.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsConnectionError.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>i)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/i18n/hooks/useTranslations.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$context$2f$KnockMsTeamsProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/ms-teams/context/KnockMsTeamsProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsChannelCombobox$2f$MsTeamsErrorMessage$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsErrorMessage.mjs [app-client] (ecmascript)");
;
;
;
const i = ()=>{
    const { t: r } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"])(), { connectionStatus: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$context$2f$KnockMsTeamsProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockMsTeamsClient"])();
    return e === "disconnected" || e === "error" ? /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsChannelCombobox$2f$MsTeamsErrorMessage$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        message: r(e === "disconnected" ? "msTeamsConnectionErrorOccurred" : "msTeamsConnectionErrorExists")
    }) : null;
};
;
 //# sourceMappingURL=MsTeamsConnectionError.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsTeamCombobox.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MsTeamsTeamCombobox": (()=>N)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$context$2f$KnockMsTeamsProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/ms-teams/context/KnockMsTeamsProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$hooks$2f$useMsTeamsTeams$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMsTeamsTeams$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/ms-teams/hooks/useMsTeamsTeams.mjs [app-client] (ecmascript) <export default as useMsTeamsTeams>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+combobox@0.0.81__********************************/node_modules/@telegraph/combobox/dist/esm/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$layout$40$0$2e$1$2e$16_re_5659a1ede1b28748b983bae01168197c$2f$node_modules$2f40$telegraph$2f$layout$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+layout@0.1.16_re_5659a1ede1b28748b983bae01168197c/node_modules/@telegraph/layout/dist/esm/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/utils.mjs [app-client] (ecmascript)");
;
;
;
;
;
const N = ({ team: s, onTeamChange: m, getChannelCount: d, queryOptions: u })=>{
    const { connectionStatus: n } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$context$2f$KnockMsTeamsProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockMsTeamsClient"])(), { data: i, isLoading: l } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$hooks$2f$useMsTeamsTeams$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMsTeamsTeams$3e$__["useMsTeamsTeams"])({
        queryOptions: u
    }), r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sortByDisplayName"])(i), [
        i
    ]), p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>n === "disconnected" || n === "error", [
        n
    ]), f = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>n === "connecting" || n === "disconnecting" || l, [
        n,
        l
    ]);
    return /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$layout$40$0$2e$1$2e$16_re_5659a1ede1b28748b983bae01168197c$2f$node_modules$2f40$telegraph$2f$layout$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Box"], {
        w: "full",
        minW: "0"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Root, {
        value: s == null ? void 0 : s.id,
        onValueChange: (t)=>{
            const a = r.find((g)=>g.id === t);
            a && m(a);
        },
        placeholder: "Select team",
        disabled: p || f || r.length === 0,
        modal: // Modal comboboxes cause page layout to shift when body has padding. See KNO-7854.
        !1
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Trigger, null), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Content, null, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Search, {
        className: "rtk-combobox__search"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Options, {
        maxHeight: "36"
    }, r.map((t)=>{
        const a = d(t.id);
        return /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Option, {
            key: t.id,
            value: t.id
        }, a > 0 ? `${t.displayName} (${a})` : t.displayName);
    })), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Empty, null))));
};
;
 //# sourceMappingURL=MsTeamsTeamCombobox.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsChannelCombobox.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>v)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$hooks$2f$useConnectedMsTeamsChannels$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useConnectedMsTeamsChannels$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/ms-teams/hooks/useConnectedMsTeamsChannels.mjs [app-client] (ecmascript) <export default as useConnectedMsTeamsChannels>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$icon$40$0$2e$0$2e$50_reac_8190e300309f1a2a8df538acb48e4c5d$2f$node_modules$2f40$telegraph$2f$icon$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+icon@0.0.50_reac_8190e300309f1a2a8df538acb48e4c5d/node_modules/@telegraph/icon/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$icon$40$0$2e$0$2e$50_reac_8190e300309f1a2a8df538acb48e4c5d$2f$node_modules$2f40$telegraph$2f$icon$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+icon@0.0.50_reac_8190e300309f1a2a8df538acb48e4c5d/node_modules/@telegraph/icon/dist/esm/index.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$436$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$lucide$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__Lucide$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.436.0_react@19.1.0/node_modules/lucide-react/dist/esm/lucide-react.js [app-client] (ecmascript) <export * as Lucide>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$layout$40$0$2e$1$2e$16_re_5659a1ede1b28748b983bae01168197c$2f$node_modules$2f40$telegraph$2f$layout$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+layout@0.1.16_re_5659a1ede1b28748b983bae01168197c/node_modules/@telegraph/layout/dist/esm/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$typography$40$0$2e$1$2e$1_81bd34c438eb5bcf5d175e262063666f$2f$node_modules$2f40$telegraph$2f$typography$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+typography@0.1.1_81bd34c438eb5bcf5d175e262063666f/node_modules/@telegraph/typography/dist/esm/index.mjs [app-client] (ecmascript)");
/* empty css               */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsChannelCombobox$2f$MsTeamsChannelInTeamCombobox$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsChannelInTeamCombobox.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsChannelCombobox$2f$MsTeamsConnectionError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsConnectionError.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsChannelCombobox$2f$MsTeamsTeamCombobox$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsTeamCombobox.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
/* empty css            */ const v = ({ msTeamsChannelsRecipientObject: o, teamQueryOptions: s, channelQueryOptions: l })=>{
    const [t, i] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null), { data: a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$hooks$2f$useConnectedMsTeamsChannels$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useConnectedMsTeamsChannels$3e$__["useConnectedMsTeamsChannels"])({
        msTeamsChannelsRecipientObject: o
    }), c = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])((p)=>(a == null ? void 0 : a.filter((m)=>m.ms_teams_team_id === p && !!m.ms_teams_channel_id).length) ?? 0, [
        a
    ]);
    return /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$layout$40$0$2e$1$2e$16_re_5659a1ede1b28748b983bae01168197c$2f$node_modules$2f40$telegraph$2f$layout$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Stack"], {
        className: "tgph rtk-combobox__grid",
        gap: "3"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$typography$40$0$2e$1$2e$1_81bd34c438eb5bcf5d175e262063666f$2f$node_modules$2f40$telegraph$2f$typography$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text"], {
        color: "gray",
        size: "2",
        as: "div"
    }, "Team"), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsChannelCombobox$2f$MsTeamsTeamCombobox$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MsTeamsTeamCombobox"], {
        team: t,
        onTeamChange: i,
        getChannelCount: c,
        queryOptions: s
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$layout$40$0$2e$1$2e$16_re_5659a1ede1b28748b983bae01168197c$2f$node_modules$2f40$telegraph$2f$layout$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Stack"], {
        alignItems: "center",
        gap: "3",
        minHeight: "8",
        style: {
            alignSelf: "start"
        }
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$icon$40$0$2e$0$2e$50_reac_8190e300309f1a2a8df538acb48e4c5d$2f$node_modules$2f40$telegraph$2f$icon$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Icon"], {
        color: "gray",
        size: "1",
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$436$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$lucide$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__Lucide$3e$__["Lucide"].CornerDownRight,
        "aria-hidden": !0
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$typography$40$0$2e$1$2e$1_81bd34c438eb5bcf5d175e262063666f$2f$node_modules$2f40$telegraph$2f$typography$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text"], {
        color: "gray",
        size: "2",
        as: "div"
    }, "Channels")), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsChannelCombobox$2f$MsTeamsChannelInTeamCombobox$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MsTeamsChannelInTeamCombobox"], {
        teamId: t == null ? void 0 : t.id,
        msTeamsChannelsRecipientObject: o,
        queryOptions: l
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsChannelCombobox$2f$MsTeamsConnectionError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null));
};
;
 //# sourceMappingURL=MsTeamsChannelCombobox.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackIcon/SlackIcon.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SlackIcon": (()=>s)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
const s = ({ height: t, width: c })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        style: {
            height: t,
            width: c
        },
        viewBox: "0 0 122.8 122.8"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M25.8 77.6c0 7.1-5.8 12.9-12.9 12.9S0 84.7 0 77.6s5.8-12.9 12.9-12.9h12.9v12.9zm6.5 0c0-7.1 5.8-12.9 12.9-12.9s12.9 5.8 12.9 12.9v32.3c0 7.1-5.8 12.9-12.9 12.9s-12.9-5.8-12.9-12.9V77.6z",
        fill: "#e01e5a"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M45.2 25.8c-7.1 0-12.9-5.8-12.9-12.9S38.1 0 45.2 0s12.9 5.8 12.9 12.9v12.9H45.2zm0 6.5c7.1 0 12.9 5.8 12.9 12.9s-5.8 12.9-12.9 12.9H12.9C5.8 58.1 0 52.3 0 45.2s5.8-12.9 12.9-12.9h32.3z",
        fill: "#36c5f0"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M97 45.2c0-7.1 5.8-12.9 12.9-12.9s12.9 5.8 12.9 12.9-5.8 12.9-12.9 12.9H97V45.2zm-6.5 0c0 7.1-5.8 12.9-12.9 12.9s-12.9-5.8-12.9-12.9V12.9C64.7 5.8 70.5 0 77.6 0s12.9 5.8 12.9 12.9v32.3z",
        fill: "#2eb67d"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: "M77.6 97c7.1 0 12.9 5.8 12.9 12.9s-5.8 12.9-12.9 12.9-12.9-5.8-12.9-12.9V97h12.9zm0-6.5c-7.1 0-12.9-5.8-12.9-12.9s5.8-12.9 12.9-12.9h32.3c7.1 0 12.9 5.8 12.9 12.9s-5.8 12.9-12.9 12.9H77.6z",
        fill: "#ecb22e"
    }));
;
 //# sourceMappingURL=SlackIcon.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackAuthButton/SlackAuthButton.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SlackAuthButton": (()=>B)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/i18n/hooks/useTranslations.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$core$2f$context$2f$KnockProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/core/context/KnockProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$slack$2f$context$2f$KnockSlackProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/slack/context/KnockSlackProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$slack$2f$hooks$2f$useSlackAuth$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useSlackAuth$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/slack/hooks/useSlackAuth.mjs [app-client] (ecmascript) <export default as useSlackAuth>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/utils.mjs [app-client] (ecmascript)");
/* empty css               */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackIcon$2f$SlackIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackIcon/SlackIcon.mjs [app-client] (ecmascript)");
;
;
;
;
/* empty css            */ const B = ({ slackClientId: p, redirectUrl: b, onAuthenticationComplete: a, scopes: l, additionalScopes: i })=>{
    const { t } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"])(), u = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$core$2f$context$2f$KnockProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockClient"])(), { setConnectionStatus: c, connectionStatus: n, setActionLabel: o, actionLabel: m, errorLabel: h } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$slack$2f$context$2f$KnockSlackProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockSlackClient"])(), E = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            scopes: l,
            additionalScopes: i
        }), [
        l,
        i
    ]), { buildSlackAuthUrl: k, disconnectFromSlack: f } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$slack$2f$hooks$2f$useSlackAuth$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useSlackAuth$3e$__["useSlackAuth"])(p, b, E);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const _ = (r)=>{
            if (r.origin === u.host) try {
                r.data === "authComplete" && c("connected"), r.data === "authFailed" && c("error"), a && a(r.data);
            } catch  {
                c("error");
            }
        };
        return window.addEventListener("message", _, !1), ()=>{
            window.removeEventListener("message", _);
        };
    }, [
        u.host,
        a,
        c
    ]);
    const g = t("slackDisconnect") || null, x = t("slackReconnect") || null;
    return n === "connecting" || n === "disconnecting" ? /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rsk-connect__button rsk-connect__button--loading"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackIcon$2f$SlackIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SlackIcon"], {
        height: "16px",
        width: "16px"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", null, t(n === "connecting" ? "slackConnecting" : "slackDisconnecting"))) : n === "error" ? /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        onClick: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["openPopupWindow"])(k()),
        className: "rsk-connect__button rsk-connect__button--error",
        onMouseEnter: ()=>o(x),
        onMouseLeave: ()=>o(null)
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackIcon$2f$SlackIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SlackIcon"], {
        height: "16px",
        width: "16px"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", {
        className: "rsk-connect__button__text--error"
    }, m || h || t("slackError"))) : n === "disconnected" ? /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        onClick: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["openPopupWindow"])(k()),
        className: "rsk-connect__button rsk-connect__button--disconnected"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackIcon$2f$SlackIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SlackIcon"], {
        height: "16px",
        width: "16px"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", null, t("slackConnect"))) : /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        onClick: f,
        className: "rsk-connect__button rsk-connect__button--connected",
        onMouseEnter: ()=>o(g),
        onMouseLeave: ()=>o(null)
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackIcon$2f$SlackIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SlackIcon"], {
        height: "16px",
        width: "16px"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", {
        className: "rsk-connect__button__text--connected"
    }, m || t("slackConnected")));
};
;
 //# sourceMappingURL=SlackAuthButton.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackAuthContainer/SlackAuthContainer.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SlackAuthContainer": (()=>o)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/i18n/hooks/useTranslations.mjs [app-client] (ecmascript)");
/* empty css               */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackIcon$2f$SlackIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackIcon/SlackIcon.mjs [app-client] (ecmascript)");
;
;
;
/* empty css            */ const o = ({ actionButton: t })=>{
    const { t: a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"])();
    return /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rsk-auth"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rsk-auth__header"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackIcon$2f$SlackIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SlackIcon"], {
        height: "32px",
        width: "32px"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", null, t)), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rsk-auth__title"
    }, "Slack"), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rsk-auth__description"
    }, a("slackConnectContainerDescription")));
};
;
 //# sourceMappingURL=SlackAuthContainer.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/utils.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "sortSlackChannelsAlphabetically": (()=>l)
});
const l = (e)=>[
        ...e
    ].sort((a, o)=>a.name.toLowerCase().localeCompare(o.name.toLowerCase()));
;
 //# sourceMappingURL=utils.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackChannelCombobox/SlackErrorMessage.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>n)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$icon$40$0$2e$0$2e$50_reac_8190e300309f1a2a8df538acb48e4c5d$2f$node_modules$2f40$telegraph$2f$icon$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+icon@0.0.50_reac_8190e300309f1a2a8df538acb48e4c5d/node_modules/@telegraph/icon/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$icon$40$0$2e$0$2e$50_reac_8190e300309f1a2a8df538acb48e4c5d$2f$node_modules$2f40$telegraph$2f$icon$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+icon@0.0.50_reac_8190e300309f1a2a8df538acb48e4c5d/node_modules/@telegraph/icon/dist/esm/index.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$436$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$lucide$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__Lucide$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.436.0_react@19.1.0/node_modules/lucide-react/dist/esm/lucide-react.js [app-client] (ecmascript) <export * as Lucide>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$typography$40$0$2e$1$2e$1_81bd34c438eb5bcf5d175e262063666f$2f$node_modules$2f40$telegraph$2f$typography$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+typography@0.1.1_81bd34c438eb5bcf5d175e262063666f/node_modules/@telegraph/typography/dist/esm/index.mjs [app-client] (ecmascript)");
;
;
;
const n = ({ message: r })=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rsk-combobox__error"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", null, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$icon$40$0$2e$0$2e$50_reac_8190e300309f1a2a8df538acb48e4c5d$2f$node_modules$2f40$telegraph$2f$icon$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Icon"], {
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$436$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$lucide$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__Lucide$3e$__["Lucide"].Info,
        color: "black",
        size: "1",
        "aria-hidden": !0
    })), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$typography$40$0$2e$1$2e$1_81bd34c438eb5bcf5d175e262063666f$2f$node_modules$2f40$telegraph$2f$typography$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text"], {
        as: "div",
        color: "black",
        size: "1"
    }, r));
;
 //# sourceMappingURL=SlackErrorMessage.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackChannelCombobox/SlackConnectionError.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>l)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/i18n/hooks/useTranslations.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$slack$2f$context$2f$KnockSlackProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/slack/context/KnockSlackProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackChannelCombobox$2f$SlackErrorMessage$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackChannelCombobox/SlackErrorMessage.mjs [app-client] (ecmascript)");
;
;
;
const l = ()=>{
    const { t: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"])(), { connectionStatus: r } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$slack$2f$context$2f$KnockSlackProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockSlackClient"])();
    return r === "disconnected" || r === "error" ? /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackChannelCombobox$2f$SlackErrorMessage$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        message: e(r === "disconnected" ? "slackConnectionErrorOccurred" : "slackConnectionErrorExists")
    }) : null;
};
;
 //# sourceMappingURL=SlackConnectionError.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackAddChannelInput/SlackAddChannelInput.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>A)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/i18n/hooks/useTranslations.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Spinner$2f$Spinner$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Spinner/Spinner.mjs [app-client] (ecmascript)");
/* empty css                                          */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lodash$2e$debounce$40$4$2e$0$2e$8$2f$node_modules$2f$lodash$2e$debounce$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js [app-client] (ecmascript)");
/* empty css               */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackChannelCombobox$2f$SlackConnectionError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackChannelCombobox/SlackConnectionError.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackIcon$2f$SlackIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackIcon/SlackIcon.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
/* empty css            */ const A = ({ inErrorState: i, connectedChannels: c = [], updateConnectedChannels: m, connectedChannelsError: u, connectedChannelsUpdating: p })=>{
    const { t } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"])(), [n, a] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null), [l, r] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null), d = ()=>{
        if (!n) return;
        if (l && r(null), c.find((k)=>k.channel_id === n)) return a(""), r(t("slackChannelAlreadyConnected") || "");
        const h = [
            ...c,
            {
                channel_id: n
            }
        ];
        m(h), a("");
    };
    return /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "rsk-connect-channel"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("input", {
        className: `rsk-connect-channel__input ${(i || !!l) && !n && "rsk-connect-channel__input--error"}`,
        tabIndex: -1,
        id: "slack-channel-search",
        type: "text",
        placeholder: l || u || t("slackChannelId"),
        onChange: (o)=>a(o.target.value),
        value: n || ""
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("button", {
        className: "rsk-connect-channel__button",
        onClick: d
    }, p ? /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Spinner$2f$Spinner$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Spinner"], {
        size: "15px",
        thickness: 3
    }) : /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackIcon$2f$SlackIcon$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SlackIcon"], {
        height: "16px",
        width: "16px"
    }), t("slackConnectChannel")), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackChannelCombobox$2f$SlackConnectionError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null));
};
;
 //# sourceMappingURL=SlackAddChannelInput.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackChannelCombobox/SlackChannelCombobox.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SlackChannelCombobox": (()=>Q)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/i18n/hooks/useTranslations.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$slack$2f$context$2f$KnockSlackProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/slack/context/KnockSlackProvider.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$slack$2f$hooks$2f$useSlackChannels$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useSlackChannels$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/slack/hooks/useSlackChannels.mjs [app-client] (ecmascript) <export default as useSlackChannels>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$slack$2f$hooks$2f$useConnectedSlackChannels$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useConnectedSlackChannels$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/modules/slack/hooks/useConnectedSlackChannels.mjs [app-client] (ecmascript) <export default as useConnectedSlackChannels>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+combobox@0.0.81__********************************/node_modules/@telegraph/combobox/dist/esm/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$icon$40$0$2e$0$2e$50_reac_8190e300309f1a2a8df538acb48e4c5d$2f$node_modules$2f40$telegraph$2f$icon$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+icon@0.0.50_reac_8190e300309f1a2a8df538acb48e4c5d/node_modules/@telegraph/icon/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$icon$40$0$2e$0$2e$50_reac_8190e300309f1a2a8df538acb48e4c5d$2f$node_modules$2f40$telegraph$2f$icon$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+icon@0.0.50_reac_8190e300309f1a2a8df538acb48e4c5d/node_modules/@telegraph/icon/dist/esm/index.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$436$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$lucide$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__Lucide$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.436.0_react@19.1.0/node_modules/lucide-react/dist/esm/lucide-react.js [app-client] (ecmascript) <export * as Lucide>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$layout$40$0$2e$1$2e$16_re_5659a1ede1b28748b983bae01168197c$2f$node_modules$2f40$telegraph$2f$layout$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+layout@0.1.16_re_5659a1ede1b28748b983bae01168197c/node_modules/@telegraph/layout/dist/esm/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$typography$40$0$2e$1$2e$1_81bd34c438eb5bcf5d175e262063666f$2f$node_modules$2f40$telegraph$2f$typography$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@telegraph+typography@0.1.1_81bd34c438eb5bcf5d175e262063666f/node_modules/@telegraph/typography/dist/esm/index.mjs [app-client] (ecmascript)");
/* empty css               */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/utils.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackAddChannelInput$2f$SlackAddChannelInput$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackAddChannelInput/SlackAddChannelInput.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackChannelCombobox$2f$SlackConnectionError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackChannelCombobox/SlackConnectionError.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackChannelCombobox$2f$SlackErrorMessage$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackChannelCombobox/SlackErrorMessage.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
/* empty css            */ const w = 1e3, Q = ({ slackChannelsRecipientObject: _, queryOptions: p, inputMessages: o })=>{
    const { t: m } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$i18n$2f$hooks$2f$useTranslations$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslations"])(), { connectionStatus: r, errorLabel: s } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$slack$2f$context$2f$KnockSlackProvider$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKnockSlackClient"])(), { data: k, isLoading: E } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$slack$2f$hooks$2f$useSlackChannels$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useSlackChannels$3e$__["useSlackChannels"])({
        queryOptions: p
    }), t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$utils$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sortSlackChannelsAlphabetically"])(k), [
        k
    ]), { data: i, updateConnectedChannels: S, error: h, updating: L } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$modules$2f$slack$2f$hooks$2f$useConnectedSlackChannels$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useConnectedSlackChannels$3e$__["useConnectedSlackChannels"])({
        slackChannelsRecipientObject: _
    }), a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        const e = new Map(t.map((c)=>[
                c.id,
                c
            ]));
        return (i == null ? void 0 : i.filter((c)=>e.has(c.channel_id || ""))) || [];
    }, [
        i,
        t
    ]), C = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>r === "disconnected" || r === "error" || !!h, [
        h,
        r
    ]), b = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>r === "connecting" || r === "disconnecting" || E, [
        r,
        E
    ]), x = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        const e = {
            disconnected: m("slackSearchbarDisconnected"),
            noChannelsConnected: m("slackSearchbarNoChannelsConnected"),
            noSlackChannelsFound: m("slackSearchbarNoChannelsFound"),
            channelsError: m("slackSearchbarChannelsError")
        };
        if (r === "disconnected") return (o == null ? void 0 : o.disconnected) || e.disconnected;
        if (r === "error") return (o == null ? void 0 : o.error) || s;
        if (!b && t.length === 0) return (o == null ? void 0 : o.noSlackChannelsFound) || e.noSlackChannelsFound;
        const c = (a == null ? void 0 : a.length) || 0;
        return a && c === 0 ? (o == null ? void 0 : o.noChannelsConnected) || e.noChannelsConnected : "";
    }, [
        r,
        b,
        t,
        a,
        o,
        s,
        m
    ]), N = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>a.map((e)=>e.channel_id), [
        a
    ]);
    return t.length > w ? /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackAddChannelInput$2f$SlackAddChannelInput$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        inErrorState: C,
        connectedChannels: a || [],
        updateConnectedChannels: S,
        connectedChannelsError: h,
        connectedChannelsUpdating: L
    }) : /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$layout$40$0$2e$1$2e$16_re_5659a1ede1b28748b983bae01168197c$2f$node_modules$2f40$telegraph$2f$layout$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Stack"], {
        className: "tgph rsk-combobox__grid",
        gap: "3"
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$typography$40$0$2e$1$2e$1_81bd34c438eb5bcf5d175e262063666f$2f$node_modules$2f40$telegraph$2f$typography$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text"], {
        color: "gray",
        size: "2",
        as: "div",
        minHeight: "8",
        className: "rsk-combobox__label"
    }, "Channels"), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Root, {
        value: N,
        onValueChange: (e)=>{
            const c = e.map((A)=>({
                    channel_id: A
                }));
            S(c).catch(console.error);
        },
        placeholder: x ?? "",
        disabled: C || t.length === 0,
        errored: C,
        closeOnSelect: !1,
        layout: "wrap",
        modal: // Modal comboboxes cause page layout to shift when body has padding. See KNO-7854.
        !1
    }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Trigger, null), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Content, null, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Search, {
        label: m("slackSearchChannels"),
        className: "rsk-combobox__search"
    }), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Options, {
        maxHeight: "36"
    }, t.map((e)=>/* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Option, {
            key: e.id,
            value: e.id
        }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$layout$40$0$2e$1$2e$16_re_5659a1ede1b28748b983bae01168197c$2f$node_modules$2f40$telegraph$2f$layout$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Stack"], {
            align: "center",
            gap: "1"
        }, /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$icon$40$0$2e$0$2e$50_reac_8190e300309f1a2a8df538acb48e4c5d$2f$node_modules$2f40$telegraph$2f$icon$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Icon"], {
            icon: e.is_private ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$436$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$lucide$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__Lucide$3e$__["Lucide"].Lock : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$436$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$lucide$2d$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__Lucide$3e$__["Lucide"].Hash,
            size: "0",
            "aria-hidden": !0
        }), e.name)))), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$telegraph$2b$combobox$40$0$2e$0$2e$81_$5f$********************************$2f$node_modules$2f40$telegraph$2f$combobox$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Combobox"].Empty, null))), /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackChannelCombobox$2f$SlackConnectionError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null), !!h && /* @__PURE__ */ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackChannelCombobox$2f$SlackErrorMessage$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        message: h
    }));
};
;
 //# sourceMappingURL=SlackChannelCombobox.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/index.mjs [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* empty css           */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Button$2f$Button$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Button/Button.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Button$2f$ButtonGroup$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Button/ButtonGroup.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Icons$2f$Bell$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Icons/Bell.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Icons$2f$CheckmarkCircle$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Icons/CheckmarkCircle.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Icons$2f$ChevronDown$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Icons/ChevronDown.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Icons$2f$CloseCircle$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Icons/CloseCircle.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Spinner$2f$Spinner$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Spinner/Spinner.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$hooks$2f$useOnBottomScroll$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/hooks/useOnBottomScroll.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$EmptyFeed$2f$EmptyFeed$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/EmptyFeed/EmptyFeed.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationCell$2f$NotificationCell$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationCell/NotificationCell.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationCell$2f$Avatar$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationCell/Avatar.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeed$2f$NotificationFeed$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeed/NotificationFeed.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeed$2f$NotificationFeedHeader$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeed/NotificationFeedHeader.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeed$2f$MarkAsRead$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeed/MarkAsRead.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeedContainer$2f$NotificationFeedContainer$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeedContainer/NotificationFeedContainer.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeedPopover$2f$NotificationFeedPopover$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeedPopover/NotificationFeedPopover.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationIconButton$2f$NotificationIconButton$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationIconButton/NotificationIconButton.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$UnseenBadge$2f$UnseenBadge$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/UnseenBadge/UnseenBadge.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$guide$2f$components$2f$Banner$2f$Banner$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/guide/components/Banner/Banner.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$guide$2f$components$2f$Card$2f$Card$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/guide/components/Card/Card.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$guide$2f$components$2f$Modal$2f$Modal$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/guide/components/Modal/Modal.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsAuthButton$2f$MsTeamsAuthButton$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsAuthButton/MsTeamsAuthButton.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsAuthContainer$2f$MsTeamsAuthContainer$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsAuthContainer/MsTeamsAuthContainer.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsChannelCombobox$2f$MsTeamsChannelCombobox$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsChannelCombobox.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackAuthButton$2f$SlackAuthButton$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackAuthButton/SlackAuthButton.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackAuthContainer$2f$SlackAuthContainer$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackAuthContainer/SlackAuthContainer.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackChannelCombobox$2f$SlackChannelCombobox$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackChannelCombobox/SlackChannelCombobox.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
 //# sourceMappingURL=index.mjs.map
}}),
"[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Button$2f$Button$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Button/Button.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Button$2f$ButtonGroup$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Button/ButtonGroup.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Icons$2f$Bell$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Icons/Bell.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Icons$2f$CheckmarkCircle$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Icons/CheckmarkCircle.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Icons$2f$ChevronDown$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Icons/ChevronDown.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Icons$2f$CloseCircle$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Icons/CloseCircle.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$components$2f$Spinner$2f$Spinner$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/components/Spinner/Spinner.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$core$2f$hooks$2f$useOnBottomScroll$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/core/hooks/useOnBottomScroll.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$EmptyFeed$2f$EmptyFeed$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/EmptyFeed/EmptyFeed.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationCell$2f$NotificationCell$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationCell/NotificationCell.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationCell$2f$Avatar$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationCell/Avatar.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeed$2f$NotificationFeed$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeed/NotificationFeed.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeed$2f$NotificationFeedHeader$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeed/NotificationFeedHeader.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeed$2f$MarkAsRead$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeed/MarkAsRead.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeedContainer$2f$NotificationFeedContainer$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeedContainer/NotificationFeedContainer.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationFeedPopover$2f$NotificationFeedPopover$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationFeedPopover/NotificationFeedPopover.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$NotificationIconButton$2f$NotificationIconButton$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/NotificationIconButton/NotificationIconButton.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$feed$2f$components$2f$UnseenBadge$2f$UnseenBadge$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/feed/components/UnseenBadge/UnseenBadge.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$guide$2f$components$2f$Banner$2f$Banner$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/guide/components/Banner/Banner.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$guide$2f$components$2f$Card$2f$Card$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/guide/components/Card/Card.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$guide$2f$components$2f$Modal$2f$Modal$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/guide/components/Modal/Modal.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsAuthButton$2f$MsTeamsAuthButton$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsAuthButton/MsTeamsAuthButton.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsAuthContainer$2f$MsTeamsAuthContainer$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsAuthContainer/MsTeamsAuthContainer.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$ms$2d$teams$2f$components$2f$MsTeamsChannelCombobox$2f$MsTeamsChannelCombobox$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsChannelCombobox.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackAuthButton$2f$SlackAuthButton$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackAuthButton/SlackAuthButton.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackAuthContainer$2f$SlackAuthContainer$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackAuthContainer/SlackAuthContainer.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$modules$2f$slack$2f$components$2f$SlackChannelCombobox$2f$SlackChannelCombobox$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/modules/slack/components/SlackChannelCombobox/SlackChannelCombobox.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$2d$core$40$0$2e$6$2e$1_1cd36181420d457e15c6cbc284fa3aa3$2f$node_modules$2f40$knocklabs$2f$react$2d$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react-core@0.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/@knocklabs/react-core/dist/esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$knocklabs$2b$react$40$0$2e$7$2e$11_$40$ty_6fc524c218170b7480adccae4612663b$2f$node_modules$2f40$knocklabs$2f$react$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/esm/index.mjs [app-client] (ecmascript) <locals>");
}}),
}]);

//# sourceMappingURL=8ec23_%40knocklabs_react_dist_esm_a583128c._.js.map