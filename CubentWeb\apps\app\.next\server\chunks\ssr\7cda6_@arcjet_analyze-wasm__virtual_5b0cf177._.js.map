{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40arcjet%2Banalyze-wasm%401.0.0-beta.7/node_modules/%40arcjet/analyze-wasm/_virtual/arcjet_analyze_js_req.component.core2.js"], "sourcesContent": ["// @generated by wasm2module - DO NOT EDIT\n/* eslint-disable */\n// @ts-nocheck\n\n/**\n * This file contains an Arcjet Wasm binary inlined as a base64\n * [Data URL](https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/Data_URLs)\n * with the application/wasm MIME type.\n *\n * This was chosen to save on storage space over inlining the file directly as\n * a Uint8Array, which would take up ~3x the space of the Wasm file. See\n * https://blobfolio.com/2019/better-binary-batter-mixing-base64-and-uint8array/\n * for more details.\n *\n * It is then decoded into an ArrayBuffer to be used directly via WebAssembly's\n * `compile()` function in our entry point file.\n *\n * This is all done to avoid trying to read or bundle the Wasm asset in various\n * ways based on the platform or bundler a user is targeting. One example being\n * that Next.js requires special `asyncWebAssembly` webpack config to load our\n * Wasm file if we don't do this.\n *\n * In the future, we hope to do away with this workaround when all bundlers\n * properly support consistent asset bundling techniques.\n */\n\nconst wasmBase64 = \"data:application/wasm;base64,AGFzbQEAAAABFQNgA39/fwBgBH9/f38Bf2ACf38BfwMIBwABAgICAgAEBQFwAQcHBygIATAAAAExAAEBMgACATMAAwE0AAQBNQAFATYABggkaW1wb3J0cwEACl0HDQAgACABIAJBABEAAAsPACAAIAEgAiADQQERAQALCwAgACABQQIRAgALCwAgACABQQMRAgALCwAgACABQQQRAgALCwAgACABQQURAgALDQAgACABIAJBBhEAAAsALwlwcm9kdWNlcnMBDHByb2Nlc3NlZC1ieQENd2l0LWNvbXBvbmVudAcwLjIyNi4wALwDBG5hbWUAExJ3aXQtY29tcG9uZW50OnNoaW0BnwMHACxpbmRpcmVjdC1hcmNqZXQ6anMtcmVxL2JvdC1pZGVudGlmaWVyLWRldGVjdAEoaW5kaXJlY3QtYXJjamV0OmpzLXJlcS92ZXJpZnktYm90LXZlcmlmeQI+aW5kaXJlY3QtYXJjamV0OmpzLXJlcS9lbWFpbC12YWxpZGF0b3Itb3ZlcnJpZGVzLWlzLWZyZWUtZW1haWwDRGluZGlyZWN0LWFyY2pldDpqcy1yZXEvZW1haWwtdmFsaWRhdG9yLW92ZXJyaWRlcy1pcy1kaXNwb3NhYmxlLWVtYWlsBD9pbmRpcmVjdC1hcmNqZXQ6anMtcmVxL2VtYWlsLXZhbGlkYXRvci1vdmVycmlkZXMtaGFzLW14LXJlY29yZHMFPWluZGlyZWN0LWFyY2pldDpqcy1yZXEvZW1haWwtdmFsaWRhdG9yLW92ZXJyaWRlcy1oYXMtZ3JhdmF0YXIGPmluZGlyZWN0LWFyY2pldDpqcy1yZXEvc2Vuc2l0aXZlLWluZm9ybWF0aW9uLWlkZW50aWZpZXItZGV0ZWN0\";\n/**\n * Returns a WebAssembly.Module for an Arcjet Wasm binary, decoded from a base64\n * Data URL.\n */\n// TODO: Switch back to top-level await when our platforms all support it\nasync function wasm() {\n  // This uses fetch to decode the wasm data url, but disabling cache so files\n  // larger than 2mb don't fail to parse in the Next.js App Router\n  const wasmDecode = await fetch(wasmBase64, { cache: \"no-store\" });\n  const buf = await wasmDecode.arrayBuffer();\n  // And then we return it as a WebAssembly.Module\n  return WebAssembly.compile(buf);\n}\n\nexport { wasm };\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,kBAAkB,GAClB,cAAc;AAEd;;;;;;;;;;;;;;;;;;;;CAoBC;;;AAED,MAAM,aAAa;AACnB;;;CAGC,GACD,yEAAyE;AACzE,eAAe;IACb,4EAA4E;IAC5E,gEAAgE;IAChE,MAAM,aAAa,MAAM,MAAM,YAAY;QAAE,OAAO;IAAW;IAC/D,MAAM,MAAM,MAAM,WAAW,WAAW;IACxC,gDAAgD;IAChD,OAAO,YAAY,OAAO,CAAC;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40arcjet%2Banalyze-wasm%401.0.0-beta.7/node_modules/%40arcjet/analyze-wasm/_virtual/arcjet_analyze_js_req.component.core3.js"], "sourcesContent": ["// @generated by wasm2module - DO NOT EDIT\n/* eslint-disable */\n// @ts-nocheck\n\n/**\n * This file contains an Arcjet Wasm binary inlined as a base64\n * [Data URL](https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/Data_URLs)\n * with the application/wasm MIME type.\n *\n * This was chosen to save on storage space over inlining the file directly as\n * a Uint8Array, which would take up ~3x the space of the Wasm file. See\n * https://blobfolio.com/2019/better-binary-batter-mixing-base64-and-uint8array/\n * for more details.\n *\n * It is then decoded into an ArrayBuffer to be used directly via WebAssembly's\n * `compile()` function in our entry point file.\n *\n * This is all done to avoid trying to read or bundle the Wasm asset in various\n * ways based on the platform or bundler a user is targeting. One example being\n * that Next.js requires special `asyncWebAssembly` webpack config to load our\n * Wasm file if we don't do this.\n *\n * In the future, we hope to do away with this workaround when all bundlers\n * properly support consistent asset bundling techniques.\n */\n\nconst wasmBase64 = \"data:application/wasm;base64,AGFzbQEAAAABFQNgA39/fwBgBH9/f38Bf2ACf38BfwIzCAABMAAAAAExAAEAATIAAgABMwACAAE0AAIAATUAAgABNgAAAAgkaW1wb3J0cwFwAQcHCQ0BAEEACwcAAQIDBAUGAC8JcHJvZHVjZXJzAQxwcm9jZXNzZWQtYnkBDXdpdC1jb21wb25lbnQHMC4yMjYuMAAcBG5hbWUAFRR3aXQtY29tcG9uZW50OmZpeHVwcw==\";\n/**\n * Returns a WebAssembly.Module for an Arcjet Wasm binary, decoded from a base64\n * Data URL.\n */\n// TODO: Switch back to top-level await when our platforms all support it\nasync function wasm() {\n  // This uses fetch to decode the wasm data url, but disabling cache so files\n  // larger than 2mb don't fail to parse in the Next.js App Router\n  const wasmDecode = await fetch(wasmBase64, { cache: \"no-store\" });\n  const buf = await wasmDecode.arrayBuffer();\n  // And then we return it as a WebAssembly.Module\n  return WebAssembly.compile(buf);\n}\n\nexport { wasm };\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,kBAAkB,GAClB,cAAc;AAEd;;;;;;;;;;;;;;;;;;;;CAoBC;;;AAED,MAAM,aAAa;AACnB;;;CAGC,GACD,yEAAyE;AACzE,eAAe;IACb,4EAA4E;IAC5E,gEAAgE;IAChE,MAAM,aAAa,MAAM,MAAM,YAAY;QAAE,OAAO;IAAW;IAC/D,MAAM,MAAM,MAAM,WAAW,WAAW;IACxC,gDAAgD;IAChD,OAAO,YAAY,OAAO,CAAC;AAC7B", "ignoreList": [0], "debugId": null}}]}