module.exports = {

"[project]/apps/app/app/opengraph-image.png (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/opengraph-image.5c19e39e.png");}}),
"[project]/apps/app/app/opengraph-image.png.mjs { IMAGE => \"[project]/apps/app/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$app$2f$app$2f$opengraph$2d$image$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/apps/app/app/opengraph-image.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$app$2f$app$2f$opengraph$2d$image$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 1200,
    height: 630
};
}}),

};

//# sourceMappingURL=apps_app_app_3f4853f0._.js.map