{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/digest.js"], "sourcesContent": ["import { createHash } from 'node:crypto';\nconst digest = (algorithm, data) => createHash(algorithm).update(data).digest();\nexport default digest;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,SAAS,CAAC,WAAW,OAAS,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,MAAM,CAAC,MAAM,MAAM;uCAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js"], "sourcesContent": ["import digest from '../runtime/digest.js';\nexport const encoder = new TextEncoder();\nexport const decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nexport function concat(...buffers) {\n    const size = buffers.reduce((acc, { length }) => acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    for (const buffer of buffers) {\n        buf.set(buffer, i);\n        i += buffer.length;\n    }\n    return buf;\n}\nexport function p2s(alg, p2sInput) {\n    return concat(encoder.encode(alg), new Uint8Array([0]), p2sInput);\n}\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([value >>> 24, value >>> 16, value >>> 8, value & 0xff], offset);\n}\nexport function uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nexport function uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\nexport function lengthAndInput(input) {\n    return concat(uint32be(input.length), input);\n}\nexport async function concatKdf(secret, bits, value) {\n    const iterations = Math.ceil((bits >> 3) / 32);\n    const res = new Uint8Array(iterations * 32);\n    for (let iter = 0; iter < iterations; iter++) {\n        const buf = new Uint8Array(4 + secret.length + value.length);\n        buf.set(uint32be(iter + 1));\n        buf.set(secret, 4);\n        buf.set(value, 4 + secret.length);\n        res.set(await digest('sha256', buf), iter * 32);\n    }\n    return res.slice(0, bits >> 3);\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,MAAM,UAAU,IAAI;AACpB,MAAM,UAAU,IAAI;AAC3B,MAAM,YAAY,KAAK;AAChB,SAAS,OAAO,GAAG,OAAO;IAC7B,MAAM,OAAO,QAAQ,MAAM,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,GAAK,MAAM,QAAQ;IAC/D,MAAM,MAAM,IAAI,WAAW;IAC3B,IAAI,IAAI;IACR,KAAK,MAAM,UAAU,QAAS;QAC1B,IAAI,GAAG,CAAC,QAAQ;QAChB,KAAK,OAAO,MAAM;IACtB;IACA,OAAO;AACX;AACO,SAAS,IAAI,GAAG,EAAE,QAAQ;IAC7B,OAAO,OAAO,QAAQ,MAAM,CAAC,MAAM,IAAI,WAAW;QAAC;KAAE,GAAG;AAC5D;AACA,SAAS,cAAc,GAAG,EAAE,KAAK,EAAE,MAAM;IACrC,IAAI,QAAQ,KAAK,SAAS,WAAW;QACjC,MAAM,IAAI,WAAW,CAAC,0BAA0B,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO;IACxF;IACA,IAAI,GAAG,CAAC;QAAC,UAAU;QAAI,UAAU;QAAI,UAAU;QAAG,QAAQ;KAAK,EAAE;AACrE;AACO,SAAS,SAAS,KAAK;IAC1B,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ;IAChC,MAAM,MAAM,QAAQ;IACpB,MAAM,MAAM,IAAI,WAAW;IAC3B,cAAc,KAAK,MAAM;IACzB,cAAc,KAAK,KAAK;IACxB,OAAO;AACX;AACO,SAAS,SAAS,KAAK;IAC1B,MAAM,MAAM,IAAI,WAAW;IAC3B,cAAc,KAAK;IACnB,OAAO;AACX;AACO,SAAS,eAAe,KAAK;IAChC,OAAO,OAAO,SAAS,MAAM,MAAM,GAAG;AAC1C;AACO,eAAe,UAAU,MAAM,EAAE,IAAI,EAAE,KAAK;IAC/C,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI;IAC3C,MAAM,MAAM,IAAI,WAAW,aAAa;IACxC,IAAK,IAAI,OAAO,GAAG,OAAO,YAAY,OAAQ;QAC1C,MAAM,MAAM,IAAI,WAAW,IAAI,OAAO,MAAM,GAAG,MAAM,MAAM;QAC3D,IAAI,GAAG,CAAC,SAAS,OAAO;QACxB,IAAI,GAAG,CAAC,QAAQ;QAChB,IAAI,GAAG,CAAC,OAAO,IAAI,OAAO,MAAM;QAChC,IAAI,GAAG,CAAC,MAAM,CAAA,GAAA,kNAAA,CAAA,UAAM,AAAD,EAAE,UAAU,MAAM,OAAO;IAChD;IACA,OAAO,IAAI,KAAK,CAAC,GAAG,QAAQ;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/base64url.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON> } from 'node:buffer';\nimport { decoder } from '../lib/buffer_utils.js';\nfunction normalize(input) {\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = decoder.decode(encoded);\n    }\n    return encoded;\n}\nconst encode = (input) => Buffer.from(input).toString('base64url');\nexport const decodeBase64 = (input) => new Uint8Array(Buffer.from(input, 'base64'));\nexport const encodeBase64 = (input) => Buffer.from(input).toString('base64');\nexport { encode };\nexport const decode = (input) => new Uint8Array(Buffer.from(normalize(input), 'base64url'));\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AACA,SAAS,UAAU,KAAK;IACpB,IAAI,UAAU;IACd,IAAI,mBAAmB,YAAY;QAC/B,UAAU,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC;IAC7B;IACA,OAAO;AACX;AACA,MAAM,SAAS,CAAC,QAAU,qHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,OAAO,QAAQ,CAAC;AAC/C,MAAM,eAAe,CAAC,QAAU,IAAI,WAAW,qHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,OAAO;AAClE,MAAM,eAAe,CAAC,QAAU,qHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,OAAO,QAAQ,CAAC;;AAE5D,MAAM,SAAS,CAAC,QAAU,IAAI,WAAW,qHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/util/errors.js"], "sourcesContent": ["export class JOSEError extends Error {\n    static code = 'ERR_JOSE_GENERIC';\n    code = 'ERR_JOSE_GENERIC';\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nexport class JWTClaimValidationFailed extends JOSEError {\n    static code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JWTExpired extends JOSEError {\n    static code = 'ERR_JWT_EXPIRED';\n    code = 'ERR_JWT_EXPIRED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JOSEAlgNotAllowed extends J<PERSON><PERSON><PERSON>r {\n    static code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n    code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n}\nexport class JOSENotSupported extends JOSEError {\n    static code = 'ERR_JOSE_NOT_SUPPORTED';\n    code = 'ERR_JOSE_NOT_SUPPORTED';\n}\nexport class JWEDecryptionFailed extends JOSEError {\n    static code = 'ERR_JWE_DECRYPTION_FAILED';\n    code = 'ERR_JWE_DECRYPTION_FAILED';\n    constructor(message = 'decryption operation failed', options) {\n        super(message, options);\n    }\n}\nexport class JWEInvalid extends JOSEError {\n    static code = 'ERR_JWE_INVALID';\n    code = 'ERR_JWE_INVALID';\n}\nexport class JWSInvalid extends JOSEError {\n    static code = 'ERR_JWS_INVALID';\n    code = 'ERR_JWS_INVALID';\n}\nexport class JWTInvalid extends JOSEError {\n    static code = 'ERR_JWT_INVALID';\n    code = 'ERR_JWT_INVALID';\n}\nexport class JWKInvalid extends JOSEError {\n    static code = 'ERR_JWK_INVALID';\n    code = 'ERR_JWK_INVALID';\n}\nexport class JWKSInvalid extends JOSEError {\n    static code = 'ERR_JWKS_INVALID';\n    code = 'ERR_JWKS_INVALID';\n}\nexport class JWKSNoMatchingKey extends JOSEError {\n    static code = 'ERR_JWKS_NO_MATCHING_KEY';\n    code = 'ERR_JWKS_NO_MATCHING_KEY';\n    constructor(message = 'no applicable key found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSMultipleMatchingKeys extends JOSEError {\n    [Symbol.asyncIterator];\n    static code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    constructor(message = 'multiple matching keys found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSTimeout extends JOSEError {\n    static code = 'ERR_JWKS_TIMEOUT';\n    code = 'ERR_JWKS_TIMEOUT';\n    constructor(message = 'request timed out', options) {\n        super(message, options);\n    }\n}\nexport class JWSSignatureVerificationFailed extends JOSEError {\n    static code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    constructor(message = 'signature verification failed', options) {\n        super(message, options);\n    }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAO,MAAM,kBAAkB;IAC3B,OAAO,OAAO,mBAAmB;IACjC,OAAO,mBAAmB;IAC1B,YAAY,OAAO,EAAE,OAAO,CAAE;QAC1B,KAAK,CAAC,SAAS;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI;QACjC,MAAM,iBAAiB,GAAG,IAAI,EAAE,IAAI,CAAC,WAAW;IACpD;AACJ;AACO,MAAM,iCAAiC;IAC1C,OAAO,OAAO,kCAAkC;IAChD,OAAO,kCAAkC;IACzC,MAAM;IACN,OAAO;IACP,QAAQ;IACR,YAAY,OAAO,EAAE,OAAO,EAAE,QAAQ,aAAa,EAAE,SAAS,aAAa,CAAE;QACzE,KAAK,CAAC,SAAS;YAAE,OAAO;gBAAE;gBAAO;gBAAQ;YAAQ;QAAE;QACnD,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;IACnB;AACJ;AACO,MAAM,mBAAmB;IAC5B,OAAO,OAAO,kBAAkB;IAChC,OAAO,kBAAkB;IACzB,MAAM;IACN,OAAO;IACP,QAAQ;IACR,YAAY,OAAO,EAAE,OAAO,EAAE,QAAQ,aAAa,EAAE,SAAS,aAAa,CAAE;QACzE,KAAK,CAAC,SAAS;YAAE,OAAO;gBAAE;gBAAO;gBAAQ;YAAQ;QAAE;QACnD,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;IACnB;AACJ;AACO,MAAM,0BAA0B;IACnC,OAAO,OAAO,2BAA2B;IACzC,OAAO,2BAA2B;AACtC;AACO,MAAM,yBAAyB;IAClC,OAAO,OAAO,yBAAyB;IACvC,OAAO,yBAAyB;AACpC;AACO,MAAM,4BAA4B;IACrC,OAAO,OAAO,4BAA4B;IAC1C,OAAO,4BAA4B;IACnC,YAAY,UAAU,6BAA6B,EAAE,OAAO,CAAE;QAC1D,KAAK,CAAC,SAAS;IACnB;AACJ;AACO,MAAM,mBAAmB;IAC5B,OAAO,OAAO,kBAAkB;IAChC,OAAO,kBAAkB;AAC7B;AACO,MAAM,mBAAmB;IAC5B,OAAO,OAAO,kBAAkB;IAChC,OAAO,kBAAkB;AAC7B;AACO,MAAM,mBAAmB;IAC5B,OAAO,OAAO,kBAAkB;IAChC,OAAO,kBAAkB;AAC7B;AACO,MAAM,mBAAmB;IAC5B,OAAO,OAAO,kBAAkB;IAChC,OAAO,kBAAkB;AAC7B;AACO,MAAM,oBAAoB;IAC7B,OAAO,OAAO,mBAAmB;IACjC,OAAO,mBAAmB;AAC9B;AACO,MAAM,0BAA0B;IACnC,OAAO,OAAO,2BAA2B;IACzC,OAAO,2BAA2B;IAClC,YAAY,UAAU,iDAAiD,EAAE,OAAO,CAAE;QAC9E,KAAK,CAAC,SAAS;IACnB;AACJ;AACO,MAAM,iCAAiC;IAC1C,CAAC,OAAO,aAAa,CAAC,CAAC;IACvB,OAAO,OAAO,kCAAkC;IAChD,OAAO,kCAAkC;IACzC,YAAY,UAAU,sDAAsD,EAAE,OAAO,CAAE;QACnF,KAAK,CAAC,SAAS;IACnB;AACJ;AACO,MAAM,oBAAoB;IAC7B,OAAO,OAAO,mBAAmB;IACjC,OAAO,mBAAmB;IAC1B,YAAY,UAAU,mBAAmB,EAAE,OAAO,CAAE;QAChD,KAAK,CAAC,SAAS;IACnB;AACJ;AACO,MAAM,uCAAuC;IAChD,OAAO,OAAO,wCAAwC;IACtD,OAAO,wCAAwC;IAC/C,YAAY,UAAU,+BAA+B,EAAE,OAAO,CAAE;QAC5D,KAAK,CAAC,SAAS;IACnB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/dsa_digest.js"], "sourcesContent": ["import { JOSENotSupported } from '../util/errors.js';\nexport default function dsaDigest(alg) {\n    switch (alg) {\n        case 'PS256':\n        case 'RS256':\n        case 'ES256':\n        case 'ES256K':\n            return 'sha256';\n        case 'PS384':\n        case 'RS384':\n        case 'ES384':\n            return 'sha384';\n        case 'PS512':\n        case 'RS512':\n        case 'ES512':\n            return 'sha512';\n        case 'Ed25519':\n        case 'EdDSA':\n            return undefined;\n        default:\n            throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,UAAU,GAAG;IACjC,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,2DAA2D,CAAC;IAC1G;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/hmac_digest.js"], "sourcesContent": ["import { JOSENotSupported } from '../util/errors.js';\nexport default function hmacDigest(alg) {\n    switch (alg) {\n        case 'HS256':\n            return 'sha256';\n        case 'HS384':\n            return 'sha384';\n        case 'HS512':\n            return 'sha512';\n        default:\n            throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,WAAW,GAAG;IAClC,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,2DAA2D,CAAC;IAC1G;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/webcrypto.js"], "sourcesContent": ["import * as crypto from 'node:crypto';\nimport * as util from 'node:util';\nconst webcrypto = crypto.webcrypto;\nexport default webcrypto;\nexport const isCryptoKey = (key) => util.types.isCryptoKey(key);\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACA,MAAM,YAAY,qHAAA,CAAA,YAAgB;uCACnB;AACR,MAAM,cAAc,CAAC,MAAQ,iHAAA,CAAA,QAAU,CAAC,WAAW,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/is_key_object.js"], "sourcesContent": ["import * as util from 'node:util';\nexport default (obj) => util.types.isKeyObject(obj);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAC,MAAQ,iHAAA,CAAA,QAAU,CAAC,WAAW,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/invalid_key_input.js"], "sourcesContent": ["function message(msg, actual, ...types) {\n    types = types.filter(<PERSON><PERSON><PERSON>);\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `one of type ${types.join(', ')}, or ${last}.`;\n    }\n    else if (types.length === 2) {\n        msg += `one of type ${types[0]} or ${types[1]}.`;\n    }\n    else {\n        msg += `of type ${types[0]}.`;\n    }\n    if (actual == null) {\n        msg += ` Received ${actual}`;\n    }\n    else if (typeof actual === 'function' && actual.name) {\n        msg += ` Received function ${actual.name}`;\n    }\n    else if (typeof actual === 'object' && actual != null) {\n        if (actual.constructor?.name) {\n            msg += ` Received an instance of ${actual.constructor.name}`;\n        }\n    }\n    return msg;\n}\nexport default (actual, ...types) => {\n    return message('Key must be ', actual, ...types);\n};\nexport function withAlg(alg, actual, ...types) {\n    return message(`Key for the ${alg} algorithm must be `, actual, ...types);\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,QAAQ,GAAG,EAAE,MAAM,EAAE,GAAG,KAAK;IAClC,QAAQ,MAAM,MAAM,CAAC;IACrB,IAAI,MAAM,MAAM,GAAG,GAAG;QAClB,MAAM,OAAO,MAAM,GAAG;QACtB,OAAO,CAAC,YAAY,EAAE,MAAM,IAAI,CAAC,MAAM,KAAK,EAAE,KAAK,CAAC,CAAC;IACzD,OACK,IAAI,MAAM,MAAM,KAAK,GAAG;QACzB,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IACpD,OACK;QACD,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IACjC;IACA,IAAI,UAAU,MAAM;QAChB,OAAO,CAAC,UAAU,EAAE,QAAQ;IAChC,OACK,IAAI,OAAO,WAAW,cAAc,OAAO,IAAI,EAAE;QAClD,OAAO,CAAC,mBAAmB,EAAE,OAAO,IAAI,EAAE;IAC9C,OACK,IAAI,OAAO,WAAW,YAAY,UAAU,MAAM;QACnD,IAAI,OAAO,WAAW,EAAE,MAAM;YAC1B,OAAO,CAAC,yBAAyB,EAAE,OAAO,WAAW,CAAC,IAAI,EAAE;QAChE;IACJ;IACA,OAAO;AACX;uCACe,CAAC,QAAQ,GAAG;IACvB,OAAO,QAAQ,gBAAgB,WAAW;AAC9C;AACO,SAAS,QAAQ,GAAG,EAAE,MAAM,EAAE,GAAG,KAAK;IACzC,OAAO,QAAQ,CAAC,YAAY,EAAE,IAAI,mBAAmB,CAAC,EAAE,WAAW;AACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/is_key_like.js"], "sourcesContent": ["import webcrypto, { isCryptoKey } from './webcrypto.js';\nimport isKeyObject from './is_key_object.js';\nexport default (key) => isKeyObject(key) || isCryptoKey(key);\nconst types = ['KeyObject'];\nif (globalThis.CryptoKey || webcrypto?.CryptoKey) {\n    types.push('CryptoKey');\n}\nexport { types };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;uCACe,CAAC,MAAQ,CAAA,GAAA,yNAAA,CAAA,UAAW,AAAD,EAAE,QAAQ,CAAA,GAAA,qNAAA,CAAA,cAAW,AAAD,EAAE;AACxD,MAAM,QAAQ;IAAC;CAAY;AAC3B,IAAI,WAAW,SAAS,IAAI,qNAAA,CAAA,UAAS,EAAE,WAAW;IAC9C,MAAM,IAAI,CAAC;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/is_object.js"], "sourcesContent": ["function isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nexport default function isObject(input) {\n    if (!isObjectLike(input) || Object.prototype.toString.call(input) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(input) === null) {\n        return true;\n    }\n    let proto = input;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(input) === proto;\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,aAAa,KAAK;IACvB,OAAO,OAAO,UAAU,YAAY,UAAU;AAClD;AACe,SAAS,SAAS,KAAK;IAClC,IAAI,CAAC,aAAa,UAAU,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,mBAAmB;QACrF,OAAO;IACX;IACA,IAAI,OAAO,cAAc,CAAC,WAAW,MAAM;QACvC,OAAO;IACX;IACA,IAAI,QAAQ;IACZ,MAAO,OAAO,cAAc,CAAC,WAAW,KAAM;QAC1C,QAAQ,OAAO,cAAc,CAAC;IAClC;IACA,OAAO,OAAO,cAAc,CAAC,WAAW;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/is_jwk.js"], "sourcesContent": ["import isObject from './is_object.js';\nexport function isJWK(key) {\n    return isObject(key) && typeof key.kty === 'string';\n}\nexport function isPrivateJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'string';\n}\nexport function isPublicJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'undefined';\n}\nexport function isSecretJWK(key) {\n    return isJWK(key) && key.kty === 'oct' && typeof key.k === 'string';\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,SAAS,MAAM,GAAG;IACrB,OAAO,CAAA,GAAA,iNAAA,CAAA,UAAQ,AAAD,EAAE,QAAQ,OAAO,IAAI,GAAG,KAAK;AAC/C;AACO,SAAS,aAAa,GAAG;IAC5B,OAAO,IAAI,GAAG,KAAK,SAAS,OAAO,IAAI,CAAC,KAAK;AACjD;AACO,SAAS,YAAY,GAAG;IAC3B,OAAO,IAAI,GAAG,KAAK,SAAS,OAAO,IAAI,CAAC,KAAK;AACjD;AACO,SAAS,YAAY,GAAG;IAC3B,OAAO,MAAM,QAAQ,IAAI,GAAG,KAAK,SAAS,OAAO,IAAI,CAAC,KAAK;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/get_named_curve.js"], "sourcesContent": ["import { KeyObject } from 'node:crypto';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { isCryptoKey } from './webcrypto.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport { types } from './is_key_like.js';\nimport { isJWK } from '../lib/is_jwk.js';\nexport const weakMap = new WeakMap();\nconst namedCurveToJOSE = (namedCurve) => {\n    switch (namedCurve) {\n        case 'prime256v1':\n            return 'P-256';\n        case 'secp384r1':\n            return 'P-384';\n        case 'secp521r1':\n            return 'P-521';\n        case 'secp256k1':\n            return 'secp256k1';\n        default:\n            throw new JOSENotSupported('Unsupported key curve for this operation');\n    }\n};\nconst getNamedCurve = (kee, raw) => {\n    let key;\n    if (isCryptoKey(kee)) {\n        key = KeyObject.from(kee);\n    }\n    else if (isKeyObject(kee)) {\n        key = kee;\n    }\n    else if (isJWK(kee)) {\n        return kee.crv;\n    }\n    else {\n        throw new TypeError(invalidKeyInput(kee, ...types));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError('only \"private\" or \"public\" type keys can be used for this operation');\n    }\n    switch (key.asymmetricKeyType) {\n        case 'ed25519':\n        case 'ed448':\n            return `Ed${key.asymmetricKeyType.slice(2)}`;\n        case 'x25519':\n        case 'x448':\n            return `X${key.asymmetricKeyType.slice(1)}`;\n        case 'ec': {\n            const namedCurve = key.asymmetricKeyDetails.namedCurve;\n            if (raw) {\n                return namedCurve;\n            }\n            return namedCurveToJOSE(namedCurve);\n        }\n        default:\n            throw new TypeError('Invalid asymmetric key type for this operation');\n    }\n};\nexport default getNamedCurve;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACO,MAAM,UAAU,IAAI;AAC3B,MAAM,mBAAmB,CAAC;IACtB,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC;IACnC;AACJ;AACA,MAAM,gBAAgB,CAAC,KAAK;IACxB,IAAI;IACJ,IAAI,CAAA,GAAA,qNAAA,CAAA,cAAW,AAAD,EAAE,MAAM;QAClB,MAAM,qHAAA,CAAA,YAAS,CAAC,IAAI,CAAC;IACzB,OACK,IAAI,CAAA,GAAA,yNAAA,CAAA,UAAW,AAAD,EAAE,MAAM;QACvB,MAAM;IACV,OACK,IAAI,CAAA,GAAA,8MAAA,CAAA,QAAK,AAAD,EAAE,MAAM;QACjB,OAAO,IAAI,GAAG;IAClB,OACK;QACD,MAAM,IAAI,UAAU,CAAA,GAAA,yNAAA,CAAA,UAAe,AAAD,EAAE,QAAQ,uNAAA,CAAA,QAAK;IACrD;IACA,IAAI,IAAI,IAAI,KAAK,UAAU;QACvB,MAAM,IAAI,UAAU;IACxB;IACA,OAAQ,IAAI,iBAAiB;QACzB,KAAK;QACL,KAAK;YACD,OAAO,CAAC,EAAE,EAAE,IAAI,iBAAiB,CAAC,KAAK,CAAC,IAAI;QAChD,KAAK;QACL,KAAK;YACD,OAAO,CAAC,CAAC,EAAE,IAAI,iBAAiB,CAAC,KAAK,CAAC,IAAI;QAC/C,KAAK;YAAM;gBACP,MAAM,aAAa,IAAI,oBAAoB,CAAC,UAAU;gBACtD,IAAI,KAAK;oBACL,OAAO;gBACX;gBACA,OAAO,iBAAiB;YAC5B;QACA;YACI,MAAM,IAAI,UAAU;IAC5B;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/check_key_length.js"], "sourcesContent": ["import { KeyObject } from 'node:crypto';\nexport default (key, alg) => {\n    let modulusLength;\n    try {\n        if (key instanceof KeyObject) {\n            modulusLength = key.asymmetricKeyDetails?.modulusLength;\n        }\n        else {\n            modulusLength = Buffer.from(key.n, 'base64url').byteLength << 3;\n        }\n    }\n    catch { }\n    if (typeof modulusLength !== 'number' || modulusLength < 2048) {\n        throw new TypeError(`${alg} requires key modulusLength to be 2048 bits or larger`);\n    }\n};\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAC,KAAK;IACjB,IAAI;IACJ,IAAI;QACA,IAAI,eAAe,qHAAA,CAAA,YAAS,EAAE;YAC1B,gBAAgB,IAAI,oBAAoB,EAAE;QAC9C,OACK;YACD,gBAAgB,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,aAAa,UAAU,IAAI;QAClE;IACJ,EACA,OAAM,CAAE;IACR,IAAI,OAAO,kBAAkB,YAAY,gBAAgB,MAAM;QAC3D,MAAM,IAAI,UAAU,GAAG,IAAI,qDAAqD,CAAC;IACrF;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/node_key.js"], "sourcesContent": ["import { constants, KeyObject } from 'node:crypto';\nimport getNamedCurve from './get_named_curve.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport checkKeyLength from './check_key_length.js';\nconst ecCurveAlgMap = new Map([\n    ['ES256', 'P-256'],\n    ['ES256K', 'secp256k1'],\n    ['ES384', 'P-384'],\n    ['ES512', 'P-521'],\n]);\nexport default function keyForCrypto(alg, key) {\n    let asymmetricKeyType;\n    let asymmetricKeyDetails;\n    let isJWK;\n    if (key instanceof KeyObject) {\n        asymmetricKeyType = key.asymmetricKeyType;\n        asymmetricKeyDetails = key.asymmetricKeyDetails;\n    }\n    else {\n        isJWK = true;\n        switch (key.kty) {\n            case 'RSA':\n                asymmetricKeyType = 'rsa';\n                break;\n            case 'EC':\n                asymmetricKeyType = 'ec';\n                break;\n            case 'OKP': {\n                if (key.crv === 'Ed25519') {\n                    asymmetricKeyType = 'ed25519';\n                    break;\n                }\n                if (key.crv === 'Ed448') {\n                    asymmetricKeyType = 'ed448';\n                    break;\n                }\n                throw new TypeError('Invalid key for this operation, its crv must be Ed25519 or Ed448');\n            }\n            default:\n                throw new TypeError('Invalid key for this operation, its kty must be RSA, OKP, or EC');\n        }\n    }\n    let options;\n    switch (alg) {\n        case 'Ed25519':\n            if (asymmetricKeyType !== 'ed25519') {\n                throw new TypeError(`Invalid key for this operation, its asymmetricKeyType must be ed25519`);\n            }\n            break;\n        case 'EdDSA':\n            if (!['ed25519', 'ed448'].includes(asymmetricKeyType)) {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be ed25519 or ed448');\n            }\n            break;\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            if (asymmetricKeyType !== 'rsa') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa');\n            }\n            checkKeyLength(key, alg);\n            break;\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            if (asymmetricKeyType === 'rsa-pss') {\n                const { hashAlgorithm, mgf1HashAlgorithm, saltLength } = asymmetricKeyDetails;\n                const length = parseInt(alg.slice(-3), 10);\n                if (hashAlgorithm !== undefined &&\n                    (hashAlgorithm !== `sha${length}` || mgf1HashAlgorithm !== hashAlgorithm)) {\n                    throw new TypeError(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of \"alg\" ${alg}`);\n                }\n                if (saltLength !== undefined && saltLength > length >> 3) {\n                    throw new TypeError(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of \"alg\" ${alg}`);\n                }\n            }\n            else if (asymmetricKeyType !== 'rsa') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa or rsa-pss');\n            }\n            checkKeyLength(key, alg);\n            options = {\n                padding: constants.RSA_PKCS1_PSS_PADDING,\n                saltLength: constants.RSA_PSS_SALTLEN_DIGEST,\n            };\n            break;\n        case 'ES256':\n        case 'ES256K':\n        case 'ES384':\n        case 'ES512': {\n            if (asymmetricKeyType !== 'ec') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be ec');\n            }\n            const actual = getNamedCurve(key);\n            const expected = ecCurveAlgMap.get(alg);\n            if (actual !== expected) {\n                throw new TypeError(`Invalid key curve for the algorithm, its curve must be ${expected}, got ${actual}`);\n            }\n            options = { dsaEncoding: 'ieee-p1363' };\n            break;\n        }\n        default:\n            throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n    if (isJWK) {\n        return { format: 'jwk', key, ...options };\n    }\n    return options ? { ...options, key } : key;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACA,MAAM,gBAAgB,IAAI,IAAI;IAC1B;QAAC;QAAS;KAAQ;IAClB;QAAC;QAAU;KAAY;IACvB;QAAC;QAAS;KAAQ;IAClB;QAAC;QAAS;KAAQ;CACrB;AACc,SAAS,aAAa,GAAG,EAAE,GAAG;IACzC,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,eAAe,qHAAA,CAAA,YAAS,EAAE;QAC1B,oBAAoB,IAAI,iBAAiB;QACzC,uBAAuB,IAAI,oBAAoB;IACnD,OACK;QACD,QAAQ;QACR,OAAQ,IAAI,GAAG;YACX,KAAK;gBACD,oBAAoB;gBACpB;YACJ,KAAK;gBACD,oBAAoB;gBACpB;YACJ,KAAK;gBAAO;oBACR,IAAI,IAAI,GAAG,KAAK,WAAW;wBACvB,oBAAoB;wBACpB;oBACJ;oBACA,IAAI,IAAI,GAAG,KAAK,SAAS;wBACrB,oBAAoB;wBACpB;oBACJ;oBACA,MAAM,IAAI,UAAU;gBACxB;YACA;gBACI,MAAM,IAAI,UAAU;QAC5B;IACJ;IACA,IAAI;IACJ,OAAQ;QACJ,KAAK;YACD,IAAI,sBAAsB,WAAW;gBACjC,MAAM,IAAI,UAAU,CAAC,qEAAqE,CAAC;YAC/F;YACA;QACJ,KAAK;YACD,IAAI,CAAC;gBAAC;gBAAW;aAAQ,CAAC,QAAQ,CAAC,oBAAoB;gBACnD,MAAM,IAAI,UAAU;YACxB;YACA;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,IAAI,sBAAsB,OAAO;gBAC7B,MAAM,IAAI,UAAU;YACxB;YACA,CAAA,GAAA,4NAAA,CAAA,UAAc,AAAD,EAAE,KAAK;YACpB;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,IAAI,sBAAsB,WAAW;gBACjC,MAAM,EAAE,aAAa,EAAE,iBAAiB,EAAE,UAAU,EAAE,GAAG;gBACzD,MAAM,SAAS,SAAS,IAAI,KAAK,CAAC,CAAC,IAAI;gBACvC,IAAI,kBAAkB,aAClB,CAAC,kBAAkB,CAAC,GAAG,EAAE,QAAQ,IAAI,sBAAsB,aAAa,GAAG;oBAC3E,MAAM,IAAI,UAAU,CAAC,6FAA6F,EAAE,KAAK;gBAC7H;gBACA,IAAI,eAAe,aAAa,aAAa,UAAU,GAAG;oBACtD,MAAM,IAAI,UAAU,CAAC,yGAAyG,EAAE,KAAK;gBACzI;YACJ,OACK,IAAI,sBAAsB,OAAO;gBAClC,MAAM,IAAI,UAAU;YACxB;YACA,CAAA,GAAA,4NAAA,CAAA,UAAc,AAAD,EAAE,KAAK;YACpB,UAAU;gBACN,SAAS,qHAAA,CAAA,YAAS,CAAC,qBAAqB;gBACxC,YAAY,qHAAA,CAAA,YAAS,CAAC,sBAAsB;YAChD;YACA;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,sBAAsB,MAAM;oBAC5B,MAAM,IAAI,UAAU;gBACxB;gBACA,MAAM,SAAS,CAAA,GAAA,2NAAA,CAAA,UAAa,AAAD,EAAE;gBAC7B,MAAM,WAAW,cAAc,GAAG,CAAC;gBACnC,IAAI,WAAW,UAAU;oBACrB,MAAM,IAAI,UAAU,CAAC,uDAAuD,EAAE,SAAS,MAAM,EAAE,QAAQ;gBAC3G;gBACA,UAAU;oBAAE,aAAa;gBAAa;gBACtC;YACJ;QACA;YACI,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,2DAA2D,CAAC;IAC1G;IACA,IAAI,OAAO;QACP,OAAO;YAAE,QAAQ;YAAO;YAAK,GAAG,OAAO;QAAC;IAC5C;IACA,OAAO,UAAU;QAAE,GAAG,OAAO;QAAE;IAAI,IAAI;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/crypto_key.js"], "sourcesContent": ["function unusable(name, prop = 'algorithm.name') {\n    return new TypeError(`CryptoKey does not support this operation, its ${prop} must be ${name}`);\n}\nfunction isAlgorithm(algorithm, name) {\n    return algorithm.name === name;\n}\nfunction getHashLength(hash) {\n    return parseInt(hash.name.slice(4), 10);\n}\nfunction getNamedCurve(alg) {\n    switch (alg) {\n        case 'ES256':\n            return 'P-256';\n        case 'ES384':\n            return 'P-384';\n        case 'ES512':\n            return 'P-521';\n        default:\n            throw new Error('unreachable');\n    }\n}\nfunction checkUsage(key, usages) {\n    if (usages.length && !usages.some((expected) => key.usages.includes(expected))) {\n        let msg = 'CryptoKey does not support this operation, its usages must include ';\n        if (usages.length > 2) {\n            const last = usages.pop();\n            msg += `one of ${usages.join(', ')}, or ${last}.`;\n        }\n        else if (usages.length === 2) {\n            msg += `one of ${usages[0]} or ${usages[1]}.`;\n        }\n        else {\n            msg += `${usages[0]}.`;\n        }\n        throw new TypeError(msg);\n    }\n}\nexport function checkSigCryptoKey(key, alg, ...usages) {\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512': {\n            if (!isAlgorithm(key.algorithm, 'HMAC'))\n                throw unusable('HMAC');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'RS256':\n        case 'RS384':\n        case 'RS512': {\n            if (!isAlgorithm(key.algorithm, 'RSASSA-PKCS1-v1_5'))\n                throw unusable('RSASSA-PKCS1-v1_5');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'PS256':\n        case 'PS384':\n        case 'PS512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-PSS'))\n                throw unusable('RSA-PSS');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'EdDSA': {\n            if (key.algorithm.name !== 'Ed25519' && key.algorithm.name !== 'Ed448') {\n                throw unusable('Ed25519 or Ed448');\n            }\n            break;\n        }\n        case 'Ed25519': {\n            if (!isAlgorithm(key.algorithm, 'Ed25519'))\n                throw unusable('Ed25519');\n            break;\n        }\n        case 'ES256':\n        case 'ES384':\n        case 'ES512': {\n            if (!isAlgorithm(key.algorithm, 'ECDSA'))\n                throw unusable('ECDSA');\n            const expected = getNamedCurve(alg);\n            const actual = key.algorithm.namedCurve;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.namedCurve');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usages);\n}\nexport function checkEncCryptoKey(key, alg, ...usages) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM': {\n            if (!isAlgorithm(key.algorithm, 'AES-GCM'))\n                throw unusable('AES-GCM');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (!isAlgorithm(key.algorithm, 'AES-KW'))\n                throw unusable('AES-KW');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'ECDH': {\n            switch (key.algorithm.name) {\n                case 'ECDH':\n                case 'X25519':\n                case 'X448':\n                    break;\n                default:\n                    throw unusable('ECDH, X25519, or X448');\n            }\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW':\n            if (!isAlgorithm(key.algorithm, 'PBKDF2'))\n                throw unusable('PBKDF2');\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-OAEP'))\n                throw unusable('RSA-OAEP');\n            const expected = parseInt(alg.slice(9), 10) || 1;\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usages);\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,SAAS,IAAI,EAAE,OAAO,gBAAgB;IAC3C,OAAO,IAAI,UAAU,CAAC,+CAA+C,EAAE,KAAK,SAAS,EAAE,MAAM;AACjG;AACA,SAAS,YAAY,SAAS,EAAE,IAAI;IAChC,OAAO,UAAU,IAAI,KAAK;AAC9B;AACA,SAAS,cAAc,IAAI;IACvB,OAAO,SAAS,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI;AACxC;AACA,SAAS,cAAc,GAAG;IACtB,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,MAAM;IACxB;AACJ;AACA,SAAS,WAAW,GAAG,EAAE,MAAM;IAC3B,IAAI,OAAO,MAAM,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,WAAa,IAAI,MAAM,CAAC,QAAQ,CAAC,YAAY;QAC5E,IAAI,MAAM;QACV,IAAI,OAAO,MAAM,GAAG,GAAG;YACnB,MAAM,OAAO,OAAO,GAAG;YACvB,OAAO,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,MAAM,KAAK,EAAE,KAAK,CAAC,CAAC;QACrD,OACK,IAAI,OAAO,MAAM,KAAK,GAAG;YAC1B,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QACjD,OACK;YACD,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1B;QACA,MAAM,IAAI,UAAU;IACxB;AACJ;AACO,SAAS,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM;IACjD,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,SAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,IAAI;gBACxC,MAAM,SAAS,cAAc,IAAI,SAAS,CAAC,IAAI;gBAC/C,IAAI,WAAW,UACX,MAAM,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE;gBACtC;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,sBAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,IAAI;gBACxC,MAAM,SAAS,cAAc,IAAI,SAAS,CAAC,IAAI;gBAC/C,IAAI,WAAW,UACX,MAAM,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE;gBACtC;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,YAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,IAAI;gBACxC,MAAM,SAAS,cAAc,IAAI,SAAS,CAAC,IAAI;gBAC/C,IAAI,WAAW,UACX,MAAM,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE;gBACtC;YACJ;QACA,KAAK;YAAS;gBACV,IAAI,IAAI,SAAS,CAAC,IAAI,KAAK,aAAa,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS;oBACpE,MAAM,SAAS;gBACnB;gBACA;YACJ;QACA,KAAK;YAAW;gBACZ,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,YAC5B,MAAM,SAAS;gBACnB;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,UAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,cAAc;gBAC/B,MAAM,SAAS,IAAI,SAAS,CAAC,UAAU;gBACvC,IAAI,WAAW,UACX,MAAM,SAAS,UAAU;gBAC7B;YACJ;QACA;YACI,MAAM,IAAI,UAAU;IAC5B;IACA,WAAW,KAAK;AACpB;AACO,SAAS,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM;IACjD,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YAAW;gBACZ,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,YAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;gBAC3C,MAAM,SAAS,IAAI,SAAS,CAAC,MAAM;gBACnC,IAAI,WAAW,UACX,MAAM,SAAS,UAAU;gBAC7B;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAU;gBACX,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,WAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;gBAC3C,MAAM,SAAS,IAAI,SAAS,CAAC,MAAM;gBACnC,IAAI,WAAW,UACX,MAAM,SAAS,UAAU;gBAC7B;YACJ;QACA,KAAK;YAAQ;gBACT,OAAQ,IAAI,SAAS,CAAC,IAAI;oBACtB,KAAK;oBACL,KAAK;oBACL,KAAK;wBACD;oBACJ;wBACI,MAAM,SAAS;gBACvB;gBACA;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YACD,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,WAC5B,MAAM,SAAS;YACnB;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAgB;gBACjB,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,aAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,IAAI,OAAO;gBAC/C,MAAM,SAAS,cAAc,IAAI,SAAS,CAAC,IAAI;gBAC/C,IAAI,WAAW,UACX,MAAM,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE;gBACtC;YACJ;QACA;YACI,MAAM,IAAI,UAAU;IAC5B;IACA,WAAW,KAAK;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 852, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/get_sign_verify_key.js"], "sourcesContent": ["import { KeyObject, createS<PERSON>ret<PERSON><PERSON> } from 'node:crypto';\nimport { isCrypto<PERSON>ey } from './webcrypto.js';\nimport { checkSigCrypto<PERSON>ey } from '../lib/crypto_key.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport { types } from './is_key_like.js';\nimport * as jwk from '../lib/is_jwk.js';\nexport default function getSignVerifyKey(alg, key, usage) {\n    if (key instanceof Uint8Array) {\n        if (!alg.startsWith('HS')) {\n            throw new TypeError(invalidKeyInput(key, ...types));\n        }\n        return createSecretKey(key);\n    }\n    if (key instanceof KeyObject) {\n        return key;\n    }\n    if (isCryptoKey(key)) {\n        checkSigCryptoKey(key, alg, usage);\n        return KeyObject.from(key);\n    }\n    if (jwk.isJWK(key)) {\n        if (alg.startsWith('HS')) {\n            return createS<PERSON><PERSON><PERSON><PERSON>(Buffer.from(key.k, 'base64url'));\n        }\n        return key;\n    }\n    throw new TypeError(invalidKeyInput(key, ...types, 'Uint8Array', 'JSON Web Key'));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACe,SAAS,iBAAiB,GAAG,EAAE,GAAG,EAAE,KAAK;IACpD,IAAI,eAAe,YAAY;QAC3B,IAAI,CAAC,IAAI,UAAU,CAAC,OAAO;YACvB,MAAM,IAAI,UAAU,CAAA,GAAA,yNAAA,CAAA,UAAe,AAAD,EAAE,QAAQ,uNAAA,CAAA,QAAK;QACrD;QACA,OAAO,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAAE;IAC3B;IACA,IAAI,eAAe,qHAAA,CAAA,YAAS,EAAE;QAC1B,OAAO;IACX;IACA,IAAI,CAAA,GAAA,qNAAA,CAAA,cAAW,AAAD,EAAE,MAAM;QAClB,CAAA,GAAA,kNAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,KAAK;QAC5B,OAAO,qHAAA,CAAA,YAAS,CAAC,IAAI,CAAC;IAC1B;IACA,IAAI,CAAA,GAAA,8MAAA,CAAA,QAAS,AAAD,EAAE,MAAM;QAChB,IAAI,IAAI,UAAU,CAAC,OAAO;YACtB,OAAO,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;QAC9C;QACA,OAAO;IACX;IACA,MAAM,IAAI,UAAU,CAAA,GAAA,yNAAA,CAAA,UAAe,AAAD,EAAE,QAAQ,uNAAA,CAAA,QAAK,EAAE,cAAc;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 895, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/sign.js"], "sourcesContent": ["import * as crypto from 'node:crypto';\nimport { promisify } from 'node:util';\nimport nodeDigest from './dsa_digest.js';\nimport hmacDigest from './hmac_digest.js';\nimport nodeKey from './node_key.js';\nimport getSignKey from './get_sign_verify_key.js';\nconst oneShotSign = promisify(crypto.sign);\nconst sign = async (alg, key, data) => {\n    const k = getSignKey(alg, key, 'sign');\n    if (alg.startsWith('HS')) {\n        const hmac = crypto.createHmac(hmacDigest(alg), k);\n        hmac.update(data);\n        return hmac.digest();\n    }\n    return oneShotSign(nodeDigest(alg), data, nodeKey(alg, k));\n};\nexport default sign;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA,MAAM,cAAc,CAAA,GAAA,iHAAA,CAAA,YAAS,AAAD,EAAE,qHAAA,CAAA,OAAW;AACzC,MAAM,OAAO,OAAO,KAAK,KAAK;IAC1B,MAAM,IAAI,CAAA,GAAA,+NAAA,CAAA,UAAU,AAAD,EAAE,KAAK,KAAK;IAC/B,IAAI,IAAI,UAAU,CAAC,OAAO;QACtB,MAAM,OAAO,CAAA,GAAA,qHAAA,CAAA,aAAiB,AAAD,EAAE,CAAA,GAAA,uNAAA,CAAA,UAAU,AAAD,EAAE,MAAM;QAChD,KAAK,MAAM,CAAC;QACZ,OAAO,KAAK,MAAM;IACtB;IACA,OAAO,YAAY,CAAA,GAAA,sNAAA,CAAA,UAAU,AAAD,EAAE,MAAM,MAAM,CAAA,GAAA,oNAAA,CAAA,UAAO,AAAD,EAAE,KAAK;AAC3D;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 927, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/is_disjoint.js"], "sourcesContent": ["const isDisjoint = (...headers) => {\n    const sources = headers.filter(Boolean);\n    if (sources.length === 0 || sources.length === 1) {\n        return true;\n    }\n    let acc;\n    for (const header of sources) {\n        const parameters = Object.keys(header);\n        if (!acc || acc.size === 0) {\n            acc = new Set(parameters);\n            continue;\n        }\n        for (const parameter of parameters) {\n            if (acc.has(parameter)) {\n                return false;\n            }\n            acc.add(parameter);\n        }\n    }\n    return true;\n};\nexport default isDisjoint;\n"], "names": [], "mappings": ";;;AAAA,MAAM,aAAa,CAAC,GAAG;IACnB,MAAM,UAAU,QAAQ,MAAM,CAAC;IAC/B,IAAI,QAAQ,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;QAC9C,OAAO;IACX;IACA,IAAI;IACJ,KAAK,MAAM,UAAU,QAAS;QAC1B,MAAM,aAAa,OAAO,IAAI,CAAC;QAC/B,IAAI,CAAC,OAAO,IAAI,IAAI,KAAK,GAAG;YACxB,MAAM,IAAI,IAAI;YACd;QACJ;QACA,KAAK,MAAM,aAAa,WAAY;YAChC,IAAI,IAAI,GAAG,CAAC,YAAY;gBACpB,OAAO;YACX;YACA,IAAI,GAAG,CAAC;QACZ;IACJ;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/check_key_type.js"], "sourcesContent": ["import { withAlg as invalidKeyInput } from './invalid_key_input.js';\nimport isKeyLike, { types } from '../runtime/is_key_like.js';\nimport * as jwk from './is_jwk.js';\nconst tag = (key) => key?.[Symbol.toStringTag];\nconst jwkMatchesOp = (alg, key, usage) => {\n    if (key.use !== undefined && key.use !== 'sig') {\n        throw new TypeError('Invalid key for this operation, when present its use must be sig');\n    }\n    if (key.key_ops !== undefined && key.key_ops.includes?.(usage) !== true) {\n        throw new TypeError(`Invalid key for this operation, when present its key_ops must include ${usage}`);\n    }\n    if (key.alg !== undefined && key.alg !== alg) {\n        throw new TypeError(`Invalid key for this operation, when present its alg must be ${alg}`);\n    }\n    return true;\n};\nconst symmetricTypeCheck = (alg, key, usage, allowJwk) => {\n    if (key instanceof Uint8Array)\n        return;\n    if (allowJwk && jwk.isJWK(key)) {\n        if (jwk.isSecretJWK(key) && jwkMatchesOp(alg, key, usage))\n            return;\n        throw new TypeError(`JSON Web Key for symmetric algorithms must have JWK \"kty\" (Key Type) equal to \"oct\" and the JWK \"k\" (Key Value) present`);\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, ...types, 'Uint8Array', allowJwk ? 'JSON Web Key' : null));\n    }\n    if (key.type !== 'secret') {\n        throw new TypeError(`${tag(key)} instances for symmetric algorithms must be of type \"secret\"`);\n    }\n};\nconst asymmetricTypeCheck = (alg, key, usage, allowJwk) => {\n    if (allowJwk && jwk.isJWK(key)) {\n        switch (usage) {\n            case 'sign':\n                if (jwk.isPrivateJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a private JWK`);\n            case 'verify':\n                if (jwk.isPublicJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a public JWK`);\n        }\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, ...types, allowJwk ? 'JSON Web Key' : null));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithms must not be of type \"secret\"`);\n    }\n    if (usage === 'sign' && key.type === 'public') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm signing must be of type \"private\"`);\n    }\n    if (usage === 'decrypt' && key.type === 'public') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm decryption must be of type \"private\"`);\n    }\n    if (key.algorithm && usage === 'verify' && key.type === 'private') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm verifying must be of type \"public\"`);\n    }\n    if (key.algorithm && usage === 'encrypt' && key.type === 'private') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm encryption must be of type \"public\"`);\n    }\n};\nfunction checkKeyType(allowJwk, alg, key, usage) {\n    const symmetric = alg.startsWith('HS') ||\n        alg === 'dir' ||\n        alg.startsWith('PBES2') ||\n        /^A\\d{3}(?:GCM)?KW$/.test(alg);\n    if (symmetric) {\n        symmetricTypeCheck(alg, key, usage, allowJwk);\n    }\n    else {\n        asymmetricTypeCheck(alg, key, usage, allowJwk);\n    }\n}\nexport default checkKeyType.bind(undefined, false);\nexport const checkKeyTypeWithJwk = checkKeyType.bind(undefined, true);\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AACA,MAAM,MAAM,CAAC,MAAQ,KAAK,CAAC,OAAO,WAAW,CAAC;AAC9C,MAAM,eAAe,CAAC,KAAK,KAAK;IAC5B,IAAI,IAAI,GAAG,KAAK,aAAa,IAAI,GAAG,KAAK,OAAO;QAC5C,MAAM,IAAI,UAAU;IACxB;IACA,IAAI,IAAI,OAAO,KAAK,aAAa,IAAI,OAAO,CAAC,QAAQ,GAAG,WAAW,MAAM;QACrE,MAAM,IAAI,UAAU,CAAC,sEAAsE,EAAE,OAAO;IACxG;IACA,IAAI,IAAI,GAAG,KAAK,aAAa,IAAI,GAAG,KAAK,KAAK;QAC1C,MAAM,IAAI,UAAU,CAAC,6DAA6D,EAAE,KAAK;IAC7F;IACA,OAAO;AACX;AACA,MAAM,qBAAqB,CAAC,KAAK,KAAK,OAAO;IACzC,IAAI,eAAe,YACf;IACJ,IAAI,YAAY,CAAA,GAAA,8MAAA,CAAA,QAAS,AAAD,EAAE,MAAM;QAC5B,IAAI,CAAA,GAAA,8MAAA,CAAA,cAAe,AAAD,EAAE,QAAQ,aAAa,KAAK,KAAK,QAC/C;QACJ,MAAM,IAAI,UAAU,CAAC,uHAAuH,CAAC;IACjJ;IACA,IAAI,CAAC,CAAA,GAAA,uNAAA,CAAA,UAAS,AAAD,EAAE,MAAM;QACjB,MAAM,IAAI,UAAU,CAAA,GAAA,yNAAA,CAAA,UAAe,AAAD,EAAE,KAAK,QAAQ,uNAAA,CAAA,QAAK,EAAE,cAAc,WAAW,iBAAiB;IACtG;IACA,IAAI,IAAI,IAAI,KAAK,UAAU;QACvB,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,4DAA4D,CAAC;IACjG;AACJ;AACA,MAAM,sBAAsB,CAAC,KAAK,KAAK,OAAO;IAC1C,IAAI,YAAY,CAAA,GAAA,8MAAA,CAAA,QAAS,AAAD,EAAE,MAAM;QAC5B,OAAQ;YACJ,KAAK;gBACD,IAAI,CAAA,GAAA,8MAAA,CAAA,eAAgB,AAAD,EAAE,QAAQ,aAAa,KAAK,KAAK,QAChD;gBACJ,MAAM,IAAI,UAAU,CAAC,gDAAgD,CAAC;YAC1E,KAAK;gBACD,IAAI,CAAA,GAAA,8MAAA,CAAA,cAAe,AAAD,EAAE,QAAQ,aAAa,KAAK,KAAK,QAC/C;gBACJ,MAAM,IAAI,UAAU,CAAC,+CAA+C,CAAC;QAC7E;IACJ;IACA,IAAI,CAAC,CAAA,GAAA,uNAAA,CAAA,UAAS,AAAD,EAAE,MAAM;QACjB,MAAM,IAAI,UAAU,CAAA,GAAA,yNAAA,CAAA,UAAe,AAAD,EAAE,KAAK,QAAQ,uNAAA,CAAA,QAAK,EAAE,WAAW,iBAAiB;IACxF;IACA,IAAI,IAAI,IAAI,KAAK,UAAU;QACvB,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,iEAAiE,CAAC;IACtG;IACA,IAAI,UAAU,UAAU,IAAI,IAAI,KAAK,UAAU;QAC3C,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,qEAAqE,CAAC;IAC1G;IACA,IAAI,UAAU,aAAa,IAAI,IAAI,KAAK,UAAU;QAC9C,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,wEAAwE,CAAC;IAC7G;IACA,IAAI,IAAI,SAAS,IAAI,UAAU,YAAY,IAAI,IAAI,KAAK,WAAW;QAC/D,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,sEAAsE,CAAC;IAC3G;IACA,IAAI,IAAI,SAAS,IAAI,UAAU,aAAa,IAAI,IAAI,KAAK,WAAW;QAChE,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,uEAAuE,CAAC;IAC5G;AACJ;AACA,SAAS,aAAa,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK;IAC3C,MAAM,YAAY,IAAI,UAAU,CAAC,SAC7B,QAAQ,SACR,IAAI,UAAU,CAAC,YACf,qBAAqB,IAAI,CAAC;IAC9B,IAAI,WAAW;QACX,mBAAmB,KAAK,KAAK,OAAO;IACxC,OACK;QACD,oBAAoB,KAAK,KAAK,OAAO;IACzC;AACJ;uCACe,aAAa,IAAI,CAAC,WAAW;AACrC,MAAM,sBAAsB,aAAa,IAAI,CAAC,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1040, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/validate_crit.js"], "sourcesContent": ["import { JOSENotSupported } from '../util/errors.js';\nfunction validateCrit(Err, recognizedDefault, recognizedOption, protectedHeader, joseHeader) {\n    if (joseHeader.crit !== undefined && protectedHeader?.crit === undefined) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be integrity protected');\n    }\n    if (!protectedHeader || protectedHeader.crit === undefined) {\n        return new Set();\n    }\n    if (!Array.isArray(protectedHeader.crit) ||\n        protectedHeader.crit.length === 0 ||\n        protectedHeader.crit.some((input) => typeof input !== 'string' || input.length === 0)) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be an array of non-empty strings when present');\n    }\n    let recognized;\n    if (recognizedOption !== undefined) {\n        recognized = new Map([...Object.entries(recognizedOption), ...recognizedDefault.entries()]);\n    }\n    else {\n        recognized = recognizedDefault;\n    }\n    for (const parameter of protectedHeader.crit) {\n        if (!recognized.has(parameter)) {\n            throw new JOSENotSupported(`Extension Header Parameter \"${parameter}\" is not recognized`);\n        }\n        if (joseHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" is missing`);\n        }\n        if (recognized.get(parameter) && protectedHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" MUST be integrity protected`);\n        }\n    }\n    return new Set(protectedHeader.crit);\n}\nexport default validateCrit;\n"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,aAAa,GAAG,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAE,UAAU;IACvF,IAAI,WAAW,IAAI,KAAK,aAAa,iBAAiB,SAAS,WAAW;QACtE,MAAM,IAAI,IAAI;IAClB;IACA,IAAI,CAAC,mBAAmB,gBAAgB,IAAI,KAAK,WAAW;QACxD,OAAO,IAAI;IACf;IACA,IAAI,CAAC,MAAM,OAAO,CAAC,gBAAgB,IAAI,KACnC,gBAAgB,IAAI,CAAC,MAAM,KAAK,KAChC,gBAAgB,IAAI,CAAC,IAAI,CAAC,CAAC,QAAU,OAAO,UAAU,YAAY,MAAM,MAAM,KAAK,IAAI;QACvF,MAAM,IAAI,IAAI;IAClB;IACA,IAAI;IACJ,IAAI,qBAAqB,WAAW;QAChC,aAAa,IAAI,IAAI;eAAI,OAAO,OAAO,CAAC;eAAsB,kBAAkB,OAAO;SAAG;IAC9F,OACK;QACD,aAAa;IACjB;IACA,KAAK,MAAM,aAAa,gBAAgB,IAAI,CAAE;QAC1C,IAAI,CAAC,WAAW,GAAG,CAAC,YAAY;YAC5B,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC,CAAC,4BAA4B,EAAE,UAAU,mBAAmB,CAAC;QAC5F;QACA,IAAI,UAAU,CAAC,UAAU,KAAK,WAAW;YACrC,MAAM,IAAI,IAAI,CAAC,4BAA4B,EAAE,UAAU,YAAY,CAAC;QACxE;QACA,IAAI,WAAW,GAAG,CAAC,cAAc,eAAe,CAAC,UAAU,KAAK,WAAW;YACvE,MAAM,IAAI,IAAI,CAAC,4BAA4B,EAAE,UAAU,6BAA6B,CAAC;QACzF;IACJ;IACA,OAAO,IAAI,IAAI,gBAAgB,IAAI;AACvC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1084, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/jws/flattened/sign.js"], "sourcesContent": ["import { encode as base64url } from '../../runtime/base64url.js';\nimport sign from '../../runtime/sign.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport { JWSInvalid } from '../../util/errors.js';\nimport { encoder, decoder, concat } from '../../lib/buffer_utils.js';\nimport { checkKeyTypeWithJwk } from '../../lib/check_key_type.js';\nimport validateCrit from '../../lib/validate_crit.js';\nexport class FlattenedSign {\n    _payload;\n    _protectedHeader;\n    _unprotectedHeader;\n    constructor(payload) {\n        if (!(payload instanceof Uint8Array)) {\n            throw new TypeError('payload must be an instance of Uint8Array');\n        }\n        this._payload = payload;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this._unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this._unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        if (!this._protectedHeader && !this._unprotectedHeader) {\n            throw new JWSInvalid('either setProtectedHeader or setUnprotectedHeader must be called before #sign()');\n        }\n        if (!isDisjoint(this._protectedHeader, this._unprotectedHeader)) {\n            throw new JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this._protectedHeader,\n            ...this._unprotectedHeader,\n        };\n        const extensions = validateCrit(JWSInvalid, new Map([['b64', true]]), options?.crit, this._protectedHeader, joseHeader);\n        let b64 = true;\n        if (extensions.has('b64')) {\n            b64 = this._protectedHeader.b64;\n            if (typeof b64 !== 'boolean') {\n                throw new JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n            }\n        }\n        const { alg } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        checkKeyTypeWithJwk(alg, key, 'sign');\n        let payload = this._payload;\n        if (b64) {\n            payload = encoder.encode(base64url(payload));\n        }\n        let protectedHeader;\n        if (this._protectedHeader) {\n            protectedHeader = encoder.encode(base64url(JSON.stringify(this._protectedHeader)));\n        }\n        else {\n            protectedHeader = encoder.encode('');\n        }\n        const data = concat(protectedHeader, encoder.encode('.'), payload);\n        const signature = await sign(alg, key, data);\n        const jws = {\n            signature: base64url(signature),\n            payload: '',\n        };\n        if (b64) {\n            jws.payload = decoder.decode(payload);\n        }\n        if (this._unprotectedHeader) {\n            jws.header = this._unprotectedHeader;\n        }\n        if (this._protectedHeader) {\n            jws.protected = decoder.decode(protectedHeader);\n        }\n        return jws;\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACO,MAAM;IACT,SAAS;IACT,iBAAiB;IACjB,mBAAmB;IACnB,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,CAAC,mBAAmB,UAAU,GAAG;YAClC,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,mBAAmB,eAAe,EAAE;QAChC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,gBAAgB,GAAG;QACxB,OAAO,IAAI;IACf;IACA,qBAAqB,iBAAiB,EAAE;QACpC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,kBAAkB,GAAG;QAC1B,OAAO,IAAI;IACf;IACA,MAAM,KAAK,GAAG,EAAE,OAAO,EAAE;QACrB,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACpD,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;QACzB;QACA,IAAI,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAU,AAAD,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,GAAG;YAC7D,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;QACzB;QACA,MAAM,aAAa;YACf,GAAG,IAAI,CAAC,gBAAgB;YACxB,GAAG,IAAI,CAAC,kBAAkB;QAC9B;QACA,MAAM,aAAa,CAAA,GAAA,qNAAA,CAAA,UAAY,AAAD,EAAE,+MAAA,CAAA,aAAU,EAAE,IAAI,IAAI;YAAC;gBAAC;gBAAO;aAAK;SAAC,GAAG,SAAS,MAAM,IAAI,CAAC,gBAAgB,EAAE;QAC5G,IAAI,MAAM;QACV,IAAI,WAAW,GAAG,CAAC,QAAQ;YACvB,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG;YAC/B,IAAI,OAAO,QAAQ,WAAW;gBAC1B,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;YACzB;QACJ;QACA,MAAM,EAAE,GAAG,EAAE,GAAG;QAChB,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;YACjC,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;QACzB;QACA,CAAA,GAAA,sNAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,KAAK;QAC9B,IAAI,UAAU,IAAI,CAAC,QAAQ;QAC3B,IAAI,KAAK;YACL,UAAU,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE;QACvC;QACA,IAAI;QACJ,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,kBAAkB,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,KAAK,SAAS,CAAC,IAAI,CAAC,gBAAgB;QACnF,OACK;YACD,kBAAkB,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACrC;QACA,MAAM,OAAO,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,iBAAiB,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC,MAAM;QAC1D,MAAM,YAAY,MAAM,CAAA,GAAA,gNAAA,CAAA,UAAI,AAAD,EAAE,KAAK,KAAK;QACvC,MAAM,MAAM;YACR,WAAW,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE;YACrB,SAAS;QACb;QACA,IAAI,KAAK;YACL,IAAI,OAAO,GAAG,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACjC;QACA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,MAAM,GAAG,IAAI,CAAC,kBAAkB;QACxC;QACA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,SAAS,GAAG,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACnC;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/jws/compact/sign.js"], "sourcesContent": ["import { FlattenedSign } from '../flattened/sign.js';\nexport class CompactSign {\n    _flattened;\n    constructor(payload) {\n        this._flattened = new FlattenedSign(payload);\n    }\n    setProtectedHeader(protectedHeader) {\n        this._flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    async sign(key, options) {\n        const jws = await this._flattened.sign(key, options);\n        if (jws.payload === undefined) {\n            throw new TypeError('use the flattened module for creating JWS with b64: false');\n        }\n        return `${jws.protected}.${jws.payload}.${jws.signature}`;\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM;IACT,WAAW;IACX,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,UAAU,GAAG,IAAI,yNAAA,CAAA,gBAAa,CAAC;IACxC;IACA,mBAAmB,eAAe,EAAE;QAChC,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;QACnC,OAAO,IAAI;IACf;IACA,MAAM,KAAK,GAAG,EAAE,OAAO,EAAE;QACrB,MAAM,MAAM,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK;QAC5C,IAAI,IAAI,OAAO,KAAK,WAAW;YAC3B,MAAM,IAAI,UAAU;QACxB;QACA,OAAO,GAAG,IAAI,SAAS,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,SAAS,EAAE;IAC7D;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1216, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/util/base64url.js"], "sourcesContent": ["import * as base64url from '../runtime/base64url.js';\nexport const encode = base64url.encode;\nexport const decode = base64url.decode;\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,SAAS,qNAAA,CAAA,SAAgB;AAC/B,MAAM,SAAS,qNAAA,CAAA,SAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/verify.js"], "sourcesContent": ["import * as crypto from 'node:crypto';\nimport { promisify } from 'node:util';\nimport nodeDigest from './dsa_digest.js';\nimport nodeKey from './node_key.js';\nimport sign from './sign.js';\nimport getVerifyKey from './get_sign_verify_key.js';\nconst oneShotVerify = promisify(crypto.verify);\nconst verify = async (alg, key, signature, data) => {\n    const k = getVerifyKey(alg, key, 'verify');\n    if (alg.startsWith('HS')) {\n        const expected = await sign(alg, k, data);\n        const actual = signature;\n        try {\n            return crypto.timingSafeEqual(actual, expected);\n        }\n        catch {\n            return false;\n        }\n    }\n    const algorithm = nodeDigest(alg);\n    const keyInput = nodeKey(alg, k);\n    try {\n        return await oneShotVerify(algorithm, data, keyInput, signature);\n    }\n    catch {\n        return false;\n    }\n};\nexport default verify;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA,MAAM,gBAAgB,CAAA,GAAA,iHAAA,CAAA,YAAS,AAAD,EAAE,qHAAA,CAAA,SAAa;AAC7C,MAAM,SAAS,OAAO,KAAK,KAAK,WAAW;IACvC,MAAM,IAAI,CAAA,GAAA,+NAAA,CAAA,UAAY,AAAD,EAAE,KAAK,KAAK;IACjC,IAAI,IAAI,UAAU,CAAC,OAAO;QACtB,MAAM,WAAW,MAAM,CAAA,GAAA,gNAAA,CAAA,UAAI,AAAD,EAAE,KAAK,GAAG;QACpC,MAAM,SAAS;QACf,IAAI;YACA,OAAO,CAAA,GAAA,qHAAA,CAAA,kBAAsB,AAAD,EAAE,QAAQ;QAC1C,EACA,OAAM;YACF,OAAO;QACX;IACJ;IACA,MAAM,YAAY,CAAA,GAAA,sNAAA,CAAA,UAAU,AAAD,EAAE;IAC7B,MAAM,WAAW,CAAA,GAAA,oNAAA,CAAA,UAAO,AAAD,EAAE,KAAK;IAC9B,IAAI;QACA,OAAO,MAAM,cAAc,WAAW,MAAM,UAAU;IAC1D,EACA,OAAM;QACF,OAAO;IACX;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1282, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/validate_algorithms.js"], "sourcesContent": ["const validateAlgorithms = (option, algorithms) => {\n    if (algorithms !== undefined &&\n        (!Array.isArray(algorithms) || algorithms.some((s) => typeof s !== 'string'))) {\n        throw new TypeError(`\"${option}\" option must be an array of strings`);\n    }\n    if (!algorithms) {\n        return undefined;\n    }\n    return new Set(algorithms);\n};\nexport default validateAlgorithms;\n"], "names": [], "mappings": ";;;AAAA,MAAM,qBAAqB,CAAC,QAAQ;IAChC,IAAI,eAAe,aACf,CAAC,CAAC,MAAM,OAAO,CAAC,eAAe,WAAW,IAAI,CAAC,CAAC,IAAM,OAAO,MAAM,SAAS,GAAG;QAC/E,MAAM,IAAI,UAAU,CAAC,CAAC,EAAE,OAAO,oCAAoC,CAAC;IACxE;IACA,IAAI,CAAC,YAAY;QACb,OAAO;IACX;IACA,OAAO,IAAI,IAAI;AACnB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/asn1.js"], "sourcesContent": ["import { createPrivate<PERSON><PERSON>, create<PERSON><PERSON><PERSON><PERSON><PERSON>, KeyObject } from 'node:crypto';\nimport { <PERSON>uffer } from 'node:buffer';\nimport { isCryptoKey } from './webcrypto.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport { types } from './is_key_like.js';\nconst genericExport = (keyType, keyFormat, key) => {\n    let keyObject;\n    if (isCryptoKey(key)) {\n        if (!key.extractable) {\n            throw new TypeError('CryptoKey is not extractable');\n        }\n        keyObject = KeyObject.from(key);\n    }\n    else if (isKeyObject(key)) {\n        keyObject = key;\n    }\n    else {\n        throw new TypeError(invalidKeyInput(key, ...types));\n    }\n    if (keyObject.type !== keyType) {\n        throw new TypeError(`key is not a ${keyType} key`);\n    }\n    return keyObject.export({ format: 'pem', type: keyFormat });\n};\nexport const toSPKI = (key) => {\n    return genericExport('public', 'spki', key);\n};\nexport const toPKCS8 = (key) => {\n    return genericExport('private', 'pkcs8', key);\n};\nexport const fromPKCS8 = (pem) => createPrivateKey({\n    key: Buffer.from(pem.replace(/(?:-----(?:BEGIN|END) PRIVATE KEY-----|\\s)/g, ''), 'base64'),\n    type: 'pkcs8',\n    format: 'der',\n});\nexport const fromSPKI = (pem) => createPublicKey({\n    key: Buffer.from(pem.replace(/(?:-----(?:BEGIN|END) PUBLIC KEY-----|\\s)/g, ''), 'base64'),\n    type: 'spki',\n    format: 'der',\n});\nexport const fromX509 = (pem) => createPublicKey({\n    key: pem,\n    type: 'spki',\n    format: 'pem',\n});\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA,MAAM,gBAAgB,CAAC,SAAS,WAAW;IACvC,IAAI;IACJ,IAAI,CAAA,GAAA,qNAAA,CAAA,cAAW,AAAD,EAAE,MAAM;QAClB,IAAI,CAAC,IAAI,WAAW,EAAE;YAClB,MAAM,IAAI,UAAU;QACxB;QACA,YAAY,qHAAA,CAAA,YAAS,CAAC,IAAI,CAAC;IAC/B,OACK,IAAI,CAAA,GAAA,yNAAA,CAAA,UAAW,AAAD,EAAE,MAAM;QACvB,YAAY;IAChB,OACK;QACD,MAAM,IAAI,UAAU,CAAA,GAAA,yNAAA,CAAA,UAAe,AAAD,EAAE,QAAQ,uNAAA,CAAA,QAAK;IACrD;IACA,IAAI,UAAU,IAAI,KAAK,SAAS;QAC5B,MAAM,IAAI,UAAU,CAAC,aAAa,EAAE,QAAQ,IAAI,CAAC;IACrD;IACA,OAAO,UAAU,MAAM,CAAC;QAAE,QAAQ;QAAO,MAAM;IAAU;AAC7D;AACO,MAAM,SAAS,CAAC;IACnB,OAAO,cAAc,UAAU,QAAQ;AAC3C;AACO,MAAM,UAAU,CAAC;IACpB,OAAO,cAAc,WAAW,SAAS;AAC7C;AACO,MAAM,YAAY,CAAC,MAAQ,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD,EAAE;QAC/C,KAAK,qHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,+CAA+C,KAAK;QACjF,MAAM;QACN,QAAQ;IACZ;AACO,MAAM,WAAW,CAAC,MAAQ,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAAE;QAC7C,KAAK,qHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,8CAA8C,KAAK;QAChF,MAAM;QACN,QAAQ;IACZ;AACO,MAAM,WAAW,CAAC,MAAQ,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAAE;QAC7C,KAAK;QACL,MAAM;QACN,QAAQ;IACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1367, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/jwk_to_key.js"], "sourcesContent": ["import { createPrivate<PERSON><PERSON>, createP<PERSON><PERSON><PERSON><PERSON> } from 'node:crypto';\nconst parse = (key) => {\n    if (key.d) {\n        return createPrivateKey({ format: 'jwk', key });\n    }\n    return createPublicKey({ format: 'jwk', key });\n};\nexport default parse;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,QAAQ,CAAC;IACX,IAAI,IAAI,CAAC,EAAE;QACP,OAAO,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD,EAAE;YAAE,QAAQ;YAAO;QAAI;IACjD;IACA,OAAO,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ;QAAO;IAAI;AAChD;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1391, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/key/import.js"], "sourcesContent": ["import { decode as decodeBase64URL } from '../runtime/base64url.js';\nimport { fromSPKI, fromPKCS8, fromX509 } from '../runtime/asn1.js';\nimport asKeyObject from '../runtime/jwk_to_key.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport isObject from '../lib/is_object.js';\nexport async function importSPKI(spki, alg, options) {\n    if (typeof spki !== 'string' || spki.indexOf('-----BEGIN PUBLIC KEY-----') !== 0) {\n        throw new TypeError('\"spki\" must be SPKI formatted string');\n    }\n    return fromSPKI(spki, alg, options);\n}\nexport async function importX509(x509, alg, options) {\n    if (typeof x509 !== 'string' || x509.indexOf('-----BEGIN CERTIFICATE-----') !== 0) {\n        throw new TypeError('\"x509\" must be X.509 formatted string');\n    }\n    return fromX509(x509, alg, options);\n}\nexport async function importPKCS8(pkcs8, alg, options) {\n    if (typeof pkcs8 !== 'string' || pkcs8.indexOf('-----BEGIN PRIVATE KEY-----') !== 0) {\n        throw new TypeError('\"pkcs8\" must be PKCS#8 formatted string');\n    }\n    return fromPKCS8(pkcs8, alg, options);\n}\nexport async function importJWK(jwk, alg) {\n    if (!isObject(jwk)) {\n        throw new TypeError('JWK must be an object');\n    }\n    alg ||= jwk.alg;\n    switch (jwk.kty) {\n        case 'oct':\n            if (typeof jwk.k !== 'string' || !jwk.k) {\n                throw new TypeError('missing \"k\" (Key Value) Parameter value');\n            }\n            return decodeBase64URL(jwk.k);\n        case 'RSA':\n            if ('oth' in jwk && jwk.oth !== undefined) {\n                throw new JOSENotSupported('RSA JWK \"oth\" (Other Primes Info) Parameter value is not supported');\n            }\n        case 'EC':\n        case 'OKP':\n            return asKeyObject({ ...jwk, alg });\n        default:\n            throw new JOSENotSupported('Unsupported \"kty\" (Key Type) Parameter value');\n    }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACO,eAAe,WAAW,IAAI,EAAE,GAAG,EAAE,OAAO;IAC/C,IAAI,OAAO,SAAS,YAAY,KAAK,OAAO,CAAC,kCAAkC,GAAG;QAC9E,MAAM,IAAI,UAAU;IACxB;IACA,OAAO,CAAA,GAAA,gNAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,KAAK;AAC/B;AACO,eAAe,WAAW,IAAI,EAAE,GAAG,EAAE,OAAO;IAC/C,IAAI,OAAO,SAAS,YAAY,KAAK,OAAO,CAAC,mCAAmC,GAAG;QAC/E,MAAM,IAAI,UAAU;IACxB;IACA,OAAO,CAAA,GAAA,gNAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,KAAK;AAC/B;AACO,eAAe,YAAY,KAAK,EAAE,GAAG,EAAE,OAAO;IACjD,IAAI,OAAO,UAAU,YAAY,MAAM,OAAO,CAAC,mCAAmC,GAAG;QACjF,MAAM,IAAI,UAAU;IACxB;IACA,OAAO,CAAA,GAAA,gNAAA,CAAA,YAAS,AAAD,EAAE,OAAO,KAAK;AACjC;AACO,eAAe,UAAU,GAAG,EAAE,GAAG;IACpC,IAAI,CAAC,CAAA,GAAA,iNAAA,CAAA,UAAQ,AAAD,EAAE,MAAM;QAChB,MAAM,IAAI,UAAU;IACxB;IACA,QAAQ,IAAI,GAAG;IACf,OAAQ,IAAI,GAAG;QACX,KAAK;YACD,IAAI,OAAO,IAAI,CAAC,KAAK,YAAY,CAAC,IAAI,CAAC,EAAE;gBACrC,MAAM,IAAI,UAAU;YACxB;YACA,OAAO,CAAA,GAAA,qNAAA,CAAA,SAAe,AAAD,EAAE,IAAI,CAAC;QAChC,KAAK;YACD,IAAI,SAAS,OAAO,IAAI,GAAG,KAAK,WAAW;gBACvC,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC;YAC/B;QACJ,KAAK;QACL,KAAK;YACD,OAAO,CAAA,GAAA,sNAAA,CAAA,UAAW,AAAD,EAAE;gBAAE,GAAG,GAAG;gBAAE;YAAI;QACrC;YACI,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC;IACnC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1456, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/jws/flattened/verify.js"], "sourcesContent": ["import { decode as base64url } from '../../runtime/base64url.js';\nimport verify from '../../runtime/verify.js';\nimport { JOSEAlgNotAllowed, JWSInvalid, JWSSignatureVerificationFailed } from '../../util/errors.js';\nimport { concat, encoder, decoder } from '../../lib/buffer_utils.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport isObject from '../../lib/is_object.js';\nimport { checkKeyTypeWithJwk } from '../../lib/check_key_type.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport validateAlgorithms from '../../lib/validate_algorithms.js';\nimport { isJWK } from '../../lib/is_jwk.js';\nimport { importJWK } from '../../key/import.js';\nexport async function flattenedVerify(jws, key, options) {\n    if (!isObject(jws)) {\n        throw new JWSInvalid('Flattened JWS must be an object');\n    }\n    if (jws.protected === undefined && jws.header === undefined) {\n        throw new JWSInvalid('Flattened JWS must have either of the \"protected\" or \"header\" members');\n    }\n    if (jws.protected !== undefined && typeof jws.protected !== 'string') {\n        throw new JWSInvalid('JWS Protected Header incorrect type');\n    }\n    if (jws.payload === undefined) {\n        throw new JWSInvalid('JWS Payload missing');\n    }\n    if (typeof jws.signature !== 'string') {\n        throw new JWSInvalid('JWS Signature missing or incorrect type');\n    }\n    if (jws.header !== undefined && !isObject(jws.header)) {\n        throw new JWSInvalid('JWS Unprotected Header incorrect type');\n    }\n    let parsedProt = {};\n    if (jws.protected) {\n        try {\n            const protectedHeader = base64url(jws.protected);\n            parsedProt = JSON.parse(decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new JWSInvalid('JWS Protected Header is invalid');\n        }\n    }\n    if (!isDisjoint(parsedProt, jws.header)) {\n        throw new JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jws.header,\n    };\n    const extensions = validateCrit(JWSInvalid, new Map([['b64', true]]), options?.crit, parsedProt, joseHeader);\n    let b64 = true;\n    if (extensions.has('b64')) {\n        b64 = parsedProt.b64;\n        if (typeof b64 !== 'boolean') {\n            throw new JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n        }\n    }\n    const { alg } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n    }\n    const algorithms = options && validateAlgorithms('algorithms', options.algorithms);\n    if (algorithms && !algorithms.has(alg)) {\n        throw new JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter value not allowed');\n    }\n    if (b64) {\n        if (typeof jws.payload !== 'string') {\n            throw new JWSInvalid('JWS Payload must be a string');\n        }\n    }\n    else if (typeof jws.payload !== 'string' && !(jws.payload instanceof Uint8Array)) {\n        throw new JWSInvalid('JWS Payload must be a string or an Uint8Array instance');\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jws);\n        resolvedKey = true;\n        checkKeyTypeWithJwk(alg, key, 'verify');\n        if (isJWK(key)) {\n            key = await importJWK(key, alg);\n        }\n    }\n    else {\n        checkKeyTypeWithJwk(alg, key, 'verify');\n    }\n    const data = concat(encoder.encode(jws.protected ?? ''), encoder.encode('.'), typeof jws.payload === 'string' ? encoder.encode(jws.payload) : jws.payload);\n    let signature;\n    try {\n        signature = base64url(jws.signature);\n    }\n    catch {\n        throw new JWSInvalid('Failed to base64url decode the signature');\n    }\n    const verified = await verify(alg, key, signature, data);\n    if (!verified) {\n        throw new JWSSignatureVerificationFailed();\n    }\n    let payload;\n    if (b64) {\n        try {\n            payload = base64url(jws.payload);\n        }\n        catch {\n            throw new JWSInvalid('Failed to base64url decode the payload');\n        }\n    }\n    else if (typeof jws.payload === 'string') {\n        payload = encoder.encode(jws.payload);\n    }\n    else {\n        payload = jws.payload;\n    }\n    const result = { payload };\n    if (jws.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jws.header !== undefined) {\n        result.unprotectedHeader = jws.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key };\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AACO,eAAe,gBAAgB,GAAG,EAAE,GAAG,EAAE,OAAO;IACnD,IAAI,CAAC,CAAA,GAAA,iNAAA,CAAA,UAAQ,AAAD,EAAE,MAAM;QAChB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,IAAI,SAAS,KAAK,aAAa,IAAI,MAAM,KAAK,WAAW;QACzD,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,IAAI,SAAS,KAAK,aAAa,OAAO,IAAI,SAAS,KAAK,UAAU;QAClE,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,IAAI,OAAO,KAAK,WAAW;QAC3B,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,OAAO,IAAI,SAAS,KAAK,UAAU;QACnC,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,IAAI,MAAM,KAAK,aAAa,CAAC,CAAA,GAAA,iNAAA,CAAA,UAAQ,AAAD,EAAE,IAAI,MAAM,GAAG;QACnD,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,aAAa,CAAC;IAClB,IAAI,IAAI,SAAS,EAAE;QACf,IAAI;YACA,MAAM,kBAAkB,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,IAAI,SAAS;YAC/C,aAAa,KAAK,KAAK,CAAC,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QAC3C,EACA,OAAM;YACF,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;QACzB;IACJ;IACA,IAAI,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAU,AAAD,EAAE,YAAY,IAAI,MAAM,GAAG;QACrC,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,aAAa;QACf,GAAG,UAAU;QACb,GAAG,IAAI,MAAM;IACjB;IACA,MAAM,aAAa,CAAA,GAAA,qNAAA,CAAA,UAAY,AAAD,EAAE,+MAAA,CAAA,aAAU,EAAE,IAAI,IAAI;QAAC;YAAC;YAAO;SAAK;KAAC,GAAG,SAAS,MAAM,YAAY;IACjG,IAAI,MAAM;IACV,IAAI,WAAW,GAAG,CAAC,QAAQ;QACvB,MAAM,WAAW,GAAG;QACpB,IAAI,OAAO,QAAQ,WAAW;YAC1B,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;QACzB;IACJ;IACA,MAAM,EAAE,GAAG,EAAE,GAAG;IAChB,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;QACjC,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,aAAa,WAAW,CAAA,GAAA,2NAAA,CAAA,UAAkB,AAAD,EAAE,cAAc,QAAQ,UAAU;IACjF,IAAI,cAAc,CAAC,WAAW,GAAG,CAAC,MAAM;QACpC,MAAM,IAAI,+MAAA,CAAA,oBAAiB,CAAC;IAChC;IACA,IAAI,KAAK;QACL,IAAI,OAAO,IAAI,OAAO,KAAK,UAAU;YACjC,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;QACzB;IACJ,OACK,IAAI,OAAO,IAAI,OAAO,KAAK,YAAY,CAAC,CAAC,IAAI,OAAO,YAAY,UAAU,GAAG;QAC9E,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,cAAc;IAClB,IAAI,OAAO,QAAQ,YAAY;QAC3B,MAAM,MAAM,IAAI,YAAY;QAC5B,cAAc;QACd,CAAA,GAAA,sNAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,KAAK;QAC9B,IAAI,CAAA,GAAA,8MAAA,CAAA,QAAK,AAAD,EAAE,MAAM;YACZ,MAAM,MAAM,CAAA,GAAA,8MAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAC/B;IACJ,OACK;QACD,CAAA,GAAA,sNAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,KAAK;IAClC;IACA,MAAM,OAAO,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC,IAAI,SAAS,IAAI,KAAK,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC,MAAM,OAAO,IAAI,OAAO,KAAK,WAAW,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC,IAAI,OAAO,IAAI,IAAI,OAAO;IACzJ,IAAI;IACJ,IAAI;QACA,YAAY,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,IAAI,SAAS;IACvC,EACA,OAAM;QACF,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,WAAW,MAAM,CAAA,GAAA,kNAAA,CAAA,UAAM,AAAD,EAAE,KAAK,KAAK,WAAW;IACnD,IAAI,CAAC,UAAU;QACX,MAAM,IAAI,+MAAA,CAAA,iCAA8B;IAC5C;IACA,IAAI;IACJ,IAAI,KAAK;QACL,IAAI;YACA,UAAU,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,IAAI,OAAO;QACnC,EACA,OAAM;YACF,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;QACzB;IACJ,OACK,IAAI,OAAO,IAAI,OAAO,KAAK,UAAU;QACtC,UAAU,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC,IAAI,OAAO;IACxC,OACK;QACD,UAAU,IAAI,OAAO;IACzB;IACA,MAAM,SAAS;QAAE;IAAQ;IACzB,IAAI,IAAI,SAAS,KAAK,WAAW;QAC7B,OAAO,eAAe,GAAG;IAC7B;IACA,IAAI,IAAI,MAAM,KAAK,WAAW;QAC1B,OAAO,iBAAiB,GAAG,IAAI,MAAM;IACzC;IACA,IAAI,aAAa;QACb,OAAO;YAAE,GAAG,MAAM;YAAE;QAAI;IAC5B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1601, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/jws/compact/verify.js"], "sourcesContent": ["import { flattenedVerify } from '../flattened/verify.js';\nimport { JWSInvalid } from '../../util/errors.js';\nimport { decoder } from '../../lib/buffer_utils.js';\nexport async function compactVerify(jws, key, options) {\n    if (jws instanceof Uint8Array) {\n        jws = decoder.decode(jws);\n    }\n    if (typeof jws !== 'string') {\n        throw new JWSInvalid('Compact JWS must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: payload, 2: signature, length } = jws.split('.');\n    if (length !== 3) {\n        throw new JWSInvalid('Invalid Compact JWS');\n    }\n    const verified = await flattenedVerify({ payload, protected: protectedHeader, signature }, key, options);\n    const result = { payload: verified.payload, protectedHeader: verified.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: verified.key };\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,eAAe,cAAc,GAAG,EAAE,GAAG,EAAE,OAAO;IACjD,IAAI,eAAe,YAAY;QAC3B,MAAM,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC;IACzB;IACA,IAAI,OAAO,QAAQ,UAAU;QACzB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,EAAE,GAAG,eAAe,EAAE,GAAG,OAAO,EAAE,GAAG,SAAS,EAAE,MAAM,EAAE,GAAG,IAAI,KAAK,CAAC;IAC3E,IAAI,WAAW,GAAG;QACd,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,WAAW,MAAM,CAAA,GAAA,2NAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAS,WAAW;QAAiB;IAAU,GAAG,KAAK;IAChG,MAAM,SAAS;QAAE,SAAS,SAAS,OAAO;QAAE,iBAAiB,SAAS,eAAe;IAAC;IACtF,IAAI,OAAO,QAAQ,YAAY;QAC3B,OAAO;YAAE,GAAG,MAAM;YAAE,KAAK,SAAS,GAAG;QAAC;IAC1C;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1644, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/iv.js"], "sourcesContent": ["import { JOSENotSupported } from '../util/errors.js';\nimport random from '../runtime/random.js';\nexport function bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A128GCMKW':\n        case 'A192GCM':\n        case 'A192GCMKW':\n        case 'A256GCM':\n        case 'A256GCMKW':\n            return 96;\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return 128;\n        default:\n            throw new JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\nexport default (alg) => random(new Uint8Array(bitLength(alg) >> 3));\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,UAAU,GAAG;IACzB,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC,CAAC,2BAA2B,EAAE,KAAK;IACtE;AACJ;uCACe,CAAC,MAAQ,CAAA,GAAA,kKAAA,CAAA,UAAM,AAAD,EAAE,IAAI,WAAW,UAAU,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1676, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/check_iv_length.js"], "sourcesContent": ["import { JWEInvalid } from '../util/errors.js';\nimport { bitLength } from './iv.js';\nconst checkIvLength = (enc, iv) => {\n    if (iv.length << 3 !== bitLength(enc)) {\n        throw new JWEInvalid('Invalid Initialization Vector length');\n    }\n};\nexport default checkIvLength;\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,MAAM,gBAAgB,CAAC,KAAK;IACxB,IAAI,GAAG,MAAM,IAAI,MAAM,CAAA,GAAA,0MAAA,CAAA,YAAS,AAAD,EAAE,MAAM;QACnC,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1695, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/check_cek_length.js"], "sourcesContent": ["import { JWEInvalid, JOSENotSupported } from '../util/errors.js';\nimport isKeyObject from './is_key_object.js';\nconst checkCekLength = (enc, cek) => {\n    let expected;\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            expected = parseInt(enc.slice(-3), 10);\n            break;\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            expected = parseInt(enc.slice(1, 4), 10);\n            break;\n        default:\n            throw new JOSENotSupported(`Content Encryption Algorithm ${enc} is not supported either by JOSE or your javascript runtime`);\n    }\n    if (cek instanceof Uint8Array) {\n        const actual = cek.byteLength << 3;\n        if (actual !== expected) {\n            throw new JWEInvalid(`Invalid Content Encryption Key length. Expected ${expected} bits, got ${actual} bits`);\n        }\n        return;\n    }\n    if (isKeyObject(cek) && cek.type === 'secret') {\n        const actual = cek.symmetricKeySize << 3;\n        if (actual !== expected) {\n            throw new JWEInvalid(`Invalid Content Encryption Key length. Expected ${expected} bits, got ${actual} bits`);\n        }\n        return;\n    }\n    throw new TypeError('Invalid Content Encryption Key type');\n};\nexport default checkCekLength;\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,MAAM,iBAAiB,CAAC,KAAK;IACzB,IAAI;IACJ,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,WAAW,SAAS,IAAI,KAAK,CAAC,CAAC,IAAI;YACnC;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,WAAW,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;YACrC;QACJ;YACI,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC,CAAC,6BAA6B,EAAE,IAAI,2DAA2D,CAAC;IACnI;IACA,IAAI,eAAe,YAAY;QAC3B,MAAM,SAAS,IAAI,UAAU,IAAI;QACjC,IAAI,WAAW,UAAU;YACrB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC,CAAC,gDAAgD,EAAE,SAAS,WAAW,EAAE,OAAO,KAAK,CAAC;QAC/G;QACA;IACJ;IACA,IAAI,CAAA,GAAA,yNAAA,CAAA,UAAW,AAAD,EAAE,QAAQ,IAAI,IAAI,KAAK,UAAU;QAC3C,MAAM,SAAS,IAAI,gBAAgB,IAAI;QACvC,IAAI,WAAW,UAAU;YACrB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC,CAAC,gDAAgD,EAAE,SAAS,WAAW,EAAE,OAAO,KAAK,CAAC;QAC/G;QACA;IACJ;IACA,MAAM,IAAI,UAAU;AACxB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/timing_safe_equal.js"], "sourcesContent": ["import { timingSafeEqual as impl } from 'node:crypto';\nconst timingSafeEqual = impl;\nexport default timingSafeEqual;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,kBAAkB,qHAAA,CAAA,kBAAI;uCACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1754, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/cbc_tag.js"], "sourcesContent": ["import { createHmac } from 'node:crypto';\nimport { concat, uint64be } from '../lib/buffer_utils.js';\nexport default function cbcTag(aad, iv, ciphertext, macSize, macKey, keySize) {\n    const macData = concat(aad, iv, ciphertext, uint64be(aad.length << 3));\n    const hmac = createHmac(`sha${macSize}`, macKey);\n    hmac.update(macData);\n    return hmac.digest().slice(0, keySize >> 3);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACe,SAAS,OAAO,GAAG,EAAE,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO;IACxE,MAAM,UAAU,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,KAAK,IAAI,YAAY,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,MAAM,IAAI;IACnE,MAAM,OAAO,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD,EAAE,CAAC,GAAG,EAAE,SAAS,EAAE;IACzC,KAAK,MAAM,CAAC;IACZ,OAAO,KAAK,MAAM,GAAG,KAAK,CAAC,GAAG,WAAW;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1773, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/ciphers.js"], "sourcesContent": ["import { getCiphers } from 'node:crypto';\nlet ciphers;\nexport default (algorithm) => {\n    ciphers ||= new Set(getCiphers());\n    return ciphers.has(algorithm);\n};\n"], "names": [], "mappings": ";;;AAAA;;AACA,IAAI;uCACW,CAAC;IACZ,YAAY,IAAI,IAAI,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD;IAC7B,OAAO,QAAQ,GAAG,CAAC;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1789, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/decrypt.js"], "sourcesContent": ["import { createDecipheriv, KeyObject } from 'node:crypto';\nimport checkIvLength from '../lib/check_iv_length.js';\nimport checkCekLength from './check_cek_length.js';\nimport { concat } from '../lib/buffer_utils.js';\nimport { JOSENotSupported, JWEDecryptionFailed, JWEInvalid } from '../util/errors.js';\nimport timingSafeEqual from './timing_safe_equal.js';\nimport cbcTag from './cbc_tag.js';\nimport { isCryptoKey } from './webcrypto.js';\nimport { checkEncCryptoKey } from '../lib/crypto_key.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport supported from './ciphers.js';\nimport { types } from './is_key_like.js';\nfunction cbcDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    if (isKeyObject(cek)) {\n        cek = cek.export();\n    }\n    const encKey = cek.subarray(keySize >> 3);\n    const macKey = cek.subarray(0, keySize >> 3);\n    const macSize = parseInt(enc.slice(-3), 10);\n    const algorithm = `aes-${keySize}-cbc`;\n    if (!supported(algorithm)) {\n        throw new JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const expectedTag = cbcTag(aad, iv, ciphertext, macSize, macKey, keySize);\n    let macCheckPassed;\n    try {\n        macCheckPassed = timingSafeEqual(tag, expectedTag);\n    }\n    catch {\n    }\n    if (!macCheckPassed) {\n        throw new JWEDecryptionFailed();\n    }\n    let plaintext;\n    try {\n        const decipher = createDecipheriv(algorithm, encKey, iv);\n        plaintext = concat(decipher.update(ciphertext), decipher.final());\n    }\n    catch {\n    }\n    if (!plaintext) {\n        throw new JWEDecryptionFailed();\n    }\n    return plaintext;\n}\nfunction gcmDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const algorithm = `aes-${keySize}-gcm`;\n    if (!supported(algorithm)) {\n        throw new JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    try {\n        const decipher = createDecipheriv(algorithm, cek, iv, { authTagLength: 16 });\n        decipher.setAuthTag(tag);\n        if (aad.byteLength) {\n            decipher.setAAD(aad, { plaintextLength: ciphertext.length });\n        }\n        const plaintext = decipher.update(ciphertext);\n        decipher.final();\n        return plaintext;\n    }\n    catch {\n        throw new JWEDecryptionFailed();\n    }\n}\nconst decrypt = (enc, cek, ciphertext, iv, tag, aad) => {\n    let key;\n    if (isCryptoKey(cek)) {\n        checkEncCryptoKey(cek, enc, 'decrypt');\n        key = KeyObject.from(cek);\n    }\n    else if (cek instanceof Uint8Array || isKeyObject(cek)) {\n        key = cek;\n    }\n    else {\n        throw new TypeError(invalidKeyInput(cek, ...types, 'Uint8Array'));\n    }\n    if (!iv) {\n        throw new JWEInvalid('JWE Initialization Vector missing');\n    }\n    if (!tag) {\n        throw new JWEInvalid('JWE Authentication Tag missing');\n    }\n    checkCekLength(enc, key);\n    checkIvLength(enc, iv);\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return cbcDecrypt(enc, key, ciphertext, iv, tag, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            return gcmDecrypt(enc, key, ciphertext, iv, tag, aad);\n        default:\n            throw new JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n};\nexport default decrypt;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AACA,SAAS,WAAW,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG;IAClD,MAAM,UAAU,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IAC1C,IAAI,CAAA,GAAA,yNAAA,CAAA,UAAW,AAAD,EAAE,MAAM;QAClB,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,SAAS,IAAI,QAAQ,CAAC,WAAW;IACvC,MAAM,SAAS,IAAI,QAAQ,CAAC,GAAG,WAAW;IAC1C,MAAM,UAAU,SAAS,IAAI,KAAK,CAAC,CAAC,IAAI;IACxC,MAAM,YAAY,CAAC,IAAI,EAAE,QAAQ,IAAI,CAAC;IACtC,IAAI,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAS,AAAD,EAAE,YAAY;QACvB,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,4CAA4C,CAAC;IACvF;IACA,MAAM,cAAc,CAAA,GAAA,mNAAA,CAAA,UAAM,AAAD,EAAE,KAAK,IAAI,YAAY,SAAS,QAAQ;IACjE,IAAI;IACJ,IAAI;QACA,iBAAiB,CAAA,GAAA,6NAAA,CAAA,UAAe,AAAD,EAAE,KAAK;IAC1C,EACA,OAAM,CACN;IACA,IAAI,CAAC,gBAAgB;QACjB,MAAM,IAAI,+MAAA,CAAA,sBAAmB;IACjC;IACA,IAAI;IACJ,IAAI;QACA,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,QAAQ;QACrD,YAAY,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,SAAS,MAAM,CAAC,aAAa,SAAS,KAAK;IAClE,EACA,OAAM,CACN;IACA,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,+MAAA,CAAA,sBAAmB;IACjC;IACA,OAAO;AACX;AACA,SAAS,WAAW,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG;IAClD,MAAM,UAAU,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IAC1C,MAAM,YAAY,CAAC,IAAI,EAAE,QAAQ,IAAI,CAAC;IACtC,IAAI,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAS,AAAD,EAAE,YAAY;QACvB,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,4CAA4C,CAAC;IACvF;IACA,IAAI;QACA,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,KAAK,IAAI;YAAE,eAAe;QAAG;QAC1E,SAAS,UAAU,CAAC;QACpB,IAAI,IAAI,UAAU,EAAE;YAChB,SAAS,MAAM,CAAC,KAAK;gBAAE,iBAAiB,WAAW,MAAM;YAAC;QAC9D;QACA,MAAM,YAAY,SAAS,MAAM,CAAC;QAClC,SAAS,KAAK;QACd,OAAO;IACX,EACA,OAAM;QACF,MAAM,IAAI,+MAAA,CAAA,sBAAmB;IACjC;AACJ;AACA,MAAM,UAAU,CAAC,KAAK,KAAK,YAAY,IAAI,KAAK;IAC5C,IAAI;IACJ,IAAI,CAAA,GAAA,qNAAA,CAAA,cAAW,AAAD,EAAE,MAAM;QAClB,CAAA,GAAA,kNAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,KAAK;QAC5B,MAAM,qHAAA,CAAA,YAAS,CAAC,IAAI,CAAC;IACzB,OACK,IAAI,eAAe,cAAc,CAAA,GAAA,yNAAA,CAAA,UAAW,AAAD,EAAE,MAAM;QACpD,MAAM;IACV,OACK;QACD,MAAM,IAAI,UAAU,CAAA,GAAA,yNAAA,CAAA,UAAe,AAAD,EAAE,QAAQ,uNAAA,CAAA,QAAK,EAAE;IACvD;IACA,IAAI,CAAC,IAAI;QACL,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,CAAC,KAAK;QACN,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,CAAA,GAAA,4NAAA,CAAA,UAAc,AAAD,EAAE,KAAK;IACpB,CAAA,GAAA,uNAAA,CAAA,UAAa,AAAD,EAAE,KAAK;IACnB,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,WAAW,KAAK,KAAK,YAAY,IAAI,KAAK;QACrD,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,WAAW,KAAK,KAAK,YAAY,IAAI,KAAK;QACrD;YACI,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC;IACnC;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1909, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/aeskw.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON> } from 'node:buffer';\nimport { KeyObject, createDecipher<PERSON>, createCipher<PERSON>, createS<PERSON>ret<PERSON><PERSON> } from 'node:crypto';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { concat } from '../lib/buffer_utils.js';\nimport { isCrypto<PERSON>ey } from './webcrypto.js';\nimport { checkEncCryptoKey } from '../lib/crypto_key.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport supported from './ciphers.js';\nimport { types } from './is_key_like.js';\nfunction checkKeySize(key, alg) {\n    if (key.symmetricKeySize << 3 !== parseInt(alg.slice(1, 4), 10)) {\n        throw new TypeError(`Invalid key size for alg: ${alg}`);\n    }\n}\nfunction ensureKeyObject(key, alg, usage) {\n    if (isKeyObject(key)) {\n        return key;\n    }\n    if (key instanceof Uint8Array) {\n        return createS<PERSON>ret<PERSON><PERSON>(key);\n    }\n    if (isCryptoKey(key)) {\n        checkEncCryptoKey(key, alg, usage);\n        return KeyObject.from(key);\n    }\n    throw new TypeError(invalidKeyInput(key, ...types, 'Uint8Array'));\n}\nexport const wrap = (alg, key, cek) => {\n    const size = parseInt(alg.slice(1, 4), 10);\n    const algorithm = `aes${size}-wrap`;\n    if (!supported(algorithm)) {\n        throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n    const keyObject = ensureKeyObject(key, alg, 'wrapKey');\n    checkKeySize(keyObject, alg);\n    const cipher = createCipheriv(algorithm, keyObject, Buffer.alloc(8, 0xa6));\n    return concat(cipher.update(cek), cipher.final());\n};\nexport const unwrap = (alg, key, encryptedKey) => {\n    const size = parseInt(alg.slice(1, 4), 10);\n    const algorithm = `aes${size}-wrap`;\n    if (!supported(algorithm)) {\n        throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n    const keyObject = ensureKeyObject(key, alg, 'unwrapKey');\n    checkKeySize(keyObject, alg);\n    const cipher = createDecipheriv(algorithm, keyObject, Buffer.alloc(8, 0xa6));\n    return concat(cipher.update(encryptedKey), cipher.final());\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AACA,SAAS,aAAa,GAAG,EAAE,GAAG;IAC1B,IAAI,IAAI,gBAAgB,IAAI,MAAM,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK;QAC7D,MAAM,IAAI,UAAU,CAAC,0BAA0B,EAAE,KAAK;IAC1D;AACJ;AACA,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,KAAK;IACpC,IAAI,CAAA,GAAA,yNAAA,CAAA,UAAW,AAAD,EAAE,MAAM;QAClB,OAAO;IACX;IACA,IAAI,eAAe,YAAY;QAC3B,OAAO,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAAE;IAC3B;IACA,IAAI,CAAA,GAAA,qNAAA,CAAA,cAAW,AAAD,EAAE,MAAM;QAClB,CAAA,GAAA,kNAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,KAAK;QAC5B,OAAO,qHAAA,CAAA,YAAS,CAAC,IAAI,CAAC;IAC1B;IACA,MAAM,IAAI,UAAU,CAAA,GAAA,yNAAA,CAAA,UAAe,AAAD,EAAE,QAAQ,uNAAA,CAAA,QAAK,EAAE;AACvD;AACO,MAAM,OAAO,CAAC,KAAK,KAAK;IAC3B,MAAM,OAAO,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IACvC,MAAM,YAAY,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC;IACnC,IAAI,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAS,AAAD,EAAE,YAAY;QACvB,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,2DAA2D,CAAC;IACtG;IACA,MAAM,YAAY,gBAAgB,KAAK,KAAK;IAC5C,aAAa,WAAW;IACxB,MAAM,SAAS,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,WAAW,qHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,GAAG;IACpE,OAAO,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,OAAO,MAAM,CAAC,MAAM,OAAO,KAAK;AAClD;AACO,MAAM,SAAS,CAAC,KAAK,KAAK;IAC7B,MAAM,OAAO,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IACvC,MAAM,YAAY,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC;IACnC,IAAI,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAS,AAAD,EAAE,YAAY;QACvB,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,2DAA2D,CAAC;IACtG;IACA,MAAM,YAAY,gBAAgB,KAAK,KAAK;IAC5C,aAAa,WAAW;IACxB,MAAM,SAAS,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,WAAW,qHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,GAAG;IACtE,OAAO,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,OAAO,MAAM,CAAC,eAAe,OAAO,KAAK;AAC3D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1979, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/ecdhes.js"], "sourcesContent": ["import { di<PERSON><PERSON><PERSON><PERSON><PERSON>, generate<PERSON>ey<PERSON>air as generateKey<PERSON>airCb, KeyObject } from 'node:crypto';\nimport { promisify } from 'node:util';\nimport getNamedCurve from './get_named_curve.js';\nimport { encoder, concat, uint32be, lengthAndInput, concatKdf } from '../lib/buffer_utils.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { isCryptoKey } from './webcrypto.js';\nimport { checkEncCryptoKey } from '../lib/crypto_key.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport { types } from './is_key_like.js';\nconst generateKeyPair = promisify(generateKeyPairCb);\nexport async function deriveKey(publicKee, privateKee, algorithm, keyLength, apu = new Uint8Array(0), apv = new Uint8Array(0)) {\n    let publicKey;\n    if (isCryptoKey(publicKee)) {\n        checkEncCryptoKey(publicKee, 'ECDH');\n        publicKey = KeyObject.from(publicKee);\n    }\n    else if (isKeyObject(publicKee)) {\n        publicKey = publicKee;\n    }\n    else {\n        throw new TypeError(invalidKeyInput(publicKee, ...types));\n    }\n    let privateKey;\n    if (isCryptoKey(privateKee)) {\n        checkEncCryptoKey(privateKee, 'ECDH', 'deriveBits');\n        privateKey = KeyObject.from(privateKee);\n    }\n    else if (isKeyObject(privateKee)) {\n        privateKey = privateKee;\n    }\n    else {\n        throw new TypeError(invalidKeyInput(privateKee, ...types));\n    }\n    const value = concat(lengthAndInput(encoder.encode(algorithm)), lengthAndInput(apu), lengthAndInput(apv), uint32be(keyLength));\n    const sharedSecret = diffieHellman({ privateKey, publicKey });\n    return concatKdf(sharedSecret, keyLength, value);\n}\nexport async function generateEpk(kee) {\n    let key;\n    if (isCryptoKey(kee)) {\n        key = KeyObject.from(kee);\n    }\n    else if (isKeyObject(kee)) {\n        key = kee;\n    }\n    else {\n        throw new TypeError(invalidKeyInput(kee, ...types));\n    }\n    switch (key.asymmetricKeyType) {\n        case 'x25519':\n            return generateKeyPair('x25519');\n        case 'x448': {\n            return generateKeyPair('x448');\n        }\n        case 'ec': {\n            const namedCurve = getNamedCurve(key);\n            return generateKeyPair('ec', { namedCurve });\n        }\n        default:\n            throw new JOSENotSupported('Invalid or unsupported EPK');\n    }\n}\nexport const ecdhAllowed = (key) => ['P-256', 'P-384', 'P-521', 'X25519', 'X448'].includes(getNamedCurve(key));\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AACA,MAAM,kBAAkB,CAAA,GAAA,iHAAA,CAAA,YAAS,AAAD,EAAE,qHAAA,CAAA,kBAAiB;AAC5C,eAAe,UAAU,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,IAAI,WAAW,EAAE,EAAE,MAAM,IAAI,WAAW,EAAE;IACzH,IAAI;IACJ,IAAI,CAAA,GAAA,qNAAA,CAAA,cAAW,AAAD,EAAE,YAAY;QACxB,CAAA,GAAA,kNAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;QAC7B,YAAY,qHAAA,CAAA,YAAS,CAAC,IAAI,CAAC;IAC/B,OACK,IAAI,CAAA,GAAA,yNAAA,CAAA,UAAW,AAAD,EAAE,YAAY;QAC7B,YAAY;IAChB,OACK;QACD,MAAM,IAAI,UAAU,CAAA,GAAA,yNAAA,CAAA,UAAe,AAAD,EAAE,cAAc,uNAAA,CAAA,QAAK;IAC3D;IACA,IAAI;IACJ,IAAI,CAAA,GAAA,qNAAA,CAAA,cAAW,AAAD,EAAE,aAAa;QACzB,CAAA,GAAA,kNAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,QAAQ;QACtC,aAAa,qHAAA,CAAA,YAAS,CAAC,IAAI,CAAC;IAChC,OACK,IAAI,CAAA,GAAA,yNAAA,CAAA,UAAW,AAAD,EAAE,aAAa;QAC9B,aAAa;IACjB,OACK;QACD,MAAM,IAAI,UAAU,CAAA,GAAA,yNAAA,CAAA,UAAe,AAAD,EAAE,eAAe,uNAAA,CAAA,QAAK;IAC5D;IACA,MAAM,QAAQ,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,oNAAA,CAAA,iBAAc,AAAD,EAAE,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC,aAAa,CAAA,GAAA,oNAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,CAAA,GAAA,oNAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE;IACnH,MAAM,eAAe,CAAA,GAAA,qHAAA,CAAA,gBAAa,AAAD,EAAE;QAAE;QAAY;IAAU;IAC3D,OAAO,CAAA,GAAA,oNAAA,CAAA,YAAS,AAAD,EAAE,cAAc,WAAW;AAC9C;AACO,eAAe,YAAY,GAAG;IACjC,IAAI;IACJ,IAAI,CAAA,GAAA,qNAAA,CAAA,cAAW,AAAD,EAAE,MAAM;QAClB,MAAM,qHAAA,CAAA,YAAS,CAAC,IAAI,CAAC;IACzB,OACK,IAAI,CAAA,GAAA,yNAAA,CAAA,UAAW,AAAD,EAAE,MAAM;QACvB,MAAM;IACV,OACK;QACD,MAAM,IAAI,UAAU,CAAA,GAAA,yNAAA,CAAA,UAAe,AAAD,EAAE,QAAQ,uNAAA,CAAA,QAAK;IACrD;IACA,OAAQ,IAAI,iBAAiB;QACzB,KAAK;YACD,OAAO,gBAAgB;QAC3B,KAAK;YAAQ;gBACT,OAAO,gBAAgB;YAC3B;QACA,KAAK;YAAM;gBACP,MAAM,aAAa,CAAA,GAAA,2NAAA,CAAA,UAAa,AAAD,EAAE;gBACjC,OAAO,gBAAgB,MAAM;oBAAE;gBAAW;YAC9C;QACA;YACI,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC;IACnC;AACJ;AACO,MAAM,cAAc,CAAC,MAAQ;QAAC;QAAS;QAAS;QAAS;QAAU;KAAO,CAAC,QAAQ,CAAC,CAAA,GAAA,2NAAA,CAAA,UAAa,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2071, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/check_p2s.js"], "sourcesContent": ["import { JWEInvalid } from '../util/errors.js';\nexport default function checkP2s(p2s) {\n    if (!(p2s instanceof Uint8Array) || p2s.length < 8) {\n        throw new JWEInvalid('PBES2 Salt Input must be 8 or more octets');\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,SAAS,GAAG;IAChC,IAAI,CAAC,CAAC,eAAe,UAAU,KAAK,IAAI,MAAM,GAAG,GAAG;QAChD,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2087, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/pbes2kw.js"], "sourcesContent": ["import { promisify } from 'node:util';\nimport { KeyObject, pbkdf2 as pbkdf2cb } from 'node:crypto';\nimport random from './random.js';\nimport { p2s as concatSalt } from '../lib/buffer_utils.js';\nimport { encode as base64url } from './base64url.js';\nimport { wrap, unwrap } from './aeskw.js';\nimport checkP2s from '../lib/check_p2s.js';\nimport { isCryptoKey } from './webcrypto.js';\nimport { checkEncCryptoKey } from '../lib/crypto_key.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport { types } from './is_key_like.js';\nconst pbkdf2 = promisify(pbkdf2cb);\nfunction getPassword(key, alg) {\n    if (isKeyObject(key)) {\n        return key.export();\n    }\n    if (key instanceof Uint8Array) {\n        return key;\n    }\n    if (isCryptoKey(key)) {\n        checkEncCryptoKey(key, alg, 'deriveBits', 'deriveKey');\n        return KeyObject.from(key).export();\n    }\n    throw new TypeError(invalidKeyInput(key, ...types, 'Uint8Array'));\n}\nexport const encrypt = async (alg, key, cek, p2c = 2048, p2s = random(new Uint8Array(16))) => {\n    checkP2s(p2s);\n    const salt = concatSalt(alg, p2s);\n    const keylen = parseInt(alg.slice(13, 16), 10) >> 3;\n    const password = getPassword(key, alg);\n    const derivedKey = await pbkdf2(password, salt, p2c, keylen, `sha${alg.slice(8, 11)}`);\n    const encryptedKey = await wrap(alg.slice(-6), derivedKey, cek);\n    return { encryptedKey, p2c, p2s: base64url(p2s) };\n};\nexport const decrypt = async (alg, key, encryptedKey, p2c, p2s) => {\n    checkP2s(p2s);\n    const salt = concatSalt(alg, p2s);\n    const keylen = parseInt(alg.slice(13, 16), 10) >> 3;\n    const password = getPassword(key, alg);\n    const derivedKey = await pbkdf2(password, salt, p2c, keylen, `sha${alg.slice(8, 11)}`);\n    return unwrap(alg.slice(-6), derivedKey, encryptedKey);\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AACA,MAAM,SAAS,CAAA,GAAA,iHAAA,CAAA,YAAS,AAAD,EAAE,qHAAA,CAAA,SAAQ;AACjC,SAAS,YAAY,GAAG,EAAE,GAAG;IACzB,IAAI,CAAA,GAAA,yNAAA,CAAA,UAAW,AAAD,EAAE,MAAM;QAClB,OAAO,IAAI,MAAM;IACrB;IACA,IAAI,eAAe,YAAY;QAC3B,OAAO;IACX;IACA,IAAI,CAAA,GAAA,qNAAA,CAAA,cAAW,AAAD,EAAE,MAAM;QAClB,CAAA,GAAA,kNAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,KAAK,cAAc;QAC1C,OAAO,qHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,KAAK,MAAM;IACrC;IACA,MAAM,IAAI,UAAU,CAAA,GAAA,yNAAA,CAAA,UAAe,AAAD,EAAE,QAAQ,uNAAA,CAAA,QAAK,EAAE;AACvD;AACO,MAAM,UAAU,OAAO,KAAK,KAAK,KAAK,MAAM,IAAI,EAAE,MAAM,CAAA,GAAA,kKAAA,CAAA,UAAM,AAAD,EAAE,IAAI,WAAW,IAAI;IACrF,CAAA,GAAA,iNAAA,CAAA,UAAQ,AAAD,EAAE;IACT,MAAM,OAAO,CAAA,GAAA,oNAAA,CAAA,MAAU,AAAD,EAAE,KAAK;IAC7B,MAAM,SAAS,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO;IAClD,MAAM,WAAW,YAAY,KAAK;IAClC,MAAM,aAAa,MAAM,OAAO,UAAU,MAAM,KAAK,QAAQ,CAAC,GAAG,EAAE,IAAI,KAAK,CAAC,GAAG,KAAK;IACrF,MAAM,eAAe,MAAM,CAAA,GAAA,iNAAA,CAAA,OAAI,AAAD,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,YAAY;IAC3D,OAAO;QAAE;QAAc;QAAK,KAAK,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE;IAAK;AACpD;AACO,MAAM,UAAU,OAAO,KAAK,KAAK,cAAc,KAAK;IACvD,CAAA,GAAA,iNAAA,CAAA,UAAQ,AAAD,EAAE;IACT,MAAM,OAAO,CAAA,GAAA,oNAAA,CAAA,MAAU,AAAD,EAAE,KAAK;IAC7B,MAAM,SAAS,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO;IAClD,MAAM,WAAW,YAAY,KAAK;IAClC,MAAM,aAAa,MAAM,OAAO,UAAU,MAAM,KAAK,QAAQ,CAAC,GAAG,EAAE,IAAI,KAAK,CAAC,GAAG,KAAK;IACrF,OAAO,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,YAAY;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/rsaes.js"], "sourcesContent": ["import { KeyObject, publicEncrypt, constants, privateDecrypt } from 'node:crypto';\nimport { deprecate } from 'node:util';\nimport checkKeyLength from './check_key_length.js';\nimport { isCryptoKey } from './webcrypto.js';\nimport { checkEncCryptoKey } from '../lib/crypto_key.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport { types } from './is_key_like.js';\nconst checkKey = (key, alg) => {\n    if (key.asymmetricKeyType !== 'rsa') {\n        throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa');\n    }\n    checkKeyLength(key, alg);\n};\nconst RSA1_5 = deprecate(() => constants.RSA_PKCS1_PADDING, 'The RSA1_5 \"alg\" (JWE Algorithm) is deprecated and will be removed in the next major revision.');\nconst resolvePadding = (alg) => {\n    switch (alg) {\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512':\n            return constants.RSA_PKCS1_OAEP_PADDING;\n        case 'RSA1_5':\n            return RSA1_5();\n        default:\n            return undefined;\n    }\n};\nconst resolveOaepHash = (alg) => {\n    switch (alg) {\n        case 'RSA-OAEP':\n            return 'sha1';\n        case 'RSA-OAEP-256':\n            return 'sha256';\n        case 'RSA-OAEP-384':\n            return 'sha384';\n        case 'RSA-OAEP-512':\n            return 'sha512';\n        default:\n            return undefined;\n    }\n};\nfunction ensureKeyObject(key, alg, ...usages) {\n    if (isKeyObject(key)) {\n        return key;\n    }\n    if (isCryptoKey(key)) {\n        checkEncCryptoKey(key, alg, ...usages);\n        return KeyObject.from(key);\n    }\n    throw new TypeError(invalidKeyInput(key, ...types));\n}\nexport const encrypt = (alg, key, cek) => {\n    const padding = resolvePadding(alg);\n    const oaepHash = resolveOaepHash(alg);\n    const keyObject = ensureKeyObject(key, alg, 'wrapKey', 'encrypt');\n    checkKey(keyObject, alg);\n    return publicEncrypt({ key: keyObject, oaepHash, padding }, cek);\n};\nexport const decrypt = (alg, key, encryptedKey) => {\n    const padding = resolvePadding(alg);\n    const oaepHash = resolveOaepHash(alg);\n    const keyObject = ensureKeyObject(key, alg, 'unwrapKey', 'decrypt');\n    checkKey(keyObject, alg);\n    return privateDecrypt({ key: keyObject, oaepHash, padding }, encryptedKey);\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACA,MAAM,WAAW,CAAC,KAAK;IACnB,IAAI,IAAI,iBAAiB,KAAK,OAAO;QACjC,MAAM,IAAI,UAAU;IACxB;IACA,CAAA,GAAA,4NAAA,CAAA,UAAc,AAAD,EAAE,KAAK;AACxB;AACA,MAAM,SAAS,CAAA,GAAA,iHAAA,CAAA,YAAS,AAAD,EAAE,IAAM,qHAAA,CAAA,YAAS,CAAC,iBAAiB,EAAE;AAC5D,MAAM,iBAAiB,CAAC;IACpB,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,qHAAA,CAAA,YAAS,CAAC,sBAAsB;QAC3C,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ;AACA,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM;IACxC,IAAI,CAAA,GAAA,yNAAA,CAAA,UAAW,AAAD,EAAE,MAAM;QAClB,OAAO;IACX;IACA,IAAI,CAAA,GAAA,qNAAA,CAAA,cAAW,AAAD,EAAE,MAAM;QAClB,CAAA,GAAA,kNAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,QAAQ;QAC/B,OAAO,qHAAA,CAAA,YAAS,CAAC,IAAI,CAAC;IAC1B;IACA,MAAM,IAAI,UAAU,CAAA,GAAA,yNAAA,CAAA,UAAe,AAAD,EAAE,QAAQ,uNAAA,CAAA,QAAK;AACrD;AACO,MAAM,UAAU,CAAC,KAAK,KAAK;IAC9B,MAAM,UAAU,eAAe;IAC/B,MAAM,WAAW,gBAAgB;IACjC,MAAM,YAAY,gBAAgB,KAAK,KAAK,WAAW;IACvD,SAAS,WAAW;IACpB,OAAO,CAAA,GAAA,qHAAA,CAAA,gBAAa,AAAD,EAAE;QAAE,KAAK;QAAW;QAAU;IAAQ,GAAG;AAChE;AACO,MAAM,UAAU,CAAC,KAAK,KAAK;IAC9B,MAAM,UAAU,eAAe;IAC/B,MAAM,WAAW,gBAAgB;IACjC,MAAM,YAAY,gBAAgB,KAAK,KAAK,aAAa;IACzD,SAAS,WAAW;IACpB,OAAO,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE;QAAE,KAAK;QAAW;QAAU;IAAQ,GAAG;AACjE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2248, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/normalize_key.js"], "sourcesContent": ["export default {};\n"], "names": [], "mappings": ";;;uCAAe,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/cek.js"], "sourcesContent": ["import { JOSENotSupported } from '../util/errors.js';\nimport random from '../runtime/random.js';\nexport function bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n            return 128;\n        case 'A192GCM':\n            return 192;\n        case 'A256GCM':\n        case 'A128CBC-HS256':\n            return 256;\n        case 'A192CBC-HS384':\n            return 384;\n        case 'A256CBC-HS512':\n            return 512;\n        default:\n            throw new JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\nexport default (alg) => random(new Uint8Array(bitLength(alg) >> 3));\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,UAAU,GAAG;IACzB,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC,CAAC,2BAA2B,EAAE,KAAK;IACtE;AACJ;uCACe,CAAC,MAAQ,CAAA,GAAA,kKAAA,CAAA,UAAM,AAAD,EAAE,IAAI,WAAW,UAAU,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/encrypt.js"], "sourcesContent": ["import { createCipheriv, KeyObject } from 'node:crypto';\nimport checkIvLength from '../lib/check_iv_length.js';\nimport checkCekLength from './check_cek_length.js';\nimport { concat } from '../lib/buffer_utils.js';\nimport cbcTag from './cbc_tag.js';\nimport { isCryptoKey } from './webcrypto.js';\nimport { checkEncCryptoKey } from '../lib/crypto_key.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport generateIv from '../lib/iv.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport supported from './ciphers.js';\nimport { types } from './is_key_like.js';\nfunction cbcEncrypt(enc, plaintext, cek, iv, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    if (isKeyObject(cek)) {\n        cek = cek.export();\n    }\n    const encKey = cek.subarray(keySize >> 3);\n    const macKey = cek.subarray(0, keySize >> 3);\n    const algorithm = `aes-${keySize}-cbc`;\n    if (!supported(algorithm)) {\n        throw new JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const cipher = createCipheriv(algorithm, encKey, iv);\n    const ciphertext = concat(cipher.update(plaintext), cipher.final());\n    const macSize = parseInt(enc.slice(-3), 10);\n    const tag = cbcTag(aad, iv, ciphertext, macSize, macKey, keySize);\n    return { ciphertext, tag, iv };\n}\nfunction gcmEncrypt(enc, plaintext, cek, iv, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const algorithm = `aes-${keySize}-gcm`;\n    if (!supported(algorithm)) {\n        throw new JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const cipher = createCipheriv(algorithm, cek, iv, { authTagLength: 16 });\n    if (aad.byteLength) {\n        cipher.setAAD(aad, { plaintextLength: plaintext.length });\n    }\n    const ciphertext = cipher.update(plaintext);\n    cipher.final();\n    const tag = cipher.getAuthTag();\n    return { ciphertext, tag, iv };\n}\nconst encrypt = (enc, plaintext, cek, iv, aad) => {\n    let key;\n    if (isCryptoKey(cek)) {\n        checkEncCryptoKey(cek, enc, 'encrypt');\n        key = KeyObject.from(cek);\n    }\n    else if (cek instanceof Uint8Array || isKeyObject(cek)) {\n        key = cek;\n    }\n    else {\n        throw new TypeError(invalidKeyInput(cek, ...types, 'Uint8Array'));\n    }\n    checkCekLength(enc, key);\n    if (iv) {\n        checkIvLength(enc, iv);\n    }\n    else {\n        iv = generateIv(enc);\n    }\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return cbcEncrypt(enc, plaintext, key, iv, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            return gcmEncrypt(enc, plaintext, key, iv, aad);\n        default:\n            throw new JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n};\nexport default encrypt;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AACA,SAAS,WAAW,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG;IAC5C,MAAM,UAAU,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IAC1C,IAAI,CAAA,GAAA,yNAAA,CAAA,UAAW,AAAD,EAAE,MAAM;QAClB,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,SAAS,IAAI,QAAQ,CAAC,WAAW;IACvC,MAAM,SAAS,IAAI,QAAQ,CAAC,GAAG,WAAW;IAC1C,MAAM,YAAY,CAAC,IAAI,EAAE,QAAQ,IAAI,CAAC;IACtC,IAAI,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAS,AAAD,EAAE,YAAY;QACvB,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,4CAA4C,CAAC;IACvF;IACA,MAAM,SAAS,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,QAAQ;IACjD,MAAM,aAAa,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,OAAO,MAAM,CAAC,YAAY,OAAO,KAAK;IAChE,MAAM,UAAU,SAAS,IAAI,KAAK,CAAC,CAAC,IAAI;IACxC,MAAM,MAAM,CAAA,GAAA,mNAAA,CAAA,UAAM,AAAD,EAAE,KAAK,IAAI,YAAY,SAAS,QAAQ;IACzD,OAAO;QAAE;QAAY;QAAK;IAAG;AACjC;AACA,SAAS,WAAW,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG;IAC5C,MAAM,UAAU,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IAC1C,MAAM,YAAY,CAAC,IAAI,EAAE,QAAQ,IAAI,CAAC;IACtC,IAAI,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAS,AAAD,EAAE,YAAY;QACvB,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,4CAA4C,CAAC;IACvF;IACA,MAAM,SAAS,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,KAAK,IAAI;QAAE,eAAe;IAAG;IACtE,IAAI,IAAI,UAAU,EAAE;QAChB,OAAO,MAAM,CAAC,KAAK;YAAE,iBAAiB,UAAU,MAAM;QAAC;IAC3D;IACA,MAAM,aAAa,OAAO,MAAM,CAAC;IACjC,OAAO,KAAK;IACZ,MAAM,MAAM,OAAO,UAAU;IAC7B,OAAO;QAAE;QAAY;QAAK;IAAG;AACjC;AACA,MAAM,UAAU,CAAC,KAAK,WAAW,KAAK,IAAI;IACtC,IAAI;IACJ,IAAI,CAAA,GAAA,qNAAA,CAAA,cAAW,AAAD,EAAE,MAAM;QAClB,CAAA,GAAA,kNAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,KAAK;QAC5B,MAAM,qHAAA,CAAA,YAAS,CAAC,IAAI,CAAC;IACzB,OACK,IAAI,eAAe,cAAc,CAAA,GAAA,yNAAA,CAAA,UAAW,AAAD,EAAE,MAAM;QACpD,MAAM;IACV,OACK;QACD,MAAM,IAAI,UAAU,CAAA,GAAA,yNAAA,CAAA,UAAe,AAAD,EAAE,QAAQ,uNAAA,CAAA,QAAK,EAAE;IACvD;IACA,CAAA,GAAA,4NAAA,CAAA,UAAc,AAAD,EAAE,KAAK;IACpB,IAAI,IAAI;QACJ,CAAA,GAAA,uNAAA,CAAA,UAAa,AAAD,EAAE,KAAK;IACvB,OACK;QACD,KAAK,CAAA,GAAA,0MAAA,CAAA,UAAU,AAAD,EAAE;IACpB;IACA,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,WAAW,KAAK,WAAW,KAAK,IAAI;QAC/C,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,WAAW,KAAK,WAAW,KAAK,IAAI;QAC/C;YACI,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC;IACnC;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2399, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/aesgcmkw.js"], "sourcesContent": ["import encrypt from '../runtime/encrypt.js';\nimport decrypt from '../runtime/decrypt.js';\nimport { encode as base64url } from '../runtime/base64url.js';\nexport async function wrap(alg, key, cek, iv) {\n    const jweAlgorithm = alg.slice(0, 7);\n    const wrapped = await encrypt(jweAlgorithm, cek, key, iv, new Uint8Array(0));\n    return {\n        encryptedKey: wrapped.ciphertext,\n        iv: base64url(wrapped.iv),\n        tag: base64url(wrapped.tag),\n    };\n}\nexport async function unwrap(alg, key, encryptedKey, iv, tag) {\n    const jweAlgorithm = alg.slice(0, 7);\n    return decrypt(jweAlgorithm, key, encryptedKey, iv, tag, new Uint8Array(0));\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AACO,eAAe,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACxC,MAAM,eAAe,IAAI,KAAK,CAAC,GAAG;IAClC,MAAM,UAAU,MAAM,CAAA,GAAA,mNAAA,CAAA,UAAO,AAAD,EAAE,cAAc,KAAK,KAAK,IAAI,IAAI,WAAW;IACzE,OAAO;QACH,cAAc,QAAQ,UAAU;QAChC,IAAI,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,QAAQ,EAAE;QACxB,KAAK,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,QAAQ,GAAG;IAC9B;AACJ;AACO,eAAe,OAAO,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE,EAAE,EAAE,GAAG;IACxD,MAAM,eAAe,IAAI,KAAK,CAAC,GAAG;IAClC,OAAO,CAAA,GAAA,mNAAA,CAAA,UAAO,AAAD,EAAE,cAAc,KAAK,cAAc,IAAI,KAAK,IAAI,WAAW;AAC5E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2428, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/decrypt_key_management.js"], "sourcesContent": ["import { unwrap as aesKw } from '../runtime/aeskw.js';\nimport * as ECDH from '../runtime/ecdhes.js';\nimport { decrypt as pbes2Kw } from '../runtime/pbes2kw.js';\nimport { decrypt as rsaEs } from '../runtime/rsaes.js';\nimport { decode as base64url } from '../runtime/base64url.js';\nimport normalize from '../runtime/normalize_key.js';\nimport { JOSENotSupported, JWEInvalid } from '../util/errors.js';\nimport { bitLength as cekLength } from '../lib/cek.js';\nimport { importJWK } from '../key/import.js';\nimport checkKeyType from './check_key_type.js';\nimport isObject from './is_object.js';\nimport { unwrap as aesGcmKw } from './aesgcmkw.js';\nasync function decryptKeyManagement(alg, key, encrypted<PERSON>ey, jose<PERSON><PERSON>er, options) {\n    checkKeyType(alg, key, 'decrypt');\n    key = (await normalize.normalizePrivateKey?.(key, alg)) || key;\n    switch (alg) {\n        case 'dir': {\n            if (encryptedKey !== undefined)\n                throw new JWEInvalid('Encountered unexpected JWE Encrypted Key');\n            return key;\n        }\n        case 'ECDH-ES':\n            if (encryptedKey !== undefined)\n                throw new JWEInvalid('Encountered unexpected JWE Encrypted Key');\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            if (!isObject(joseHeader.epk))\n                throw new JWEInvalid(`JOSE Header \"epk\" (Ephemeral Public Key) missing or invalid`);\n            if (!ECDH.ecdhAllowed(key))\n                throw new JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            const epk = await importJWK(joseHeader.epk, alg);\n            let partyUInfo;\n            let partyVInfo;\n            if (joseHeader.apu !== undefined) {\n                if (typeof joseHeader.apu !== 'string')\n                    throw new JWEInvalid(`JOSE Header \"apu\" (Agreement PartyUInfo) invalid`);\n                try {\n                    partyUInfo = base64url(joseHeader.apu);\n                }\n                catch {\n                    throw new JWEInvalid('Failed to base64url decode the apu');\n                }\n            }\n            if (joseHeader.apv !== undefined) {\n                if (typeof joseHeader.apv !== 'string')\n                    throw new JWEInvalid(`JOSE Header \"apv\" (Agreement PartyVInfo) invalid`);\n                try {\n                    partyVInfo = base64url(joseHeader.apv);\n                }\n                catch {\n                    throw new JWEInvalid('Failed to base64url decode the apv');\n                }\n            }\n            const sharedSecret = await ECDH.deriveKey(epk, key, alg === 'ECDH-ES' ? joseHeader.enc : alg, alg === 'ECDH-ES' ? cekLength(joseHeader.enc) : parseInt(alg.slice(-5, -2), 10), partyUInfo, partyVInfo);\n            if (alg === 'ECDH-ES')\n                return sharedSecret;\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            return aesKw(alg.slice(-6), sharedSecret, encryptedKey);\n        }\n        case 'RSA1_5':\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            return rsaEs(alg, key, encryptedKey);\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.p2c !== 'number')\n                throw new JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) missing or invalid`);\n            const p2cLimit = options?.maxPBES2Count || 10_000;\n            if (joseHeader.p2c > p2cLimit)\n                throw new JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) out is of acceptable bounds`);\n            if (typeof joseHeader.p2s !== 'string')\n                throw new JWEInvalid(`JOSE Header \"p2s\" (PBES2 Salt) missing or invalid`);\n            let p2s;\n            try {\n                p2s = base64url(joseHeader.p2s);\n            }\n            catch {\n                throw new JWEInvalid('Failed to base64url decode the p2s');\n            }\n            return pbes2Kw(alg, key, encryptedKey, joseHeader.p2c, p2s);\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            return aesKw(alg, key, encryptedKey);\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.iv !== 'string')\n                throw new JWEInvalid(`JOSE Header \"iv\" (Initialization Vector) missing or invalid`);\n            if (typeof joseHeader.tag !== 'string')\n                throw new JWEInvalid(`JOSE Header \"tag\" (Authentication Tag) missing or invalid`);\n            let iv;\n            try {\n                iv = base64url(joseHeader.iv);\n            }\n            catch {\n                throw new JWEInvalid('Failed to base64url decode the iv');\n            }\n            let tag;\n            try {\n                tag = base64url(joseHeader.tag);\n            }\n            catch {\n                throw new JWEInvalid('Failed to base64url decode the tag');\n            }\n            return aesGcmKw(alg, key, encryptedKey, iv, tag);\n        }\n        default: {\n            throw new JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n}\nexport default decryptKeyManagement;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AACA,eAAe,qBAAqB,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO;IAC3E,CAAA,GAAA,sNAAA,CAAA,UAAY,AAAD,EAAE,KAAK,KAAK;IACvB,MAAM,AAAC,MAAM,yNAAA,CAAA,UAAS,CAAC,mBAAmB,GAAG,KAAK,QAAS;IAC3D,OAAQ;QACJ,KAAK;YAAO;gBACR,IAAI,iBAAiB,WACjB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;gBACzB,OAAO;YACX;QACA,KAAK;YACD,IAAI,iBAAiB,WACjB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;QAC7B,KAAK;QACL,KAAK;QACL,KAAK;YAAkB;gBACnB,IAAI,CAAC,CAAA,GAAA,iNAAA,CAAA,UAAQ,AAAD,EAAE,WAAW,GAAG,GACxB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC,CAAC,2DAA2D,CAAC;gBACtF,IAAI,CAAC,CAAA,GAAA,kNAAA,CAAA,cAAgB,AAAD,EAAE,MAClB,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC;gBAC/B,MAAM,MAAM,MAAM,CAAA,GAAA,8MAAA,CAAA,YAAS,AAAD,EAAE,WAAW,GAAG,EAAE;gBAC5C,IAAI;gBACJ,IAAI;gBACJ,IAAI,WAAW,GAAG,KAAK,WAAW;oBAC9B,IAAI,OAAO,WAAW,GAAG,KAAK,UAC1B,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC,CAAC,gDAAgD,CAAC;oBAC3E,IAAI;wBACA,aAAa,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,WAAW,GAAG;oBACzC,EACA,OAAM;wBACF,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;oBACzB;gBACJ;gBACA,IAAI,WAAW,GAAG,KAAK,WAAW;oBAC9B,IAAI,OAAO,WAAW,GAAG,KAAK,UAC1B,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC,CAAC,gDAAgD,CAAC;oBAC3E,IAAI;wBACA,aAAa,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,WAAW,GAAG;oBACzC,EACA,OAAM;wBACF,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;oBACzB;gBACJ;gBACA,MAAM,eAAe,MAAM,CAAA,GAAA,kNAAA,CAAA,YAAc,AAAD,EAAE,KAAK,KAAK,QAAQ,YAAY,WAAW,GAAG,GAAG,KAAK,QAAQ,YAAY,CAAA,GAAA,2MAAA,CAAA,YAAS,AAAD,EAAE,WAAW,GAAG,IAAI,SAAS,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY;gBAC3L,IAAI,QAAQ,WACR,OAAO;gBACX,IAAI,iBAAiB,WACjB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;gBACzB,OAAO,CAAA,GAAA,iNAAA,CAAA,SAAK,AAAD,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,cAAc;YAC9C;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAgB;gBACjB,IAAI,iBAAiB,WACjB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;gBACzB,OAAO,CAAA,GAAA,iNAAA,CAAA,UAAK,AAAD,EAAE,KAAK,KAAK;YAC3B;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAsB;gBACvB,IAAI,iBAAiB,WACjB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;gBACzB,IAAI,OAAO,WAAW,GAAG,KAAK,UAC1B,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC,CAAC,kDAAkD,CAAC;gBAC7E,MAAM,WAAW,SAAS,iBAAiB;gBAC3C,IAAI,WAAW,GAAG,GAAG,UACjB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC,CAAC,2DAA2D,CAAC;gBACtF,IAAI,OAAO,WAAW,GAAG,KAAK,UAC1B,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC,CAAC,iDAAiD,CAAC;gBAC5E,IAAI;gBACJ,IAAI;oBACA,MAAM,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,WAAW,GAAG;gBAClC,EACA,OAAM;oBACF,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;gBACzB;gBACA,OAAO,CAAA,GAAA,mNAAA,CAAA,UAAO,AAAD,EAAE,KAAK,KAAK,cAAc,WAAW,GAAG,EAAE;YAC3D;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAU;gBACX,IAAI,iBAAiB,WACjB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;gBACzB,OAAO,CAAA,GAAA,iNAAA,CAAA,SAAK,AAAD,EAAE,KAAK,KAAK;YAC3B;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAa;gBACd,IAAI,iBAAiB,WACjB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;gBACzB,IAAI,OAAO,WAAW,EAAE,KAAK,UACzB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC,CAAC,2DAA2D,CAAC;gBACtF,IAAI,OAAO,WAAW,GAAG,KAAK,UAC1B,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC,CAAC,yDAAyD,CAAC;gBACpF,IAAI;gBACJ,IAAI;oBACA,KAAK,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,WAAW,EAAE;gBAChC,EACA,OAAM;oBACF,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;gBACzB;gBACA,IAAI;gBACJ,IAAI;oBACA,MAAM,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,WAAW,GAAG;gBAClC,EACA,OAAM;oBACF,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;gBACzB;gBACA,OAAO,CAAA,GAAA,gNAAA,CAAA,SAAQ,AAAD,EAAE,KAAK,KAAK,cAAc,IAAI;YAChD;QACA;YAAS;gBACL,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC;YAC/B;IACJ;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2563, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/jwe/flattened/decrypt.js"], "sourcesContent": ["import { decode as base64url } from '../../runtime/base64url.js';\nimport decrypt from '../../runtime/decrypt.js';\nimport { JOSEAlgNotAllowed, JOSENotSupported, JWEInvalid } from '../../util/errors.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport isObject from '../../lib/is_object.js';\nimport decryptKeyManagement from '../../lib/decrypt_key_management.js';\nimport { encoder, decoder, concat } from '../../lib/buffer_utils.js';\nimport generateCek from '../../lib/cek.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport validateAlgorithms from '../../lib/validate_algorithms.js';\nexport async function flattenedDecrypt(jwe, key, options) {\n    if (!isObject(jwe)) {\n        throw new JWEInvalid('Flattened JWE must be an object');\n    }\n    if (jwe.protected === undefined && jwe.header === undefined && jwe.unprotected === undefined) {\n        throw new JWEInvalid('JOSE Header missing');\n    }\n    if (jwe.iv !== undefined && typeof jwe.iv !== 'string') {\n        throw new JWEInvalid('JWE Initialization Vector incorrect type');\n    }\n    if (typeof jwe.ciphertext !== 'string') {\n        throw new JWEInvalid('JWE Ciphertext missing or incorrect type');\n    }\n    if (jwe.tag !== undefined && typeof jwe.tag !== 'string') {\n        throw new JWEInvalid('JWE Authentication Tag incorrect type');\n    }\n    if (jwe.protected !== undefined && typeof jwe.protected !== 'string') {\n        throw new JWEInvalid('JWE Protected Header incorrect type');\n    }\n    if (jwe.encrypted_key !== undefined && typeof jwe.encrypted_key !== 'string') {\n        throw new JWEInvalid('JWE Encrypted Key incorrect type');\n    }\n    if (jwe.aad !== undefined && typeof jwe.aad !== 'string') {\n        throw new JWEInvalid('JWE AAD incorrect type');\n    }\n    if (jwe.header !== undefined && !isObject(jwe.header)) {\n        throw new JWEInvalid('JWE Shared Unprotected Header incorrect type');\n    }\n    if (jwe.unprotected !== undefined && !isObject(jwe.unprotected)) {\n        throw new JWEInvalid('JWE Per-Recipient Unprotected Header incorrect type');\n    }\n    let parsedProt;\n    if (jwe.protected) {\n        try {\n            const protectedHeader = base64url(jwe.protected);\n            parsedProt = JSON.parse(decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new JWEInvalid('JWE Protected Header is invalid');\n        }\n    }\n    if (!isDisjoint(parsedProt, jwe.header, jwe.unprotected)) {\n        throw new JWEInvalid('JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jwe.header,\n        ...jwe.unprotected,\n    };\n    validateCrit(JWEInvalid, new Map(), options?.crit, parsedProt, joseHeader);\n    if (joseHeader.zip !== undefined) {\n        throw new JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n    }\n    const { alg, enc } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new JWEInvalid('missing JWE Algorithm (alg) in JWE Header');\n    }\n    if (typeof enc !== 'string' || !enc) {\n        throw new JWEInvalid('missing JWE Encryption Algorithm (enc) in JWE Header');\n    }\n    const keyManagementAlgorithms = options && validateAlgorithms('keyManagementAlgorithms', options.keyManagementAlgorithms);\n    const contentEncryptionAlgorithms = options &&\n        validateAlgorithms('contentEncryptionAlgorithms', options.contentEncryptionAlgorithms);\n    if ((keyManagementAlgorithms && !keyManagementAlgorithms.has(alg)) ||\n        (!keyManagementAlgorithms && alg.startsWith('PBES2'))) {\n        throw new JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter value not allowed');\n    }\n    if (contentEncryptionAlgorithms && !contentEncryptionAlgorithms.has(enc)) {\n        throw new JOSEAlgNotAllowed('\"enc\" (Encryption Algorithm) Header Parameter value not allowed');\n    }\n    let encryptedKey;\n    if (jwe.encrypted_key !== undefined) {\n        try {\n            encryptedKey = base64url(jwe.encrypted_key);\n        }\n        catch {\n            throw new JWEInvalid('Failed to base64url decode the encrypted_key');\n        }\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jwe);\n        resolvedKey = true;\n    }\n    let cek;\n    try {\n        cek = await decryptKeyManagement(alg, key, encryptedKey, joseHeader, options);\n    }\n    catch (err) {\n        if (err instanceof TypeError || err instanceof JWEInvalid || err instanceof JOSENotSupported) {\n            throw err;\n        }\n        cek = generateCek(enc);\n    }\n    let iv;\n    let tag;\n    if (jwe.iv !== undefined) {\n        try {\n            iv = base64url(jwe.iv);\n        }\n        catch {\n            throw new JWEInvalid('Failed to base64url decode the iv');\n        }\n    }\n    if (jwe.tag !== undefined) {\n        try {\n            tag = base64url(jwe.tag);\n        }\n        catch {\n            throw new JWEInvalid('Failed to base64url decode the tag');\n        }\n    }\n    const protectedHeader = encoder.encode(jwe.protected ?? '');\n    let additionalData;\n    if (jwe.aad !== undefined) {\n        additionalData = concat(protectedHeader, encoder.encode('.'), encoder.encode(jwe.aad));\n    }\n    else {\n        additionalData = protectedHeader;\n    }\n    let ciphertext;\n    try {\n        ciphertext = base64url(jwe.ciphertext);\n    }\n    catch {\n        throw new JWEInvalid('Failed to base64url decode the ciphertext');\n    }\n    const plaintext = await decrypt(enc, cek, ciphertext, iv, tag, additionalData);\n    const result = { plaintext };\n    if (jwe.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jwe.aad !== undefined) {\n        try {\n            result.additionalAuthenticatedData = base64url(jwe.aad);\n        }\n        catch {\n            throw new JWEInvalid('Failed to base64url decode the aad');\n        }\n    }\n    if (jwe.unprotected !== undefined) {\n        result.sharedUnprotectedHeader = jwe.unprotected;\n    }\n    if (jwe.header !== undefined) {\n        result.unprotectedHeader = jwe.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key };\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AACO,eAAe,iBAAiB,GAAG,EAAE,GAAG,EAAE,OAAO;IACpD,IAAI,CAAC,CAAA,GAAA,iNAAA,CAAA,UAAQ,AAAD,EAAE,MAAM;QAChB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,IAAI,SAAS,KAAK,aAAa,IAAI,MAAM,KAAK,aAAa,IAAI,WAAW,KAAK,WAAW;QAC1F,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,IAAI,EAAE,KAAK,aAAa,OAAO,IAAI,EAAE,KAAK,UAAU;QACpD,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,OAAO,IAAI,UAAU,KAAK,UAAU;QACpC,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,IAAI,GAAG,KAAK,aAAa,OAAO,IAAI,GAAG,KAAK,UAAU;QACtD,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,IAAI,SAAS,KAAK,aAAa,OAAO,IAAI,SAAS,KAAK,UAAU;QAClE,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,IAAI,aAAa,KAAK,aAAa,OAAO,IAAI,aAAa,KAAK,UAAU;QAC1E,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,IAAI,GAAG,KAAK,aAAa,OAAO,IAAI,GAAG,KAAK,UAAU;QACtD,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,IAAI,MAAM,KAAK,aAAa,CAAC,CAAA,GAAA,iNAAA,CAAA,UAAQ,AAAD,EAAE,IAAI,MAAM,GAAG;QACnD,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,IAAI,WAAW,KAAK,aAAa,CAAC,CAAA,GAAA,iNAAA,CAAA,UAAQ,AAAD,EAAE,IAAI,WAAW,GAAG;QAC7D,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI;IACJ,IAAI,IAAI,SAAS,EAAE;QACf,IAAI;YACA,MAAM,kBAAkB,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,IAAI,SAAS;YAC/C,aAAa,KAAK,KAAK,CAAC,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QAC3C,EACA,OAAM;YACF,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;QACzB;IACJ;IACA,IAAI,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAU,AAAD,EAAE,YAAY,IAAI,MAAM,EAAE,IAAI,WAAW,GAAG;QACtD,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,aAAa;QACf,GAAG,UAAU;QACb,GAAG,IAAI,MAAM;QACb,GAAG,IAAI,WAAW;IACtB;IACA,CAAA,GAAA,qNAAA,CAAA,UAAY,AAAD,EAAE,+MAAA,CAAA,aAAU,EAAE,IAAI,OAAO,SAAS,MAAM,YAAY;IAC/D,IAAI,WAAW,GAAG,KAAK,WAAW;QAC9B,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC;IAC/B;IACA,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACrB,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;QACjC,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;QACjC,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,0BAA0B,WAAW,CAAA,GAAA,2NAAA,CAAA,UAAkB,AAAD,EAAE,2BAA2B,QAAQ,uBAAuB;IACxH,MAAM,8BAA8B,WAChC,CAAA,GAAA,2NAAA,CAAA,UAAkB,AAAD,EAAE,+BAA+B,QAAQ,2BAA2B;IACzF,IAAI,AAAC,2BAA2B,CAAC,wBAAwB,GAAG,CAAC,QACxD,CAAC,2BAA2B,IAAI,UAAU,CAAC,UAAW;QACvD,MAAM,IAAI,+MAAA,CAAA,oBAAiB,CAAC;IAChC;IACA,IAAI,+BAA+B,CAAC,4BAA4B,GAAG,CAAC,MAAM;QACtE,MAAM,IAAI,+MAAA,CAAA,oBAAiB,CAAC;IAChC;IACA,IAAI;IACJ,IAAI,IAAI,aAAa,KAAK,WAAW;QACjC,IAAI;YACA,eAAe,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,IAAI,aAAa;QAC9C,EACA,OAAM;YACF,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;QACzB;IACJ;IACA,IAAI,cAAc;IAClB,IAAI,OAAO,QAAQ,YAAY;QAC3B,MAAM,MAAM,IAAI,YAAY;QAC5B,cAAc;IAClB;IACA,IAAI;IACJ,IAAI;QACA,MAAM,MAAM,CAAA,GAAA,8NAAA,CAAA,UAAoB,AAAD,EAAE,KAAK,KAAK,cAAc,YAAY;IACzE,EACA,OAAO,KAAK;QACR,IAAI,eAAe,aAAa,eAAe,+MAAA,CAAA,aAAU,IAAI,eAAe,+MAAA,CAAA,mBAAgB,EAAE;YAC1F,MAAM;QACV;QACA,MAAM,CAAA,GAAA,2MAAA,CAAA,UAAW,AAAD,EAAE;IACtB;IACA,IAAI;IACJ,IAAI;IACJ,IAAI,IAAI,EAAE,KAAK,WAAW;QACtB,IAAI;YACA,KAAK,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,IAAI,EAAE;QACzB,EACA,OAAM;YACF,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;QACzB;IACJ;IACA,IAAI,IAAI,GAAG,KAAK,WAAW;QACvB,IAAI;YACA,MAAM,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,IAAI,GAAG;QAC3B,EACA,OAAM;YACF,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;QACzB;IACJ;IACA,MAAM,kBAAkB,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC,IAAI,SAAS,IAAI;IACxD,IAAI;IACJ,IAAI,IAAI,GAAG,KAAK,WAAW;QACvB,iBAAiB,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,iBAAiB,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC,MAAM,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC,IAAI,GAAG;IACxF,OACK;QACD,iBAAiB;IACrB;IACA,IAAI;IACJ,IAAI;QACA,aAAa,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,IAAI,UAAU;IACzC,EACA,OAAM;QACF,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,YAAY,MAAM,CAAA,GAAA,mNAAA,CAAA,UAAO,AAAD,EAAE,KAAK,KAAK,YAAY,IAAI,KAAK;IAC/D,MAAM,SAAS;QAAE;IAAU;IAC3B,IAAI,IAAI,SAAS,KAAK,WAAW;QAC7B,OAAO,eAAe,GAAG;IAC7B;IACA,IAAI,IAAI,GAAG,KAAK,WAAW;QACvB,IAAI;YACA,OAAO,2BAA2B,GAAG,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,IAAI,GAAG;QAC1D,EACA,OAAM;YACF,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;QACzB;IACJ;IACA,IAAI,IAAI,WAAW,KAAK,WAAW;QAC/B,OAAO,uBAAuB,GAAG,IAAI,WAAW;IACpD;IACA,IAAI,IAAI,MAAM,KAAK,WAAW;QAC1B,OAAO,iBAAiB,GAAG,IAAI,MAAM;IACzC;IACA,IAAI,aAAa;QACb,OAAO;YAAE,GAAG,MAAM;YAAE;QAAI;IAC5B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2738, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/jwe/compact/decrypt.js"], "sourcesContent": ["import { flattenedDecrypt } from '../flattened/decrypt.js';\nimport { JWEInvalid } from '../../util/errors.js';\nimport { decoder } from '../../lib/buffer_utils.js';\nexport async function compactDecrypt(jwe, key, options) {\n    if (jwe instanceof Uint8Array) {\n        jwe = decoder.decode(jwe);\n    }\n    if (typeof jwe !== 'string') {\n        throw new JWEInvalid('Compact JWE must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: encryptedKey, 2: iv, 3: ciphertext, 4: tag, length, } = jwe.split('.');\n    if (length !== 5) {\n        throw new JWEInvalid('Invalid Compact JWE');\n    }\n    const decrypted = await flattenedDecrypt({\n        ciphertext,\n        iv: iv || undefined,\n        protected: protectedHeader,\n        tag: tag || undefined,\n        encrypted_key: encryptedKey || undefined,\n    }, key, options);\n    const result = { plaintext: decrypted.plaintext, protectedHeader: decrypted.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,eAAe,eAAe,GAAG,EAAE,GAAG,EAAE,OAAO;IAClD,IAAI,eAAe,YAAY;QAC3B,MAAM,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC;IACzB;IACA,IAAI,OAAO,QAAQ,UAAU;QACzB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,EAAE,GAAG,eAAe,EAAE,GAAG,YAAY,EAAE,GAAG,EAAE,EAAE,GAAG,UAAU,EAAE,GAAG,GAAG,EAAE,MAAM,EAAG,GAAG,IAAI,KAAK,CAAC;IACjG,IAAI,WAAW,GAAG;QACd,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,YAAY,MAAM,CAAA,GAAA,4NAAA,CAAA,mBAAgB,AAAD,EAAE;QACrC;QACA,IAAI,MAAM;QACV,WAAW;QACX,KAAK,OAAO;QACZ,eAAe,gBAAgB;IACnC,GAAG,KAAK;IACR,MAAM,SAAS;QAAE,WAAW,UAAU,SAAS;QAAE,iBAAiB,UAAU,eAAe;IAAC;IAC5F,IAAI,OAAO,QAAQ,YAAY;QAC3B,OAAO;YAAE,GAAG,MAAM;YAAE,KAAK,UAAU,GAAG;QAAC;IAC3C;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2783, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/epoch.js"], "sourcesContent": ["export default (date) => Math.floor(date.getTime() / 1000);\n"], "names": [], "mappings": ";;;uCAAe,CAAC,OAAS,KAAK,KAAK,CAAC,KAAK,OAAO,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2793, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/secs.js"], "sourcesContent": ["const minute = 60;\nconst hour = minute * 60;\nconst day = hour * 24;\nconst week = day * 7;\nconst year = day * 365.25;\nconst REGEX = /^(\\+|\\-)? ?(\\d+|\\d+\\.\\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i;\nexport default (str) => {\n    const matched = REGEX.exec(str);\n    if (!matched || (matched[4] && matched[1])) {\n        throw new TypeError('Invalid time period format');\n    }\n    const value = parseFloat(matched[2]);\n    const unit = matched[3].toLowerCase();\n    let numericDate;\n    switch (unit) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n        case 's':\n            numericDate = Math.round(value);\n            break;\n        case 'minute':\n        case 'minutes':\n        case 'min':\n        case 'mins':\n        case 'm':\n            numericDate = Math.round(value * minute);\n            break;\n        case 'hour':\n        case 'hours':\n        case 'hr':\n        case 'hrs':\n        case 'h':\n            numericDate = Math.round(value * hour);\n            break;\n        case 'day':\n        case 'days':\n        case 'd':\n            numericDate = Math.round(value * day);\n            break;\n        case 'week':\n        case 'weeks':\n        case 'w':\n            numericDate = Math.round(value * week);\n            break;\n        default:\n            numericDate = Math.round(value * year);\n            break;\n    }\n    if (matched[1] === '-' || matched[4] === 'ago') {\n        return -numericDate;\n    }\n    return numericDate;\n};\n"], "names": [], "mappings": ";;;AAAA,MAAM,SAAS;AACf,MAAM,OAAO,SAAS;AACtB,MAAM,MAAM,OAAO;AACnB,MAAM,OAAO,MAAM;AACnB,MAAM,OAAO,MAAM;AACnB,MAAM,QAAQ;uCACC,CAAC;IACZ,MAAM,UAAU,MAAM,IAAI,CAAC;IAC3B,IAAI,CAAC,WAAY,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,EAAG;QACxC,MAAM,IAAI,UAAU;IACxB;IACA,MAAM,QAAQ,WAAW,OAAO,CAAC,EAAE;IACnC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,WAAW;IACnC,IAAI;IACJ,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,cAAc,KAAK,KAAK,CAAC;YACzB;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,cAAc,KAAK,KAAK,CAAC,QAAQ;YACjC;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,cAAc,KAAK,KAAK,CAAC,QAAQ;YACjC;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,cAAc,KAAK,KAAK,CAAC,QAAQ;YACjC;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,cAAc,KAAK,KAAK,CAAC,QAAQ;YACjC;QACJ;YACI,cAAc,KAAK,KAAK,CAAC,QAAQ;YACjC;IACR;IACA,IAAI,OAAO,CAAC,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,KAAK,OAAO;QAC5C,OAAO,CAAC;IACZ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2857, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/jwt_claims_set.js"], "sourcesContent": ["import { JWTClaimValidationFailed, JWTExpired, JWTInvalid } from '../util/errors.js';\nimport { decoder } from './buffer_utils.js';\nimport epoch from './epoch.js';\nimport secs from './secs.js';\nimport isObject from './is_object.js';\nconst normalizeTyp = (value) => value.toLowerCase().replace(/^application\\//, '');\nconst checkAudiencePresence = (audPayload, audOption) => {\n    if (typeof audPayload === 'string') {\n        return audOption.includes(audPayload);\n    }\n    if (Array.isArray(audPayload)) {\n        return audOption.some(Set.prototype.has.bind(new Set(audPayload)));\n    }\n    return false;\n};\nexport default (protectedHeader, encodedPayload, options = {}) => {\n    let payload;\n    try {\n        payload = JSON.parse(decoder.decode(encodedPayload));\n    }\n    catch {\n    }\n    if (!isObject(payload)) {\n        throw new JWTInvalid('JWT Claims Set must be a top-level JSON object');\n    }\n    const { typ } = options;\n    if (typ &&\n        (typeof protectedHeader.typ !== 'string' ||\n            normalizeTyp(protectedHeader.typ) !== normalizeTyp(typ))) {\n        throw new JWTClaimValidationFailed('unexpected \"typ\" JWT header value', payload, 'typ', 'check_failed');\n    }\n    const { requiredClaims = [], issuer, subject, audience, maxTokenAge } = options;\n    const presenceCheck = [...requiredClaims];\n    if (maxTokenAge !== undefined)\n        presenceCheck.push('iat');\n    if (audience !== undefined)\n        presenceCheck.push('aud');\n    if (subject !== undefined)\n        presenceCheck.push('sub');\n    if (issuer !== undefined)\n        presenceCheck.push('iss');\n    for (const claim of new Set(presenceCheck.reverse())) {\n        if (!(claim in payload)) {\n            throw new JWTClaimValidationFailed(`missing required \"${claim}\" claim`, payload, claim, 'missing');\n        }\n    }\n    if (issuer &&\n        !(Array.isArray(issuer) ? issuer : [issuer]).includes(payload.iss)) {\n        throw new JWTClaimValidationFailed('unexpected \"iss\" claim value', payload, 'iss', 'check_failed');\n    }\n    if (subject && payload.sub !== subject) {\n        throw new JWTClaimValidationFailed('unexpected \"sub\" claim value', payload, 'sub', 'check_failed');\n    }\n    if (audience &&\n        !checkAudiencePresence(payload.aud, typeof audience === 'string' ? [audience] : audience)) {\n        throw new JWTClaimValidationFailed('unexpected \"aud\" claim value', payload, 'aud', 'check_failed');\n    }\n    let tolerance;\n    switch (typeof options.clockTolerance) {\n        case 'string':\n            tolerance = secs(options.clockTolerance);\n            break;\n        case 'number':\n            tolerance = options.clockTolerance;\n            break;\n        case 'undefined':\n            tolerance = 0;\n            break;\n        default:\n            throw new TypeError('Invalid clockTolerance option type');\n    }\n    const { currentDate } = options;\n    const now = epoch(currentDate || new Date());\n    if ((payload.iat !== undefined || maxTokenAge) && typeof payload.iat !== 'number') {\n        throw new JWTClaimValidationFailed('\"iat\" claim must be a number', payload, 'iat', 'invalid');\n    }\n    if (payload.nbf !== undefined) {\n        if (typeof payload.nbf !== 'number') {\n            throw new JWTClaimValidationFailed('\"nbf\" claim must be a number', payload, 'nbf', 'invalid');\n        }\n        if (payload.nbf > now + tolerance) {\n            throw new JWTClaimValidationFailed('\"nbf\" claim timestamp check failed', payload, 'nbf', 'check_failed');\n        }\n    }\n    if (payload.exp !== undefined) {\n        if (typeof payload.exp !== 'number') {\n            throw new JWTClaimValidationFailed('\"exp\" claim must be a number', payload, 'exp', 'invalid');\n        }\n        if (payload.exp <= now - tolerance) {\n            throw new JWTExpired('\"exp\" claim timestamp check failed', payload, 'exp', 'check_failed');\n        }\n    }\n    if (maxTokenAge) {\n        const age = now - payload.iat;\n        const max = typeof maxTokenAge === 'number' ? maxTokenAge : secs(maxTokenAge);\n        if (age - tolerance > max) {\n            throw new JWTExpired('\"iat\" claim timestamp check failed (too far in the past)', payload, 'iat', 'check_failed');\n        }\n        if (age < 0 - tolerance) {\n            throw new JWTClaimValidationFailed('\"iat\" claim timestamp check failed (it should be in the past)', payload, 'iat', 'check_failed');\n        }\n    }\n    return payload;\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACA,MAAM,eAAe,CAAC,QAAU,MAAM,WAAW,GAAG,OAAO,CAAC,kBAAkB;AAC9E,MAAM,wBAAwB,CAAC,YAAY;IACvC,IAAI,OAAO,eAAe,UAAU;QAChC,OAAO,UAAU,QAAQ,CAAC;IAC9B;IACA,IAAI,MAAM,OAAO,CAAC,aAAa;QAC3B,OAAO,UAAU,IAAI,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI;IACzD;IACA,OAAO;AACX;uCACe,CAAC,iBAAiB,gBAAgB,UAAU,CAAC,CAAC;IACzD,IAAI;IACJ,IAAI;QACA,UAAU,KAAK,KAAK,CAAC,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC;IACxC,EACA,OAAM,CACN;IACA,IAAI,CAAC,CAAA,GAAA,iNAAA,CAAA,UAAQ,AAAD,EAAE,UAAU;QACpB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,EAAE,GAAG,EAAE,GAAG;IAChB,IAAI,OACA,CAAC,OAAO,gBAAgB,GAAG,KAAK,YAC5B,aAAa,gBAAgB,GAAG,MAAM,aAAa,IAAI,GAAG;QAC9D,MAAM,IAAI,+MAAA,CAAA,2BAAwB,CAAC,qCAAqC,SAAS,OAAO;IAC5F;IACA,MAAM,EAAE,iBAAiB,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;IACxE,MAAM,gBAAgB;WAAI;KAAe;IACzC,IAAI,gBAAgB,WAChB,cAAc,IAAI,CAAC;IACvB,IAAI,aAAa,WACb,cAAc,IAAI,CAAC;IACvB,IAAI,YAAY,WACZ,cAAc,IAAI,CAAC;IACvB,IAAI,WAAW,WACX,cAAc,IAAI,CAAC;IACvB,KAAK,MAAM,SAAS,IAAI,IAAI,cAAc,OAAO,IAAK;QAClD,IAAI,CAAC,CAAC,SAAS,OAAO,GAAG;YACrB,MAAM,IAAI,+MAAA,CAAA,2BAAwB,CAAC,CAAC,kBAAkB,EAAE,MAAM,OAAO,CAAC,EAAE,SAAS,OAAO;QAC5F;IACJ;IACA,IAAI,UACA,CAAC,CAAC,MAAM,OAAO,CAAC,UAAU,SAAS;QAAC;KAAO,EAAE,QAAQ,CAAC,QAAQ,GAAG,GAAG;QACpE,MAAM,IAAI,+MAAA,CAAA,2BAAwB,CAAC,gCAAgC,SAAS,OAAO;IACvF;IACA,IAAI,WAAW,QAAQ,GAAG,KAAK,SAAS;QACpC,MAAM,IAAI,+MAAA,CAAA,2BAAwB,CAAC,gCAAgC,SAAS,OAAO;IACvF;IACA,IAAI,YACA,CAAC,sBAAsB,QAAQ,GAAG,EAAE,OAAO,aAAa,WAAW;QAAC;KAAS,GAAG,WAAW;QAC3F,MAAM,IAAI,+MAAA,CAAA,2BAAwB,CAAC,gCAAgC,SAAS,OAAO;IACvF;IACA,IAAI;IACJ,OAAQ,OAAO,QAAQ,cAAc;QACjC,KAAK;YACD,YAAY,CAAA,GAAA,4MAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,cAAc;YACvC;QACJ,KAAK;YACD,YAAY,QAAQ,cAAc;YAClC;QACJ,KAAK;YACD,YAAY;YACZ;QACJ;YACI,MAAM,IAAI,UAAU;IAC5B;IACA,MAAM,EAAE,WAAW,EAAE,GAAG;IACxB,MAAM,MAAM,CAAA,GAAA,6MAAA,CAAA,UAAK,AAAD,EAAE,eAAe,IAAI;IACrC,IAAI,CAAC,QAAQ,GAAG,KAAK,aAAa,WAAW,KAAK,OAAO,QAAQ,GAAG,KAAK,UAAU;QAC/E,MAAM,IAAI,+MAAA,CAAA,2BAAwB,CAAC,gCAAgC,SAAS,OAAO;IACvF;IACA,IAAI,QAAQ,GAAG,KAAK,WAAW;QAC3B,IAAI,OAAO,QAAQ,GAAG,KAAK,UAAU;YACjC,MAAM,IAAI,+MAAA,CAAA,2BAAwB,CAAC,gCAAgC,SAAS,OAAO;QACvF;QACA,IAAI,QAAQ,GAAG,GAAG,MAAM,WAAW;YAC/B,MAAM,IAAI,+MAAA,CAAA,2BAAwB,CAAC,sCAAsC,SAAS,OAAO;QAC7F;IACJ;IACA,IAAI,QAAQ,GAAG,KAAK,WAAW;QAC3B,IAAI,OAAO,QAAQ,GAAG,KAAK,UAAU;YACjC,MAAM,IAAI,+MAAA,CAAA,2BAAwB,CAAC,gCAAgC,SAAS,OAAO;QACvF;QACA,IAAI,QAAQ,GAAG,IAAI,MAAM,WAAW;YAChC,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC,sCAAsC,SAAS,OAAO;QAC/E;IACJ;IACA,IAAI,aAAa;QACb,MAAM,MAAM,MAAM,QAAQ,GAAG;QAC7B,MAAM,MAAM,OAAO,gBAAgB,WAAW,cAAc,CAAA,GAAA,4MAAA,CAAA,UAAI,AAAD,EAAE;QACjE,IAAI,MAAM,YAAY,KAAK;YACvB,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC,4DAA4D,SAAS,OAAO;QACrG;QACA,IAAI,MAAM,IAAI,WAAW;YACrB,MAAM,IAAI,+MAAA,CAAA,2BAAwB,CAAC,iEAAiE,SAAS,OAAO;QACxH;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2971, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/jwt/decrypt.js"], "sourcesContent": ["import { compactDecrypt } from '../jwe/compact/decrypt.js';\nimport jwtPayload from '../lib/jwt_claims_set.js';\nimport { JWTClaimValidationFailed } from '../util/errors.js';\nexport async function jwtDecrypt(jwt, key, options) {\n    const decrypted = await compactDecrypt(jwt, key, options);\n    const payload = jwtPayload(decrypted.protectedHeader, decrypted.plaintext, options);\n    const { protectedHeader } = decrypted;\n    if (protectedHeader.iss !== undefined && protectedHeader.iss !== payload.iss) {\n        throw new JWTClaimValidationFailed('replicated \"iss\" claim header parameter mismatch', payload, 'iss', 'mismatch');\n    }\n    if (protectedHeader.sub !== undefined && protectedHeader.sub !== payload.sub) {\n        throw new JWTClaimValidationFailed('replicated \"sub\" claim header parameter mismatch', payload, 'sub', 'mismatch');\n    }\n    if (protectedHeader.aud !== undefined &&\n        JSON.stringify(protectedHeader.aud) !== JSON.stringify(payload.aud)) {\n        throw new JWTClaimValidationFailed('replicated \"aud\" claim header parameter mismatch', payload, 'aud', 'mismatch');\n    }\n    const result = { payload, protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,eAAe,WAAW,GAAG,EAAE,GAAG,EAAE,OAAO;IAC9C,MAAM,YAAY,MAAM,CAAA,GAAA,0NAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK;IACjD,MAAM,UAAU,CAAA,GAAA,sNAAA,CAAA,UAAU,AAAD,EAAE,UAAU,eAAe,EAAE,UAAU,SAAS,EAAE;IAC3E,MAAM,EAAE,eAAe,EAAE,GAAG;IAC5B,IAAI,gBAAgB,GAAG,KAAK,aAAa,gBAAgB,GAAG,KAAK,QAAQ,GAAG,EAAE;QAC1E,MAAM,IAAI,+MAAA,CAAA,2BAAwB,CAAC,oDAAoD,SAAS,OAAO;IAC3G;IACA,IAAI,gBAAgB,GAAG,KAAK,aAAa,gBAAgB,GAAG,KAAK,QAAQ,GAAG,EAAE;QAC1E,MAAM,IAAI,+MAAA,CAAA,2BAAwB,CAAC,oDAAoD,SAAS,OAAO;IAC3G;IACA,IAAI,gBAAgB,GAAG,KAAK,aACxB,KAAK,SAAS,CAAC,gBAAgB,GAAG,MAAM,KAAK,SAAS,CAAC,QAAQ,GAAG,GAAG;QACrE,MAAM,IAAI,+MAAA,CAAA,2BAAwB,CAAC,oDAAoD,SAAS,OAAO;IAC3G;IACA,MAAM,SAAS;QAAE;QAAS;IAAgB;IAC1C,IAAI,OAAO,QAAQ,YAAY;QAC3B,OAAO;YAAE,GAAG,MAAM;YAAE,KAAK,UAAU,GAAG;QAAC;IAC3C;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3011, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/private_symbols.js"], "sourcesContent": ["export const unprotected = Symbol();\n"], "names": [], "mappings": ";;;AAAO,MAAM,cAAc", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3021, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/runtime/key_to_jwk.js"], "sourcesContent": ["import { KeyObject } from 'node:crypto';\nimport { encode as base64url } from './base64url.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { isCryptoKey } from './webcrypto.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport { types } from './is_key_like.js';\nconst keyToJWK = (key) => {\n    let keyObject;\n    if (isCryptoKey(key)) {\n        if (!key.extractable) {\n            throw new TypeError('CryptoKey is not extractable');\n        }\n        keyObject = KeyObject.from(key);\n    }\n    else if (isKeyObject(key)) {\n        keyObject = key;\n    }\n    else if (key instanceof Uint8Array) {\n        return {\n            kty: 'oct',\n            k: base64url(key),\n        };\n    }\n    else {\n        throw new TypeError(invalidKeyInput(key, ...types, 'Uint8Array'));\n    }\n    if (keyObject.type !== 'secret' &&\n        !['rsa', 'ec', 'ed25519', 'x25519', 'ed448', 'x448'].includes(keyObject.asymmetricKeyType)) {\n        throw new JOSENotSupported('Unsupported key asymmetricKeyType');\n    }\n    return keyObject.export({ format: 'jwk' });\n};\nexport default keyToJWK;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA,MAAM,WAAW,CAAC;IACd,IAAI;IACJ,IAAI,CAAA,GAAA,qNAAA,CAAA,cAAW,AAAD,EAAE,MAAM;QAClB,IAAI,CAAC,IAAI,WAAW,EAAE;YAClB,MAAM,IAAI,UAAU;QACxB;QACA,YAAY,qHAAA,CAAA,YAAS,CAAC,IAAI,CAAC;IAC/B,OACK,IAAI,CAAA,GAAA,yNAAA,CAAA,UAAW,AAAD,EAAE,MAAM;QACvB,YAAY;IAChB,OACK,IAAI,eAAe,YAAY;QAChC,OAAO;YACH,KAAK;YACL,GAAG,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE;QACjB;IACJ,OACK;QACD,MAAM,IAAI,UAAU,CAAA,GAAA,yNAAA,CAAA,UAAe,AAAD,EAAE,QAAQ,uNAAA,CAAA,QAAK,EAAE;IACvD;IACA,IAAI,UAAU,IAAI,KAAK,YACnB,CAAC;QAAC;QAAO;QAAM;QAAW;QAAU;QAAS;KAAO,CAAC,QAAQ,CAAC,UAAU,iBAAiB,GAAG;QAC5F,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC;IAC/B;IACA,OAAO,UAAU,MAAM,CAAC;QAAE,QAAQ;IAAM;AAC5C;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3076, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/key/export.js"], "sourcesContent": ["import { toSPKI as exportPublic } from '../runtime/asn1.js';\nimport { toPKCS8 as exportPrivate } from '../runtime/asn1.js';\nimport keyToJWK from '../runtime/key_to_jwk.js';\nexport async function exportSPKI(key) {\n    return exportPublic(key);\n}\nexport async function exportPKCS8(key) {\n    return exportPrivate(key);\n}\nexport async function exportJWK(key) {\n    return keyToJWK(key);\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AACO,eAAe,WAAW,GAAG;IAChC,OAAO,CAAA,GAAA,gNAAA,CAAA,SAAY,AAAD,EAAE;AACxB;AACO,eAAe,YAAY,GAAG;IACjC,OAAO,CAAA,GAAA,gNAAA,CAAA,UAAa,AAAD,EAAE;AACzB;AACO,eAAe,UAAU,GAAG;IAC/B,OAAO,CAAA,GAAA,sNAAA,CAAA,UAAQ,AAAD,EAAE;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/lib/encrypt_key_management.js"], "sourcesContent": ["import { wrap as aesKw } from '../runtime/aeskw.js';\nimport * as ECDH from '../runtime/ecdhes.js';\nimport { encrypt as pbes2Kw } from '../runtime/pbes2kw.js';\nimport { encrypt as rsaEs } from '../runtime/rsaes.js';\nimport { encode as base64url } from '../runtime/base64url.js';\nimport normalize from '../runtime/normalize_key.js';\nimport generateCek, { bitLength as cekLength } from '../lib/cek.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { exportJWK } from '../key/export.js';\nimport checkKeyType from './check_key_type.js';\nimport { wrap as aesGcmKw } from './aesgcmkw.js';\nasync function encryptKeyManagement(alg, enc, key, providedCek, providedParameters = {}) {\n    let encryptedKey;\n    let parameters;\n    let cek;\n    checkKeyType(alg, key, 'encrypt');\n    key = (await normalize.normalizePublicKey?.(key, alg)) || key;\n    switch (alg) {\n        case 'dir': {\n            cek = key;\n            break;\n        }\n        case 'ECDH-ES':\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            if (!ECDH.ecdhAllowed(key)) {\n                throw new JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            }\n            const { apu, apv } = providedParameters;\n            let { epk: ephemeralKey } = providedParameters;\n            ephemeralKey ||= (await ECDH.generateEpk(key)).privateKey;\n            const { x, y, crv, kty } = await exportJWK(ephemeralKey);\n            const sharedSecret = await ECDH.deriveKey(key, ephemeralKey, alg === 'ECDH-ES' ? enc : alg, alg === 'ECDH-ES' ? cekLength(enc) : parseInt(alg.slice(-5, -2), 10), apu, apv);\n            parameters = { epk: { x, crv, kty } };\n            if (kty === 'EC')\n                parameters.epk.y = y;\n            if (apu)\n                parameters.apu = base64url(apu);\n            if (apv)\n                parameters.apv = base64url(apv);\n            if (alg === 'ECDH-ES') {\n                cek = sharedSecret;\n                break;\n            }\n            cek = providedCek || generateCek(enc);\n            const kwAlg = alg.slice(-6);\n            encryptedKey = await aesKw(kwAlg, sharedSecret, cek);\n            break;\n        }\n        case 'RSA1_5':\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            cek = providedCek || generateCek(enc);\n            encryptedKey = await rsaEs(alg, key, cek);\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            cek = providedCek || generateCek(enc);\n            const { p2c, p2s } = providedParameters;\n            ({ encryptedKey, ...parameters } = await pbes2Kw(alg, key, cek, p2c, p2s));\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            cek = providedCek || generateCek(enc);\n            encryptedKey = await aesKw(alg, key, cek);\n            break;\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            cek = providedCek || generateCek(enc);\n            const { iv } = providedParameters;\n            ({ encryptedKey, ...parameters } = await aesGcmKw(alg, key, cek, iv));\n            break;\n        }\n        default: {\n            throw new JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n    return { cek, encryptedKey, parameters };\n}\nexport default encryptKeyManagement;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AACA,eAAe,qBAAqB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,qBAAqB,CAAC,CAAC;IACnF,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,CAAA,GAAA,sNAAA,CAAA,UAAY,AAAD,EAAE,KAAK,KAAK;IACvB,MAAM,AAAC,MAAM,yNAAA,CAAA,UAAS,CAAC,kBAAkB,GAAG,KAAK,QAAS;IAC1D,OAAQ;QACJ,KAAK;YAAO;gBACR,MAAM;gBACN;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAkB;gBACnB,IAAI,CAAC,CAAA,GAAA,kNAAA,CAAA,cAAgB,AAAD,EAAE,MAAM;oBACxB,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC;gBAC/B;gBACA,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;gBACrB,IAAI,EAAE,KAAK,YAAY,EAAE,GAAG;gBAC5B,iBAAiB,CAAC,MAAM,CAAA,GAAA,kNAAA,CAAA,cAAgB,AAAD,EAAE,IAAI,EAAE,UAAU;gBACzD,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,CAAA,GAAA,8MAAA,CAAA,YAAS,AAAD,EAAE;gBAC3C,MAAM,eAAe,MAAM,CAAA,GAAA,kNAAA,CAAA,YAAc,AAAD,EAAE,KAAK,cAAc,QAAQ,YAAY,MAAM,KAAK,QAAQ,YAAY,CAAA,GAAA,2MAAA,CAAA,YAAS,AAAD,EAAE,OAAO,SAAS,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK;gBACvK,aAAa;oBAAE,KAAK;wBAAE;wBAAG;wBAAK;oBAAI;gBAAE;gBACpC,IAAI,QAAQ,MACR,WAAW,GAAG,CAAC,CAAC,GAAG;gBACvB,IAAI,KACA,WAAW,GAAG,GAAG,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE;gBAC/B,IAAI,KACA,WAAW,GAAG,GAAG,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE;gBAC/B,IAAI,QAAQ,WAAW;oBACnB,MAAM;oBACN;gBACJ;gBACA,MAAM,eAAe,CAAA,GAAA,2MAAA,CAAA,UAAW,AAAD,EAAE;gBACjC,MAAM,QAAQ,IAAI,KAAK,CAAC,CAAC;gBACzB,eAAe,MAAM,CAAA,GAAA,iNAAA,CAAA,OAAK,AAAD,EAAE,OAAO,cAAc;gBAChD;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAgB;gBACjB,MAAM,eAAe,CAAA,GAAA,2MAAA,CAAA,UAAW,AAAD,EAAE;gBACjC,eAAe,MAAM,CAAA,GAAA,iNAAA,CAAA,UAAK,AAAD,EAAE,KAAK,KAAK;gBACrC;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAsB;gBACvB,MAAM,eAAe,CAAA,GAAA,2MAAA,CAAA,UAAW,AAAD,EAAE;gBACjC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;gBACrB,CAAC,EAAE,YAAY,EAAE,GAAG,YAAY,GAAG,MAAM,CAAA,GAAA,mNAAA,CAAA,UAAO,AAAD,EAAE,KAAK,KAAK,KAAK,KAAK,IAAI;gBACzE;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAU;gBACX,MAAM,eAAe,CAAA,GAAA,2MAAA,CAAA,UAAW,AAAD,EAAE;gBACjC,eAAe,MAAM,CAAA,GAAA,iNAAA,CAAA,OAAK,AAAD,EAAE,KAAK,KAAK;gBACrC;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAa;gBACd,MAAM,eAAe,CAAA,GAAA,2MAAA,CAAA,UAAW,AAAD,EAAE;gBACjC,MAAM,EAAE,EAAE,EAAE,GAAG;gBACf,CAAC,EAAE,YAAY,EAAE,GAAG,YAAY,GAAG,MAAM,CAAA,GAAA,gNAAA,CAAA,OAAQ,AAAD,EAAE,KAAK,KAAK,KAAK,GAAG;gBACpE;YACJ;QACA;YAAS;gBACL,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC;YAC/B;IACJ;IACA,OAAO;QAAE;QAAK;QAAc;IAAW;AAC3C;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/jwe/flattened/encrypt.js"], "sourcesContent": ["import { encode as base64url } from '../../runtime/base64url.js';\nimport { unprotected } from '../../lib/private_symbols.js';\nimport encrypt from '../../runtime/encrypt.js';\nimport encryptKeyManagement from '../../lib/encrypt_key_management.js';\nimport { JOSENotSupported, JWEInvalid } from '../../util/errors.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport { encoder, decoder, concat } from '../../lib/buffer_utils.js';\nimport validateCrit from '../../lib/validate_crit.js';\nexport class FlattenedEncrypt {\n    _plaintext;\n    _protectedHeader;\n    _sharedUnprotectedHeader;\n    _unprotectedHeader;\n    _aad;\n    _cek;\n    _iv;\n    _keyManagementParameters;\n    constructor(plaintext) {\n        if (!(plaintext instanceof Uint8Array)) {\n            throw new TypeError('plaintext must be an instance of Uint8Array');\n        }\n        this._plaintext = plaintext;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this._keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this._keyManagementParameters = parameters;\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setSharedUnprotectedHeader(sharedUnprotectedHeader) {\n        if (this._sharedUnprotectedHeader) {\n            throw new TypeError('setSharedUnprotectedHeader can only be called once');\n        }\n        this._sharedUnprotectedHeader = sharedUnprotectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this._unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this._unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    setAdditionalAuthenticatedData(aad) {\n        this._aad = aad;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this._cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this._cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this._iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this._iv = iv;\n        return this;\n    }\n    async encrypt(key, options) {\n        if (!this._protectedHeader && !this._unprotectedHeader && !this._sharedUnprotectedHeader) {\n            throw new JWEInvalid('either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()');\n        }\n        if (!isDisjoint(this._protectedHeader, this._unprotectedHeader, this._sharedUnprotectedHeader)) {\n            throw new JWEInvalid('JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this._protectedHeader,\n            ...this._unprotectedHeader,\n            ...this._sharedUnprotectedHeader,\n        };\n        validateCrit(JWEInvalid, new Map(), options?.crit, this._protectedHeader, joseHeader);\n        if (joseHeader.zip !== undefined) {\n            throw new JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n        }\n        const { alg, enc } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new JWEInvalid('JWE \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        if (typeof enc !== 'string' || !enc) {\n            throw new JWEInvalid('JWE \"enc\" (Encryption Algorithm) Header Parameter missing or invalid');\n        }\n        let encryptedKey;\n        if (this._cek && (alg === 'dir' || alg === 'ECDH-ES')) {\n            throw new TypeError(`setContentEncryptionKey cannot be called with JWE \"alg\" (Algorithm) Header ${alg}`);\n        }\n        let cek;\n        {\n            let parameters;\n            ({ cek, encryptedKey, parameters } = await encryptKeyManagement(alg, enc, key, this._cek, this._keyManagementParameters));\n            if (parameters) {\n                if (options && unprotected in options) {\n                    if (!this._unprotectedHeader) {\n                        this.setUnprotectedHeader(parameters);\n                    }\n                    else {\n                        this._unprotectedHeader = { ...this._unprotectedHeader, ...parameters };\n                    }\n                }\n                else if (!this._protectedHeader) {\n                    this.setProtectedHeader(parameters);\n                }\n                else {\n                    this._protectedHeader = { ...this._protectedHeader, ...parameters };\n                }\n            }\n        }\n        let additionalData;\n        let protectedHeader;\n        let aadMember;\n        if (this._protectedHeader) {\n            protectedHeader = encoder.encode(base64url(JSON.stringify(this._protectedHeader)));\n        }\n        else {\n            protectedHeader = encoder.encode('');\n        }\n        if (this._aad) {\n            aadMember = base64url(this._aad);\n            additionalData = concat(protectedHeader, encoder.encode('.'), encoder.encode(aadMember));\n        }\n        else {\n            additionalData = protectedHeader;\n        }\n        const { ciphertext, tag, iv } = await encrypt(enc, this._plaintext, cek, this._iv, additionalData);\n        const jwe = {\n            ciphertext: base64url(ciphertext),\n        };\n        if (iv) {\n            jwe.iv = base64url(iv);\n        }\n        if (tag) {\n            jwe.tag = base64url(tag);\n        }\n        if (encryptedKey) {\n            jwe.encrypted_key = base64url(encryptedKey);\n        }\n        if (aadMember) {\n            jwe.aad = aadMember;\n        }\n        if (this._protectedHeader) {\n            jwe.protected = decoder.decode(protectedHeader);\n        }\n        if (this._sharedUnprotectedHeader) {\n            jwe.unprotected = this._sharedUnprotectedHeader;\n        }\n        if (this._unprotectedHeader) {\n            jwe.header = this._unprotectedHeader;\n        }\n        return jwe;\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACO,MAAM;IACT,WAAW;IACX,iBAAiB;IACjB,yBAAyB;IACzB,mBAAmB;IACnB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,yBAAyB;IACzB,YAAY,SAAS,CAAE;QACnB,IAAI,CAAC,CAAC,qBAAqB,UAAU,GAAG;YACpC,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,UAAU,GAAG;IACtB;IACA,2BAA2B,UAAU,EAAE;QACnC,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,wBAAwB,GAAG;QAChC,OAAO,IAAI;IACf;IACA,mBAAmB,eAAe,EAAE;QAChC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,gBAAgB,GAAG;QACxB,OAAO,IAAI;IACf;IACA,2BAA2B,uBAAuB,EAAE;QAChD,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,wBAAwB,GAAG;QAChC,OAAO,IAAI;IACf;IACA,qBAAqB,iBAAiB,EAAE;QACpC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,kBAAkB,GAAG;QAC1B,OAAO,IAAI;IACf;IACA,+BAA+B,GAAG,EAAE;QAChC,IAAI,CAAC,IAAI,GAAG;QACZ,OAAO,IAAI;IACf;IACA,wBAAwB,GAAG,EAAE;QACzB,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,IAAI,GAAG;QACZ,OAAO,IAAI;IACf;IACA,wBAAwB,EAAE,EAAE;QACxB,IAAI,IAAI,CAAC,GAAG,EAAE;YACV,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,GAAG,GAAG;QACX,OAAO,IAAI;IACf;IACA,MAAM,QAAQ,GAAG,EAAE,OAAO,EAAE;QACxB,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;YACtF,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;QACzB;QACA,IAAI,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAU,AAAD,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,wBAAwB,GAAG;YAC5F,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;QACzB;QACA,MAAM,aAAa;YACf,GAAG,IAAI,CAAC,gBAAgB;YACxB,GAAG,IAAI,CAAC,kBAAkB;YAC1B,GAAG,IAAI,CAAC,wBAAwB;QACpC;QACA,CAAA,GAAA,qNAAA,CAAA,UAAY,AAAD,EAAE,+MAAA,CAAA,aAAU,EAAE,IAAI,OAAO,SAAS,MAAM,IAAI,CAAC,gBAAgB,EAAE;QAC1E,IAAI,WAAW,GAAG,KAAK,WAAW;YAC9B,MAAM,IAAI,+MAAA,CAAA,mBAAgB,CAAC;QAC/B;QACA,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;QACrB,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;YACjC,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;QACzB;QACA,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;YACjC,MAAM,IAAI,+MAAA,CAAA,aAAU,CAAC;QACzB;QACA,IAAI;QACJ,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,SAAS,QAAQ,SAAS,GAAG;YACnD,MAAM,IAAI,UAAU,CAAC,2EAA2E,EAAE,KAAK;QAC3G;QACA,IAAI;QACJ;YACI,IAAI;YACJ,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,MAAM,CAAA,GAAA,8NAAA,CAAA,UAAoB,AAAD,EAAE,KAAK,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,wBAAwB,CAAC;YACxH,IAAI,YAAY;gBACZ,IAAI,WAAW,uNAAA,CAAA,cAAW,IAAI,SAAS;oBACnC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;wBAC1B,IAAI,CAAC,oBAAoB,CAAC;oBAC9B,OACK;wBACD,IAAI,CAAC,kBAAkB,GAAG;4BAAE,GAAG,IAAI,CAAC,kBAAkB;4BAAE,GAAG,UAAU;wBAAC;oBAC1E;gBACJ,OACK,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;oBAC7B,IAAI,CAAC,kBAAkB,CAAC;gBAC5B,OACK;oBACD,IAAI,CAAC,gBAAgB,GAAG;wBAAE,GAAG,IAAI,CAAC,gBAAgB;wBAAE,GAAG,UAAU;oBAAC;gBACtE;YACJ;QACJ;QACA,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,kBAAkB,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,KAAK,SAAS,CAAC,IAAI,CAAC,gBAAgB;QACnF,OACK;YACD,kBAAkB,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACrC;QACA,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,YAAY,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE,IAAI,CAAC,IAAI;YAC/B,iBAAiB,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,iBAAiB,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC,MAAM,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACjF,OACK;YACD,iBAAiB;QACrB;QACA,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,MAAM,CAAA,GAAA,mNAAA,CAAA,UAAO,AAAD,EAAE,KAAK,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE;QACnF,MAAM,MAAM;YACR,YAAY,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE;QAC1B;QACA,IAAI,IAAI;YACJ,IAAI,EAAE,GAAG,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE;QACvB;QACA,IAAI,KAAK;YACL,IAAI,GAAG,GAAG,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE;QACxB;QACA,IAAI,cAAc;YACd,IAAI,aAAa,GAAG,CAAA,GAAA,qNAAA,CAAA,SAAS,AAAD,EAAE;QAClC;QACA,IAAI,WAAW;YACX,IAAI,GAAG,GAAG;QACd;QACA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,SAAS,GAAG,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACnC;QACA,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,WAAW,GAAG,IAAI,CAAC,wBAAwB;QACnD;QACA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,MAAM,GAAG,IAAI,CAAC,kBAAkB;QACxC;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/jwe/compact/encrypt.js"], "sourcesContent": ["import { FlattenedEncrypt } from '../flattened/encrypt.js';\nexport class CompactEncrypt {\n    _flattened;\n    constructor(plaintext) {\n        this._flattened = new FlattenedEncrypt(plaintext);\n    }\n    setContentEncryptionKey(cek) {\n        this._flattened.setContentEncryptionKey(cek);\n        return this;\n    }\n    setInitializationVector(iv) {\n        this._flattened.setInitializationVector(iv);\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        this._flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        this._flattened.setKeyManagementParameters(parameters);\n        return this;\n    }\n    async encrypt(key, options) {\n        const jwe = await this._flattened.encrypt(key, options);\n        return [jwe.protected, jwe.encrypted_key, jwe.iv, jwe.ciphertext, jwe.tag].join('.');\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM;IACT,WAAW;IACX,YAAY,SAAS,CAAE;QACnB,IAAI,CAAC,UAAU,GAAG,IAAI,4NAAA,CAAA,mBAAgB,CAAC;IAC3C;IACA,wBAAwB,GAAG,EAAE;QACzB,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC;QACxC,OAAO,IAAI;IACf;IACA,wBAAwB,EAAE,EAAE;QACxB,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC;QACxC,OAAO,IAAI;IACf;IACA,mBAAmB,eAAe,EAAE;QAChC,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;QACnC,OAAO,IAAI;IACf;IACA,2BAA2B,UAAU,EAAE;QACnC,IAAI,CAAC,UAAU,CAAC,0BAA0B,CAAC;QAC3C,OAAO,IAAI;IACf;IACA,MAAM,QAAQ,GAAG,EAAE,OAAO,EAAE;QACxB,MAAM,MAAM,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK;QAC/C,OAAO;YAAC,IAAI,SAAS;YAAE,IAAI,aAAa;YAAE,IAAI,EAAE;YAAE,IAAI,UAAU;YAAE,IAAI,GAAG;SAAC,CAAC,IAAI,CAAC;IACpF;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3446, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/jwt/produce.js"], "sourcesContent": ["import epoch from '../lib/epoch.js';\nimport isObject from '../lib/is_object.js';\nimport secs from '../lib/secs.js';\nfunction validateInput(label, input) {\n    if (!Number.isFinite(input)) {\n        throw new TypeError(`Invalid ${label} input`);\n    }\n    return input;\n}\nexport class ProduceJWT {\n    _payload;\n    constructor(payload = {}) {\n        if (!isObject(payload)) {\n            throw new TypeError('JWT Claims Set MUST be an object');\n        }\n        this._payload = payload;\n    }\n    setIssuer(issuer) {\n        this._payload = { ...this._payload, iss: issuer };\n        return this;\n    }\n    setSubject(subject) {\n        this._payload = { ...this._payload, sub: subject };\n        return this;\n    }\n    setAudience(audience) {\n        this._payload = { ...this._payload, aud: audience };\n        return this;\n    }\n    setJti(jwtId) {\n        this._payload = { ...this._payload, jti: jwtId };\n        return this;\n    }\n    setNotBefore(input) {\n        if (typeof input === 'number') {\n            this._payload = { ...this._payload, nbf: validateInput('setNotBefore', input) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, nbf: validateInput('setNotBefore', epoch(input)) };\n        }\n        else {\n            this._payload = { ...this._payload, nbf: epoch(new Date()) + secs(input) };\n        }\n        return this;\n    }\n    setExpirationTime(input) {\n        if (typeof input === 'number') {\n            this._payload = { ...this._payload, exp: validateInput('setExpirationTime', input) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, exp: validateInput('setExpirationTime', epoch(input)) };\n        }\n        else {\n            this._payload = { ...this._payload, exp: epoch(new Date()) + secs(input) };\n        }\n        return this;\n    }\n    setIssuedAt(input) {\n        if (typeof input === 'undefined') {\n            this._payload = { ...this._payload, iat: epoch(new Date()) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, iat: validateInput('setIssuedAt', epoch(input)) };\n        }\n        else if (typeof input === 'string') {\n            this._payload = {\n                ...this._payload,\n                iat: validateInput('setIssuedAt', epoch(new Date()) + secs(input)),\n            };\n        }\n        else {\n            this._payload = { ...this._payload, iat: validateInput('setIssuedAt', input) };\n        }\n        return this;\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,SAAS,cAAc,KAAK,EAAE,KAAK;IAC/B,IAAI,CAAC,OAAO,QAAQ,CAAC,QAAQ;QACzB,MAAM,IAAI,UAAU,CAAC,QAAQ,EAAE,MAAM,MAAM,CAAC;IAChD;IACA,OAAO;AACX;AACO,MAAM;IACT,SAAS;IACT,YAAY,UAAU,CAAC,CAAC,CAAE;QACtB,IAAI,CAAC,CAAA,GAAA,iNAAA,CAAA,UAAQ,AAAD,EAAE,UAAU;YACpB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,UAAU,MAAM,EAAE;QACd,IAAI,CAAC,QAAQ,GAAG;YAAE,GAAG,IAAI,CAAC,QAAQ;YAAE,KAAK;QAAO;QAChD,OAAO,IAAI;IACf;IACA,WAAW,OAAO,EAAE;QAChB,IAAI,CAAC,QAAQ,GAAG;YAAE,GAAG,IAAI,CAAC,QAAQ;YAAE,KAAK;QAAQ;QACjD,OAAO,IAAI;IACf;IACA,YAAY,QAAQ,EAAE;QAClB,IAAI,CAAC,QAAQ,GAAG;YAAE,GAAG,IAAI,CAAC,QAAQ;YAAE,KAAK;QAAS;QAClD,OAAO,IAAI;IACf;IACA,OAAO,KAAK,EAAE;QACV,IAAI,CAAC,QAAQ,GAAG;YAAE,GAAG,IAAI,CAAC,QAAQ;YAAE,KAAK;QAAM;QAC/C,OAAO,IAAI;IACf;IACA,aAAa,KAAK,EAAE;QAChB,IAAI,OAAO,UAAU,UAAU;YAC3B,IAAI,CAAC,QAAQ,GAAG;gBAAE,GAAG,IAAI,CAAC,QAAQ;gBAAE,KAAK,cAAc,gBAAgB;YAAO;QAClF,OACK,IAAI,iBAAiB,MAAM;YAC5B,IAAI,CAAC,QAAQ,GAAG;gBAAE,GAAG,IAAI,CAAC,QAAQ;gBAAE,KAAK,cAAc,gBAAgB,CAAA,GAAA,6MAAA,CAAA,UAAK,AAAD,EAAE;YAAQ;QACzF,OACK;YACD,IAAI,CAAC,QAAQ,GAAG;gBAAE,GAAG,IAAI,CAAC,QAAQ;gBAAE,KAAK,CAAA,GAAA,6MAAA,CAAA,UAAK,AAAD,EAAE,IAAI,UAAU,CAAA,GAAA,4MAAA,CAAA,UAAI,AAAD,EAAE;YAAO;QAC7E;QACA,OAAO,IAAI;IACf;IACA,kBAAkB,KAAK,EAAE;QACrB,IAAI,OAAO,UAAU,UAAU;YAC3B,IAAI,CAAC,QAAQ,GAAG;gBAAE,GAAG,IAAI,CAAC,QAAQ;gBAAE,KAAK,cAAc,qBAAqB;YAAO;QACvF,OACK,IAAI,iBAAiB,MAAM;YAC5B,IAAI,CAAC,QAAQ,GAAG;gBAAE,GAAG,IAAI,CAAC,QAAQ;gBAAE,KAAK,cAAc,qBAAqB,CAAA,GAAA,6MAAA,CAAA,UAAK,AAAD,EAAE;YAAQ;QAC9F,OACK;YACD,IAAI,CAAC,QAAQ,GAAG;gBAAE,GAAG,IAAI,CAAC,QAAQ;gBAAE,KAAK,CAAA,GAAA,6MAAA,CAAA,UAAK,AAAD,EAAE,IAAI,UAAU,CAAA,GAAA,4MAAA,CAAA,UAAI,AAAD,EAAE;YAAO;QAC7E;QACA,OAAO,IAAI;IACf;IACA,YAAY,KAAK,EAAE;QACf,IAAI,OAAO,UAAU,aAAa;YAC9B,IAAI,CAAC,QAAQ,GAAG;gBAAE,GAAG,IAAI,CAAC,QAAQ;gBAAE,KAAK,CAAA,GAAA,6MAAA,CAAA,UAAK,AAAD,EAAE,IAAI;YAAQ;QAC/D,OACK,IAAI,iBAAiB,MAAM;YAC5B,IAAI,CAAC,QAAQ,GAAG;gBAAE,GAAG,IAAI,CAAC,QAAQ;gBAAE,KAAK,cAAc,eAAe,CAAA,GAAA,6MAAA,CAAA,UAAK,AAAD,EAAE;YAAQ;QACxF,OACK,IAAI,OAAO,UAAU,UAAU;YAChC,IAAI,CAAC,QAAQ,GAAG;gBACZ,GAAG,IAAI,CAAC,QAAQ;gBAChB,KAAK,cAAc,eAAe,CAAA,GAAA,6MAAA,CAAA,UAAK,AAAD,EAAE,IAAI,UAAU,CAAA,GAAA,4MAAA,CAAA,UAAI,AAAD,EAAE;YAC/D;QACJ,OACK;YACD,IAAI,CAAC,QAAQ,GAAG;gBAAE,GAAG,IAAI,CAAC,QAAQ;gBAAE,KAAK,cAAc,eAAe;YAAO;QACjF;QACA,OAAO,IAAI;IACf;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3566, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/jose%405.10.0/node_modules/jose/dist/node/esm/jwt/encrypt.js"], "sourcesContent": ["import { CompactEncrypt } from '../jwe/compact/encrypt.js';\nimport { encoder } from '../lib/buffer_utils.js';\nimport { ProduceJWT } from './produce.js';\nexport class EncryptJWT extends ProduceJWT {\n    _cek;\n    _iv;\n    _keyManagementParameters;\n    _protectedHeader;\n    _replicateIssuerAsHeader;\n    _replicateSubjectAsHeader;\n    _replicateAudienceAsHeader;\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this._keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this._keyManagementParameters = parameters;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this._cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this._cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this._iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this._iv = iv;\n        return this;\n    }\n    replicateIssuerAsHeader() {\n        this._replicateIssuerAsHeader = true;\n        return this;\n    }\n    replicateSubjectAsHeader() {\n        this._replicateSubjectAsHeader = true;\n        return this;\n    }\n    replicateAudienceAsHeader() {\n        this._replicateAudienceAsHeader = true;\n        return this;\n    }\n    async encrypt(key, options) {\n        const enc = new CompactEncrypt(encoder.encode(JSON.stringify(this._payload)));\n        if (this._replicateIssuerAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, iss: this._payload.iss };\n        }\n        if (this._replicateSubjectAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, sub: this._payload.sub };\n        }\n        if (this._replicateAudienceAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, aud: this._payload.aud };\n        }\n        enc.setProtectedHeader(this._protectedHeader);\n        if (this._iv) {\n            enc.setInitializationVector(this._iv);\n        }\n        if (this._cek) {\n            enc.setContentEncryptionKey(this._cek);\n        }\n        if (this._keyManagementParameters) {\n            enc.setKeyManagementParameters(this._keyManagementParameters);\n        }\n        return enc.encrypt(key, options);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,MAAM,mBAAmB,+MAAA,CAAA,aAAU;IACtC,KAAK;IACL,IAAI;IACJ,yBAAyB;IACzB,iBAAiB;IACjB,yBAAyB;IACzB,0BAA0B;IAC1B,2BAA2B;IAC3B,mBAAmB,eAAe,EAAE;QAChC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,gBAAgB,GAAG;QACxB,OAAO,IAAI;IACf;IACA,2BAA2B,UAAU,EAAE;QACnC,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,wBAAwB,GAAG;QAChC,OAAO,IAAI;IACf;IACA,wBAAwB,GAAG,EAAE;QACzB,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,IAAI,GAAG;QACZ,OAAO,IAAI;IACf;IACA,wBAAwB,EAAE,EAAE;QACxB,IAAI,IAAI,CAAC,GAAG,EAAE;YACV,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,GAAG,GAAG;QACX,OAAO,IAAI;IACf;IACA,0BAA0B;QACtB,IAAI,CAAC,wBAAwB,GAAG;QAChC,OAAO,IAAI;IACf;IACA,2BAA2B;QACvB,IAAI,CAAC,yBAAyB,GAAG;QACjC,OAAO,IAAI;IACf;IACA,4BAA4B;QACxB,IAAI,CAAC,0BAA0B,GAAG;QAClC,OAAO,IAAI;IACf;IACA,MAAM,QAAQ,GAAG,EAAE,OAAO,EAAE;QACxB,MAAM,MAAM,IAAI,0NAAA,CAAA,iBAAc,CAAC,oNAAA,CAAA,UAAO,CAAC,MAAM,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,QAAQ;QAC1E,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,gBAAgB,GAAG;gBAAE,GAAG,IAAI,CAAC,gBAAgB;gBAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,GAAG;YAAC;QAC/E;QACA,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,IAAI,CAAC,gBAAgB,GAAG;gBAAE,GAAG,IAAI,CAAC,gBAAgB;gBAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,GAAG;YAAC;QAC/E;QACA,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjC,IAAI,CAAC,gBAAgB,GAAG;gBAAE,GAAG,IAAI,CAAC,gBAAgB;gBAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,GAAG;YAAC;QAC/E;QACA,IAAI,kBAAkB,CAAC,IAAI,CAAC,gBAAgB;QAC5C,IAAI,IAAI,CAAC,GAAG,EAAE;YACV,IAAI,uBAAuB,CAAC,IAAI,CAAC,GAAG;QACxC;QACA,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,uBAAuB,CAAC,IAAI,CAAC,IAAI;QACzC;QACA,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,0BAA0B,CAAC,IAAI,CAAC,wBAAwB;QAChE;QACA,OAAO,IAAI,OAAO,CAAC,KAAK;IAC5B;AACJ", "ignoreList": [0], "debugId": null}}]}