{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/app/app/not-found.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nexport default function NotFound() {\n  const router = useRouter();\n\n  useEffect(() => {\n    // Redirect to dashboard on 404\n    router.replace('/dashboard');\n  }, [router]);\n\n  // Show a brief loading message while redirecting\n  return (\n    <div className=\"flex items-center justify-center min-h-screen\">\n      <div className=\"text-center\">\n        <p className=\"text-muted-foreground\">Redirecting to dashboard...</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iPAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,+BAA+B;QAC/B,OAAO,OAAO,CAAC;IACjB,GAAG;QAAC;KAAO;IAEX,iDAAiD;IACjD,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;;;;;;AAI7C", "debugId": null}}]}