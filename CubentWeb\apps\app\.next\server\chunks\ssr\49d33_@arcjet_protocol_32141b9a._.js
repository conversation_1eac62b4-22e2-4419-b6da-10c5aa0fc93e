module.exports = {

"[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/proto/decide/v1alpha1/decide_pb.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// @generated by protoc-gen-es v1.10.0
// @generated from file proto/decide/v1alpha1/decide.proto (package proto.decide.v1alpha1, syntax proto3)
/* eslint-disable */ // @ts-nocheck
__turbopack_context__.s({
    "BotReason": (()=>BotReason),
    "BotRule": (()=>BotRule),
    "BotRule_Patterns": (()=>BotRule_Patterns),
    "BotType": (()=>BotType),
    "BotV2Reason": (()=>BotV2Reason),
    "BotV2Rule": (()=>BotV2Rule),
    "BotV2RuleVersion": (()=>BotV2RuleVersion),
    "Conclusion": (()=>Conclusion),
    "DecideRequest": (()=>DecideRequest),
    "DecideResponse": (()=>DecideResponse),
    "Decision": (()=>Decision),
    "EdgeRuleReason": (()=>EdgeRuleReason),
    "EmailReason": (()=>EmailReason),
    "EmailRule": (()=>EmailRule),
    "EmailRuleVersion": (()=>EmailRuleVersion),
    "EmailType": (()=>EmailType),
    "ErrorReason": (()=>ErrorReason),
    "IdentifiedEntity": (()=>IdentifiedEntity),
    "IpDetails": (()=>IpDetails),
    "Mode": (()=>Mode),
    "RateLimitAlgorithm": (()=>RateLimitAlgorithm),
    "RateLimitReason": (()=>RateLimitReason),
    "RateLimitRule": (()=>RateLimitRule),
    "RateLimitRuleVersion": (()=>RateLimitRuleVersion),
    "Reason": (()=>Reason),
    "ReportRequest": (()=>ReportRequest),
    "ReportResponse": (()=>ReportResponse),
    "RequestDetails": (()=>RequestDetails),
    "Rule": (()=>Rule),
    "RuleResult": (()=>RuleResult),
    "RuleState": (()=>RuleState),
    "SDKStack": (()=>SDKStack),
    "SensitiveInfoReason": (()=>SensitiveInfoReason),
    "SensitiveInfoRule": (()=>SensitiveInfoRule),
    "SensitiveInfoRuleVersion": (()=>SensitiveInfoRuleVersion),
    "ShieldReason": (()=>ShieldReason),
    "ShieldRule": (()=>ShieldRule),
    "ShieldRuleVersion": (()=>ShieldRuleVersion)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/proto3.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$google$2f$protobuf$2f$timestamp_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/google/protobuf/timestamp_pb.js [app-rsc] (ecmascript)");
;
const BotType = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeEnum("proto.decide.v1alpha1.BotType", [
    {
        no: 0,
        name: "BOT_TYPE_UNSPECIFIED",
        localName: "UNSPECIFIED"
    },
    {
        no: 1,
        name: "BOT_TYPE_NOT_ANALYZED",
        localName: "NOT_ANALYZED"
    },
    {
        no: 2,
        name: "BOT_TYPE_AUTOMATED",
        localName: "AUTOMATED"
    },
    {
        no: 3,
        name: "BOT_TYPE_LIKELY_AUTOMATED",
        localName: "LIKELY_AUTOMATED"
    },
    {
        no: 4,
        name: "BOT_TYPE_LIKELY_NOT_A_BOT",
        localName: "LIKELY_NOT_A_BOT"
    },
    {
        no: 5,
        name: "BOT_TYPE_VERIFIED_BOT",
        localName: "VERIFIED_BOT"
    }
]);
const EmailType = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeEnum("proto.decide.v1alpha1.EmailType", [
    {
        no: 0,
        name: "EMAIL_TYPE_UNSPECIFIED",
        localName: "UNSPECIFIED"
    },
    {
        no: 1,
        name: "EMAIL_TYPE_DISPOSABLE",
        localName: "DISPOSABLE"
    },
    {
        no: 2,
        name: "EMAIL_TYPE_FREE",
        localName: "FREE"
    },
    {
        no: 3,
        name: "EMAIL_TYPE_NO_MX_RECORDS",
        localName: "NO_MX_RECORDS"
    },
    {
        no: 4,
        name: "EMAIL_TYPE_NO_GRAVATAR",
        localName: "NO_GRAVATAR"
    },
    {
        no: 5,
        name: "EMAIL_TYPE_INVALID",
        localName: "INVALID"
    }
]);
const Mode = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeEnum("proto.decide.v1alpha1.Mode", [
    {
        no: 0,
        name: "MODE_UNSPECIFIED",
        localName: "UNSPECIFIED"
    },
    {
        no: 1,
        name: "MODE_DRY_RUN",
        localName: "DRY_RUN"
    },
    {
        no: 2,
        name: "MODE_LIVE",
        localName: "LIVE"
    }
]);
const RuleState = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeEnum("proto.decide.v1alpha1.RuleState", [
    {
        no: 0,
        name: "RULE_STATE_UNSPECIFIED",
        localName: "UNSPECIFIED"
    },
    {
        no: 1,
        name: "RULE_STATE_RUN",
        localName: "RUN"
    },
    {
        no: 2,
        name: "RULE_STATE_NOT_RUN",
        localName: "NOT_RUN"
    },
    {
        no: 3,
        name: "RULE_STATE_DRY_RUN",
        localName: "DRY_RUN"
    },
    {
        no: 4,
        name: "RULE_STATE_CACHED",
        localName: "CACHED"
    }
]);
const Conclusion = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeEnum("proto.decide.v1alpha1.Conclusion", [
    {
        no: 0,
        name: "CONCLUSION_UNSPECIFIED",
        localName: "UNSPECIFIED"
    },
    {
        no: 1,
        name: "CONCLUSION_ALLOW",
        localName: "ALLOW"
    },
    {
        no: 2,
        name: "CONCLUSION_DENY",
        localName: "DENY"
    },
    {
        no: 3,
        name: "CONCLUSION_CHALLENGE",
        localName: "CHALLENGE"
    },
    {
        no: 4,
        name: "CONCLUSION_ERROR",
        localName: "ERROR"
    }
]);
const SDKStack = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeEnum("proto.decide.v1alpha1.SDKStack", [
    {
        no: 0,
        name: "SDK_STACK_UNSPECIFIED"
    },
    {
        no: 1,
        name: "SDK_STACK_NODEJS"
    },
    {
        no: 2,
        name: "SDK_STACK_NEXTJS"
    },
    {
        no: 3,
        name: "SDK_STACK_PYTHON"
    },
    {
        no: 4,
        name: "SDK_STACK_DJANGO"
    },
    {
        no: 5,
        name: "SDK_STACK_BUN"
    },
    {
        no: 6,
        name: "SDK_STACK_DENO"
    },
    {
        no: 7,
        name: "SDK_STACK_SVELTEKIT"
    },
    {
        no: 8,
        name: "SDK_STACK_HONO"
    },
    {
        no: 9,
        name: "SDK_STACK_NUXT"
    },
    {
        no: 10,
        name: "SDK_STACK_NESTJS"
    },
    {
        no: 11,
        name: "SDK_STACK_REMIX"
    },
    {
        no: 12,
        name: "SDK_STACK_ASTRO"
    }
]);
const RateLimitAlgorithm = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeEnum("proto.decide.v1alpha1.RateLimitAlgorithm", [
    {
        no: 0,
        name: "RATE_LIMIT_ALGORITHM_UNSPECIFIED",
        localName: "UNSPECIFIED"
    },
    {
        no: 1,
        name: "RATE_LIMIT_ALGORITHM_TOKEN_BUCKET",
        localName: "TOKEN_BUCKET"
    },
    {
        no: 2,
        name: "RATE_LIMIT_ALGORITHM_FIXED_WINDOW",
        localName: "FIXED_WINDOW"
    },
    {
        no: 3,
        name: "RATE_LIMIT_ALGORITHM_SLIDING_WINDOW",
        localName: "SLIDING_WINDOW"
    }
]);
const RateLimitRuleVersion = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeEnum("proto.decide.v1alpha1.RateLimitRuleVersion", [
    {
        no: 0,
        name: "RATE_LIMIT_RULE_VERSION_UNSPECIFIED",
        localName: "UNSPECIFIED"
    }
]);
const BotV2RuleVersion = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeEnum("proto.decide.v1alpha1.BotV2RuleVersion", [
    {
        no: 0,
        name: "BOT_V2_RULE_VERSION_UNSPECIFIED",
        localName: "UNSPECIFIED"
    }
]);
const EmailRuleVersion = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeEnum("proto.decide.v1alpha1.EmailRuleVersion", [
    {
        no: 0,
        name: "EMAIL_RULE_VERSION_UNSPECIFIED",
        localName: "UNSPECIFIED"
    }
]);
const SensitiveInfoRuleVersion = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeEnum("proto.decide.v1alpha1.SensitiveInfoRuleVersion", [
    {
        no: 0,
        name: "SENSITIVE_INFO_RULE_VERSION_UNSPECIFIED",
        localName: "UNSPECIFIED"
    }
]);
const ShieldRuleVersion = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeEnum("proto.decide.v1alpha1.ShieldRuleVersion", [
    {
        no: 0,
        name: "SHIELD_RULE_VERSION_UNSPECIFIED",
        localName: "UNSPECIFIED"
    }
]);
const IpDetails = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.IpDetails", ()=>[
        {
            no: 1,
            name: "latitude",
            kind: "scalar",
            T: 1 /* ScalarType.DOUBLE */ 
        },
        {
            no: 2,
            name: "longitude",
            kind: "scalar",
            T: 1 /* ScalarType.DOUBLE */ 
        },
        {
            no: 3,
            name: "accuracy_radius",
            kind: "scalar",
            T: 5 /* ScalarType.INT32 */ 
        },
        {
            no: 4,
            name: "timezone",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 5,
            name: "postal_code",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 6,
            name: "city",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 7,
            name: "region",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 8,
            name: "country",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 9,
            name: "country_name",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 10,
            name: "continent",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 11,
            name: "continent_name",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 12,
            name: "asn",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 13,
            name: "asn_name",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 14,
            name: "asn_domain",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 15,
            name: "asn_type",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 16,
            name: "asn_country",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 17,
            name: "service",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 18,
            name: "is_hosting",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        },
        {
            no: 19,
            name: "is_vpn",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        },
        {
            no: 20,
            name: "is_proxy",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        },
        {
            no: 21,
            name: "is_tor",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        },
        {
            no: 22,
            name: "is_relay",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        }
    ]);
const Reason = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.Reason", ()=>[
        {
            no: 1,
            name: "rate_limit",
            kind: "message",
            T: RateLimitReason,
            oneof: "reason"
        },
        {
            no: 2,
            name: "edge_rule",
            kind: "message",
            T: EdgeRuleReason,
            oneof: "reason"
        },
        {
            no: 3,
            name: "bot",
            kind: "message",
            T: BotReason,
            oneof: "reason"
        },
        {
            no: 4,
            name: "shield",
            kind: "message",
            T: ShieldReason,
            oneof: "reason"
        },
        {
            no: 5,
            name: "email",
            kind: "message",
            T: EmailReason,
            oneof: "reason"
        },
        {
            no: 6,
            name: "error",
            kind: "message",
            T: ErrorReason,
            oneof: "reason"
        },
        {
            no: 7,
            name: "sensitive_info",
            kind: "message",
            T: SensitiveInfoReason,
            oneof: "reason"
        },
        {
            no: 8,
            name: "bot_v2",
            kind: "message",
            T: BotV2Reason,
            oneof: "reason"
        }
    ]);
const RateLimitReason = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.RateLimitReason", ()=>[
        {
            no: 1,
            name: "max",
            kind: "scalar",
            T: 13 /* ScalarType.UINT32 */ 
        },
        {
            no: 2,
            name: "count",
            kind: "scalar",
            T: 5 /* ScalarType.INT32 */ 
        },
        {
            no: 3,
            name: "remaining",
            kind: "scalar",
            T: 13 /* ScalarType.UINT32 */ 
        },
        {
            no: 4,
            name: "reset_time",
            kind: "message",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$google$2f$protobuf$2f$timestamp_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Timestamp"]
        },
        {
            no: 5,
            name: "reset_in_seconds",
            kind: "scalar",
            T: 13 /* ScalarType.UINT32 */ 
        },
        {
            no: 6,
            name: "window_in_seconds",
            kind: "scalar",
            T: 13 /* ScalarType.UINT32 */ 
        }
    ]);
const EdgeRuleReason = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.EdgeRuleReason", []);
const BotReason = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.BotReason", ()=>[
        {
            no: 1,
            name: "bot_type",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(BotType)
        },
        {
            no: 2,
            name: "bot_score",
            kind: "scalar",
            T: 5 /* ScalarType.INT32 */ 
        },
        {
            no: 3,
            name: "user_agent_match",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        },
        {
            no: 5,
            name: "ip_hosting",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        },
        {
            no: 6,
            name: "ip_vpn",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        },
        {
            no: 7,
            name: "ip_proxy",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        },
        {
            no: 8,
            name: "ip_tor",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        },
        {
            no: 9,
            name: "ip_relay",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        }
    ]);
const BotV2Reason = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.BotV2Reason", ()=>[
        {
            no: 1,
            name: "allowed",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ ,
            repeated: true
        },
        {
            no: 2,
            name: "denied",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ ,
            repeated: true
        },
        {
            no: 3,
            name: "verified",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        },
        {
            no: 4,
            name: "spoofed",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        }
    ]);
const ShieldReason = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.ShieldReason", ()=>[
        {
            no: 1,
            name: "shield_triggered",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        },
        {
            no: 2,
            name: "suspicious",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        }
    ]);
const EmailReason = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.EmailReason", ()=>[
        {
            no: 1,
            name: "email_types",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(EmailType),
            repeated: true
        }
    ]);
const ErrorReason = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.ErrorReason", ()=>[
        {
            no: 1,
            name: "message",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        }
    ]);
const IdentifiedEntity = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.IdentifiedEntity", ()=>[
        {
            no: 1,
            name: "identified_type",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 2,
            name: "start",
            kind: "scalar",
            T: 13 /* ScalarType.UINT32 */ 
        },
        {
            no: 3,
            name: "end",
            kind: "scalar",
            T: 13 /* ScalarType.UINT32 */ 
        }
    ]);
const SensitiveInfoReason = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.SensitiveInfoReason", ()=>[
        {
            no: 1,
            name: "allowed",
            kind: "message",
            T: IdentifiedEntity,
            repeated: true
        },
        {
            no: 2,
            name: "denied",
            kind: "message",
            T: IdentifiedEntity,
            repeated: true
        }
    ]);
const RateLimitRule = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.RateLimitRule", ()=>[
        {
            no: 1,
            name: "mode",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(Mode)
        },
        {
            no: 2,
            name: "match",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 3,
            name: "characteristics",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ ,
            repeated: true
        },
        {
            no: 4,
            name: "window",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 5,
            name: "max",
            kind: "scalar",
            T: 13 /* ScalarType.UINT32 */ 
        },
        {
            no: 6,
            name: "timeout",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 7,
            name: "algorithm",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(RateLimitAlgorithm)
        },
        {
            no: 8,
            name: "refill_rate",
            kind: "scalar",
            T: 13 /* ScalarType.UINT32 */ 
        },
        {
            no: 9,
            name: "interval",
            kind: "scalar",
            T: 13 /* ScalarType.UINT32 */ 
        },
        {
            no: 10,
            name: "capacity",
            kind: "scalar",
            T: 13 /* ScalarType.UINT32 */ 
        },
        {
            no: 12,
            name: "window_in_seconds",
            kind: "scalar",
            T: 13 /* ScalarType.UINT32 */ 
        },
        {
            no: 13,
            name: "version",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(RateLimitRuleVersion)
        }
    ]);
const BotRule = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.BotRule", ()=>[
        {
            no: 1,
            name: "mode",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(Mode)
        },
        {
            no: 2,
            name: "block",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(BotType),
            repeated: true
        },
        {
            no: 3,
            name: "patterns",
            kind: "message",
            T: BotRule_Patterns
        }
    ]);
const BotRule_Patterns = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.BotRule.Patterns", ()=>[
        {
            no: 1,
            name: "add",
            kind: "map",
            K: 9 /* ScalarType.STRING */ ,
            V: {
                kind: "enum",
                T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(BotType)
            }
        },
        {
            no: 2,
            name: "remove",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ ,
            repeated: true
        }
    ], {
    localName: "BotRule_Patterns"
});
const BotV2Rule = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.BotV2Rule", ()=>[
        {
            no: 1,
            name: "mode",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(Mode)
        },
        {
            no: 2,
            name: "allow",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ ,
            repeated: true
        },
        {
            no: 3,
            name: "deny",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ ,
            repeated: true
        },
        {
            no: 4,
            name: "version",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(BotV2RuleVersion)
        }
    ]);
const EmailRule = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.EmailRule", ()=>[
        {
            no: 1,
            name: "mode",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(Mode)
        },
        {
            no: 2,
            name: "block",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(EmailType),
            repeated: true
        },
        {
            no: 3,
            name: "require_top_level_domain",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        },
        {
            no: 4,
            name: "allow_domain_literal",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        },
        {
            no: 5,
            name: "allow",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(EmailType),
            repeated: true
        },
        {
            no: 6,
            name: "deny",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(EmailType),
            repeated: true
        },
        {
            no: 7,
            name: "version",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(EmailRuleVersion)
        }
    ]);
const SensitiveInfoRule = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.SensitiveInfoRule", ()=>[
        {
            no: 1,
            name: "mode",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(Mode)
        },
        {
            no: 2,
            name: "allow",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ ,
            repeated: true
        },
        {
            no: 3,
            name: "deny",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ ,
            repeated: true
        },
        {
            no: 4,
            name: "version",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(SensitiveInfoRuleVersion)
        }
    ]);
const ShieldRule = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.ShieldRule", ()=>[
        {
            no: 1,
            name: "mode",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(Mode)
        },
        {
            no: 2,
            name: "auto_added",
            kind: "scalar",
            T: 8 /* ScalarType.BOOL */ 
        },
        {
            no: 3,
            name: "characteristics",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ ,
            repeated: true
        },
        {
            no: 4,
            name: "version",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(ShieldRuleVersion)
        }
    ]);
const Rule = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.Rule", ()=>[
        {
            no: 1,
            name: "rate_limit",
            kind: "message",
            T: RateLimitRule,
            oneof: "rule"
        },
        {
            no: 2,
            name: "bots",
            kind: "message",
            T: BotRule,
            oneof: "rule"
        },
        {
            no: 3,
            name: "email",
            kind: "message",
            T: EmailRule,
            oneof: "rule"
        },
        {
            no: 4,
            name: "shield",
            kind: "message",
            T: ShieldRule,
            oneof: "rule"
        },
        {
            no: 5,
            name: "sensitive_info",
            kind: "message",
            T: SensitiveInfoRule,
            oneof: "rule"
        },
        {
            no: 6,
            name: "bot_v2",
            kind: "message",
            T: BotV2Rule,
            oneof: "rule"
        }
    ]);
const RuleResult = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.RuleResult", ()=>[
        {
            no: 1,
            name: "rule_id",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 2,
            name: "state",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(RuleState)
        },
        {
            no: 3,
            name: "conclusion",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(Conclusion)
        },
        {
            no: 4,
            name: "reason",
            kind: "message",
            T: Reason
        },
        {
            no: 5,
            name: "ttl",
            kind: "scalar",
            T: 13 /* ScalarType.UINT32 */ 
        }
    ]);
const RequestDetails = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.RequestDetails", ()=>[
        {
            no: 1,
            name: "ip",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 2,
            name: "method",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 3,
            name: "protocol",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 4,
            name: "host",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 5,
            name: "path",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 6,
            name: "headers",
            kind: "map",
            K: 9 /* ScalarType.STRING */ ,
            V: {
                kind: "scalar",
                T: 9 /* ScalarType.STRING */ 
            }
        },
        {
            no: 7,
            name: "body",
            kind: "scalar",
            T: 12 /* ScalarType.BYTES */ 
        },
        {
            no: 8,
            name: "extra",
            kind: "map",
            K: 9 /* ScalarType.STRING */ ,
            V: {
                kind: "scalar",
                T: 9 /* ScalarType.STRING */ 
            }
        },
        {
            no: 9,
            name: "email",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 10,
            name: "cookies",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 11,
            name: "query",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        }
    ]);
const Decision = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.Decision", ()=>[
        {
            no: 1,
            name: "id",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 2,
            name: "conclusion",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(Conclusion)
        },
        {
            no: 3,
            name: "reason",
            kind: "message",
            T: Reason
        },
        {
            no: 4,
            name: "rule_results",
            kind: "message",
            T: RuleResult,
            repeated: true
        },
        {
            no: 5,
            name: "ttl",
            kind: "scalar",
            T: 13 /* ScalarType.UINT32 */ 
        },
        {
            no: 6,
            name: "ip_details",
            kind: "message",
            T: IpDetails
        }
    ]);
const DecideRequest = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.DecideRequest", ()=>[
        {
            no: 1,
            name: "sdk_stack",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(SDKStack)
        },
        {
            no: 2,
            name: "sdk_version",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 4,
            name: "details",
            kind: "message",
            T: RequestDetails
        },
        {
            no: 5,
            name: "rules",
            kind: "message",
            T: Rule,
            repeated: true
        },
        {
            no: 6,
            name: "characteristics",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ ,
            repeated: true
        }
    ]);
const DecideResponse = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.DecideResponse", ()=>[
        {
            no: 1,
            name: "decision",
            kind: "message",
            T: Decision
        },
        {
            no: 2,
            name: "extra",
            kind: "map",
            K: 9 /* ScalarType.STRING */ ,
            V: {
                kind: "scalar",
                T: 9 /* ScalarType.STRING */ 
            }
        }
    ]);
const ReportRequest = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.ReportRequest", ()=>[
        {
            no: 1,
            name: "sdk_stack",
            kind: "enum",
            T: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].getEnumType(SDKStack)
        },
        {
            no: 2,
            name: "sdk_version",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ 
        },
        {
            no: 4,
            name: "details",
            kind: "message",
            T: RequestDetails
        },
        {
            no: 5,
            name: "decision",
            kind: "message",
            T: Decision
        },
        {
            no: 6,
            name: "rules",
            kind: "message",
            T: Rule,
            repeated: true
        },
        {
            no: 8,
            name: "characteristics",
            kind: "scalar",
            T: 9 /* ScalarType.STRING */ ,
            repeated: true
        }
    ]);
const ReportResponse = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$proto3$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["proto3"].makeMessageType("proto.decide.v1alpha1.ReportResponse", ()=>[
        {
            no: 2,
            name: "extra",
            kind: "map",
            K: 9 /* ScalarType.STRING */ ,
            V: {
                kind: "scalar",
                T: 9 /* ScalarType.STRING */ 
            }
        }
    ]);
}}),
"[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/well-known-bots.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/** Automatically generated - DO NOT MANUALLY EDIT */ __turbopack_context__.s({
    "categories": (()=>categories)
});
const categories = Object.freeze({
    "CATEGORY:ACADEMIC": Object.freeze([
        "BLACKBOARD_CRAWLER",
        "CISPA_CRAWLER",
        "COMMONCRAWL_CRAWLER",
        "DOMAINSPROJECT_CRAWLER",
        "EPFL_CRAWLER",
        "EZID_CRAWLER",
        "LAW_UNIMI_CRAWLER",
        "LEIPZIG_FINDLINKS",
        "LEIPZIG_LCC",
        "MACOCU_CRAWLER",
        "NICT_CRAWLER",
        "SEMANTICSCHOLAR_CRAWLER",
        "TURNITIN_CRAWLER",
        "UTEXAS_CRAWLER",
        "YAMANALAB_CRAWLER"
    ]),
    "CATEGORY:ADVERTISING": Object.freeze([
        "BING_ADS",
        "GOOGLE_ADSBOT",
        "GOOGLE_ADSBOT_MOBILE",
        "GOOGLE_ADSENSE",
        "GOOGLE_ADSENSE_GOOGLEBOT",
        "GOOGLE_ADWORDS",
        "MOAT_CRAWLER",
        "MSN_CRAWLER"
    ]),
    "CATEGORY:AI": Object.freeze([
        "AI_SEARCH_BOT",
        "AI2_CRAWLER",
        "AI2_CRAWLER_DOLMA",
        "AIHIT_CRAWLER",
        "ANTHROPIC_CRAWLER",
        "BOTIFY_CRAWLER",
        "BYTEDANCE_CRAWLER",
        "COHERE_CRAWLER",
        "COMMONCRAWL_CRAWLER",
        "DIFFBOT_CRAWLER",
        "FACEBOOK_CRAWLER",
        "FACEBOOK_SHARE_CRAWLER",
        "FRIENDLYCRAWLER",
        "GLUTENFREEPLEASURE_CRAWLER",
        "GOOGLE_CRAWLER_CLOUDVERTEX",
        "IASK_CRAWLER",
        "IMAGESIFT_CRAWLER",
        "IMG2DATASET",
        "INFEGY_CRAWLER",
        "INTEGRALADS_CRAWLER",
        "LEADCRUNCH_CRAWLER",
        "MEDIATOOLKIT_CRAWLER",
        "META_CRAWLER",
        "META_CRAWLER_USER",
        "NICT_CRAWLER",
        "NTENT_CRAWLER",
        "OMGILI_CRAWLER",
        "OPENAI_CRAWLER",
        "OPENAI_CRAWLER_SEARCH",
        "OPENAI_CRAWLER_USER",
        "PERPLEXITY_CRAWLER",
        "PETALSEARCH_CRAWLER",
        "PRIMAL_CRAWLER",
        "PYTHON_SCRAPY",
        "SAFEDNS_CRAWLER",
        "SEARCHATLAS_CRAWLER",
        "SEMANTICSCHOLAR_CRAWLER",
        "SENTIONE_CRAWLER",
        "STORYGIZE_CRAWLER",
        "TIKTOK_CRAWLER",
        "TIMPI_CRAWLER",
        "TURNITIN_CRAWLER",
        "VELEN_CRAWLER",
        "WEBZIO_CRAWLER_AI",
        "YOU_CRAWLER"
    ]),
    "CATEGORY:AMAZON": Object.freeze([
        "AMAZON_ALEXA_CRAWLER",
        "AMAZON_CLOUDFRONT",
        "AMAZON_CRAWLER"
    ]),
    "CATEGORY:APPLE": Object.freeze([
        "APPLE_CRAWLER",
        "IMESSAGE_PREVIEW"
    ]),
    "CATEGORY:ARCHIVE": Object.freeze([
        "ARCHIVEORG_ARCHIVER",
        "CLOUDFLARE_ARCHIVER",
        "COMMONCRAWL_CRAWLER",
        "EZID_CRAWLER",
        "INTERNETARCHIVE_CRAWLER_OSS",
        "IRC_ARCHIVEBOT",
        "LINKARCHIVER",
        "NICECRAWLER_ARCHIVE",
        "TRENDSMAP_CRAWLER",
        "WEBARCHIVE_CRAWLER"
    ]),
    "CATEGORY:FEEDFETCHER": Object.freeze([
        "APPLE_FEEDFETCHER",
        "AWARIO_CRAWLER_RSS",
        "BAZQUX_FEEDFETCHER",
        "BLOGTRAFFIC_FEEDFETCHER",
        "FEEDBIN_CRAWLER",
        "FEEDLY_FEEDFETCHER",
        "FEEDSPOT_FEEDFETCHER",
        "FRESHRSS_FEEDFETCHER",
        "G2READER_CRAWLER",
        "GOOGLE_FEEDFETCHER",
        "INOREADER_AGGREGATOR",
        "MINIFLUX_FEEDFETCHER",
        "NAVER_CRAWLER_RSS",
        "NEWSBLUR_AGGREGATOR",
        "POCKET_CRAWLER",
        "REFIND_CRAWLER",
        "RSSBOT_FEEDFETCHER",
        "RSSING_CRAWLER",
        "RSSMICRO_FEEDFETCHER",
        "SERENDEPUTY_CRAWLER",
        "STARTME_CRAWLER",
        "SUPERFEEDR_CRAWLER",
        "THEOLDREADER_CRAWLER",
        "TTRSS_FEEDFETCHER",
        "WORDPRESS_CRAWLER_RSS"
    ]),
    "CATEGORY:GOOGLE": Object.freeze([
        "GOOGLE_ADSBOT",
        "GOOGLE_ADSBOT_MOBILE",
        "GOOGLE_ADSENSE",
        "GOOGLE_ADSENSE_GOOGLEBOT",
        "GOOGLE_ADWORDS",
        "GOOGLE_APPENGINE",
        "GOOGLE_CERTIFICATES_BRIDGE",
        "GOOGLE_CRAWLER",
        "GOOGLE_CRAWLER_CLOUDVERTEX",
        "GOOGLE_CRAWLER_IMAGE",
        "GOOGLE_CRAWLER_MOBILE",
        "GOOGLE_CRAWLER_NEWS",
        "GOOGLE_CRAWLER_OTHER",
        "GOOGLE_CRAWLER_SAFETY",
        "GOOGLE_CRAWLER_STORE",
        "GOOGLE_CRAWLER_VIDEO",
        "GOOGLE_FAVICON",
        "GOOGLE_FEEDFETCHER",
        "GOOGLE_INSPECTION_TOOL",
        "GOOGLE_LIGHTHOUSE",
        "GOOGLE_PHYSICAL_WEB",
        "GOOGLE_PREVIEW",
        "GOOGLE_PUSH_NOTIFICATIONS",
        "GOOGLE_READ_ALOUD",
        "GOOGLE_SITE_VERIFICATION",
        "GOOGLE_STRUCTURED_DATA_TESTING_TOOL",
        "GOOGLE_WEB_SNIPPET",
        "GOOGLE_XRAWLER"
    ]),
    "CATEGORY:META": Object.freeze([
        "FACEBOOK_CRAWLER",
        "FACEBOOK_SHARE_CRAWLER",
        "META_CRAWLER",
        "META_CRAWLER_USER",
        "WHATSAPP_CRAWLER"
    ]),
    "CATEGORY:MICROSOFT": Object.freeze([
        "AZURE_APP_INSIGHTS",
        "BING_ADS",
        "BING_CRAWLER",
        "BING_OFFICE_STORE",
        "BING_PREVIEW",
        "MICROSOFT_PREVIEW",
        "MICROSOFT_RESEARCH_CRAWLER",
        "MSN_CRAWLER",
        "SKYPE_PREVIEW"
    ]),
    "CATEGORY:MONITOR": Object.freeze([
        "AZURE_APP_INSIGHTS",
        "BETTERUPTIME_MONITOR",
        "BRANDVERITY_CRAWLER",
        "CHANGEDETECTION_CRAWLER",
        "DATADOG_MONITOR_SYNTHETICS",
        "DEADLINKCHECKER",
        "DISQUS_CRAWLER",
        "DUBBOT_CRAWLER",
        "DYNATRACE_MONITOR",
        "FLIPBOARD_PROXY",
        "FREEWEBMONITORING_MONITOR",
        "FRESHWORKS_MONITOR",
        "GOOGLE_CERTIFICATES_BRIDGE",
        "GOOGLE_SITE_VERIFICATION",
        "GOOGLE_STRUCTURED_DATA_TESTING_TOOL",
        "HUBSPOT_CRAWLER",
        "HYDROZEN_MONITOR",
        "KUMA_MONITOR",
        "MONITORBACKLINKS_CRAWLER",
        "NIXSTATS_CRAWLER",
        "OUTBRAIN_LINK_CHECKER",
        "PAGEPEEKER_CRAWLER",
        "PINGDOM_CRAWLER",
        "SAFEDNS_CRAWLER",
        "SENTRY_CRAWLER",
        "SURLY_CRAWLER",
        "TESTOMATO_CRAWLER",
        "UPTIME_MONITOR",
        "UPTIMEBOT_MONITOR",
        "UPTIMEROBOT_MONITOR",
        "VERCEL_MONITOR_PREVIEW",
        "W3C_VALIDATOR_CSS",
        "W3C_VALIDATOR_FEED",
        "W3C_VALIDATOR_HTML",
        "W3C_VALIDATOR_HTML_NU",
        "W3C_VALIDATOR_I18N",
        "W3C_VALIDATOR_LINKS",
        "W3C_VALIDATOR_MOBILE",
        "W3C_VALIDATOR_UNIFIED",
        "WEBPAGETEST_CRAWLER",
        "XENU_CRAWLER",
        "ZABBIX_MONITOR"
    ]),
    "CATEGORY:OPTIMIZER": Object.freeze([
        "CONDUCTOR_CRAWLER",
        "DAREBOOST_CRAWLER",
        "DUBBOT_CRAWLER",
        "GOOGLE_LIGHTHOUSE",
        "GOOGLE_STRUCTURED_DATA_TESTING_TOOL",
        "MONSIDO_CRAWLER",
        "MOZ_SITE_AUDIT",
        "SCREAMINGFROG_CRAWLER",
        "SISTRIX_CRAWLER",
        "TESTOMATO_CRAWLER",
        "WEBPAGETEST_CRAWLER"
    ]),
    "CATEGORY:PREVIEW": Object.freeze([
        "BING_PREVIEW",
        "BITLY_CRAWLER",
        "DISCORD_CRAWLER",
        "DUCKDUCKGO_CRAWLER_FAVICONS",
        "EMBEDLY_CRAWLER",
        "FACEBOOK_SHARE_CRAWLER",
        "FLIPBOARD_PROXY",
        "GOOGLE_FAVICON",
        "GOOGLE_PREVIEW",
        "GOOGLE_WEB_SNIPPET",
        "IFRAMELY_PREVIEW",
        "IMESSAGE_PREVIEW",
        "MASTODON_CRAWLER",
        "META_CRAWLER_USER",
        "MICROSOFT_PREVIEW",
        "PAGEPEEKER_CRAWLER",
        "SKYPE_PREVIEW",
        "SLACK_IMAGE_PROXY",
        "SNAP_PREVIEW",
        "STEAM_PREVIEW",
        "SYNAPSE_CRAWLER",
        "TELEGRAM_CRAWLER",
        "TWITTER_CRAWLER",
        "VERCEL_MONITOR_PREVIEW",
        "VIBER_CRAWLER",
        "WHATSAPP_CRAWLER",
        "YAHOO_PREVIEW"
    ]),
    "CATEGORY:PROGRAMMATIC": Object.freeze([
        "CODA_SERVER_FETCHER",
        "GIGABLAST_CRAWLER_OSS",
        "GO_HTTP",
        "GOOGLE_APPENGINE",
        "GOWIKI_CRAWLER",
        "HTTP_GET",
        "JAVA_APACHE_HTTPCLIENT",
        "JAVA_ASYNCHTTPCLIENT",
        "JAVA_CRAWLER4J",
        "JAVA_HTTPUNIT",
        "JAVA_JERSEY",
        "JAVA_JETTY",
        "JAVA_OKHTTP",
        "JAVA_SNACKTORY",
        "JAVASCRIPT_AXIOS",
        "JAVASCRIPT_NODE_FETCH",
        "JAVASCRIPT_PHANTOM",
        "PERL_LIBWWW",
        "PERL_PCORE",
        "PHP_CURLCLASS",
        "PHP_PHPCRAWL",
        "PHP_SIMPLE_SCRAPER",
        "PHP_SIMPLEPIE",
        "PYTHON_AIOHTTP",
        "PYTHON_BITBOT",
        "PYTHON_HTTPX",
        "PYTHON_OPENGRAPH",
        "PYTHON_REQUESTS",
        "PYTHON_SCRAPY",
        "PYTHON_URLLIB",
        "RUBY_METAINSPECTOR"
    ]),
    "CATEGORY:SEARCH_ENGINE": Object.freeze([
        "ADDSEARCH_CRAWLER",
        "AHREFS_CRAWLER",
        "ALEXANDRIA_CRAWLER",
        "APPLE_CRAWLER",
        "ASK_CRAWLER",
        "AVIRA_CRAWLER",
        "BING_CRAWLER",
        "BING_OFFICE_STORE",
        "DUCKDUCKGO_CRAWLER",
        "DUCKDUCKGO_CRAWLER_FAVICONS",
        "ENTIREWEB_CRAWLER",
        "GEEDO_CRAWLER",
        "GEEDO_CRAWLER_PRODUCTS",
        "GOOGLE_CRAWLER",
        "GOOGLE_CRAWLER_IMAGE",
        "GOOGLE_CRAWLER_MOBILE",
        "GOOGLE_CRAWLER_NEWS",
        "GOOGLE_CRAWLER_OTHER",
        "GOOGLE_CRAWLER_STORE",
        "GOOGLE_CRAWLER_VIDEO",
        "GOOGLE_INSPECTION_TOOL",
        "IASK_CRAWLER",
        "LINGUEE_CRAWLER",
        "MAJESTIC_CRAWLER",
        "MARGINALIA_CRAWLER",
        "MOJEEK_CRAWLER",
        "NETESTATE_CRAWLER",
        "OPENAI_CRAWLER_SEARCH",
        "PETALSEARCH_CRAWLER",
        "PIPL_CRAWLER",
        "QWANT_CRAWLER",
        "SEEKPORT_CRAWLER",
        "STRACT_CRAWLER",
        "WEBZIO_CRAWLER",
        "WELLKNOWN_CRAWLER",
        "YACY_CRAWLER",
        "YAHOO_CRAWLER",
        "YANDEX_CRAWLER",
        "YANDEX_CRAWLER_JAVASCRIPT"
    ]),
    "CATEGORY:SLACK": Object.freeze([
        "SLACK_CRAWLER",
        "SLACK_IMAGE_PROXY"
    ]),
    "CATEGORY:SOCIAL": Object.freeze([
        "DIGG_CRAWLER",
        "DISCORD_CRAWLER",
        "EVERYONESOCIAL_CRAWLER",
        "FACEBOOK_CRAWLER",
        "FACEBOOK_SHARE_CRAWLER",
        "GOOGLE_PREVIEW",
        "GOOGLE_WEB_SNIPPET",
        "GROUPME_CRAWLER",
        "IFRAMELY_PREVIEW",
        "IMESSAGE_PREVIEW",
        "IRC_ARCHIVEBOT",
        "LEMMY_CRAWLER",
        "LINKARCHIVER",
        "LINKEDIN_CRAWLER",
        "MASTODON_CRAWLER",
        "NETICLE_CRAWLER",
        "PINTREST_CRAWLER",
        "REDDIT_CRAWLER",
        "SNAP_PREVIEW",
        "STEAM_PREVIEW",
        "SYNAPSE_CRAWLER",
        "TELEGRAM_CRAWLER",
        "TIKTOK_CRAWLER",
        "TRENDSMAP_CRAWLER",
        "TWEETEDTIMES_CRAWLER",
        "TWITTER_CRAWLER",
        "VIBER_CRAWLER",
        "WHATSAPP_CRAWLER"
    ]),
    "CATEGORY:TOOL": Object.freeze([
        "CURL",
        "DCRAWL",
        "DOMAINSPROJECT_CRAWLER",
        "GIGABLAST_CRAWLER_OSS",
        "HEADLESS_CHROME",
        "IMG2DATASET",
        "INTERNETARCHIVE_CRAWLER_OSS",
        "IPIP_CRAWLER",
        "L9EXPLORE",
        "LAW_UNIMI_CRAWLER",
        "NAGIOS_CHECK_HTTP",
        "NMAP",
        "NUTCH",
        "POSTMAN",
        "SUMMALY_CRAWLER",
        "WGET",
        "XENU_CRAWLER",
        "ZGRAB"
    ]),
    "CATEGORY:UNKNOWN": Object.freeze([
        "A6CORP_CRAWLER",
        "ABOUNDEX_CRAWLER",
        "ACAPBOT",
        "ACOON_CRAWLER",
        "ADBEAT_CRAWLER",
        "ADDTHIS_CRAWLER",
        "ADMANTX_CRAWLER",
        "ADSCANNER_CRAWLER",
        "ADSTXTCRAWLER",
        "ADVBOT_CRAWLER",
        "ALPHASEOBOT_CRAWLER",
        "ANDERSPINK_CRAWLER",
        "ANTIBOT",
        "APERCITE_CRAWLER",
        "ARA_CRAWLER",
        "ARATURKA_CRAWLER",
        "AROCOM_CRAWLER",
        "ASPIEGEL_CRAWLER",
        "AUDISTO_CRAWLER",
        "AWARIO_CRAWLER",
        "AWARIO_CRAWLER_SMART",
        "AWESOMECRAWLER",
        "B2BBOT",
        "BACKLINKTEST_CRAWLER",
        "BAIDU_CLOUD_WATCH",
        "BAIDU_CRAWLER",
        "BETABOT",
        "BIDSWITCH_CRAWLER",
        "BIGDATACORP_CRAWLER",
        "BIGLOTRON",
        "BINLAR",
        "BITSIGHT_CRAWLER",
        "BLOGMURA_CRAWLER",
        "BLP_BBOT",
        "BNF_CRAWLER",
        "BOMBORA_CRAWLER",
        "BOXCAR_CRAWLER",
        "BRAINOBOT",
        "BRANDONMEDIA_CRAWLER",
        "BRANDWATCH_CRAWLER",
        "BRIGHTEDGE_CRAWLER",
        "BUBLUP_CRAWLER",
        "BUILTWITH_CRAWLER",
        "BUZZSTREAM_CRAWLER",
        "CAPSULINK_CRAWLER",
        "CAREERX_CRAWLER",
        "CENTURYBOT",
        "CHECKMARKNETWORK_CRAWLER",
        "CHLOOE_CRAWLER",
        "CINCRAWDATA_CRAWLER",
        "CITESEERX_CRAWLER",
        "CLICKAGY_CRAWLER",
        "CLIQZ_CRAWLER",
        "CLOUDSYSTEMNETWORKS_CRAWLER",
        "COCCOC_CRAWLER",
        "COCOLYZE_CRAWLER",
        "CODEWISE_CRAWLER",
        "COGNITIVESEO_CRAWLER",
        "COMPANYBOOK_CRAWLER",
        "CONTENT_CRAWLER_SPIDER",
        "CONTEXTAD_CRAWLER",
        "CONTXBOT",
        "CONVERA_CRAWLER",
        "COOKIEBOT_CRAWLER",
        "CREATIVECOMMONS_CRAWLER",
        "CRITEO_CRAWLER",
        "CRYSTALSEMANTICS_CRAWLER",
        "CUREBOT_CRAWLER",
        "CUTBOT_CRAWLER",
        "CXENSE_CRAWLER",
        "CYBERPATROL_CRAWLER",
        "DATAFEEDWATCH_CRAWLER",
        "DATAFORSEO_CRAWLER",
        "DATAGNION_CRAWLER",
        "DATANYZE_CRAWLER",
        "DATAPROVIDER_CRAWLER",
        "DATENBUTLER_CRAWLER",
        "DAUM_CRAWLER",
        "DEEPNOC_CRAWLER",
        "DEUSU_CRAWLER",
        "DIGINCORE_CRAWLER",
        "DIGITALDRAGON_CRAWLER",
        "DISCOVERYENGINE_CRAWLER",
        "DNYZ_CRAWLER",
        "DOMAINCRAWLER_CRAWLER",
        "DOMAINREANIMATOR_CRAWLER",
        "DOMAINSBOT_CRAWLER",
        "DOMAINSTATS_CRAWLER",
        "DOMAINTOOLS_CRAWLER",
        "DOTNETDOTCOM_CRAWLER",
        "DRAGONMETRICS_CRAWLER",
        "DRIFTNET_CRAWLER",
        "DUEDIL_CRAWLER",
        "EC2LINKFINDER",
        "EDISTER_CRAWLER",
        "ELISABOT",
        "EPICTIONS_CRAWLER",
        "ERIGHT_CRAWLER",
        "EUROPARCHIVE_CRAWLER",
        "EVENTURES_CRAWLER",
        "EVENTURES_CRAWLER_BATCH",
        "EXENSA_CRAWLER",
        "EXPERIBOT_CRAWLER",
        "EXTLINKS_CRAWLER",
        "EYEOTA_CRAWLER",
        "FAST_CRAWLER",
        "FAST_CRAWLER_ENTERPRISE",
        "FEDORAPLANET_CRAWLER",
        "FEEDAFEVER_CRAWLER",
        "FEMTOSEARCH_CRAWLER",
        "FINDTHATFILE_CRAWLER",
        "FLAMINGOSEARCH_CRAWLER",
        "FLUFFY",
        "FR_CRAWLER",
        "FUELBOT",
        "FYREBOT",
        "G00G1E_CRAWLER",
        "G2WEBSERVICES_CRAWLER",
        "GARLIK_CRAWLER",
        "GENIEO_CRAWLER",
        "GIGABLAST_CRAWLER",
        "GINGER_CRAWLER",
        "GNAM_GNAM_SPIDER",
        "GNOWIT_CRAWLER",
        "GOO_CRAWLER",
        "GRAPESHOT_CRAWLER",
        "GROB_CRAWLER",
        "GROUPHIGH_CRAWLER",
        "GRUB",
        "GSLFBOT",
        "GWENE_CRAWLER",
        "HAOSOU_CRAWLER",
        "HATENA_CRAWLER",
        "HEADLINE_CRAWLER",
        "HOYER_CRAWLER",
        "HTTRACK",
        "HYPEFACTORS_CRAWLER",
        "HYPESTAT_CRAWLER",
        "HYSCORE_CRAWLER",
        "IA_ARCHIVER",
        "IDEASANDCODE_CRAWLER",
        "INDEED_CRAWLER",
        "INETDEX_CRAWLER",
        "INFOO_CRAWLER",
        "INTEGROMEDB_CRAWLER",
        "INTELIUM_CRAWLER",
        "IONOS_CRAWLER",
        "IP_WEB_CRAWLER",
        "ISKANIE_CRAWLER",
        "ISS_CRAWLER",
        "IT2MEDIA_CRAWLER",
        "ITINFLUENTIALS_CRAWLER",
        "JAMIEMBROWN_CRAWLER",
        "JETSLIDE_CRAWLER",
        "JOBBOERSE_CRAWLER",
        "JOOBLE_CRAWLER",
        "JUSPROG_CRAWLER",
        "JYXO_CRAWLER",
        "K7COMPUTING_CRAWLER",
        "KEMVI_CRAWLER",
        "KOMODIA_CRAWLER",
        "KOSMIO_CRAWLER",
        "LANDAUMEDIA_CRAWLER",
        "LASERLIKE_CRAWLER",
        "LB_SPIDER",
        "LEIKI_CRAWLER",
        "LIGHTSPEEDSYSTEMS_CRAWLER",
        "LINE_CRAWLER",
        "LINKAPEDIA_CRAWLER",
        "LINKDEX_CRAWLER",
        "LINKFLUENCE_CRAWLER",
        "LINKIS_CRAWLER",
        "LIPPERHEY_CRAWLER",
        "LIVELAP_CRAWLER",
        "LOGLY_CRAWLER",
        "LOOP_CRAWLER",
        "LSSBOT",
        "LSSBOT_ROCKET",
        "LTX71_CRAWLER",
        "LUMINATOR_CRAWLER",
        "MAILRU_CRAWLER",
        "MAPPYDATA_CRAWLER",
        "MAUIBOT",
        "MEGAINDEX_CRAWLER",
        "MELTWATER_CRAWLER",
        "METADATALABS_CRAWLER",
        "METAJOB_CRAWLER",
        "METAURI_CRAWLER",
        "METRICSTOOLS_CRAWLER",
        "MIGNIFY_CRAWLER",
        "MIGNIFY_IMRBOT",
        "MIXNODE_CACHE",
        "MOODLE_CRAWLER",
        "MOREOVER_CRAWLER",
        "MOZ_CRAWLER",
        "MUCKRACK_CRAWLER",
        "MULTIVIEWBOT",
        "NAVER_CRAWLER",
        "NEEVA_CRAWLER",
        "NERDBYNATURE_CRAWLER",
        "NERDYBOT_CRAWLER",
        "NETCRAFT_CRAWLER",
        "NETSYSTEMSRESEARCH_CRAWLER",
        "NETVIBES_CRAWLER",
        "NEWSHARECOUNTS_CRAWLER",
        "NEWSPAPER",
        "NEXTCLOUD_CRAWLER",
        "NIKI_BOT",
        "NING_CRAWLER",
        "NINJABOT",
        "NUZZEL_CRAWLER",
        "OCARINABOT",
        "OKRU_CRAWLER",
        "OPENGRAPHCHECK_CRAWLER",
        "OPENHOSE_CRAWLER",
        "OPENINDEX_CRAWLER",
        "ORANGE_CRAWLER",
        "ORANGE_FTGROUP_CRAWLER",
        "OUTCLICKS_CRAWLER",
        "PAGE_TO_RSS",
        "PAGETHING_CRAWLER",
        "PALOALTONETWORKS_CRAWLER",
        "PANSCIENT_CRAWLER",
        "PAPERLI_CRAWLER",
        "PHXBOT",
        "PICSEARCH_CRAWLER",
        "POSTRANK_CRAWLER",
        "PRCY_CRAWLER",
        "PRIVACORE_CRAWLER",
        "PRIVACYAWARE_CRAWLER",
        "PROFOUND_CRAWLER",
        "PROXIMIC_CRAWLER",
        "PULSEPOINT_CRAWLER",
        "PURE_CRAWLER",
        "RANKACTIVE_CRAWLER",
        "RETREVO_PAGE_ANALYZER",
        "RIDDER_CRAWLER",
        "RIVVA_CRAWLER",
        "RYTE_CRAWLER",
        "SCAN_INTERFAX_CRAWLER",
        "SCHMORP_CRAWLER",
        "SCOUTJET_CRAWLER",
        "SCRIBD_CRAWLER",
        "SCRITCH_CRAWLER",
        "SEEKBOT_CRAWLER",
        "SEEWITHKIDS_CRAWLER",
        "SEMANTICAUDIENCE_CRAWLER",
        "SEMPITECH_CRAWLER",
        "SEMRUSH_CRAWLER",
        "SENUTO_CRAWLER",
        "SEOBILITY_CRAWLER",
        "SEOKICKS_CRAWLER",
        "SEOLIZER_CRAWLER",
        "SEOPROFILER_CRAWLER",
        "SEOSCANNERS_CRAWLER",
        "SEOSTAR_CRAWLER",
        "SEOZOOM_CRAWLER",
        "SERPSTATBOT_CRAWLER",
        "SEZNAM_CRAWLER",
        "SIMILARTECH_CRAWLER",
        "SIMPLE_CRAWLER",
        "SISTRIX_007AC9_CRAWLER",
        "SITEBOT_CRAWLER",
        "SITECHECKER_CRAWLER",
        "SITEEXPLORER_CRAWLER",
        "SITEIMPROVE_CRAWLER",
        "SOCIALRANK_CRAWLER",
        "SOFTBYTELABS_CRAWLER",
        "SOGOU_CRAWLER",
        "STUTTGART_CRAWLER",
        "SUMMIFY_CRAWLER",
        "SWIMGBOT",
        "SYSOMOS_CRAWLER",
        "T3VERSIONS_CRAWLER",
        "TABOOLA_CRAWLER",
        "TAGOO_CRAWLER",
        "TANGIBLEE_CRAWLER",
        "THINKLAB_CRAWLER",
        "TIGER_CRAWLER",
        "TINEYE_CRAWLER",
        "TISCALI_CRAWLER",
        "TOMBASCRAPER_CRAWLER",
        "TOPLIST_CRAWLER",
        "TORUS_CRAWLER",
        "TOUTIAO_CRAWLER",
        "TRAACKR_CRAWLER",
        "TRACEMYFILE_CRAWLER",
        "TRENDICTION_CRAWLER",
        "TROVE_CRAWLER",
        "TROVIT_CRAWLER",
        "TWEETMEMEBOT",
        "TWENGA_CRAWLER",
        "TWINGLY_CRAWLER",
        "TWOIP_CRAWLER",
        "TWOIP_CRAWLER_CMS",
        "TWURLY_CRAWLER",
        "UBERMETRICS_CRAWLER",
        "UBT_CRAWLER",
        "UPFLOW_CRAWLER",
        "URLCLASSIFICATION_CRAWLER",
        "USINE_NOUVELLE_CRAWLER",
        "UTORRENT_CRAWLER",
        "VEBIDOO_CRAWLER",
        "VEOOZ_CRAWLER",
        "VERISIGN_IPS_AGENT",
        "VIGIL_CRAWLER",
        "VIPNYTT_CRAWLER",
        "VIRUSTOTAL_CRAWLER",
        "VKROBOT_CRAWLER",
        "VKSHARE_CRAWLER",
        "VUHUV_CRAWLER",
        "WAREBAY_CRAWLER",
        "WEBCEO_CRAWLER",
        "WEBCOMPANY_CRAWLER",
        "WEBDATASTATS_CRAWLER",
        "WEBEAVER_CRAWLER",
        "WEBMEUP_CRAWLER",
        "WEBMON",
        "WESEE_CRAWLER",
        "WOCODI_CRAWLER",
        "WOORANK_CRAWLER",
        "WOORANK_CRAWLER_REVIEW",
        "WORDPRESS_CRAWLER",
        "WORDUP_CRAWLER",
        "WORIO_CRAWLER",
        "WOTBOX_CRAWLER",
        "XOVIBOT_CRAWLER",
        "YANGA_CRAWLER",
        "YELLOWBP_CRAWLER",
        "YISOU_CRAWLER",
        "YOOZ_CRAWLER",
        "ZOOMINFO_CRAWLER",
        "ZUM_CRAWLER",
        "ZUPERLIST_CRAWLER"
    ]),
    "CATEGORY:VERCEL": Object.freeze([
        "VERCEL_CRAWLER",
        "VERCEL_MONITOR_PREVIEW"
    ]),
    "CATEGORY:WEBHOOK": Object.freeze([
        "STRIPE_WEBHOOK"
    ]),
    "CATEGORY:YAHOO": Object.freeze([
        "YAHOO_CRAWLER",
        "YAHOO_CRAWLER_JAPAN",
        "YAHOO_PREVIEW"
    ])
});
;
}}),
"[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/index.js [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ArcjetAllowDecision": (()=>ArcjetAllowDecision),
    "ArcjetBotReason": (()=>ArcjetBotReason),
    "ArcjetChallengeDecision": (()=>ArcjetChallengeDecision),
    "ArcjetConclusion": (()=>ArcjetConclusion),
    "ArcjetDecision": (()=>ArcjetDecision),
    "ArcjetDenyDecision": (()=>ArcjetDenyDecision),
    "ArcjetEdgeRuleReason": (()=>ArcjetEdgeRuleReason),
    "ArcjetEmailReason": (()=>ArcjetEmailReason),
    "ArcjetEmailType": (()=>ArcjetEmailType),
    "ArcjetErrorDecision": (()=>ArcjetErrorDecision),
    "ArcjetErrorReason": (()=>ArcjetErrorReason),
    "ArcjetIpDetails": (()=>ArcjetIpDetails),
    "ArcjetMode": (()=>ArcjetMode),
    "ArcjetRateLimitAlgorithm": (()=>ArcjetRateLimitAlgorithm),
    "ArcjetRateLimitReason": (()=>ArcjetRateLimitReason),
    "ArcjetReason": (()=>ArcjetReason),
    "ArcjetRuleResult": (()=>ArcjetRuleResult),
    "ArcjetRuleState": (()=>ArcjetRuleState),
    "ArcjetRuleType": (()=>ArcjetRuleType),
    "ArcjetSensitiveInfoReason": (()=>ArcjetSensitiveInfoReason),
    "ArcjetSensitiveInfoType": (()=>ArcjetSensitiveInfoType),
    "ArcjetShieldReason": (()=>ArcjetShieldReason),
    "ArcjetStack": (()=>ArcjetStack)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$typeid$2d$js$40$1$2e$2$2e$0$2f$node_modules$2f$typeid$2d$js$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/typeid-js@1.2.0/node_modules/typeid-js/dist/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/proto/decide/v1alpha1/decide_pb.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$well$2d$known$2d$bots$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/well-known-bots.js [app-rsc] (ecmascript)");
;
;
;
const ArcjetMode = Object.freeze({
    /**
     * @deprecated Use the string `"LIVE"` instead.
     **/ LIVE: "LIVE",
    /**
     * @deprecated Use the string `"DRY_RUN"` instead.
     **/ DRY_RUN: "DRY_RUN"
});
const ArcjetRateLimitAlgorithm = Object.freeze({
    /**
     * @deprecated Use the string `"TOKEN_BUCKET"` instead.
     **/ TOKEN_BUCKET: "TOKEN_BUCKET",
    /**
     * @deprecated Use the string `"FIXED_WINDOW"` instead.
     **/ FIXED_WINDOW: "FIXED_WINDOW",
    /**
     * @deprecated Use the string `"SLIDING_WINDOW"` instead.
     **/ SLIDING_WINDOW: "SLIDING_WINDOW"
});
const ArcjetEmailType = Object.freeze({
    /**
     * @deprecated Use the string `"DISPOSABLE"` instead.
     **/ DISPOSABLE: "DISPOSABLE",
    /**
     * @deprecated Use the string `"FREE"` instead.
     **/ FREE: "FREE",
    /**
     * @deprecated Use the string `"NO_MX_RECORDS"` instead.
     **/ NO_MX_RECORDS: "NO_MX_RECORDS",
    /**
     * @deprecated Use the string `"NO_GRAVATAR"` instead.
     **/ NO_GRAVATAR: "NO_GRAVATAR",
    /**
     * @deprecated Use the string `"INVALID"` instead.
     **/ INVALID: "INVALID"
});
const ArcjetStack = Object.freeze({
    /**
     * @deprecated Use the string `"NODEJS"` instead.
     **/ NODEJS: "NODEJS",
    /**
     * @deprecated Use the string `"NEXTJS"` instead.
     **/ NEXTJS: "NEXTJS",
    /**
     * @deprecated Use the string `"BUN"` instead.
     **/ BUN: "BUN",
    /**
     * @deprecated Use the string `"SVELTEKIT"` instead.
     **/ SVELTEKIT: "SVELTEKIT",
    /**
     * @deprecated Use the string `"DENO"` instead.
     **/ DENO: "DENO",
    /**
     * @deprecated Use the string `"NESTJS"` instead.
     **/ NESTJS: "NESTJS",
    /**
     * @deprecated Use the string `"REMIX"` instead.
     **/ REMIX: "REMIX",
    /**
     * @deprecated Use the string `"ASTRO"` instead.
     **/ ASTRO: "ASTRO"
});
const ArcjetRuleState = Object.freeze({
    /**
     * @deprecated Use the string `"RUN"` instead.
     **/ RUN: "RUN",
    /**
     * @deprecated Use the string `"NOT_RUN"` instead.
     **/ NOT_RUN: "NOT_RUN",
    /**
     * @deprecated Use the string `"CACHED"` instead.
     **/ CACHED: "CACHED",
    /**
     * @deprecated Use the string `"DRY_RUN"` instead.
     **/ DRY_RUN: "DRY_RUN"
});
const ArcjetConclusion = Object.freeze({
    /**
     * @deprecated Use the string `"ALLOW"` instead.
     **/ ALLOW: "ALLOW",
    /**
     * @deprecated Use the string `"DENY"` instead.
     **/ DENY: "DENY",
    /**
     * @deprecated Use the string `"CHALLENGE"` instead.
     **/ CHALLENGE: "CHALLENGE",
    /**
     * @deprecated Use the string `"ERROR"` instead.
     **/ ERROR: "ERROR"
});
const ArcjetSensitiveInfoType = Object.freeze({
    /**
     * @deprecated Use the string `"EMAIL"` instead.
     **/ EMAIL: "EMAIL",
    /**
     * @deprecated Use the string `"PHONE_NUMBER"` instead.
     **/ PHONE_NUMBER: "PHONE_NUMBER",
    /**
     * @deprecated Use the string `"IP_ADDRESS"` instead.
     **/ IP_ADDRESS: "IP_ADDRESS",
    /**
     * @deprecated Use the string `"CREDIT_CARD_NUMBER"` instead.
     **/ CREDIT_CARD_NUMBER: "CREDIT_CARD_NUMBER"
});
const ArcjetRuleType = Object.freeze({
    /**
     * @deprecated Use the string `"LOCAL"` instead.
     **/ LOCAL: "LOCAL",
    /**
     * @deprecated Use the string `"REMOTE"` instead.
     **/ REMOTE: "REMOTE"
});
class ArcjetReason {
    type;
    isSensitiveInfo() {
        return this.type === "SENSITIVE_INFO";
    }
    isRateLimit() {
        return this.type === "RATE_LIMIT";
    }
    isBot() {
        return this.type === "BOT";
    }
    isEdgeRule() {
        return this.type === "EDGE_RULE";
    }
    isShield() {
        return this.type === "SHIELD";
    }
    isEmail() {
        return this.type === "EMAIL";
    }
    isError() {
        return this.type === "ERROR";
    }
}
class ArcjetSensitiveInfoReason extends ArcjetReason {
    type = "SENSITIVE_INFO";
    denied;
    allowed;
    constructor(init){
        super();
        this.denied = init.denied;
        this.allowed = init.allowed;
    }
}
class ArcjetRateLimitReason extends ArcjetReason {
    type = "RATE_LIMIT";
    max;
    remaining;
    reset;
    window;
    resetTime;
    constructor(init){
        super();
        this.max = init.max;
        this.remaining = init.remaining;
        this.reset = init.reset;
        this.window = init.window;
        this.resetTime = init.resetTime;
    }
}
class ArcjetBotReason extends ArcjetReason {
    type = "BOT";
    allowed;
    denied;
    verified;
    spoofed;
    constructor(init){
        super();
        this.allowed = init.allowed;
        this.denied = init.denied;
        this.verified = init.verified;
        this.spoofed = init.spoofed;
    }
    isVerified() {
        return this.verified;
    }
    isSpoofed() {
        return this.spoofed;
    }
}
class ArcjetEdgeRuleReason extends ArcjetReason {
    type = "EDGE_RULE";
}
class ArcjetShieldReason extends ArcjetReason {
    type = "SHIELD";
    shieldTriggered;
    constructor(init){
        super();
        this.shieldTriggered = init.shieldTriggered ?? false;
    }
}
class ArcjetEmailReason extends ArcjetReason {
    type = "EMAIL";
    emailTypes;
    constructor(init){
        super();
        if (typeof init === "undefined") {
            this.emailTypes = [];
        } else {
            this.emailTypes = init.emailTypes ?? [];
        }
    }
}
class ArcjetErrorReason extends ArcjetReason {
    type = "ERROR";
    message;
    constructor(error){
        super();
        // TODO: Get rid of instanceof check
        if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Reason"]) {
            if (error.reason.case === "error") {
                this.message = error.reason.value.message;
                return;
            } else {
                this.message = "Missing error reason";
            }
        }
        // TODO: Get rid of instanceof check
        if (error instanceof Error) {
            this.message = error.message;
            return;
        }
        if (typeof error === "string") {
            this.message = error;
            return;
        }
        this.message = "Unknown error occurred";
    }
}
class ArcjetRuleResult {
    /**
     * The stable, deterministic, and unique identifier of the rule that generated
     * this result.
     */ ruleId;
    /**
     * The duration in seconds this result should be considered valid, also known
     * as time-to-live.
     */ ttl;
    state;
    conclusion;
    reason;
    constructor(init){
        this.ruleId = init.ruleId;
        this.ttl = init.ttl;
        this.state = init.state;
        this.conclusion = init.conclusion;
        this.reason = init.reason;
    }
    isDenied() {
        return this.conclusion === "DENY";
    }
}
class ArcjetIpDetails {
    /**
     * The estimated latitude of the IP address within the `accuracyRadius` margin
     * of error.
     */ latitude;
    /**
     * The estimated longitude of the IP address - see accuracy_radius for the
     * margin of error.
     */ longitude;
    /**
     * The accuracy radius of the IP address location in kilometers.
     */ accuracyRadius;
    /**
     * The timezone of the IP address.
     */ timezone;
    /**
     * The postal code of the IP address.
     */ postalCode;
    /**
     * The city the IP address is located in.
     */ city;
    /**
     * The region the IP address is located in.
     */ region;
    /**
     * The country code the IP address is located in.
     */ country;
    /**
     * The country name the IP address is located in.
     */ countryName;
    /**
     * The continent code the IP address is located in.
     */ continent;
    /**
     * The continent name the IP address is located in.
     */ continentName;
    /**
     * The AS number the IP address belongs to.
     */ asn;
    /**
     * The AS name the IP address belongs to.
     */ asnName;
    /**
     * The AS domain the IP address belongs to.
     */ asnDomain;
    /**
     * The ASN type: ISP, hosting, business, or education
     */ asnType;
    /**
     * The ASN country code the IP address belongs to.
     */ asnCountry;
    /**
     * The name of the service the IP address belongs to.
     */ service;
    constructor(init = {}){
        this.latitude = init.latitude;
        this.longitude = init.longitude;
        this.accuracyRadius = init.accuracyRadius;
        this.timezone = init.timezone;
        this.postalCode = init.postalCode;
        this.city = init.city;
        this.region = init.region;
        this.country = init.country;
        this.countryName = init.countryName;
        this.continent = init.continent;
        this.continentName = init.continentName;
        this.asn = init.asn;
        this.asnName = init.asnName;
        this.asnDomain = init.asnDomain;
        this.asnType = init.asnType;
        this.asnCountry = init.asnCountry;
        this.service = init.service;
        // TypeScript creates symbols on the class when using `private` or `#`
        // identifiers for tracking these properties. We don't want to end up with
        // the same issues as Next.js with private symbols so we use
        // `Object.defineProperties` here and then `@ts-expect-error` when we access
        // the values. This is mostly to improve the editor experience, as props
        // starting with `_` are sorted to the top of autocomplete.
        Object.defineProperties(this, {
            _isHosting: {
                configurable: false,
                enumerable: false,
                writable: false,
                value: init.isHosting ?? false
            },
            _isVpn: {
                configurable: false,
                enumerable: false,
                writable: false,
                value: init.isVpn ?? false
            },
            _isProxy: {
                configurable: false,
                enumerable: false,
                writable: false,
                value: init.isProxy ?? false
            },
            _isTor: {
                configurable: false,
                enumerable: false,
                writable: false,
                value: init.isTor ?? false
            },
            _isRelay: {
                configurable: false,
                enumerable: false,
                writable: false,
                value: init.isRelay ?? false
            }
        });
    }
    hasLatitude() {
        return typeof this.latitude !== "undefined";
    }
    hasLongitude() {
        return typeof this.longitude !== "undefined";
    }
    hasAccuracyRadius() {
        return typeof this.accuracyRadius !== "undefined";
    }
    hasTimezone() {
        return typeof this.timezone !== "undefined";
    }
    hasPostalCode() {
        return typeof this.postalCode !== "undefined";
    }
    // TODO: If we have city, what other data are we sure to have?
    hasCity() {
        return typeof this.city !== "undefined";
    }
    // TODO: If we have region, what other data are we sure to have?
    hasRegion() {
        return typeof this.region !== "undefined";
    }
    // If we have country, we should have country name
    // TODO: If we have country, should we also have continent?
    hasCountry() {
        return typeof this.country !== "undefined";
    }
    // If we have continent, we should have continent name
    hasContintent() {
        return typeof this.continent !== "undefined";
    }
    // If we have ASN, we should have every piece of ASN information.
    hasASN() {
        return typeof this.asn !== "undefined";
    }
    hasService() {
        return typeof this.service !== "undefined";
    }
    /**
     * @returns `true` if the IP address belongs to a hosting provider.
     */ isHosting() {
        // @ts-expect-error because we attach this with Object.defineProperties
        return this._isHosting;
    }
    /**
     * @returns `true` if the IP address belongs to a VPN provider.
     */ isVpn() {
        // @ts-expect-error because we attach this with Object.defineProperties
        return this._isVpn;
    }
    /**
     * @returns `true` if the IP address belongs to a proxy provider.
     */ isProxy() {
        // @ts-expect-error because we attach this with Object.defineProperties
        return this._isProxy;
    }
    /**
     * @returns `true` if the IP address belongs to a Tor node.
     */ isTor() {
        // @ts-expect-error because we attach this with Object.defineProperties
        return this._isTor;
    }
    /**
     * @returns `true` if the the IP address belongs to a relay service.
     */ isRelay() {
        // @ts-expect-error because we attach this with Object.defineProperties
        return this._isRelay;
    }
}
/**
 * Represents a decision returned by the Arcjet SDK.
 *
 * @property `id` - The unique ID of the decision. This can be used to look up
 * the decision in the Arcjet dashboard.
 * @property `conclusion` - Arcjet's conclusion about the request. This will be
 * one of `"ALLOW"`, `"DENY"`, `"CHALLENGE"`, or `"ERROR"`.
 * @property `reason` - A structured data type about the reason for the
 * decision. One of: {@link ArcjetRateLimitReason}, {@link ArcjetEdgeRuleReason},
 * {@link ArcjetBotReason}, {@link ArcjetShieldReason},
 * {@link ArcjetEmailReason}, or {@link ArcjetErrorReason}.
 * @property `ttl` - The duration in milliseconds this decision should be
 * considered valid, also known as time-to-live.
 * @property `results` - Each separate {@link ArcjetRuleResult} can be found here
 * or by logging into the Arcjet dashboard and searching for the decision `id`.
 */ class ArcjetDecision {
    id;
    /**
     * The duration in milliseconds this decision should be considered valid, also
     * known as time-to-live.
     */ ttl;
    results;
    /**
     * Details about the IP address that informed the `conclusion`.
     */ ip;
    constructor(init){
        if (typeof init.id === "string") {
            this.id = init.id;
        } else {
            this.id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$typeid$2d$js$40$1$2e$2$2e$0$2f$node_modules$2f$typeid$2d$js$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["typeid"])("lreq").toString();
        }
        this.results = init.results;
        this.ttl = init.ttl;
        this.ip = init.ip ?? new ArcjetIpDetails();
    }
    isAllowed() {
        return this.conclusion === "ALLOW" || this.conclusion === "ERROR";
    }
    isDenied() {
        return this.conclusion === "DENY";
    }
    isChallenged() {
        return this.conclusion === "CHALLENGE";
    }
    isErrored() {
        return this.conclusion === "ERROR";
    }
}
class ArcjetAllowDecision extends ArcjetDecision {
    conclusion = "ALLOW";
    reason;
    constructor(init){
        super(init);
        this.reason = init.reason;
    }
}
class ArcjetDenyDecision extends ArcjetDecision {
    conclusion = "DENY";
    reason;
    constructor(init){
        super(init);
        this.reason = init.reason;
    }
}
class ArcjetChallengeDecision extends ArcjetDecision {
    conclusion = "CHALLENGE";
    reason;
    constructor(init){
        super(init);
        this.reason = init.reason;
    }
}
class ArcjetErrorDecision extends ArcjetDecision {
    conclusion = "ERROR";
    reason;
    constructor(init){
        super(init);
        this.reason = init.reason;
    }
}
;
}}),
"[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/index.js [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/proto/decide/v1alpha1/decide_pb.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$well$2d$known$2d$bots$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/well-known-bots.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/convert.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ArcjetConclusionFromProtocol": (()=>ArcjetConclusionFromProtocol),
    "ArcjetConclusionToProtocol": (()=>ArcjetConclusionToProtocol),
    "ArcjetDecisionFromProtocol": (()=>ArcjetDecisionFromProtocol),
    "ArcjetDecisionToProtocol": (()=>ArcjetDecisionToProtocol),
    "ArcjetEmailTypeFromProtocol": (()=>ArcjetEmailTypeFromProtocol),
    "ArcjetEmailTypeToProtocol": (()=>ArcjetEmailTypeToProtocol),
    "ArcjetIpDetailsFromProtocol": (()=>ArcjetIpDetailsFromProtocol),
    "ArcjetModeToProtocol": (()=>ArcjetModeToProtocol),
    "ArcjetReasonFromProtocol": (()=>ArcjetReasonFromProtocol),
    "ArcjetReasonToProtocol": (()=>ArcjetReasonToProtocol),
    "ArcjetRuleResultFromProtocol": (()=>ArcjetRuleResultFromProtocol),
    "ArcjetRuleResultToProtocol": (()=>ArcjetRuleResultToProtocol),
    "ArcjetRuleStateFromProtocol": (()=>ArcjetRuleStateFromProtocol),
    "ArcjetRuleStateToProtocol": (()=>ArcjetRuleStateToProtocol),
    "ArcjetRuleToProtocol": (()=>ArcjetRuleToProtocol),
    "ArcjetStackToProtocol": (()=>ArcjetStackToProtocol),
    "isRateLimitRule": (()=>isRateLimitRule)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$google$2f$protobuf$2f$timestamp_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/google/protobuf/timestamp_pb.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/index.js [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/proto/decide/v1alpha1/decide_pb.js [app-rsc] (ecmascript)");
;
;
;
function ArcjetModeToProtocol(mode) {
    switch(mode){
        case "LIVE":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Mode"].LIVE;
        case "DRY_RUN":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Mode"].DRY_RUN;
        default:
            {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Mode"].UNSPECIFIED;
            }
    }
}
function ArcjetEmailTypeToProtocol(emailType) {
    switch(emailType){
        case "DISPOSABLE":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EmailType"].DISPOSABLE;
        case "FREE":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EmailType"].FREE;
        case "NO_MX_RECORDS":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EmailType"].NO_MX_RECORDS;
        case "NO_GRAVATAR":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EmailType"].NO_GRAVATAR;
        case "INVALID":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EmailType"].INVALID;
        default:
            {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EmailType"].UNSPECIFIED;
            }
    }
}
function ArcjetEmailTypeFromProtocol(emailType) {
    switch(emailType){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EmailType"].UNSPECIFIED:
            throw new Error("Invalid EmailType");
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EmailType"].DISPOSABLE:
            return "DISPOSABLE";
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EmailType"].FREE:
            return "FREE";
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EmailType"].NO_MX_RECORDS:
            return "NO_MX_RECORDS";
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EmailType"].NO_GRAVATAR:
            return "NO_GRAVATAR";
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EmailType"].INVALID:
            return "INVALID";
        default:
            {
                throw new Error("Invalid EmailType");
            }
    }
}
function ArcjetStackToProtocol(stack) {
    switch(stack){
        case "NODEJS":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDKStack"].SDK_STACK_NODEJS;
        case "NEXTJS":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDKStack"].SDK_STACK_NEXTJS;
        case "BUN":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDKStack"].SDK_STACK_BUN;
        case "SVELTEKIT":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDKStack"].SDK_STACK_SVELTEKIT;
        case "DENO":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDKStack"].SDK_STACK_DENO;
        case "NESTJS":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDKStack"].SDK_STACK_NESTJS;
        case "REMIX":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDKStack"].SDK_STACK_REMIX;
        case "ASTRO":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDKStack"].SDK_STACK_ASTRO;
        default:
            {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDKStack"].SDK_STACK_UNSPECIFIED;
            }
    }
}
function ArcjetRuleStateToProtocol(stack) {
    switch(stack){
        case "RUN":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RuleState"].RUN;
        case "NOT_RUN":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RuleState"].NOT_RUN;
        case "CACHED":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RuleState"].CACHED;
        case "DRY_RUN":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RuleState"].DRY_RUN;
        default:
            {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RuleState"].UNSPECIFIED;
            }
    }
}
function ArcjetRuleStateFromProtocol(ruleState) {
    switch(ruleState){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RuleState"].UNSPECIFIED:
            throw new Error("Invalid RuleState");
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RuleState"].RUN:
            return "RUN";
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RuleState"].NOT_RUN:
            return "NOT_RUN";
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RuleState"].DRY_RUN:
            return "DRY_RUN";
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RuleState"].CACHED:
            return "CACHED";
        default:
            {
                throw new Error("Invalid RuleState");
            }
    }
}
function ArcjetConclusionToProtocol(conclusion) {
    switch(conclusion){
        case "ALLOW":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Conclusion"].ALLOW;
        case "DENY":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Conclusion"].DENY;
        case "CHALLENGE":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Conclusion"].CHALLENGE;
        case "ERROR":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Conclusion"].ERROR;
        default:
            {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Conclusion"].UNSPECIFIED;
            }
    }
}
function ArcjetConclusionFromProtocol(conclusion) {
    switch(conclusion){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Conclusion"].UNSPECIFIED:
            throw new Error("Invalid Conclusion");
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Conclusion"].ALLOW:
            return "ALLOW";
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Conclusion"].DENY:
            return "DENY";
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Conclusion"].CHALLENGE:
            return "CHALLENGE";
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Conclusion"].ERROR:
            return "ERROR";
        default:
            {
                throw new Error("Invalid Conclusion");
            }
    }
}
function ArcjetReasonFromProtocol(proto) {
    if (typeof proto === "undefined") {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetReason"]();
    }
    if (typeof proto !== "object" || typeof proto.reason !== "object") {
        throw new Error("Invalid Reason");
    }
    switch(proto.reason.case){
        case "rateLimit":
            {
                const reason = proto.reason.value;
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetRateLimitReason"]({
                    max: reason.max,
                    remaining: reason.remaining,
                    reset: reason.resetInSeconds,
                    window: reason.windowInSeconds,
                    resetTime: reason.resetTime?.toDate()
                });
            }
        case "botV2":
            {
                const reason = proto.reason.value;
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetBotReason"]({
                    allowed: reason.allowed,
                    denied: reason.denied,
                    verified: reason.verified,
                    spoofed: reason.spoofed
                });
            }
        case "edgeRule":
            {
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetEdgeRuleReason"]();
            }
        case "shield":
            {
                const reason = proto.reason.value;
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetShieldReason"]({
                    shieldTriggered: reason.shieldTriggered
                });
            }
        case "email":
            {
                const reason = proto.reason.value;
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetEmailReason"]({
                    emailTypes: reason.emailTypes.map(ArcjetEmailTypeFromProtocol)
                });
            }
        case "sensitiveInfo":
            {
                const reason = proto.reason.value;
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetSensitiveInfoReason"]({
                    allowed: reason.allowed,
                    denied: reason.denied
                });
            }
        case "bot":
            {
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetErrorReason"]("bot detection v1 is deprecated");
            }
        case "error":
            {
                const reason = proto.reason.value;
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetErrorReason"](reason.message);
            }
        case undefined:
            {
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetReason"]();
            }
        default:
            {
                proto.reason;
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetReason"]();
            }
    }
}
function ArcjetReasonToProtocol(reason) {
    if (reason.isRateLimit()) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Reason"]({
            reason: {
                case: "rateLimit",
                value: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RateLimitReason"]({
                    max: reason.max,
                    remaining: reason.remaining,
                    resetInSeconds: reason.reset,
                    windowInSeconds: reason.window,
                    resetTime: reason.resetTime ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$google$2f$protobuf$2f$timestamp_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Timestamp"].fromDate(reason.resetTime) : undefined
                })
            }
        });
    }
    if (reason.isBot()) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Reason"]({
            reason: {
                case: "botV2",
                value: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BotV2Reason"]({
                    allowed: reason.allowed,
                    denied: reason.denied,
                    verified: reason.verified,
                    spoofed: reason.spoofed
                })
            }
        });
    }
    if (reason.isEdgeRule()) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Reason"]({
            reason: {
                case: "edgeRule",
                value: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EdgeRuleReason"]({})
            }
        });
    }
    if (reason.isShield()) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Reason"]({
            reason: {
                case: "shield",
                value: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ShieldReason"]({
                    shieldTriggered: reason.shieldTriggered
                })
            }
        });
    }
    if (reason.isEmail()) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Reason"]({
            reason: {
                case: "email",
                value: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EmailReason"]({
                    emailTypes: reason.emailTypes.map(ArcjetEmailTypeToProtocol)
                })
            }
        });
    }
    if (reason.isError()) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Reason"]({
            reason: {
                case: "error",
                value: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ErrorReason"]({
                    message: reason.message
                })
            }
        });
    }
    if (reason.isSensitiveInfo()) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Reason"]({
            reason: {
                case: "sensitiveInfo",
                value: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SensitiveInfoReason"]({
                    allowed: reason.allowed,
                    denied: reason.denied
                })
            }
        });
    }
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Reason"]();
}
function ArcjetRuleResultToProtocol(ruleResult) {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RuleResult"]({
        ruleId: ruleResult.ruleId,
        ttl: ruleResult.ttl,
        state: ArcjetRuleStateToProtocol(ruleResult.state),
        conclusion: ArcjetConclusionToProtocol(ruleResult.conclusion),
        reason: ArcjetReasonToProtocol(ruleResult.reason)
    });
}
function ArcjetRuleResultFromProtocol(proto) {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetRuleResult"]({
        ruleId: proto.ruleId,
        ttl: proto.ttl,
        state: ArcjetRuleStateFromProtocol(proto.state),
        conclusion: ArcjetConclusionFromProtocol(proto.conclusion),
        reason: ArcjetReasonFromProtocol(proto.reason)
    });
}
function ArcjetDecisionToProtocol(decision) {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Decision"]({
        id: decision.id,
        ttl: decision.ttl,
        conclusion: ArcjetConclusionToProtocol(decision.conclusion),
        reason: ArcjetReasonToProtocol(decision.reason),
        ruleResults: decision.results.map(ArcjetRuleResultToProtocol)
    });
}
function ArcjetIpDetailsFromProtocol(ipDetails) {
    if (!ipDetails) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetIpDetails"]();
    }
    // A default value from the Decide service means we don't have data for the
    // field so we translate to `undefined`. Some fields have interconnected logic
    // that determines if the default value can be provided to users.
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetIpDetails"]({
        // If we have a non-0 latitude, or a 0 latitude with a non-0 accuracy radius
        // then we have a latitude from the Decide service
        latitude: ipDetails.latitude || ipDetails.accuracyRadius ? ipDetails.latitude : undefined,
        // If we have a non-0 longitude, or a 0 longitude with a non-0 accuracy
        // radius then we have a longitude from the Decide service
        longitude: ipDetails.longitude || ipDetails.accuracyRadius ? ipDetails.longitude : undefined,
        // If we have a non-0 latitude/longitude/accuracyRadius, we assume that the
        // accuracyRadius value was set
        accuracyRadius: ipDetails.longitude || ipDetails.latitude || ipDetails.accuracyRadius ? ipDetails.accuracyRadius : undefined,
        timezone: ipDetails.timezone !== "" ? ipDetails.timezone : undefined,
        postalCode: ipDetails.postalCode !== "" ? ipDetails.postalCode : undefined,
        city: ipDetails.city !== "" ? ipDetails.city : undefined,
        region: ipDetails.region !== "" ? ipDetails.region : undefined,
        country: ipDetails.country !== "" ? ipDetails.country : undefined,
        countryName: ipDetails.countryName !== "" ? ipDetails.countryName : undefined,
        continent: ipDetails.continent !== "" ? ipDetails.continent : undefined,
        continentName: ipDetails.continentName !== "" ? ipDetails.continentName : undefined,
        asn: ipDetails.asn !== "" ? ipDetails.asn : undefined,
        asnName: ipDetails.asnName !== "" ? ipDetails.asnName : undefined,
        asnDomain: ipDetails.asnDomain !== "" ? ipDetails.asnDomain : undefined,
        asnType: ipDetails.asnType !== "" ? ipDetails.asnType : undefined,
        asnCountry: ipDetails.asnCountry !== "" ? ipDetails.asnCountry : undefined,
        service: ipDetails.service !== "" ? ipDetails.service : undefined,
        isHosting: ipDetails.isHosting,
        isVpn: ipDetails.isVpn,
        isProxy: ipDetails.isProxy,
        isTor: ipDetails.isTor,
        isRelay: ipDetails.isRelay
    });
}
function ArcjetDecisionFromProtocol(decision) {
    if (typeof decision === "undefined") {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetErrorDecision"]({
            reason: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetErrorReason"]("Missing Decision"),
            ttl: 0,
            results: [],
            ip: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetIpDetails"]()
        });
    }
    const results = Array.isArray(decision.ruleResults) ? decision.ruleResults.map(ArcjetRuleResultFromProtocol) : [];
    switch(decision.conclusion){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Conclusion"].ALLOW:
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetAllowDecision"]({
                id: decision.id,
                ttl: decision.ttl,
                reason: ArcjetReasonFromProtocol(decision.reason),
                results,
                ip: ArcjetIpDetailsFromProtocol(decision.ipDetails)
            });
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Conclusion"].DENY:
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetDenyDecision"]({
                id: decision.id,
                ttl: decision.ttl,
                reason: ArcjetReasonFromProtocol(decision.reason),
                results,
                ip: ArcjetIpDetailsFromProtocol(decision.ipDetails)
            });
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Conclusion"].CHALLENGE:
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetChallengeDecision"]({
                id: decision.id,
                ttl: decision.ttl,
                reason: ArcjetReasonFromProtocol(decision.reason),
                results,
                ip: ArcjetIpDetailsFromProtocol(decision.ipDetails)
            });
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Conclusion"].ERROR:
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetErrorDecision"]({
                id: decision.id,
                ttl: decision.ttl,
                reason: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetErrorReason"](decision.reason),
                results,
                ip: ArcjetIpDetailsFromProtocol(decision.ipDetails)
            });
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Conclusion"].UNSPECIFIED:
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetErrorDecision"]({
                id: decision.id,
                ttl: decision.ttl,
                reason: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetErrorReason"]("Invalid Conclusion"),
                results,
                ip: ArcjetIpDetailsFromProtocol(decision.ipDetails)
            });
        default:
            {
                decision.conclusion;
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetErrorDecision"]({
                    ttl: 0,
                    reason: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ArcjetErrorReason"]("Missing Conclusion"),
                    results: [],
                    ip: ArcjetIpDetailsFromProtocol(decision.ipDetails)
                });
            }
    }
}
function isRateLimitRule(rule) {
    return rule.type === "RATE_LIMIT";
}
function isTokenBucketRule(rule) {
    return isRateLimitRule(rule) && rule.algorithm === "TOKEN_BUCKET";
}
function isFixedWindowRule(rule) {
    return isRateLimitRule(rule) && rule.algorithm === "FIXED_WINDOW";
}
function isSlidingWindowRule(rule) {
    return isRateLimitRule(rule) && rule.algorithm === "SLIDING_WINDOW";
}
function isBotRule(rule) {
    return rule.type === "BOT";
}
function isEmailRule(rule) {
    return rule.type === "EMAIL";
}
function isShieldRule(rule) {
    return rule.type === "SHIELD";
}
function isSensitiveInfoRule(rule) {
    return rule.type === "SENSITIVE_INFO";
}
function ArcjetRuleToProtocol(rule) {
    if (isTokenBucketRule(rule)) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Rule"]({
            rule: {
                case: "rateLimit",
                value: {
                    version: rule.version,
                    mode: ArcjetModeToProtocol(rule.mode),
                    characteristics: rule.characteristics,
                    algorithm: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RateLimitAlgorithm"].TOKEN_BUCKET,
                    refillRate: rule.refillRate,
                    interval: rule.interval,
                    capacity: rule.capacity
                }
            }
        });
    }
    if (isFixedWindowRule(rule)) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Rule"]({
            rule: {
                case: "rateLimit",
                value: {
                    version: rule.version,
                    mode: ArcjetModeToProtocol(rule.mode),
                    characteristics: rule.characteristics,
                    algorithm: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RateLimitAlgorithm"].FIXED_WINDOW,
                    max: rule.max,
                    windowInSeconds: rule.window
                }
            }
        });
    }
    if (isSlidingWindowRule(rule)) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Rule"]({
            rule: {
                case: "rateLimit",
                value: {
                    version: rule.version,
                    mode: ArcjetModeToProtocol(rule.mode),
                    characteristics: rule.characteristics,
                    algorithm: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RateLimitAlgorithm"].SLIDING_WINDOW,
                    max: rule.max,
                    interval: rule.interval
                }
            }
        });
    }
    if (isEmailRule(rule)) {
        const allow = Array.isArray(rule.allow) ? rule.allow.map(ArcjetEmailTypeToProtocol) : [];
        const deny = Array.isArray(rule.deny) ? rule.deny.map(ArcjetEmailTypeToProtocol) : [];
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Rule"]({
            rule: {
                case: "email",
                value: {
                    version: rule.version,
                    mode: ArcjetModeToProtocol(rule.mode),
                    allow,
                    deny,
                    requireTopLevelDomain: rule.requireTopLevelDomain,
                    allowDomainLiteral: rule.allowDomainLiteral
                }
            }
        });
    }
    if (isBotRule(rule)) {
        const allow = Array.isArray(rule.allow) ? rule.allow : [];
        const deny = Array.isArray(rule.deny) ? rule.deny : [];
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Rule"]({
            rule: {
                case: "botV2",
                value: {
                    version: rule.version,
                    mode: ArcjetModeToProtocol(rule.mode),
                    allow,
                    deny
                }
            }
        });
    }
    if (isShieldRule(rule)) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Rule"]({
            rule: {
                case: "shield",
                value: {
                    version: rule.version,
                    mode: ArcjetModeToProtocol(rule.mode),
                    autoAdded: false
                }
            }
        });
    }
    if (isSensitiveInfoRule(rule)) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Rule"]({
            rule: {
                case: "sensitiveInfo",
                value: {
                    version: rule.version,
                    mode: ArcjetModeToProtocol(rule.mode),
                    allow: rule.allow,
                    deny: rule.deny
                }
            }
        });
    }
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Rule"]();
}
;
}}),
"[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/proto/decide/v1alpha1/decide_connect.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// @generated by protoc-gen-connect-es v1.6.1
// @generated from file proto/decide/v1alpha1/decide.proto (package proto.decide.v1alpha1, syntax proto3)
/* eslint-disable */ // @ts-nocheck
__turbopack_context__.s({
    "DecideService": (()=>DecideService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/proto/decide/v1alpha1/decide_pb.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$service$2d$type$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/service-type.js [app-rsc] (ecmascript)");
;
;
const DecideService = {
    typeName: "proto.decide.v1alpha1.DecideService",
    methods: {
        /**
     * @generated from rpc proto.decide.v1alpha1.DecideService.Decide
     */ decide: {
            name: "Decide",
            I: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DecideRequest"],
            O: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DecideResponse"],
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$service$2d$type$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MethodKind"].Unary
        },
        /**
     * @generated from rpc proto.decide.v1alpha1.DecideService.Report
     */ report: {
            name: "Report",
            I: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ReportRequest"],
            O: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ReportResponse"],
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$bufbuild$2f$protobuf$2f$dist$2f$esm$2f$service$2d$type$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MethodKind"].Unary
        }
    }
};
}}),
"[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/client.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$connectrpc$2b$connect$40$1$2e$6$2e$1_$40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$connectrpc$2f$connect$2f$dist$2f$esm$2f$promise$2d$client$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/promise-client.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$convert$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/convert.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/proto/decide/v1alpha1/decide_pb.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_connect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@arcjet+protocol@1.0.0-beta.7/node_modules/@arcjet/protocol/proto/decide/v1alpha1/decide_connect.js [app-rsc] (ecmascript)");
;
;
;
;
;
// TODO: Dedupe with `errorMessage` in core
function errorMessage(err) {
    if (err) {
        if (typeof err === "string") {
            return err;
        }
        if (typeof err === "object" && "message" in err && typeof err.message === "string") {
            return err.message;
        }
    }
    return "Unknown problem";
}
function createClient(options) {
    const { transport, sdkVersion, baseUrl, timeout } = options;
    const sdkStack = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$convert$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ArcjetStackToProtocol"])(options.sdkStack);
    const client = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$connectrpc$2b$connect$40$1$2e$6$2e$1_$40$bufbuild$2b$protobuf$40$1$2e$10$2e$1$2f$node_modules$2f40$connectrpc$2f$connect$2f$dist$2f$esm$2f$promise$2d$client$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createPromiseClient"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_connect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DecideService"], transport);
    return Object.freeze({
        async decide (context, details, rules) {
            const { log } = context;
            let hasValidateEmail = false;
            const protoRules = [];
            for (const rule of rules){
                if (rule.type === "EMAIL") {
                    hasValidateEmail = true;
                }
                protoRules.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$convert$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ArcjetRuleToProtocol"])(rule));
            }
            // Build the request object from the Protobuf generated class.
            const decideRequest = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DecideRequest"]({
                sdkStack,
                sdkVersion,
                characteristics: context.characteristics,
                details: {
                    ip: details.ip,
                    method: details.method,
                    protocol: details.protocol,
                    host: details.host,
                    path: details.path,
                    headers: Object.fromEntries(details.headers.entries()),
                    cookies: details.cookies,
                    query: details.query,
                    // TODO(#208): Re-add body
                    // body: details.body,
                    extra: details.extra,
                    email: typeof details.email === "string" ? details.email : undefined
                },
                rules: protoRules
            });
            log.debug("Decide request to %s", baseUrl);
            const response = await client.decide(decideRequest, {
                headers: {
                    Authorization: `Bearer ${context.key}`
                },
                // If an email rule is configured, we double the timeout.
                // See https://github.com/arcjet/arcjet-js/issues/1697
                timeoutMs: hasValidateEmail ? timeout * 2 : timeout
            });
            const decision = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$convert$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ArcjetDecisionFromProtocol"])(response.decision);
            log.debug({
                id: decision.id,
                fingerprint: context.fingerprint,
                path: details.path,
                runtime: context.runtime,
                ttl: decision.ttl,
                conclusion: decision.conclusion,
                reason: decision.reason,
                ruleResults: decision.results
            }, "Decide response");
            return decision;
        },
        report (context, details, decision, rules) {
            const { log } = context;
            // Build the request object from the Protobuf generated class.
            const reportRequest = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$proto$2f$decide$2f$v1alpha1$2f$decide_pb$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ReportRequest"]({
                sdkStack,
                sdkVersion,
                characteristics: context.characteristics,
                details: {
                    ip: details.ip,
                    method: details.method,
                    protocol: details.protocol,
                    host: details.host,
                    path: details.path,
                    headers: Object.fromEntries(details.headers.entries()),
                    cookies: details.cookies,
                    query: details.query,
                    // TODO(#208): Re-add body
                    // body: details.body,
                    extra: details.extra,
                    email: typeof details.email === "string" ? details.email : undefined
                },
                decision: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$convert$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ArcjetDecisionToProtocol"])(decision),
                rules: rules.map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$arcjet$2b$protocol$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f40$arcjet$2f$protocol$2f$convert$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ArcjetRuleToProtocol"])
            });
            log.debug("Report request to %s", baseUrl);
            // We use the promise API directly to avoid returning a promise from this
            // function so execution can't be paused with `await`
            const reportPromise = client.report(reportRequest, {
                headers: {
                    Authorization: `Bearer ${context.key}`
                },
                // Rules don't execute during `Report` so we don't adjust the timeout
                // if an email rule is configured.
                timeoutMs: 2_000
            }).then((response)=>{
                log.debug({
                    id: decision.id,
                    fingerprint: context.fingerprint,
                    path: details.path,
                    runtime: context.runtime,
                    ttl: decision.ttl
                }, "Report response");
            }).catch((err)=>{
                log.info("Encountered problem sending report: %s", errorMessage(err));
            });
            if (typeof context.waitUntil === "function") {
                context.waitUntil(reportPromise);
            }
        }
    });
}
;
}}),

};

//# sourceMappingURL=49d33_%40arcjet_protocol_32141b9a._.js.map