{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/auth/server.ts"], "sourcesContent": ["import 'server-only';\n\nexport * from '@clerk/nextjs/server';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/design-system/components/ui/sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Sidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"Sidebar\",\n);\nexport const SidebarContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarContent\",\n);\nexport const SidebarFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call <PERSON><PERSON><PERSON>ooter() from the server but <PERSON>barFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarFooter\",\n);\nexport const SidebarGroup = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarGroup\",\n);\nexport const SidebarGroupAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarGroupAction\",\n);\nexport const SidebarGroupContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarGroupContent\",\n);\nexport const SidebarGroupLabel = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarGroupLabel\",\n);\nexport const SidebarHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarHeader\",\n);\nexport const SidebarInput = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarInput\",\n);\nexport const SidebarInset = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarInset\",\n);\nexport const SidebarMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenu\",\n);\nexport const SidebarMenuAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuAction\",\n);\nexport const SidebarMenuBadge = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuBadge\",\n);\nexport const SidebarMenuButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuButton\",\n);\nexport const SidebarMenuItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuItem\",\n);\nexport const SidebarMenuSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuSkeleton\",\n);\nexport const SidebarMenuSub = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuSub\",\n);\nexport const SidebarMenuSubButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuSubButton\",\n);\nexport const SidebarMenuSubItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuSubItem\",\n);\nexport const SidebarProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarProvider\",\n);\nexport const SidebarRail = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarRail\",\n);\nexport const SidebarSeparator = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarSeparator\",\n);\nexport const SidebarTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarTrigger\",\n);\nexport const useSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx <module evaluation>\",\n    \"useSidebar\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,kFACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,kFACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,kFACA;AAEG,MAAM,eAAe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,kFACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,kFACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,kFACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,kFACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,kFACA;AAEG,MAAM,eAAe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,kFACA;AAEG,MAAM,eAAe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,kFACA;AAEG,MAAM,cAAc,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,kFACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,kFACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,kFACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,kFACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,kFACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,kFACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,kFACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,kFACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,kFACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,kFACA;AAEG,MAAM,cAAc,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,kFACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,kFACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,kFACA;AAEG,MAAM,aAAa,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,kFACA", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/design-system/components/ui/sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Sidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"Sidebar\",\n);\nexport const SidebarContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarContent\",\n);\nexport const SidebarFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarFooter() from the server but <PERSON><PERSON><PERSON>ooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarFooter\",\n);\nexport const SidebarGroup = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarGroup\",\n);\nexport const SidebarGroupAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarGroupAction\",\n);\nexport const SidebarGroupContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarGroupContent\",\n);\nexport const SidebarGroupLabel = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarGroupLabel\",\n);\nexport const SidebarHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarHeader\",\n);\nexport const SidebarInput = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarInput\",\n);\nexport const SidebarInset = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarInset\",\n);\nexport const SidebarMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarMenu\",\n);\nexport const SidebarMenuAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarMenuAction\",\n);\nexport const SidebarMenuBadge = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarMenuBadge\",\n);\nexport const SidebarMenuButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarMenuButton\",\n);\nexport const SidebarMenuItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarMenuItem\",\n);\nexport const SidebarMenuSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarMenuSkeleton\",\n);\nexport const SidebarMenuSub = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarMenuSub\",\n);\nexport const SidebarMenuSubButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarMenuSubButton\",\n);\nexport const SidebarMenuSubItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarMenuSubItem\",\n);\nexport const SidebarProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarProvider\",\n);\nexport const SidebarRail = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarRail\",\n);\nexport const SidebarSeparator = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarSeparator\",\n);\nexport const SidebarTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"SidebarTrigger\",\n);\nexport const useSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sidebar.tsx\",\n    \"useSidebar\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8DACA;AAEG,MAAM,eAAe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8DACA;AAEG,MAAM,eAAe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8DACA;AAEG,MAAM,eAAe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8DACA;AAEG,MAAM,cAAc,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8DACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,8DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8DACA;AAEG,MAAM,cAAc,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8DACA;AAEG,MAAM,aAAa,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8DACA", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/analytics/posthog/server.ts"], "sourcesContent": ["import 'server-only';\nimport { PostHog } from 'posthog-node';\nimport { keys } from '../keys';\n\nconst envKeys = keys();\n\nexport const analytics = envKeys.NEXT_PUBLIC_POSTHOG_KEY && envKeys.NEXT_PUBLIC_POSTHOG_HOST\n  ? new PostHog(envKeys.NEXT_PUBLIC_POSTHOG_KEY, {\n      host: envKeys.NEXT_PUBLIC_POSTHOG_HOST,\n\n      // Don't batch events and flush immediately - we're running in a serverless environment\n      flushAt: 1,\n      flushInterval: 0,\n    })\n  : null;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,UAAU,CAAA,GAAA,6HAAA,CAAA,OAAI,AAAD;AAEZ,MAAM,YAAY,QAAQ,uBAAuB,IAAI,QAAQ,wBAAwB,GACxF,IAAI,qNAAA,CAAA,UAAO,CAAC,QAAQ,uBAAuB,EAAE;IAC3C,MAAM,QAAQ,wBAAwB;IAEtC,uFAAuF;IACvF,SAAS;IACT,eAAe;AACjB,KACA", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/feature-flags/lib/create-flag.ts"], "sourcesContent": ["import { analytics } from '@repo/analytics/posthog/server';\nimport { auth } from '@repo/auth/server';\nimport { flag } from 'flags/next';\n\nexport const createFlag = (key: string) =>\n  flag({\n    key,\n    defaultValue: false,\n    async decide() {\n      const { userId } = await auth();\n\n      if (!userId) {\n        return this.defaultValue as boolean;\n      }\n\n      const isEnabled = analytics\n        ? await analytics.isFeatureEnabled(key, userId)\n        : null;\n\n      return isEnabled ?? (this.defaultValue as boolean);\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;;;;AAEO,MAAM,aAAa,CAAC,MACzB,CAAA,GAAA,8OAAA,CAAA,OAAI,AAAD,EAAE;QACH;QACA,cAAc;QACd,MAAM;YACJ,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,6RAAA,CAAA,OAAI,AAAD;YAE5B,IAAI,CAAC,QAAQ;gBACX,OAAO,IAAI,CAAC,YAAY;YAC1B;YAEA,MAAM,YAAY,0IAAA,CAAA,YAAS,GACvB,MAAM,0IAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,KAAK,UACtC;YAEJ,OAAO,aAAc,IAAI,CAAC,YAAY;QACxC;IACF", "debugId": null}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/feature-flags/index.ts"], "sourcesContent": ["import { createFlag } from './lib/create-flag';\n\nexport const showBetaFeature = createFlag('showBetaFeature');\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,kBAAkB,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/notifications/components/provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const NotificationsProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call NotificationsProvider() from the server but NotificationsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/notifications/components/provider.tsx <module evaluation>\",\n    \"NotificationsProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,wBAAwB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,gFACA", "debugId": null}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/notifications/components/provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const NotificationsProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call NotificationsProvider() from the server but NotificationsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/notifications/components/provider.tsx\",\n    \"NotificationsProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,wBAAwB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,4DACA", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/security/index.ts"], "sourcesContent": ["import arcjet, {\n  type ArcjetBotCategory,\n  type ArcjetWellKnownBot,\n  detectBot,\n  request,\n  shield,\n} from '@arcjet/next';\nimport { keys } from './keys';\n\nconst arcjetKey = keys().ARCJET_KEY;\n\nexport const secure = async (\n  allow: (ArcjetWellKnownBot | ArcjetBotCategory)[],\n  sourceRequest?: Request\n) => {\n  if (!arcjetKey) {\n    return;\n  }\n\n  const base = arcjet({\n    // Get your site key from https://app.arcjet.com\n    key: arcjetKey,\n    // Identify the user by their IP address\n    characteristics: ['ip.src'],\n    rules: [\n      // Protect against common attacks with Arcjet Shield\n      shield({\n        // Will block requests. Use \"DRY_RUN\" to log only\n        mode: 'LIVE',\n      }),\n      // Other rules are added in different routes\n    ],\n  });\n\n  const req = sourceRequest ?? (await request());\n  const aj = base.withRule(detectBot({ mode: 'LIVE', allow }));\n  const decision = await aj.protect(req);\n\n  if (decision.isDenied()) {\n    console.warn(\n      `Arcjet decision: ${JSON.stringify(decision.reason, null, 2)}`\n    );\n\n    if (decision.reason.isBot()) {\n      throw new Error('No bots allowed');\n    }\n\n    if (decision.reason.isRateLimit()) {\n      throw new Error('Rate limit exceeded');\n    }\n\n    throw new Error('Access denied');\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAOA;;;AAEA,MAAM,YAAY,CAAA,GAAA,4HAAA,CAAA,OAAI,AAAD,IAAI,UAAU;AAE5B,MAAM,SAAS,OACpB,OACA;IAEA,IAAI,CAAC,WAAW;QACd;IACF;IAEA,MAAM,OAAO,CAAA,GAAA,sQAAA,CAAA,UAAM,AAAD,EAAE;QAClB,gDAAgD;QAChD,KAAK;QACL,wCAAwC;QACxC,iBAAiB;YAAC;SAAS;QAC3B,OAAO;YACL,oDAAoD;YACpD,CAAA,GAAA,+MAAA,CAAA,SAAM,AAAD,EAAE;gBACL,iDAAiD;gBACjD,MAAM;YACR;SAED;IACH;IAEA,MAAM,MAAM,iBAAkB,MAAM,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;IAC1C,MAAM,KAAK,KAAK,QAAQ,CAAC,CAAA,GAAA,+MAAA,CAAA,YAAS,AAAD,EAAE;QAAE,MAAM;QAAQ;IAAM;IACzD,MAAM,WAAW,MAAM,GAAG,OAAO,CAAC;IAElC,IAAI,SAAS,QAAQ,IAAI;QACvB,QAAQ,IAAI,CACV,CAAC,iBAAiB,EAAE,KAAK,SAAS,CAAC,SAAS,MAAM,EAAE,MAAM,IAAI;QAGhE,IAAI,SAAS,MAAM,CAAC,KAAK,IAAI;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,SAAS,MAAM,CAAC,WAAW,IAAI;YACjC,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 554, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/app/app/%28authenticated%29/components/app-header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppHeader() from the server but AppHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/app/app/(authenticated)/components/app-header.tsx <module evaluation>\",\n    \"AppHeader\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,wFACA", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/app/app/%28authenticated%29/components/app-header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppHeader() from the server but AppHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/app/app/(authenticated)/components/app-header.tsx\",\n    \"AppHeader\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,oEACA", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/app/app/%28authenticated%29/components/posthog-identifier.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PostHogIdentifier = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostHogIdentifier() from the server but PostHogIdentifier is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/app/app/(authenticated)/components/posthog-identifier.tsx <module evaluation>\",\n    \"PostHogIdentifier\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,gGACA", "debugId": null}}, {"offset": {"line": 606, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/app/app/%28authenticated%29/components/posthog-identifier.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PostHogIdentifier = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostHogIdentifier() from the server but PostHogIdentifier is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/app/app/(authenticated)/components/posthog-identifier.tsx\",\n    \"PostHogIdentifier\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,4EACA", "debugId": null}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 630, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/app/app/%28authenticated%29/components/sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const GlobalSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call GlobalSidebar() from the server but GlobalSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/app/app/(authenticated)/components/sidebar.tsx <module evaluation>\",\n    \"GlobalSidebar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qFACA", "debugId": null}}, {"offset": {"line": 644, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/app/app/%28authenticated%29/components/sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const GlobalSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call GlobalSidebar() from the server but GlobalSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/app/app/(authenticated)/components/sidebar.tsx\",\n    \"GlobalSidebar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,iEACA", "debugId": null}}, {"offset": {"line": 658, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 668, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/app/app/%28authenticated%29/layout.tsx"], "sourcesContent": ["import { env } from '@/env';\nimport { auth, currentUser } from '@repo/auth/server';\nimport { SidebarProvider } from '@repo/design-system/components/ui/sidebar';\nimport { showBetaFeature } from '@repo/feature-flags';\nimport { NotificationsProvider } from '@repo/notifications/components/provider';\nimport { secure } from '@repo/security';\nimport type { ReactNode } from 'react';\nimport { AppHeader } from './components/app-header';\nimport { PostHogIdentifier } from './components/posthog-identifier';\nimport { GlobalSidebar } from './components/sidebar';\n\ntype AppLayoutProperties = {\n  readonly children: ReactNode;\n};\n\nconst AppLayout = async ({ children }: AppLayoutProperties) => {\n  if (env.ARCJET_KEY) {\n    await secure(['CATEGORY:PREVIEW']);\n  }\n\n  const user = await currentUser();\n  const { redirectToSignIn } = await auth();\n  const betaFeature = await showBetaFeature();\n\n  if (!user) {\n    return redirectToSignIn();\n  }\n\n  return (\n    <NotificationsProvider userId={user.id}>\n      <SidebarProvider>\n        <GlobalSidebar>\n          <div className=\"flex flex-col min-h-screen\">\n            <AppHeader />\n            <div className=\"flex-1\">\n              {betaFeature && (\n                <div className=\"m-4 rounded-full bg-blue-500 p-1.5 text-center text-sm text-white\">\n                  Beta feature now available\n                </div>\n              )}\n              {children}\n            </div>\n          </div>\n        </GlobalSidebar>\n        <PostHogIdentifier />\n      </SidebarProvider>\n    </NotificationsProvider>\n  );\n};\n\nexport default AppLayout;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;;;AAMA,MAAM,YAAY,OAAO,EAAE,QAAQ,EAAuB;IACxD,IAAI,kHAAA,CAAA,MAAG,CAAC,UAAU,EAAE;QAClB,MAAM,CAAA,GAAA,6HAAA,CAAA,SAAM,AAAD,EAAE;YAAC;SAAmB;IACnC;IAEA,MAAM,OAAO,MAAM,CAAA,GAAA,oSAAA,CAAA,cAAW,AAAD;IAC7B,MAAM,EAAE,gBAAgB,EAAE,GAAG,MAAM,CAAA,GAAA,6RAAA,CAAA,OAAI,AAAD;IACtC,MAAM,cAAc,MAAM,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAExC,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6VAAC,oJAAA,CAAA,wBAAqB;QAAC,QAAQ,KAAK,EAAE;kBACpC,cAAA,6VAAC,4JAAA,CAAA,kBAAe;;8BACd,6VAAC,iKAAA,CAAA,gBAAa;8BACZ,cAAA,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,uKAAA,CAAA,YAAS;;;;;0CACV,6VAAC;gCAAI,WAAU;;oCACZ,6BACC,6VAAC;wCAAI,WAAU;kDAAoE;;;;;;oCAIpF;;;;;;;;;;;;;;;;;;8BAIP,6VAAC,+KAAA,CAAA,oBAAiB;;;;;;;;;;;;;;;;AAI1B;uCAEe", "debugId": null}}]}