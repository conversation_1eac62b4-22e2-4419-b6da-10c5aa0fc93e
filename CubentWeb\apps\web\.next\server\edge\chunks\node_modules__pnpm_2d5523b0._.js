(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/node_modules__pnpm_2d5523b0._.js", {

"[project]/node_modules/.pnpm/@t3-oss+env-core@0.13.4_ark_5791aa5b9cbdf911b905d1920b6c6d1f/node_modules/@t3-oss/env-core/dist/src-Cq4nGjdj.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
//#region src/standard.ts
__turbopack_context__.s({
    "createEnv": (()=>createEnv)
});
function ensureSynchronous(value, message) {
    if (value instanceof Promise) throw new Error(message);
}
function parseWithDictionary(dictionary, value) {
    const result = {};
    const issues = [];
    for(const key in dictionary){
        const propResult = dictionary[key]["~standard"].validate(value[key]);
        ensureSynchronous(propResult, `Validation must be synchronous, but ${key} returned a Promise.`);
        if (propResult.issues) {
            issues.push(...propResult.issues.map((issue)=>({
                    ...issue,
                    path: [
                        key,
                        ...issue.path ?? []
                    ]
                })));
            continue;
        }
        result[key] = propResult.value;
    }
    if (issues.length) return {
        issues
    };
    return {
        value: result
    };
}
//#endregion
//#region src/index.ts
/**
* Create a new environment variable schema.
*/ function createEnv(opts) {
    const runtimeEnv = opts.runtimeEnvStrict ?? opts.runtimeEnv ?? process.env;
    const emptyStringAsUndefined = opts.emptyStringAsUndefined ?? false;
    if (emptyStringAsUndefined) {
        for (const [key, value] of Object.entries(runtimeEnv))if (value === "") delete runtimeEnv[key];
    }
    const skip = !!opts.skipValidation;
    if (skip) return runtimeEnv;
    const _client = typeof opts.client === "object" ? opts.client : {};
    const _server = typeof opts.server === "object" ? opts.server : {};
    const _shared = typeof opts.shared === "object" ? opts.shared : {};
    const isServer = opts.isServer ?? ("undefined" === "undefined" || "Deno" in window);
    const finalSchemaShape = isServer ? {
        ..._server,
        ..._shared,
        ..._client
    } : {
        ..._client,
        ..._shared
    };
    const parsed = opts.createFinalSchema?.(finalSchemaShape, isServer)["~standard"].validate(runtimeEnv) ?? parseWithDictionary(finalSchemaShape, runtimeEnv);
    ensureSynchronous(parsed, "Validation must be synchronous");
    const onValidationError = opts.onValidationError ?? ((issues)=>{
        console.error("❌ Invalid environment variables:", issues);
        throw new Error("Invalid environment variables");
    });
    const onInvalidAccess = opts.onInvalidAccess ?? (()=>{
        throw new Error("❌ Attempted to access a server-side environment variable on the client");
    });
    if (parsed.issues) return onValidationError(parsed.issues);
    const isServerAccess = (prop)=>{
        if (!opts.clientPrefix) return true;
        return !prop.startsWith(opts.clientPrefix) && !(prop in _shared);
    };
    const isValidServerAccess = (prop)=>{
        return isServer || !isServerAccess(prop);
    };
    const ignoreProp = (prop)=>{
        return prop === "__esModule" || prop === "$$typeof";
    };
    const extendedObj = (opts.extends ?? []).reduce((acc, curr)=>{
        return Object.assign(acc, curr);
    }, {});
    const fullObj = Object.assign(extendedObj, parsed.value);
    const env = new Proxy(fullObj, {
        get (target, prop) {
            if (typeof prop !== "string") return void 0;
            if (ignoreProp(prop)) return void 0;
            if (!isValidServerAccess(prop)) return onInvalidAccess(prop);
            return Reflect.get(target, prop);
        }
    });
    return env;
}
;
}}),
"[project]/node_modules/.pnpm/@t3-oss+env-core@0.13.4_ark_5791aa5b9cbdf911b905d1920b6c6d1f/node_modules/@t3-oss/env-core/dist/index.js [middleware-edge] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$src$2d$Cq4nGjdj$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@t3-oss+env-core@0.13.4_ark_5791aa5b9cbdf911b905d1920b6c6d1f/node_modules/@t3-oss/env-core/dist/src-Cq4nGjdj.js [middleware-edge] (ecmascript)");
;
;
}}),
"[project]/node_modules/.pnpm/@t3-oss+env-core@0.13.4_ark_5791aa5b9cbdf911b905d1920b6c6d1f/node_modules/@t3-oss/env-core/dist/index.js [middleware-edge] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$src$2d$Cq4nGjdj$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@t3-oss+env-core@0.13.4_ark_5791aa5b9cbdf911b905d1920b6c6d1f/node_modules/@t3-oss/env-core/dist/src-Cq4nGjdj.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@t3-oss+env-core@0.13.4_ark_5791aa5b9cbdf911b905d1920b6c6d1f/node_modules/@t3-oss/env-core/dist/index.js [middleware-edge] (ecmascript) <locals>");
}}),
"[project]/node_modules/.pnpm/@t3-oss+env-core@0.13.4_ark_5791aa5b9cbdf911b905d1920b6c6d1f/node_modules/@t3-oss/env-core/dist/presets-zod.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "fly": (()=>fly),
    "neonVercel": (()=>neonVercel),
    "netlify": (()=>netlify),
    "railway": (()=>railway),
    "render": (()=>render),
    "uploadthing": (()=>uploadthing),
    "uploadthingV6": (()=>uploadthingV6),
    "upstashRedis": (()=>upstashRedis),
    "vercel": (()=>vercel)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$src$2d$Cq4nGjdj$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@t3-oss+env-core@0.13.4_ark_5791aa5b9cbdf911b905d1920b6c6d1f/node_modules/@t3-oss/env-core/dist/src-Cq4nGjdj.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/index.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/external.js [middleware-edge] (ecmascript) <export * as z>");
;
;
//#region src/presets-zod.ts
/**
* Vercel System Environment Variables
* @see https://vercel.com/docs/projects/environment-variables/system-environment-variables#system-environment-variables
*/ const vercel = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$src$2d$Cq4nGjdj$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createEnv"])({
        server: {
            VERCEL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            CI: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_ENV: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
                "development",
                "preview",
                "production"
            ]).optional(),
            VERCEL_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_PROJECT_PRODUCTION_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_BRANCH_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_REGION: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_DEPLOYMENT_ID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_SKEW_PROTECTION_ENABLED: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_AUTOMATION_BYPASS_SECRET: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_GIT_PROVIDER: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_GIT_REPO_SLUG: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_GIT_REPO_OWNER: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_GIT_REPO_ID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_GIT_COMMIT_REF: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_GIT_COMMIT_SHA: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_GIT_COMMIT_MESSAGE: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_GIT_COMMIT_AUTHOR_LOGIN: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_GIT_COMMIT_AUTHOR_NAME: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_GIT_PREVIOUS_SHA: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            VERCEL_GIT_PULL_REQUEST_ID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
        },
        runtimeEnv: process.env
    });
/**
* Neon for Vercel Environment Variables
* @see https://neon.tech/docs/guides/vercel-native-integration#environment-variables-set-by-the-integration
*/ const neonVercel = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$src$2d$Cq4nGjdj$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createEnv"])({
        server: {
            DATABASE_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
            DATABASE_URL_UNPOOLED: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            PGHOST: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            PGHOST_UNPOOLED: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            PGUSER: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            PGDATABASE: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            PGPASSWORD: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            POSTGRES_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url().optional(),
            POSTGRES_URL_NON_POOLING: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url().optional(),
            POSTGRES_USER: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            POSTGRES_HOST: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            POSTGRES_PASSWORD: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            POSTGRES_DATABASE: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            POSTGRES_URL_NO_SSL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url().optional(),
            POSTGRES_PRISMA_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url().optional()
        },
        runtimeEnv: process.env
    });
/**
* @see https://v6.docs.uploadthing.com/getting-started/nuxt#add-env-variables
*/ const uploadthingV6 = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$src$2d$Cq4nGjdj$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createEnv"])({
        server: {
            UPLOADTHING_TOKEN: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
        },
        runtimeEnv: process.env
    });
/**
* @see https://docs.uploadthing.com/getting-started/appdir#add-env-variables
*/ const uploadthing = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$src$2d$Cq4nGjdj$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createEnv"])({
        server: {
            UPLOADTHING_TOKEN: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
        },
        runtimeEnv: process.env
    });
/**
* Render System Environment Variables
* @see https://docs.render.com/environment-variables#all-runtimes
*/ const render = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$src$2d$Cq4nGjdj$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createEnv"])({
        server: {
            IS_PULL_REQUEST: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RENDER_DISCOVERY_SERVICE: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RENDER_EXTERNAL_HOSTNAME: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RENDER_EXTERNAL_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url().optional(),
            RENDER_GIT_BRANCH: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RENDER_GIT_COMMIT: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RENDER_GIT_REPO_SLUG: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RENDER_INSTANCE_ID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RENDER_SERVICE_ID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RENDER_SERVICE_NAME: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RENDER_SERVICE_TYPE: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
                "web",
                "pserv",
                "cron",
                "worker",
                "static"
            ]).optional(),
            RENDER: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
        },
        runtimeEnv: process.env
    });
/**
* Railway Environment Variables
* @see https://docs.railway.app/reference/variables#railway-provided-variables
*/ const railway = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$src$2d$Cq4nGjdj$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createEnv"])({
        server: {
            RAILWAY_PUBLIC_DOMAIN: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_PRIVATE_DOMAIN: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_TCP_PROXY_DOMAIN: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_TCP_PROXY_PORT: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_TCP_APPLICATION_PORT: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_PROJECT_NAME: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_PROJECT_ID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_ENVIRONMENT_NAME: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_ENVIRONMENT_ID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_SERVICE_NAME: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_SERVICE_ID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_REPLICA_ID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_DEPLOYMENT_ID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_SNAPSHOT_ID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_VOLUME_NAME: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_VOLUME_MOUNT_PATH: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_RUN_UID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_GIT_COMMIT_SHA: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_GIT_AUTHOR_EMAIL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_GIT_BRANCH: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_GIT_REPO_NAME: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_GIT_REPO_OWNER: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            RAILWAY_GIT_COMMIT_MESSAGE: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
        },
        runtimeEnv: process.env
    });
/**
* Fly.io Environment Variables
* @see https://fly.io/docs/machines/runtime-environment/#environment-variables
*/ const fly = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$src$2d$Cq4nGjdj$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createEnv"])({
        server: {
            FLY_APP_NAME: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            FLY_MACHINE_ID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            FLY_ALLOC_ID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            FLY_REGION: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            FLY_PUBLIC_IP: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            FLY_IMAGE_REF: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            FLY_MACHINE_VERSION: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            FLY_PRIVATE_IP: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            FLY_PROCESS_GROUP: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            FLY_VM_MEMORY_MB: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            PRIMARY_REGION: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
        },
        runtimeEnv: process.env
    });
/**
* Netlify Environment Variables
* @see https://docs.netlify.com/configure-builds/environment-variables
*/ const netlify = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$src$2d$Cq4nGjdj$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createEnv"])({
        server: {
            NETLIFY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            BUILD_ID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            CONTEXT: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
                "production",
                "deploy-preview",
                "branch-deploy",
                "dev"
            ]).optional(),
            REPOSITORY_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            BRANCH: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            DEPLOY_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            DEPLOY_PRIME_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            DEPLOY_ID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            SITE_NAME: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            SITE_ID: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
        },
        runtimeEnv: process.env
    });
/**
* Upstash redis Environment Variables
* @see https://upstash.com/docs/redis/howto/connectwithupstashredis
*/ const upstashRedis = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$src$2d$Cq4nGjdj$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createEnv"])({
        server: {
            UPSTASH_REDIS_REST_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url(),
            UPSTASH_REDIS_REST_TOKEN: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$25$2e$28$2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
        },
        runtimeEnv: process.env
    });
;
}}),
"[project]/node_modules/.pnpm/@t3-oss+env-nextjs@0.13.4_a_5f24d1d288682fc49fcfce0396a9018e/node_modules/@t3-oss/env-nextjs/dist/index.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createEnv": (()=>createEnv)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@t3-oss+env-core@0.13.4_ark_5791aa5b9cbdf911b905d1920b6c6d1f/node_modules/@t3-oss/env-core/dist/index.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$src$2d$Cq4nGjdj$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@t3-oss+env-core@0.13.4_ark_5791aa5b9cbdf911b905d1920b6c6d1f/node_modules/@t3-oss/env-core/dist/src-Cq4nGjdj.js [middleware-edge] (ecmascript)");
;
//#region src/index.ts
const CLIENT_PREFIX = "NEXT_PUBLIC_";
/**
* Create a new environment variable schema.
*/ function createEnv(opts) {
    const client = typeof opts.client === "object" ? opts.client : {};
    const server = typeof opts.server === "object" ? opts.server : {};
    const shared = opts.shared;
    const runtimeEnv = opts.runtimeEnv ? opts.runtimeEnv : {
        ...process.env,
        ...opts.experimental__runtimeEnv
    };
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$t3$2d$oss$2b$env$2d$core$40$0$2e$13$2e$4_ark_5791aa5b9cbdf911b905d1920b6c6d1f$2f$node_modules$2f40$t3$2d$oss$2f$env$2d$core$2f$dist$2f$src$2d$Cq4nGjdj$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createEnv"])({
        ...opts,
        shared,
        client,
        server,
        clientPrefix: CLIENT_PREFIX,
        runtimeEnv
    });
}
;
}}),
"[project]/node_modules/.pnpm/map-obj@4.3.0/node_modules/map-obj/index.js [middleware-edge] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const isObject = (value)=>typeof value === 'object' && value !== null;
const mapObjectSkip = Symbol('skip');
// Customized for this use-case
const isObjectCustom = (value)=>isObject(value) && !(value instanceof RegExp) && !(value instanceof Error) && !(value instanceof Date);
const mapObject = (object, mapper, options, isSeen = new WeakMap())=>{
    options = {
        deep: false,
        target: {},
        ...options
    };
    if (isSeen.has(object)) {
        return isSeen.get(object);
    }
    isSeen.set(object, options.target);
    const { target } = options;
    delete options.target;
    const mapArray = (array)=>array.map((element)=>isObjectCustom(element) ? mapObject(element, mapper, options, isSeen) : element);
    if (Array.isArray(object)) {
        return mapArray(object);
    }
    for (const [key, value] of Object.entries(object)){
        const mapResult = mapper(key, value, object);
        if (mapResult === mapObjectSkip) {
            continue;
        }
        let [newKey, newValue, { shouldRecurse = true } = {}] = mapResult;
        // Drop `__proto__` keys.
        if (newKey === '__proto__') {
            continue;
        }
        if (options.deep && shouldRecurse && isObjectCustom(newValue)) {
            newValue = Array.isArray(newValue) ? mapArray(newValue) : mapObject(newValue, mapper, options, isSeen);
        }
        target[newKey] = newValue;
    }
    return target;
};
module.exports = (object, mapper, options)=>{
    if (!isObject(object)) {
        throw new TypeError(`Expected an object, got \`${object}\` (${typeof object})`);
    }
    return mapObject(object, mapper, options);
};
module.exports.mapObjectSkip = mapObjectSkip;
}}),
"[project]/node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */ /* global Reflect, Promise, SuppressedError, Symbol, Iterator */ __turbopack_context__.s({
    "__addDisposableResource": (()=>__addDisposableResource),
    "__assign": (()=>__assign),
    "__asyncDelegator": (()=>__asyncDelegator),
    "__asyncGenerator": (()=>__asyncGenerator),
    "__asyncValues": (()=>__asyncValues),
    "__await": (()=>__await),
    "__awaiter": (()=>__awaiter),
    "__classPrivateFieldGet": (()=>__classPrivateFieldGet),
    "__classPrivateFieldIn": (()=>__classPrivateFieldIn),
    "__classPrivateFieldSet": (()=>__classPrivateFieldSet),
    "__createBinding": (()=>__createBinding),
    "__decorate": (()=>__decorate),
    "__disposeResources": (()=>__disposeResources),
    "__esDecorate": (()=>__esDecorate),
    "__exportStar": (()=>__exportStar),
    "__extends": (()=>__extends),
    "__generator": (()=>__generator),
    "__importDefault": (()=>__importDefault),
    "__importStar": (()=>__importStar),
    "__makeTemplateObject": (()=>__makeTemplateObject),
    "__metadata": (()=>__metadata),
    "__param": (()=>__param),
    "__propKey": (()=>__propKey),
    "__read": (()=>__read),
    "__rest": (()=>__rest),
    "__rewriteRelativeImportExtension": (()=>__rewriteRelativeImportExtension),
    "__runInitializers": (()=>__runInitializers),
    "__setFunctionName": (()=>__setFunctionName),
    "__spread": (()=>__spread),
    "__spreadArray": (()=>__spreadArray),
    "__spreadArrays": (()=>__spreadArrays),
    "__values": (()=>__values),
    "default": (()=>__TURBOPACK__default__export__)
});
var extendStatics = function(d, b) {
    extendStatics = Object.setPrototypeOf || ({
        __proto__: []
    }) instanceof Array && function(d, b) {
        d.__proto__ = b;
    } || function(d, b) {
        for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
    };
    return extendStatics(d, b);
};
function __extends(d, b) {
    if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
    extendStatics(d, b);
    function __() {
        this.constructor = d;
    }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
}
var __assign = function() {
    __assign = Object.assign || function __assign(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
function __rest(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
}
function __decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function __param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) {
        if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
        return f;
    }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for(var i = decorators.length - 1; i >= 0; i--){
        var context = {};
        for(var p in contextIn)context[p] = p === "access" ? {} : contextIn[p];
        for(var p in contextIn.access)context.access[p] = contextIn.access[p];
        context.addInitializer = function(f) {
            if (done) throw new TypeError("Cannot add initializers after decoration has completed");
            extraInitializers.push(accept(f || null));
        };
        var result = (0, decorators[i])(kind === "accessor" ? {
            get: descriptor.get,
            set: descriptor.set
        } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        } else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
}
;
function __runInitializers(thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for(var i = 0; i < initializers.length; i++){
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
}
;
function __propKey(x) {
    return typeof x === "symbol" ? x : "".concat(x);
}
;
function __setFunctionName(f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", {
        configurable: true,
        value: prefix ? "".concat(prefix, " ", name) : name
    });
}
;
function __metadata(metadataKey, metadataValue) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(metadataKey, metadataValue);
}
function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}
function __generator(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(g && (g = 0, op[0] && (_ = 0)), _)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
}
var __createBinding = Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = {
            enumerable: true,
            get: function() {
                return m[k];
            }
        };
    }
    Object.defineProperty(o, k2, desc);
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
};
function __exportStar(m, o) {
    for(var p in m)if (p !== "default" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);
}
function __values(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
}
function __read(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
}
function __spread() {
    for(var ar = [], i = 0; i < arguments.length; i++)ar = ar.concat(__read(arguments[i]));
    return ar;
}
function __spreadArrays() {
    for(var s = 0, i = 0, il = arguments.length; i < il; i++)s += arguments[i].length;
    for(var r = Array(s), k = 0, i = 0; i < il; i++)for(var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)r[k] = a[j];
    return r;
}
function __spreadArray(to, from, pack) {
    if (pack || arguments.length === 2) for(var i = 0, l = from.length, ar; i < l; i++){
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
}
function __await(v) {
    return this instanceof __await ? (this.v = v, this) : new __await(v);
}
function __asyncGenerator(thisArg, _arguments, generator) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var g = generator.apply(thisArg, _arguments || []), i, q = [];
    return i = Object.create((typeof AsyncIterator === "function" ? AsyncIterator : Object).prototype), verb("next"), verb("throw"), verb("return", awaitReturn), i[Symbol.asyncIterator] = function() {
        return this;
    }, i;
    "TURBOPACK unreachable";
    function awaitReturn(f) {
        return function(v) {
            return Promise.resolve(v).then(f, reject);
        };
    }
    function verb(n, f) {
        if (g[n]) {
            i[n] = function(v) {
                return new Promise(function(a, b) {
                    q.push([
                        n,
                        v,
                        a,
                        b
                    ]) > 1 || resume(n, v);
                });
            };
            if (f) i[n] = f(i[n]);
        }
    }
    function resume(n, v) {
        try {
            step(g[n](v));
        } catch (e) {
            settle(q[0][3], e);
        }
    }
    function step(r) {
        r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);
    }
    function fulfill(value) {
        resume("next", value);
    }
    function reject(value) {
        resume("throw", value);
    }
    function settle(f, v) {
        if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]);
    }
}
function __asyncDelegator(o) {
    var i, p;
    return i = {}, verb("next"), verb("throw", function(e) {
        throw e;
    }), verb("return"), i[Symbol.iterator] = function() {
        return this;
    }, i;
    "TURBOPACK unreachable";
    function verb(n, f) {
        i[n] = o[n] ? function(v) {
            return (p = !p) ? {
                value: __await(o[n](v)),
                done: false
            } : f ? f(v) : v;
        } : f;
    }
}
function __asyncValues(o) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var m = o[Symbol.asyncIterator], i;
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function() {
        return this;
    }, i);
    "TURBOPACK unreachable";
    function verb(n) {
        i[n] = o[n] && function(v) {
            return new Promise(function(resolve, reject) {
                v = o[n](v), settle(resolve, reject, v.done, v.value);
            });
        };
    }
    function settle(resolve, reject, d, v) {
        Promise.resolve(v).then(function(v) {
            resolve({
                value: v,
                done: d
            });
        }, reject);
    }
}
function __makeTemplateObject(cooked, raw) {
    if (Object.defineProperty) {
        Object.defineProperty(cooked, "raw", {
            value: raw
        });
    } else {
        cooked.raw = raw;
    }
    return cooked;
}
;
var __setModuleDefault = Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
};
var ownKeys = function(o) {
    ownKeys = Object.getOwnPropertyNames || function(o) {
        var ar = [];
        for(var k in o)if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
        return ar;
    };
    return ownKeys(o);
};
function __importStar(mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) {
        for(var k = ownKeys(mod), i = 0; i < k.length; i++)if (k[i] !== "default") __createBinding(result, mod, k[i]);
    }
    __setModuleDefault(result, mod);
    return result;
}
function __importDefault(mod) {
    return mod && mod.__esModule ? mod : {
        default: mod
    };
}
function __classPrivateFieldGet(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
}
function __classPrivateFieldSet(receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
}
function __classPrivateFieldIn(state, receiver) {
    if (receiver === null || typeof receiver !== "object" && typeof receiver !== "function") throw new TypeError("Cannot use 'in' operator on non-object");
    return typeof state === "function" ? receiver === state : state.has(receiver);
}
function __addDisposableResource(env, value, async) {
    if (value !== null && value !== void 0) {
        if (typeof value !== "object" && typeof value !== "function") throw new TypeError("Object expected.");
        var dispose, inner;
        if (async) {
            if (!Symbol.asyncDispose) throw new TypeError("Symbol.asyncDispose is not defined.");
            dispose = value[Symbol.asyncDispose];
        }
        if (dispose === void 0) {
            if (!Symbol.dispose) throw new TypeError("Symbol.dispose is not defined.");
            dispose = value[Symbol.dispose];
            if (async) inner = dispose;
        }
        if (typeof dispose !== "function") throw new TypeError("Object not disposable.");
        if (inner) dispose = function() {
            try {
                inner.call(this);
            } catch (e) {
                return Promise.reject(e);
            }
        };
        env.stack.push({
            value: value,
            dispose: dispose,
            async: async
        });
    } else if (async) {
        env.stack.push({
            async: true
        });
    }
    return value;
}
var _SuppressedError = typeof SuppressedError === "function" ? SuppressedError : function(error, suppressed, message) {
    var e = new Error(message);
    return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
};
function __disposeResources(env) {
    function fail(e) {
        env.error = env.hasError ? new _SuppressedError(e, env.error, "An error was suppressed during disposal.") : e;
        env.hasError = true;
    }
    var r, s = 0;
    function next() {
        while(r = env.stack.pop()){
            try {
                if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);
                if (r.dispose) {
                    var result = r.dispose.call(r.value);
                    if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) {
                        fail(e);
                        return next();
                    });
                } else s |= 1;
            } catch (e) {
                fail(e);
            }
        }
        if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();
        if (env.hasError) throw env.error;
    }
    return next();
}
function __rewriteRelativeImportExtension(path, preserveJsx) {
    if (typeof path === "string" && /^\.\.?\//.test(path)) {
        return path.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i, function(m, tsx, d, ext, cm) {
            return tsx ? preserveJsx ? ".jsx" : ".js" : d && (!ext || !cm) ? m : d + ext + "." + cm.toLowerCase() + "js";
        });
    }
    return path;
}
const __TURBOPACK__default__export__ = {
    __extends,
    __assign,
    __rest,
    __decorate,
    __param,
    __esDecorate,
    __runInitializers,
    __propKey,
    __setFunctionName,
    __metadata,
    __awaiter,
    __generator,
    __createBinding,
    __exportStar,
    __values,
    __read,
    __spread,
    __spreadArrays,
    __spreadArray,
    __await,
    __asyncGenerator,
    __asyncDelegator,
    __asyncValues,
    __makeTemplateObject,
    __importStar,
    __importDefault,
    __classPrivateFieldGet,
    __classPrivateFieldSet,
    __classPrivateFieldIn,
    __addDisposableResource,
    __disposeResources,
    __rewriteRelativeImportExtension
};
}}),
"[project]/node_modules/.pnpm/lower-case@2.0.2/node_modules/lower-case/dist.es2015/index.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Source: ftp://ftp.unicode.org/Public/UCD/latest/ucd/SpecialCasing.txt
 */ __turbopack_context__.s({
    "localeLowerCase": (()=>localeLowerCase),
    "lowerCase": (()=>lowerCase)
});
var SUPPORTED_LOCALE = {
    tr: {
        regexp: /\u0130|\u0049|\u0049\u0307/g,
        map: {
            İ: "\u0069",
            I: "\u0131",
            İ: "\u0069"
        }
    },
    az: {
        regexp: /\u0130/g,
        map: {
            İ: "\u0069",
            I: "\u0131",
            İ: "\u0069"
        }
    },
    lt: {
        regexp: /\u0049|\u004A|\u012E|\u00CC|\u00CD|\u0128/g,
        map: {
            I: "\u0069\u0307",
            J: "\u006A\u0307",
            Į: "\u012F\u0307",
            Ì: "\u0069\u0307\u0300",
            Í: "\u0069\u0307\u0301",
            Ĩ: "\u0069\u0307\u0303"
        }
    }
};
function localeLowerCase(str, locale) {
    var lang = SUPPORTED_LOCALE[locale.toLowerCase()];
    if (lang) return lowerCase(str.replace(lang.regexp, function(m) {
        return lang.map[m];
    }));
    return lowerCase(str);
}
function lowerCase(str) {
    return str.toLowerCase();
} //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/.pnpm/no-case@3.0.4/node_modules/no-case/dist.es2015/index.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "noCase": (()=>noCase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lower$2d$case$40$2$2e$0$2e$2$2f$node_modules$2f$lower$2d$case$2f$dist$2e$es2015$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lower-case@2.0.2/node_modules/lower-case/dist.es2015/index.js [middleware-edge] (ecmascript)");
;
// Support camel case ("camelCase" -> "camel Case" and "CAMELCase" -> "CAMEL Case").
var DEFAULT_SPLIT_REGEXP = [
    /([a-z0-9])([A-Z])/g,
    /([A-Z])([A-Z][a-z])/g
];
// Remove all non-word characters.
var DEFAULT_STRIP_REGEXP = /[^A-Z0-9]+/gi;
function noCase(input, options) {
    if (options === void 0) {
        options = {};
    }
    var _a = options.splitRegexp, splitRegexp = _a === void 0 ? DEFAULT_SPLIT_REGEXP : _a, _b = options.stripRegexp, stripRegexp = _b === void 0 ? DEFAULT_STRIP_REGEXP : _b, _c = options.transform, transform = _c === void 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lower$2d$case$40$2$2e$0$2e$2$2f$node_modules$2f$lower$2d$case$2f$dist$2e$es2015$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["lowerCase"] : _c, _d = options.delimiter, delimiter = _d === void 0 ? " " : _d;
    var result = replace(replace(input, splitRegexp, "$1\0$2"), stripRegexp, "\0");
    var start = 0;
    var end = result.length;
    // Trim the delimiter from around the output string.
    while(result.charAt(start) === "\0")start++;
    while(result.charAt(end - 1) === "\0")end--;
    // Transform each token independently.
    return result.slice(start, end).split("\0").map(transform).join(delimiter);
}
/**
 * Replace `re` in the input string with the replacement value.
 */ function replace(input, re, value) {
    if (re instanceof RegExp) return input.replace(re, value);
    return re.reduce(function(input, re) {
        return input.replace(re, value);
    }, input);
} //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/.pnpm/dot-case@3.0.4/node_modules/dot-case/dist.es2015/index.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "dotCase": (()=>dotCase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$tslib$40$2$2e$8$2e$1$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$no$2d$case$40$3$2e$0$2e$4$2f$node_modules$2f$no$2d$case$2f$dist$2e$es2015$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/no-case@3.0.4/node_modules/no-case/dist.es2015/index.js [middleware-edge] (ecmascript)");
;
;
function dotCase(input, options) {
    if (options === void 0) {
        options = {};
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$no$2d$case$40$3$2e$0$2e$4$2f$node_modules$2f$no$2d$case$2f$dist$2e$es2015$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["noCase"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$tslib$40$2$2e$8$2e$1$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["__assign"])({
        delimiter: "."
    }, options));
} //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/.pnpm/snake-case@3.0.4/node_modules/snake-case/dist.es2015/index.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "snakeCase": (()=>snakeCase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$tslib$40$2$2e$8$2e$1$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$dot$2d$case$40$3$2e$0$2e$4$2f$node_modules$2f$dot$2d$case$2f$dist$2e$es2015$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/dot-case@3.0.4/node_modules/dot-case/dist.es2015/index.js [middleware-edge] (ecmascript)");
;
;
function snakeCase(input, options) {
    if (options === void 0) {
        options = {};
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$dot$2d$case$40$3$2e$0$2e$4$2f$node_modules$2f$dot$2d$case$2f$dist$2e$es2015$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["dotCase"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$tslib$40$2$2e$8$2e$1$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["__assign"])({
        delimiter: "_"
    }, options));
} //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/.pnpm/snakecase-keys@8.0.1/node_modules/snakecase-keys/index.js [middleware-edge] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const map = __turbopack_context__.r("[project]/node_modules/.pnpm/map-obj@4.3.0/node_modules/map-obj/index.js [middleware-edge] (ecmascript)");
const { snakeCase } = __turbopack_context__.r("[project]/node_modules/.pnpm/snake-case@3.0.4/node_modules/snake-case/dist.es2015/index.js [middleware-edge] (ecmascript)");
const PlainObjectConstructor = {}.constructor;
module.exports = function(obj, options) {
    if (Array.isArray(obj)) {
        if (obj.some((item)=>item.constructor !== PlainObjectConstructor)) {
            throw new Error('obj must be array of plain objects');
        }
    } else {
        if (obj.constructor !== PlainObjectConstructor) {
            throw new Error('obj must be an plain object');
        }
    }
    options = Object.assign({
        deep: true,
        exclude: [],
        parsingOptions: {}
    }, options);
    return map(obj, function(key, val) {
        return [
            matches(options.exclude, key) ? key : snakeCase(key, options.parsingOptions),
            val,
            mapperOptions(key, val, options)
        ];
    }, options);
};
function matches(patterns, value) {
    return patterns.some(function(pattern) {
        return typeof pattern === 'string' ? pattern === value : pattern.test(value);
    });
}
function mapperOptions(key, val, options) {
    return options.shouldRecurse ? {
        shouldRecurse: options.shouldRecurse(key, val)
    } : undefined;
}
}}),
"[project]/node_modules/.pnpm/cookie@1.0.2/node_modules/cookie/dist/index.js [middleware-edge] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.parse = parse;
exports.serialize = serialize;
/**
 * RegExp to match cookie-name in RFC 6265 sec 4.1.1
 * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2
 * which has been replaced by the token definition in RFC 7230 appendix B.
 *
 * cookie-name       = token
 * token             = 1*tchar
 * tchar             = "!" / "#" / "$" / "%" / "&" / "'" /
 *                     "*" / "+" / "-" / "." / "^" / "_" /
 *                     "`" / "|" / "~" / DIGIT / ALPHA
 *
 * Note: Allowing more characters - https://github.com/jshttp/cookie/issues/191
 * Allow same range as cookie value, except `=`, which delimits end of name.
 */ const cookieNameRegExp = /^[\u0021-\u003A\u003C\u003E-\u007E]+$/;
/**
 * RegExp to match cookie-value in RFC 6265 sec 4.1.1
 *
 * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )
 * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E
 *                     ; US-ASCII characters excluding CTLs,
 *                     ; whitespace DQUOTE, comma, semicolon,
 *                     ; and backslash
 *
 * Allowing more characters: https://github.com/jshttp/cookie/issues/191
 * Comma, backslash, and DQUOTE are not part of the parsing algorithm.
 */ const cookieValueRegExp = /^[\u0021-\u003A\u003C-\u007E]*$/;
/**
 * RegExp to match domain-value in RFC 6265 sec 4.1.1
 *
 * domain-value      = <subdomain>
 *                     ; defined in [RFC1034], Section 3.5, as
 *                     ; enhanced by [RFC1123], Section 2.1
 * <subdomain>       = <label> | <subdomain> "." <label>
 * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]
 *                     Labels must be 63 characters or less.
 *                     'let-dig' not 'letter' in the first char, per RFC1123
 * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>
 * <let-dig-hyp>     = <let-dig> | "-"
 * <let-dig>         = <letter> | <digit>
 * <letter>          = any one of the 52 alphabetic characters A through Z in
 *                     upper case and a through z in lower case
 * <digit>           = any one of the ten digits 0 through 9
 *
 * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173
 *
 * > (Note that a leading %x2E ("."), if present, is ignored even though that
 * character is not permitted, but a trailing %x2E ("."), if present, will
 * cause the user agent to ignore the attribute.)
 */ const domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;
/**
 * RegExp to match path-value in RFC 6265 sec 4.1.1
 *
 * path-value        = <any CHAR except CTLs or ";">
 * CHAR              = %x01-7F
 *                     ; defined in RFC 5234 appendix B.1
 */ const pathValueRegExp = /^[\u0020-\u003A\u003D-\u007E]*$/;
const __toString = Object.prototype.toString;
const NullObject = /* @__PURE__ */ (()=>{
    const C = function() {};
    C.prototype = Object.create(null);
    return C;
})();
/**
 * Parse a cookie header.
 *
 * Parse the given cookie header string into an object
 * The object has the various cookies as keys(names) => values
 */ function parse(str, options) {
    const obj = new NullObject();
    const len = str.length;
    // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.
    if (len < 2) return obj;
    const dec = options?.decode || decode;
    let index = 0;
    do {
        const eqIdx = str.indexOf("=", index);
        if (eqIdx === -1) break; // No more cookie pairs.
        const colonIdx = str.indexOf(";", index);
        const endIdx = colonIdx === -1 ? len : colonIdx;
        if (eqIdx > endIdx) {
            // backtrack on prior semicolon
            index = str.lastIndexOf(";", eqIdx - 1) + 1;
            continue;
        }
        const keyStartIdx = startIndex(str, index, eqIdx);
        const keyEndIdx = endIndex(str, eqIdx, keyStartIdx);
        const key = str.slice(keyStartIdx, keyEndIdx);
        // only assign once
        if (obj[key] === undefined) {
            let valStartIdx = startIndex(str, eqIdx + 1, endIdx);
            let valEndIdx = endIndex(str, endIdx, valStartIdx);
            const value = dec(str.slice(valStartIdx, valEndIdx));
            obj[key] = value;
        }
        index = endIdx + 1;
    }while (index < len)
    return obj;
}
function startIndex(str, index, max) {
    do {
        const code = str.charCodeAt(index);
        if (code !== 0x20 /*   */  && code !== 0x09 /* \t */ ) return index;
    }while (++index < max)
    return max;
}
function endIndex(str, index, min) {
    while(index > min){
        const code = str.charCodeAt(--index);
        if (code !== 0x20 /*   */  && code !== 0x09 /* \t */ ) return index + 1;
    }
    return min;
}
/**
 * Serialize data into a cookie header.
 *
 * Serialize a name value pair into a cookie string suitable for
 * http headers. An optional options object specifies cookie parameters.
 *
 * serialize('foo', 'bar', { httpOnly: true })
 *   => "foo=bar; httpOnly"
 */ function serialize(name, val, options) {
    const enc = options?.encode || encodeURIComponent;
    if (!cookieNameRegExp.test(name)) {
        throw new TypeError(`argument name is invalid: ${name}`);
    }
    const value = enc(val);
    if (!cookieValueRegExp.test(value)) {
        throw new TypeError(`argument val is invalid: ${val}`);
    }
    let str = name + "=" + value;
    if (!options) return str;
    if (options.maxAge !== undefined) {
        if (!Number.isInteger(options.maxAge)) {
            throw new TypeError(`option maxAge is invalid: ${options.maxAge}`);
        }
        str += "; Max-Age=" + options.maxAge;
    }
    if (options.domain) {
        if (!domainValueRegExp.test(options.domain)) {
            throw new TypeError(`option domain is invalid: ${options.domain}`);
        }
        str += "; Domain=" + options.domain;
    }
    if (options.path) {
        if (!pathValueRegExp.test(options.path)) {
            throw new TypeError(`option path is invalid: ${options.path}`);
        }
        str += "; Path=" + options.path;
    }
    if (options.expires) {
        if (!isDate(options.expires) || !Number.isFinite(options.expires.valueOf())) {
            throw new TypeError(`option expires is invalid: ${options.expires}`);
        }
        str += "; Expires=" + options.expires.toUTCString();
    }
    if (options.httpOnly) {
        str += "; HttpOnly";
    }
    if (options.secure) {
        str += "; Secure";
    }
    if (options.partitioned) {
        str += "; Partitioned";
    }
    if (options.priority) {
        const priority = typeof options.priority === "string" ? options.priority.toLowerCase() : undefined;
        switch(priority){
            case "low":
                str += "; Priority=Low";
                break;
            case "medium":
                str += "; Priority=Medium";
                break;
            case "high":
                str += "; Priority=High";
                break;
            default:
                throw new TypeError(`option priority is invalid: ${options.priority}`);
        }
    }
    if (options.sameSite) {
        const sameSite = typeof options.sameSite === "string" ? options.sameSite.toLowerCase() : options.sameSite;
        switch(sameSite){
            case true:
            case "strict":
                str += "; SameSite=Strict";
                break;
            case "lax":
                str += "; SameSite=Lax";
                break;
            case "none":
                str += "; SameSite=None";
                break;
            default:
                throw new TypeError(`option sameSite is invalid: ${options.sameSite}`);
        }
    }
    return str;
}
/**
 * URL-decode string value. Optimized to skip native call when no %.
 */ function decode(str) {
    if (str.indexOf("%") === -1) return str;
    try {
        return decodeURIComponent(str);
    } catch (e) {
        return str;
    }
}
/**
 * Determine if value is a Date.
 */ function isDate(val) {
    return __toString.call(val) === "[object Date]";
} //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/.pnpm/next-international@1.3.1/node_modules/next-international/dist/app/middleware/index.js [middleware-edge] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
// src/app/middleware/index.ts
var middleware_exports = {};
__export(middleware_exports, {
    createI18nMiddleware: ()=>createI18nMiddleware
});
module.exports = __toCommonJS(middleware_exports);
var import_server = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript)");
// src/common/constants.ts
var LOCALE_HEADER = "X-Next-Locale";
var LOCALE_COOKIE = "Next-Locale";
// src/helpers/log.ts
function log(type, message) {
    if ("TURBOPACK compile-time truthy", 1) {
        console[type](`[next-international] ${message}`);
    }
    return null;
}
var warn = (message)=>log("warn", message);
// src/app/middleware/index.ts
var DEFAULT_STRATEGY = "redirect";
function createI18nMiddleware(config) {
    return function I18nMiddleware(request) {
        var _a, _b, _c;
        const locale = (_a = localeFromRequest(config.locales, request, config.resolveLocaleFromRequest)) != null ? _a : config.defaultLocale;
        const nextUrl = request.nextUrl;
        if (noLocalePrefix(config.locales, nextUrl.pathname)) {
            nextUrl.pathname = `/${locale}${nextUrl.pathname}`;
            const strategy = (_b = config.urlMappingStrategy) != null ? _b : DEFAULT_STRATEGY;
            if (strategy === "rewrite" || strategy === "rewriteDefault" && locale === config.defaultLocale) {
                const response2 = import_server.NextResponse.rewrite(nextUrl);
                return addLocaleToResponse(request, response2, locale);
            } else {
                if (![
                    "redirect",
                    "rewriteDefault"
                ].includes(strategy)) {
                    warn(`Invalid urlMappingStrategy: ${strategy}. Defaulting to redirect.`);
                }
                const response2 = import_server.NextResponse.redirect(nextUrl);
                return addLocaleToResponse(request, response2, locale);
            }
        }
        let response = import_server.NextResponse.next();
        const pathnameLocale = (_c = nextUrl.pathname.split("/", 2)) == null ? void 0 : _c[1];
        if (!pathnameLocale || config.locales.includes(pathnameLocale)) {
            if (config.urlMappingStrategy === "rewrite" && pathnameLocale !== locale || config.urlMappingStrategy === "rewriteDefault" && (pathnameLocale !== locale || pathnameLocale === config.defaultLocale)) {
                const pathnameWithoutLocale = nextUrl.pathname.slice(pathnameLocale.length + 1);
                const newUrl = new URL(pathnameWithoutLocale || "/", request.url);
                newUrl.search = nextUrl.search;
                response = import_server.NextResponse.redirect(newUrl);
            }
            return addLocaleToResponse(request, response, pathnameLocale != null ? pathnameLocale : config.defaultLocale);
        }
        return response;
    };
}
function localeFromRequest(locales, request, resolveLocaleFromRequest = defaultResolveLocaleFromRequest) {
    var _a, _b;
    const locale = (_b = (_a = request.cookies.get(LOCALE_COOKIE)) == null ? void 0 : _a.value) != null ? _b : resolveLocaleFromRequest(request);
    if (!locale || !locales.includes(locale)) {
        return null;
    }
    return locale;
}
var defaultResolveLocaleFromRequest = (request)=>{
    var _a, _b, _c;
    const header = request.headers.get("Accept-Language");
    const locale = (_c = (_b = (_a = header == null ? void 0 : header.split(",", 1)) == null ? void 0 : _a[0]) == null ? void 0 : _b.split("-", 1)) == null ? void 0 : _c[0];
    return locale != null ? locale : null;
};
function noLocalePrefix(locales, pathname) {
    return locales.every((locale)=>{
        return !(pathname === `/${locale}` || pathname.startsWith(`/${locale}/`));
    });
}
function addLocaleToResponse(request, response, locale) {
    var _a;
    response.headers.set(LOCALE_HEADER, locale);
    if (((_a = request.cookies.get(LOCALE_COOKIE)) == null ? void 0 : _a.value) !== locale) {
        response.cookies.set(LOCALE_COOKIE, locale, {
            sameSite: "strict"
        });
    }
    return response;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    createI18nMiddleware
});
}}),
"[project]/node_modules/.pnpm/nosecone@1.0.0-beta.7/node_modules/nosecone/index.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Types based on
// https://github.com/josh-hemphill/csp-typed-directives/blob/6e2cbc6d3cc18bbdc9b13d42c4556e786e28b243/src/csp.types.ts
//
// MIT License
//
// Copyright (c) 2021-present, Joshua Hemphill
// Copyright (c) 2021, Tecnico Corporation
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in all
// copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
// SOFTWARE.
// Map of configuration options to the kebab-case names for
// `Content-Security-Policy` directives
__turbopack_context__.s({
    "CONTENT_SECURITY_POLICY_DIRECTIVES": (()=>CONTENT_SECURITY_POLICY_DIRECTIVES),
    "CROSS_ORIGIN_EMBEDDER_POLICIES": (()=>CROSS_ORIGIN_EMBEDDER_POLICIES),
    "CROSS_ORIGIN_OPENER_POLICIES": (()=>CROSS_ORIGIN_OPENER_POLICIES),
    "CROSS_ORIGIN_RESOURCE_POLICIES": (()=>CROSS_ORIGIN_RESOURCE_POLICIES),
    "NoseconeValidationError": (()=>NoseconeValidationError),
    "PERMITTED_CROSS_DOMAIN_POLICIES": (()=>PERMITTED_CROSS_DOMAIN_POLICIES),
    "QUOTED": (()=>QUOTED),
    "REFERRER_POLICIES": (()=>REFERRER_POLICIES),
    "SANDBOX_DIRECTIVES": (()=>SANDBOX_DIRECTIVES),
    "createContentSecurityPolicy": (()=>createContentSecurityPolicy),
    "createContentTypeOptions": (()=>createContentTypeOptions),
    "createCrossOriginEmbedderPolicy": (()=>createCrossOriginEmbedderPolicy),
    "createCrossOriginOpenerPolicy": (()=>createCrossOriginOpenerPolicy),
    "createCrossOriginResourcePolicy": (()=>createCrossOriginResourcePolicy),
    "createDnsPrefetchControl": (()=>createDnsPrefetchControl),
    "createDownloadOptions": (()=>createDownloadOptions),
    "createFrameOptions": (()=>createFrameOptions),
    "createOriginAgentCluster": (()=>createOriginAgentCluster),
    "createPermittedCrossDomainPolicies": (()=>createPermittedCrossDomainPolicies),
    "createReferrerPolicy": (()=>createReferrerPolicy),
    "createStrictTransportSecurity": (()=>createStrictTransportSecurity),
    "createXssProtection": (()=>createXssProtection),
    "default": (()=>nosecone),
    "defaults": (()=>defaults),
    "withVercelToolbar": (()=>withVercelToolbar)
});
const CONTENT_SECURITY_POLICY_DIRECTIVES = new Map([
    [
        "baseUri",
        "base-uri"
    ],
    [
        "childSrc",
        "child-src"
    ],
    [
        "defaultSrc",
        "default-src"
    ],
    [
        "frameSrc",
        "frame-src"
    ],
    [
        "workerSrc",
        "worker-src"
    ],
    [
        "connectSrc",
        "connect-src"
    ],
    [
        "fontSrc",
        "font-src"
    ],
    [
        "imgSrc",
        "img-src"
    ],
    [
        "manifestSrc",
        "manifest-src"
    ],
    [
        "mediaSrc",
        "media-src"
    ],
    [
        "objectSrc",
        "object-src"
    ],
    [
        "prefetchSrc",
        "prefetch-src"
    ],
    [
        "scriptSrc",
        "script-src"
    ],
    [
        "scriptSrcElem",
        "script-src-elem"
    ],
    [
        "scriptSrcAttr",
        "script-src-attr"
    ],
    [
        "styleSrc",
        "style-src"
    ],
    [
        "styleSrcElem",
        "style-src-elem"
    ],
    [
        "styleSrcAttr",
        "style-src-attr"
    ],
    [
        "sandbox",
        "sandbox"
    ],
    [
        "formAction",
        "form-action"
    ],
    [
        "frameAncestors",
        "frame-ancestors"
    ],
    [
        "navigateTo",
        "navigate-to"
    ],
    [
        "reportUri",
        "report-uri"
    ],
    [
        "reportTo",
        "report-to"
    ],
    [
        "requireTrustedTypesFor",
        "require-trusted-types-for"
    ],
    [
        "trustedTypes",
        "trusted-types"
    ],
    [
        "upgradeInsecureRequests",
        "upgrade-insecure-requests"
    ]
]);
// Set of valid `Cross-Origin-Embedder-Policy` values
const CROSS_ORIGIN_EMBEDDER_POLICIES = new Set([
    "require-corp",
    "credentialless",
    "unsafe-none"
]);
// Set of valid `Cross-Origin-Opener-Policy` values
const CROSS_ORIGIN_OPENER_POLICIES = new Set([
    "same-origin",
    "same-origin-allow-popups",
    "unsafe-none"
]);
// Set of valid `Cross-Origin-Resource-Policy` values
const CROSS_ORIGIN_RESOURCE_POLICIES = new Set([
    "same-origin",
    "same-site",
    "cross-origin"
]);
// Set of valid `Resource-Policy` tokens
const REFERRER_POLICIES = new Set([
    "no-referrer",
    "no-referrer-when-downgrade",
    "same-origin",
    "origin",
    "strict-origin",
    "origin-when-cross-origin",
    "strict-origin-when-cross-origin",
    "unsafe-url",
    ""
]);
// Set of valid `X-Permitted-Cross-Domain-Policies` values
const PERMITTED_CROSS_DOMAIN_POLICIES = new Set([
    "none",
    "master-only",
    "by-content-type",
    "all"
]);
// Set of valid values for the `sandbox` directive of `Content-Security-Policy`
const SANDBOX_DIRECTIVES = new Set([
    "allow-downloads-without-user-activation",
    "allow-forms",
    "allow-modals",
    "allow-orientation-lock",
    "allow-pointer-lock",
    "allow-popups",
    "allow-popups-to-escape-sandbox",
    "allow-presentation",
    "allow-same-origin",
    "allow-scripts",
    "allow-storage-access-by-user-activation",
    "allow-top-navigation",
    "allow-top-navigation-by-user-activation"
]);
// Mapping of values that need to be quoted in `Content-Security-Policy`;
// however, it does not include `nonce-*` or `sha*-*` because those are dynamic
const QUOTED = new Map([
    [
        "self",
        "'self'"
    ],
    [
        "unsafe-eval",
        "'unsafe-eval'"
    ],
    [
        "unsafe-hashes",
        "'unsafe-hashes'"
    ],
    [
        "unsafe-inline",
        "'unsafe-inline'"
    ],
    [
        "none",
        "'none'"
    ],
    [
        "strict-dynamic",
        "'strict-dynamic'"
    ],
    [
        "report-sample",
        "'report-sample'"
    ],
    [
        "wasm-unsafe-eval",
        "'wasm-unsafe-eval'"
    ],
    [
        "script",
        "'script'"
    ]
]);
const directives = {
    baseUri: [
        "'none'"
    ],
    childSrc: [
        "'none'"
    ],
    connectSrc: [
        "'self'"
    ],
    defaultSrc: [
        "'self'"
    ],
    fontSrc: [
        "'self'"
    ],
    formAction: [
        "'self'"
    ],
    frameAncestors: [
        "'none'"
    ],
    frameSrc: [
        "'none'"
    ],
    imgSrc: [
        "'self'",
        "blob:",
        "data:"
    ],
    manifestSrc: [
        "'self'"
    ],
    mediaSrc: [
        "'self'"
    ],
    objectSrc: [
        "'none'"
    ],
    scriptSrc: [
        "'self'"
    ],
    styleSrc: [
        "'self'"
    ],
    workerSrc: [
        "'self'"
    ]
};
const defaults = {
    contentSecurityPolicy: {
        directives
    },
    crossOriginEmbedderPolicy: {
        policy: "require-corp"
    },
    crossOriginOpenerPolicy: {
        policy: "same-origin"
    },
    crossOriginResourcePolicy: {
        policy: "same-origin"
    },
    originAgentCluster: true,
    referrerPolicy: {
        policy: [
            "no-referrer"
        ]
    },
    strictTransportSecurity: {
        maxAge: 365 * 24 * 60 * 60,
        includeSubDomains: true,
        preload: false
    },
    xContentTypeOptions: true,
    xDnsPrefetchControl: {
        allow: false
    },
    xDownloadOptions: true,
    xFrameOptions: {
        action: "sameorigin"
    },
    xPermittedCrossDomainPolicies: {
        permittedPolicies: "none"
    },
    xXssProtection: true
};
function resolveValue(v) {
    if (typeof v === "function") {
        return v();
    } else {
        return v;
    }
}
class NoseconeValidationError extends Error {
    constructor(message){
        super(`validation error: ${message}`);
    }
}
// Header defaults and construction inspired by
// https://github.com/helmetjs/helmet/tree/9a8e6d5322aad6090394b0bb2e81448c5f5b3e74
//
// The MIT License
//
// Copyright (c) 2012-2024 Evan Hahn, Adam Baldwin
//
// Permission is hereby granted, free of charge, to any person obtaining
// a copy of this software and associated documentation files (the
// 'Software'), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to
// permit persons to whom the Software is furnished to do so, subject to
// the following conditions:
//
// The above copyright notice and this permission notice shall be
// included in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,
// EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
// IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
// CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
// TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
// SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
function createContentSecurityPolicy({ directives = defaults.contentSecurityPolicy.directives } = defaults.contentSecurityPolicy) {
    const cspEntries = [];
    for (const [optionKey, optionValues] of Object.entries(directives)){
        const key = CONTENT_SECURITY_POLICY_DIRECTIVES.get(// @ts-expect-error because we're validating this option key
        optionKey);
        if (!key) {
            throw new NoseconeValidationError(`${optionKey} is not a Content-Security-Policy directive`);
        }
        // Skip anything falsey
        if (!optionValues) {
            continue;
        }
        // TODO: What do we want to do if array is empty? I think they work differently for some directives
        const resolvedValues = Array.isArray(optionValues) ? new Set(optionValues.map(resolveValue)) : new Set();
        // TODO: Add more validation
        for (const value of resolvedValues){
            if (QUOTED.has(// @ts-expect-error because we are validation this value
            value)) {
                throw new NoseconeValidationError(`"${value}" must be quoted using single-quotes, e.g. "'${value}'"`);
            }
            if (key === "sandbox") {
                if (!SANDBOX_DIRECTIVES.has(// @ts-expect-error because we are validation this value
                value)) {
                    throw new NoseconeValidationError("invalid sandbox value in Content-Security-Policy");
                }
            }
        }
        const values = Array.from(resolvedValues);
        const entry = `${key} ${values.join(" ")}`.trim();
        const entryWithSep = `${entry};`;
        cspEntries.push(entryWithSep);
    }
    return [
        "content-security-policy",
        cspEntries.join(" ")
    ];
}
function createCrossOriginEmbedderPolicy({ policy = defaults.crossOriginEmbedderPolicy.policy } = defaults.crossOriginEmbedderPolicy) {
    if (CROSS_ORIGIN_EMBEDDER_POLICIES.has(policy)) {
        return [
            "cross-origin-embedder-policy",
            policy
        ];
    } else {
        throw new NoseconeValidationError(`invalid value for Cross-Origin-Embedder-Policy`);
    }
}
function createCrossOriginOpenerPolicy({ policy = defaults.crossOriginOpenerPolicy.policy } = defaults.crossOriginOpenerPolicy) {
    if (CROSS_ORIGIN_OPENER_POLICIES.has(policy)) {
        return [
            "cross-origin-opener-policy",
            policy
        ];
    } else {
        throw new NoseconeValidationError(`invalid value for Cross-Origin-Opener-Policy`);
    }
}
function createCrossOriginResourcePolicy({ policy = defaults.crossOriginResourcePolicy.policy } = defaults.crossOriginResourcePolicy) {
    if (CROSS_ORIGIN_RESOURCE_POLICIES.has(policy)) {
        return [
            "cross-origin-resource-policy",
            policy
        ];
    } else {
        throw new NoseconeValidationError(`invalid value for Cross-Origin-Resource-Policy`);
    }
}
function createOriginAgentCluster() {
    return [
        "origin-agent-cluster",
        "?1"
    ];
}
function createReferrerPolicy({ policy = defaults.referrerPolicy.policy } = defaults.referrerPolicy) {
    if (Array.isArray(policy)) {
        if (policy.length > 0) {
            const tokens = new Set();
            for (const token of policy){
                if (REFERRER_POLICIES.has(token)) {
                    tokens.add(token);
                } else {
                    throw new NoseconeValidationError(`invalid value for Referrer-Policy`);
                }
            }
            return [
                "referrer-policy",
                Array.from(tokens).join(",")
            ];
        } else {
            throw new NoseconeValidationError("must provide at least one policy for Referrer-Policy");
        }
    }
    throw new NoseconeValidationError("must provide array for Referrer-Policy");
}
function createStrictTransportSecurity({ maxAge = defaults.strictTransportSecurity.maxAge, includeSubDomains = defaults.strictTransportSecurity.includeSubDomains, preload = defaults.strictTransportSecurity.preload } = defaults.strictTransportSecurity) {
    if (maxAge >= 0 && Number.isFinite(maxAge)) {
        maxAge = Math.floor(maxAge);
    } else {
        throw new NoseconeValidationError("must provide a finite, positive integer for the maxAge of Strict-Transport-Security");
    }
    const directives = [
        `max-age=${maxAge}`
    ];
    if (includeSubDomains) {
        directives.push("includeSubDomains");
    }
    if (preload) {
        directives.push("preload");
    }
    return [
        "strict-transport-security",
        directives.join("; ")
    ];
}
function createContentTypeOptions() {
    return [
        "x-content-type-options",
        "nosniff"
    ];
}
function createDnsPrefetchControl({ allow = defaults.xDnsPrefetchControl.allow } = defaults.xDnsPrefetchControl) {
    const headerValue = allow ? "on" : "off";
    return [
        "x-dns-prefetch-control",
        headerValue
    ];
}
function createDownloadOptions() {
    return [
        "x-download-options",
        "noopen"
    ];
}
function createFrameOptions({ action = defaults.xFrameOptions.action } = defaults.xFrameOptions) {
    if (typeof action === "string") {
        const headerValue = action.toUpperCase();
        if (headerValue === "SAMEORIGIN" || headerValue === "DENY") {
            return [
                "x-frame-options",
                headerValue
            ];
        }
    }
    throw new NoseconeValidationError("invalid value for X-Frame-Options");
}
function createPermittedCrossDomainPolicies({ permittedPolicies = defaults.xPermittedCrossDomainPolicies.permittedPolicies } = defaults.xPermittedCrossDomainPolicies) {
    if (PERMITTED_CROSS_DOMAIN_POLICIES.has(permittedPolicies)) {
        return [
            "x-permitted-cross-domain-policies",
            permittedPolicies
        ];
    } else {
        throw new NoseconeValidationError(`invalid value for X-Permitted-Cross-Domain-Policies`);
    }
}
function createXssProtection() {
    return [
        "x-xss-protection",
        "0"
    ];
}
function nosecone({ contentSecurityPolicy = defaults.contentSecurityPolicy, crossOriginEmbedderPolicy = defaults.crossOriginEmbedderPolicy, crossOriginOpenerPolicy = defaults.crossOriginOpenerPolicy, crossOriginResourcePolicy = defaults.crossOriginResourcePolicy, originAgentCluster = defaults.originAgentCluster, referrerPolicy = defaults.referrerPolicy, strictTransportSecurity = defaults.strictTransportSecurity, xContentTypeOptions = defaults.xContentTypeOptions, xDnsPrefetchControl = defaults.xDnsPrefetchControl, xDownloadOptions = defaults.xDownloadOptions, xFrameOptions = defaults.xFrameOptions, xPermittedCrossDomainPolicies = defaults.xPermittedCrossDomainPolicies, xXssProtection = defaults.xXssProtection } = defaults) {
    if (contentSecurityPolicy === true) {
        contentSecurityPolicy = defaults.contentSecurityPolicy;
    }
    if (crossOriginEmbedderPolicy === true) {
        crossOriginEmbedderPolicy = defaults.crossOriginEmbedderPolicy;
    }
    if (crossOriginOpenerPolicy === true) {
        crossOriginOpenerPolicy = defaults.crossOriginOpenerPolicy;
    }
    if (crossOriginResourcePolicy === true) {
        crossOriginResourcePolicy = defaults.crossOriginResourcePolicy;
    }
    if (referrerPolicy === true) {
        referrerPolicy = defaults.referrerPolicy;
    }
    if (strictTransportSecurity === true) {
        strictTransportSecurity = defaults.strictTransportSecurity;
    }
    if (xDnsPrefetchControl === true) {
        xDnsPrefetchControl = defaults.xDnsPrefetchControl;
    }
    if (xFrameOptions === true) {
        xFrameOptions = defaults.xFrameOptions;
    }
    if (xPermittedCrossDomainPolicies === true) {
        xPermittedCrossDomainPolicies = defaults.xPermittedCrossDomainPolicies;
    }
    const headers = new Headers();
    if (contentSecurityPolicy) {
        const [headerName, headerValue] = createContentSecurityPolicy(contentSecurityPolicy);
        headers.set(headerName, headerValue);
    }
    if (crossOriginEmbedderPolicy) {
        const [headerName, headerValue] = createCrossOriginEmbedderPolicy(crossOriginEmbedderPolicy);
        headers.set(headerName, headerValue);
    }
    if (crossOriginOpenerPolicy) {
        const [headerName, headerValue] = createCrossOriginOpenerPolicy(crossOriginOpenerPolicy);
        headers.set(headerName, headerValue);
    }
    if (crossOriginResourcePolicy) {
        const [headerName, headerValue] = createCrossOriginResourcePolicy(crossOriginResourcePolicy);
        headers.set(headerName, headerValue);
    }
    if (originAgentCluster) {
        const [headerName, headerValue] = createOriginAgentCluster();
        headers.set(headerName, headerValue);
    }
    if (referrerPolicy) {
        const [headerName, headerValue] = createReferrerPolicy(referrerPolicy);
        headers.set(headerName, headerValue);
    }
    if (strictTransportSecurity) {
        const [headerName, headerValue] = createStrictTransportSecurity(strictTransportSecurity);
        headers.set(headerName, headerValue);
    }
    if (xContentTypeOptions) {
        const [headerName, headerValue] = createContentTypeOptions();
        headers.set(headerName, headerValue);
    }
    if (xDnsPrefetchControl) {
        const [headerName, headerValue] = createDnsPrefetchControl(xDnsPrefetchControl);
        headers.set(headerName, headerValue);
    }
    if (xDownloadOptions) {
        const [headerName, headerValue] = createDownloadOptions();
        headers.set(headerName, headerValue);
    }
    if (xFrameOptions) {
        const [headerName, headerValue] = createFrameOptions(xFrameOptions);
        headers.set(headerName, headerValue);
    }
    if (xPermittedCrossDomainPolicies) {
        const [headerName, headerValue] = createPermittedCrossDomainPolicies(xPermittedCrossDomainPolicies);
        headers.set(headerName, headerValue);
    }
    if (xXssProtection) {
        const [headerName, headerValue] = createXssProtection();
        headers.set(headerName, headerValue);
    }
    return headers;
}
/**
 * Augment some Nosecone configuration with the values necessary for using the
 * Vercel Toolbar.
 *
 * Follows the guidance at
 * https://vercel.com/docs/workflow-collaboration/vercel-toolbar/managing-toolbar#using-a-content-security-policy
 *
 * @param config Base configuration for you application
 * @returns Augmented configuration to allow Vercel Toolbar
 */ function withVercelToolbar(config) {
    let contentSecurityPolicy = config.contentSecurityPolicy;
    if (contentSecurityPolicy === true) {
        contentSecurityPolicy = defaults.contentSecurityPolicy;
    }
    let augmentedContentSecurityPolicy = contentSecurityPolicy;
    if (contentSecurityPolicy) {
        let scriptSrc = contentSecurityPolicy.directives?.scriptSrc;
        if (scriptSrc === true) {
            scriptSrc = defaults.contentSecurityPolicy.directives.scriptSrc;
        }
        let connectSrc = contentSecurityPolicy.directives?.connectSrc;
        if (connectSrc === true) {
            connectSrc = defaults.contentSecurityPolicy.directives.connectSrc;
        }
        let imgSrc = contentSecurityPolicy.directives?.imgSrc;
        if (imgSrc === true) {
            imgSrc = defaults.contentSecurityPolicy.directives.imgSrc;
        }
        let frameSrc = contentSecurityPolicy.directives?.frameSrc;
        if (frameSrc === true) {
            frameSrc = defaults.contentSecurityPolicy.directives.frameSrc;
        }
        let styleSrc = contentSecurityPolicy.directives?.styleSrc;
        if (styleSrc === true) {
            styleSrc = defaults.contentSecurityPolicy.directives.styleSrc;
        }
        let fontSrc = contentSecurityPolicy.directives?.fontSrc;
        if (fontSrc === true) {
            fontSrc = defaults.contentSecurityPolicy.directives.fontSrc;
        }
        augmentedContentSecurityPolicy = {
            ...contentSecurityPolicy,
            directives: {
                ...contentSecurityPolicy.directives,
                scriptSrc: scriptSrc ? [
                    ...scriptSrc.filter((v)=>v !== "'none'" && v !== "https://vercel.live"),
                    "https://vercel.live"
                ] : scriptSrc,
                connectSrc: connectSrc ? [
                    ...connectSrc.filter((v)=>v !== "'none'" && v !== "https://vercel.live" && v !== "wss://ws-us3.pusher.com"),
                    "https://vercel.live",
                    "wss://ws-us3.pusher.com"
                ] : connectSrc,
                imgSrc: imgSrc ? [
                    ...imgSrc.filter((v)=>v !== "'none'" && v !== "https://vercel.live" && v !== "https://vercel.com" && v !== "data:" && v !== "blob:"),
                    "https://vercel.live",
                    "https://vercel.com",
                    "data:",
                    "blob:"
                ] : imgSrc,
                frameSrc: frameSrc ? [
                    ...frameSrc.filter((v)=>v !== "'none'" && v !== "https://vercel.live"),
                    "https://vercel.live"
                ] : frameSrc,
                styleSrc: styleSrc ? [
                    ...styleSrc.filter((v)=>v !== "'none'" && v !== "https://vercel.live" && v !== "'unsafe-inline'"),
                    "https://vercel.live",
                    "'unsafe-inline'"
                ] : styleSrc,
                fontSrc: fontSrc ? [
                    ...fontSrc.filter((v)=>v !== "'none'" && v !== "https://vercel.live" && v !== "https://assets.vercel.com"),
                    "https://vercel.live",
                    "https://assets.vercel.com"
                ] : fontSrc
            }
        };
    }
    let crossOriginEmbedderPolicy = config.crossOriginEmbedderPolicy;
    if (crossOriginEmbedderPolicy === true) {
        crossOriginEmbedderPolicy = defaults.crossOriginEmbedderPolicy;
    }
    let augmentedCrossOriginEmbedderPolicy = crossOriginEmbedderPolicy;
    if (crossOriginEmbedderPolicy) {
        augmentedCrossOriginEmbedderPolicy = {
            policy: crossOriginEmbedderPolicy.policy ? "unsafe-none" : crossOriginEmbedderPolicy.policy
        };
    }
    return {
        ...config,
        contentSecurityPolicy: augmentedContentSecurityPolicy,
        crossOriginEmbedderPolicy: augmentedCrossOriginEmbedderPolicy
    };
}
;
}}),
"[project]/node_modules/.pnpm/@nosecone+next@1.0.0-beta.7_c27ae1b5d47543c9370e9c7fd08ab71d/node_modules/@nosecone/next/index.js [middleware-edge] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createMiddleware": (()=>createMiddleware),
    "defaults": (()=>defaults)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$nosecone$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f$nosecone$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/nosecone@1.0.0-beta.7/node_modules/nosecone/index.js [middleware-edge] (ecmascript)");
;
;
const defaults = {
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$nosecone$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f$nosecone$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["defaults"],
    contentSecurityPolicy: {
        directives: {
            ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$nosecone$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f$nosecone$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["defaults"].contentSecurityPolicy.directives,
            scriptSrc: [
                ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$nosecone$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f$nosecone$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["defaults"].contentSecurityPolicy.directives.scriptSrc,
                ...nextScriptSrc()
            ],
            styleSrc: [
                ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$nosecone$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f$nosecone$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["defaults"].contentSecurityPolicy.directives.styleSrc,
                ...nextStyleSrc()
            ]
        }
    }
};
function nonce() {
    return `'nonce-${btoa(crypto.randomUUID())}'`;
}
function nextScriptSrc() {
    return ("TURBOPACK compile-time truthy", 1) ? [
        nonce,
        "'unsafe-eval'"
    ] : ("TURBOPACK unreachable", undefined);
}
function nextStyleSrc() {
    return [
        "'unsafe-inline'"
    ];
}
/**
 * Create Next.js middleware that sets secure headers on every request.
 *
 * @param options: Configuration to provide to Nosecone
 * @returns Next.js middleware that sets secure headers
 */ function createMiddleware(options = defaults) {
    return async ()=>{
        const headers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$nosecone$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f$nosecone$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])(options);
        // Setting this specific header is the way that Next.js implements
        // middleware. See:
        // https://github.com/vercel/next.js/blob/5c45d58cd058a9683e435fd3a1a9b8fede8376c3/packages/next/src/server/web/spec-extension/response.ts#L148
        // Note: we don't create the `x-middleware-override-headers` header so
        // the original headers pass through
        headers.set("x-middleware-next", "1");
        return new Response(null, {
            headers
        });
    };
}
;
}}),
"[project]/node_modules/.pnpm/@nosecone+next@1.0.0-beta.7_c27ae1b5d47543c9370e9c7fd08ab71d/node_modules/@nosecone/next/index.js [middleware-edge] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$nosecone$40$1$2e$0$2e$0$2d$beta$2e$7$2f$node_modules$2f$nosecone$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/nosecone@1.0.0-beta.7/node_modules/nosecone/index.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$nosecone$2b$next$40$1$2e$0$2e$0$2d$beta$2e$7_c27ae1b5d47543c9370e9c7fd08ab71d$2f$node_modules$2f40$nosecone$2f$next$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@nosecone+next@1.0.0-beta.7_c27ae1b5d47543c9370e9c7fd08ab71d/node_modules/@nosecone/next/index.js [middleware-edge] (ecmascript) <locals>");
}}),
"[project]/node_modules/.pnpm/@nosecone+next@1.0.0-beta.7_c27ae1b5d47543c9370e9c7fd08ab71d/node_modules/@nosecone/next/index.js [middleware-edge] (ecmascript) <locals> <export createMiddleware as noseconeMiddleware>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "noseconeMiddleware": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$nosecone$2b$next$40$1$2e$0$2e$0$2d$beta$2e$7_c27ae1b5d47543c9370e9c7fd08ab71d$2f$node_modules$2f40$nosecone$2f$next$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createMiddleware"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$nosecone$2b$next$40$1$2e$0$2e$0$2d$beta$2e$7_c27ae1b5d47543c9370e9c7fd08ab71d$2f$node_modules$2f40$nosecone$2f$next$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@nosecone+next@1.0.0-beta.7_c27ae1b5d47543c9370e9c7fd08ab71d/node_modules/@nosecone/next/index.js [middleware-edge] (ecmascript) <locals>");
}}),
}]);

//# sourceMappingURL=node_modules__pnpm_2d5523b0._.js.map