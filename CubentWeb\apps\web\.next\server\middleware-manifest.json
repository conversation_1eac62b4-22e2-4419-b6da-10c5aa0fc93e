{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/9583c_@sentry_core_build_esm_1adfbc63._.js", "server/edge/chunks/4b803_@sentry_vercel-edge_build_esm_index_de3a4916.js", "server/edge/chunks/ec4b9_zod_dist_esm_d6b871ea._.js", "server/edge/chunks/node_modules__pnpm_f5a78348._.js", "server/edge/chunks/[root-of-the-server]__a0a0371e._.js", "server/edge/chunks/apps_web_edge-wrapper_88d0945e.js", "server/edge/chunks/_c8462c85._.js", "server/edge/chunks/ec4b9_zod_dist_esm_cbcb71bd._.js", "server/edge/chunks/eec21_@clerk_shared_dist_40b2e982._.js", "server/edge/chunks/c67f4_@clerk_backend_dist_d8cc056d._.js", "server/edge/chunks/25c57_@clerk_nextjs_dist_esm_1ca17405._.js", "server/edge/chunks/node_modules__pnpm_2d5523b0._.js", "server/edge/chunks/[root-of-the-server]__b5fdeec6._.js", "server/edge/chunks/apps_web_edge-wrapper_ada886b9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|images|ingest|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|images|ingest|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "9uQifFpW3FDh5bZlRviSaCJshVr+8Qe5JU48C94JGDQ=", "__NEXT_PREVIEW_MODE_ID": "a75c3fb8e3f6d77b991ced82a7ab9f86", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "51b11ef92644936c647e1099d41da2d7fdce2329427bbc64b0cb5cb640c8ff3d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a121eff285665d9604df8ea1a267371411fce6304fbdd21dd996b11eab258fe9"}}}, "sortedMiddleware": ["/"], "functions": {}}