const CHUNK_PUBLIC_PATH = "server/instrumentation.js";
const runtime = require("./chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/[externals]_node:inspector_3e2db5b8._.js");
runtime.loadChunk("server/chunks/9583c_@sentry_core_build_cjs_7848c8fe._.js");
runtime.loadChunk("server/chunks/d96dc_@sentry_node_build_cjs_f5dba944._.js");
runtime.loadChunk("server/chunks/d486d_@opentelemetry_core_build_esm_a458a933._.js");
runtime.loadChunk("server/chunks/ffc21_@opentelemetry_semantic-conventions_build_esm_326d840b._.js");
runtime.loadChunk("server/chunks/bfb6c_@opentelemetry_semantic-conventions_build_esm_8fd92142._.js");
runtime.loadChunk("server/chunks/8b446_@opentelemetry_sdk-trace-base_build_esm_b355421b._.js");
runtime.loadChunk("server/chunks/46f7a_@opentelemetry_resources_build_esm_a7ee80be._.js");
runtime.loadChunk("server/chunks/b52d1_@sentry_nextjs_build_cjs_fae341ab._.js");
runtime.loadChunk("server/chunks/ec4b9_zod_dist_esm_b0d22a03._.js");
runtime.loadChunk("server/chunks/node_modules__pnpm_1f3d2a2e._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__b3308b75._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/api/instrumentation.ts [instrumentation] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/apps/api/instrumentation.ts [instrumentation] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
