module.exports = {

"[project]/node_modules/.pnpm/@arcjet+analyze-wasm@1.0.0-beta.7/node_modules/@arcjet/analyze-wasm/_virtual/arcjet_analyze_js_req.component.core2.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// @generated by wasm2module - DO NOT EDIT
/* eslint-disable */ // @ts-nocheck
/**
 * This file contains an Arcjet Wasm binary inlined as a base64
 * [Data URL](https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/Data_URLs)
 * with the application/wasm MIME type.
 *
 * This was chosen to save on storage space over inlining the file directly as
 * a Uint8Array, which would take up ~3x the space of the Wasm file. See
 * https://blobfolio.com/2019/better-binary-batter-mixing-base64-and-uint8array/
 * for more details.
 *
 * It is then decoded into an ArrayBuffer to be used directly via WebAssembly's
 * `compile()` function in our entry point file.
 *
 * This is all done to avoid trying to read or bundle the Wasm asset in various
 * ways based on the platform or bundler a user is targeting. One example being
 * that Next.js requires special `asyncWebAssembly` webpack config to load our
 * Wasm file if we don't do this.
 *
 * In the future, we hope to do away with this workaround when all bundlers
 * properly support consistent asset bundling techniques.
 */ __turbopack_context__.s({
    "wasm": (()=>wasm)
});
const wasmBase64 = "data:application/wasm;base64,AGFzbQEAAAABFQNgA39/fwBgBH9/f38Bf2ACf38BfwMIBwABAgICAgAEBQFwAQcHBygIATAAAAExAAEBMgACATMAAwE0AAQBNQAFATYABggkaW1wb3J0cwEACl0HDQAgACABIAJBABEAAAsPACAAIAEgAiADQQERAQALCwAgACABQQIRAgALCwAgACABQQMRAgALCwAgACABQQQRAgALCwAgACABQQURAgALDQAgACABIAJBBhEAAAsALwlwcm9kdWNlcnMBDHByb2Nlc3NlZC1ieQENd2l0LWNvbXBvbmVudAcwLjIyNi4wALwDBG5hbWUAExJ3aXQtY29tcG9uZW50OnNoaW0BnwMHACxpbmRpcmVjdC1hcmNqZXQ6anMtcmVxL2JvdC1pZGVudGlmaWVyLWRldGVjdAEoaW5kaXJlY3QtYXJjamV0OmpzLXJlcS92ZXJpZnktYm90LXZlcmlmeQI+aW5kaXJlY3QtYXJjamV0OmpzLXJlcS9lbWFpbC12YWxpZGF0b3Itb3ZlcnJpZGVzLWlzLWZyZWUtZW1haWwDRGluZGlyZWN0LWFyY2pldDpqcy1yZXEvZW1haWwtdmFsaWRhdG9yLW92ZXJyaWRlcy1pcy1kaXNwb3NhYmxlLWVtYWlsBD9pbmRpcmVjdC1hcmNqZXQ6anMtcmVxL2VtYWlsLXZhbGlkYXRvci1vdmVycmlkZXMtaGFzLW14LXJlY29yZHMFPWluZGlyZWN0LWFyY2pldDpqcy1yZXEvZW1haWwtdmFsaWRhdG9yLW92ZXJyaWRlcy1oYXMtZ3JhdmF0YXIGPmluZGlyZWN0LWFyY2pldDpqcy1yZXEvc2Vuc2l0aXZlLWluZm9ybWF0aW9uLWlkZW50aWZpZXItZGV0ZWN0";
/**
 * Returns a WebAssembly.Module for an Arcjet Wasm binary, decoded from a base64
 * Data URL.
 */ // TODO: Switch back to top-level await when our platforms all support it
async function wasm() {
    // This uses fetch to decode the wasm data url, but disabling cache so files
    // larger than 2mb don't fail to parse in the Next.js App Router
    const wasmDecode = await fetch(wasmBase64, {
        cache: "no-store"
    });
    const buf = await wasmDecode.arrayBuffer();
    // And then we return it as a WebAssembly.Module
    return WebAssembly.compile(buf);
}
;
}}),
"[project]/node_modules/.pnpm/@arcjet+analyze-wasm@1.0.0-beta.7/node_modules/@arcjet/analyze-wasm/_virtual/arcjet_analyze_js_req.component.core3.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// @generated by wasm2module - DO NOT EDIT
/* eslint-disable */ // @ts-nocheck
/**
 * This file contains an Arcjet Wasm binary inlined as a base64
 * [Data URL](https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/Data_URLs)
 * with the application/wasm MIME type.
 *
 * This was chosen to save on storage space over inlining the file directly as
 * a Uint8Array, which would take up ~3x the space of the Wasm file. See
 * https://blobfolio.com/2019/better-binary-batter-mixing-base64-and-uint8array/
 * for more details.
 *
 * It is then decoded into an ArrayBuffer to be used directly via WebAssembly's
 * `compile()` function in our entry point file.
 *
 * This is all done to avoid trying to read or bundle the Wasm asset in various
 * ways based on the platform or bundler a user is targeting. One example being
 * that Next.js requires special `asyncWebAssembly` webpack config to load our
 * Wasm file if we don't do this.
 *
 * In the future, we hope to do away with this workaround when all bundlers
 * properly support consistent asset bundling techniques.
 */ __turbopack_context__.s({
    "wasm": (()=>wasm)
});
const wasmBase64 = "data:application/wasm;base64,AGFzbQEAAAABFQNgA39/fwBgBH9/f38Bf2ACf38BfwIzCAABMAAAAAExAAEAATIAAgABMwACAAE0AAIAATUAAgABNgAAAAgkaW1wb3J0cwFwAQcHCQ0BAEEACwcAAQIDBAUGAC8JcHJvZHVjZXJzAQxwcm9jZXNzZWQtYnkBDXdpdC1jb21wb25lbnQHMC4yMjYuMAAcBG5hbWUAFRR3aXQtY29tcG9uZW50OmZpeHVwcw==";
/**
 * Returns a WebAssembly.Module for an Arcjet Wasm binary, decoded from a base64
 * Data URL.
 */ // TODO: Switch back to top-level await when our platforms all support it
async function wasm() {
    // This uses fetch to decode the wasm data url, but disabling cache so files
    // larger than 2mb don't fail to parse in the Next.js App Router
    const wasmDecode = await fetch(wasmBase64, {
        cache: "no-store"
    });
    const buf = await wasmDecode.arrayBuffer();
    // And then we return it as a WebAssembly.Module
    return WebAssembly.compile(buf);
}
;
}}),

};

//# sourceMappingURL=7cda6_%40arcjet_analyze-wasm__virtual_5b0cf177._.js.map