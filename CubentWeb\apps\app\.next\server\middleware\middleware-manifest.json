{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_ec43db50._.js", "server/edge/chunks/eec21_@clerk_shared_dist_40b2e982._.js", "server/edge/chunks/c67f4_@clerk_backend_dist_d8cc056d._.js", "server/edge/chunks/25c57_@clerk_nextjs_dist_esm_1ca17405._.js", "server/edge/chunks/ec4b9_zod_dist_esm_cbcb71bd._.js", "server/edge/chunks/node_modules__pnpm_16b65189._.js", "server/edge/chunks/[root-of-the-server]__6292175d._.js", "server/edge/chunks/apps_app_edge-wrapper_0144a8dc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*){(\\\\.json)}?", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(api|trpc)(.*){(\\\\.json)}?", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "W2GDmhqWAEvlNHlwGITKkVIGi0ZjBphzOJ0dTpjoqGM=", "__NEXT_PREVIEW_MODE_ID": "47db098cae4870c6213b62aeb5751a47", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "37bf5184352f78f55af0e4667c419e711fd65e0c283e354bde7f02720f8ba507", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1894b15f2ba20fbd18c35eb9cb87d1f13275c9f444beae45bfe503170465577e"}}}, "instrumentation": null, "functions": {}}