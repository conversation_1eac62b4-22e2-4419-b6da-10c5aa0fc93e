{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40connectrpc%2Bconnect-node%401._90f7088c2e6395363396c6520b160400/node_modules/%40connectrpc/connect-node/dist/esm/node-headers-polyfill.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { Headers as HeadersPolyfill } from \"undici\";\n// The global Headers class was introduced in Node v16.15.0, behind the\n// --experimental-fetch flag. It became available by default with Node\n// v18.0.0.\n// If this code runs in Node < 18, it installs an alternative\n// implementation if one has not already been polyfilled.\nconst [major] = process.versions.node\n    .split(\".\")\n    .map((value) => parseInt(value, 10));\nif (major < 18) {\n    if (typeof globalThis.Headers === \"undefined\") {\n        globalThis.Headers = HeadersPolyfill;\n    }\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;AACjC;;AACA,uEAAuE;AACvE,sEAAsE;AACtE,WAAW;AACX,6DAA6D;AAC7D,yDAAyD;AACzD,MAAM,CAAC,MAAM,GAAG,QAAQ,QAAQ,CAAC,IAAI,CAChC,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,QAAU,SAAS,OAAO;AACpC,IAAI,QAAQ,IAAI;IACZ,IAAI,OAAO,WAAW,OAAO,KAAK,aAAa;QAC3C,WAAW,OAAO,GAAG,mLAAA,CAAA,UAAe;IACxC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40connectrpc%2Bconnect-node%401._90f7088c2e6395363396c6520b160400/node_modules/%40connectrpc/connect-node/dist/esm/node-error.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { Code, ConnectError } from \"@connectrpc/connect\";\n/**\n * Similar to ConnectError.from(), this function turns any value into\n * a ConnectError, but special cases some Node.js specific error codes and\n * sets an appropriate Connect error code.\n */\nexport function connectErrorFromNodeReason(reason) {\n    let code = Code.Internal;\n    const chain = unwrapNodeErrorChain(reason).map(getNodeErrorProps);\n    if (chain.some((p) => p.code == \"ERR_STREAM_WRITE_AFTER_END\")) {\n        // We do not want intentional errors from the server to be shadowed\n        // by client-side errors.\n        // This can occur if the server has written a response with an error\n        // and has ended the connection. This response may already sit in a\n        // buffer on the client, while it is still writing to the request\n        // body.\n        // To avoid this problem, we wrap ERR_STREAM_WRITE_AFTER_END as a\n        // ConnectError with Code.Aborted. The special meaning of this code\n        // in this situation is documented in StreamingConn.send() and in\n        // createServerStreamingFn().\n        code = Code.Aborted;\n    }\n    else if (chain.some((p) => p.code == \"ERR_STREAM_DESTROYED\" ||\n        p.code == \"ERR_HTTP2_INVALID_STREAM\" ||\n        p.code == \"ECONNRESET\")) {\n        // A handler whose stream is suddenly destroyed usually means the client\n        // hung up. This behavior is triggered by the conformance test \"cancel_after_begin\".\n        code = Code.Aborted;\n    }\n    else if (chain.some((p) => p.code == \"ETIMEDOUT\" ||\n        p.code == \"ENOTFOUND\" ||\n        p.code == \"EAI_AGAIN\" ||\n        p.code == \"ECONNREFUSED\")) {\n        // Calling an unresolvable host should raise a ConnectError with\n        // Code.Aborted.\n        // This behavior is covered by the conformance test \"unresolvable_host\".\n        code = Code.Unavailable;\n    }\n    const ce = ConnectError.from(reason, code);\n    if (ce !== reason) {\n        ce.cause = reason;\n    }\n    return ce;\n}\n/**\n * Unwraps a chain of errors, by walking through all \"cause\" properties\n * recursively.\n * This function is useful to find the root cause of an error.\n */\nexport function unwrapNodeErrorChain(reason) {\n    const chain = [];\n    for (;;) {\n        if (!(reason instanceof Error)) {\n            break;\n        }\n        if (chain.includes(reason)) {\n            // safeguard against infinite loop when \"cause\" points to an ancestor\n            break;\n        }\n        chain.push(reason);\n        if (!(\"cause\" in reason)) {\n            break;\n        }\n        reason = reason.cause;\n    }\n    return chain;\n}\n/**\n * Returns standard Node.js error properties from the given reason, if present.\n *\n * For context: Every error raised by Node.js APIs should expose a `code`\n * property - a string that permanently identifies the error. Some errors may\n * have an additional `syscall` property - a string that identifies native\n * components, for example \"getaddrinfo\" of libuv.\n * For more information, see https://github.com/nodejs/node/blob/f6052c68c1f9a4400a723e9c0b79da14197ab754/lib/internal/errors.js\n */\nexport function getNodeErrorProps(reason) {\n    const props = {};\n    if (reason instanceof Error) {\n        if (\"code\" in reason && typeof reason.code == \"string\") {\n            props.code = reason.code;\n        }\n        if (\"syscall\" in reason && typeof reason.syscall == \"string\") {\n            props.syscall = reason.syscall;\n        }\n    }\n    return props;\n}\n/**\n * Returns a ConnectError for an HTTP/2 error code.\n */\nexport function connectErrorFromH2ResetCode(rstCode) {\n    switch (rstCode) {\n        case H2Code.PROTOCOL_ERROR:\n        case H2Code.INTERNAL_ERROR:\n        case H2Code.FLOW_CONTROL_ERROR:\n        case H2Code.SETTINGS_TIMEOUT:\n        case H2Code.FRAME_SIZE_ERROR:\n        case H2Code.COMPRESSION_ERROR:\n        case H2Code.CONNECT_ERROR:\n            return new ConnectError(`http/2 stream closed with error code ${H2Code[rstCode]} (0x${rstCode.toString(16)})`, Code.Internal);\n        case H2Code.REFUSED_STREAM:\n            return new ConnectError(`http/2 stream closed with error code ${H2Code[rstCode]} (0x${rstCode.toString(16)})`, Code.Unavailable);\n        case H2Code.CANCEL:\n            return new ConnectError(`http/2 stream closed with error code ${H2Code[rstCode]} (0x${rstCode.toString(16)})`, Code.Canceled);\n        case H2Code.ENHANCE_YOUR_CALM:\n            return new ConnectError(`http/2 stream closed with error code ${H2Code[rstCode]} (0x${rstCode.toString(16)})`, Code.ResourceExhausted);\n        case H2Code.INADEQUATE_SECURITY:\n            return new ConnectError(`http/2 stream closed with error code ${H2Code[rstCode]} (0x${rstCode.toString(16)})`, Code.PermissionDenied);\n        case H2Code.HTTP_1_1_REQUIRED:\n            return new ConnectError(`http/2 stream closed with error code ${H2Code[rstCode]} (0x${rstCode.toString(16)})`, Code.PermissionDenied);\n        case H2Code.STREAM_CLOSED:\n        default:\n            // Intentionally not mapping STREAM_CLOSED (0x5), see https://github.com/grpc/grpc/blob/master/doc/PROTOCOL-HTTP2.md#errors\n            break;\n    }\n    return undefined;\n}\nexport var H2Code;\n(function (H2Code) {\n    H2Code[H2Code[\"PROTOCOL_ERROR\"] = 1] = \"PROTOCOL_ERROR\";\n    H2Code[H2Code[\"INTERNAL_ERROR\"] = 2] = \"INTERNAL_ERROR\";\n    H2Code[H2Code[\"FLOW_CONTROL_ERROR\"] = 3] = \"FLOW_CONTROL_ERROR\";\n    H2Code[H2Code[\"SETTINGS_TIMEOUT\"] = 4] = \"SETTINGS_TIMEOUT\";\n    H2Code[H2Code[\"STREAM_CLOSED\"] = 5] = \"STREAM_CLOSED\";\n    H2Code[H2Code[\"FRAME_SIZE_ERROR\"] = 6] = \"FRAME_SIZE_ERROR\";\n    H2Code[H2Code[\"REFUSED_STREAM\"] = 7] = \"REFUSED_STREAM\";\n    H2Code[H2Code[\"CANCEL\"] = 8] = \"CANCEL\";\n    H2Code[H2Code[\"COMPRESSION_ERROR\"] = 9] = \"COMPRESSION_ERROR\";\n    H2Code[H2Code[\"CONNECT_ERROR\"] = 10] = \"CONNECT_ERROR\";\n    H2Code[H2Code[\"ENHANCE_YOUR_CALM\"] = 11] = \"ENHANCE_YOUR_CALM\";\n    H2Code[H2Code[\"INADEQUATE_SECURITY\"] = 12] = \"INADEQUATE_SECURITY\";\n    H2Code[H2Code[\"HTTP_1_1_REQUIRED\"] = 13] = \"HTTP_1_1_REQUIRED\";\n})(H2Code || (H2Code = {}));\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;;;AACjC;AAAA;;AAMO,SAAS,2BAA2B,MAAM;IAC7C,IAAI,OAAO,2QAAA,CAAA,OAAI,CAAC,QAAQ;IACxB,MAAM,QAAQ,qBAAqB,QAAQ,GAAG,CAAC;IAC/C,IAAI,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,IAAI,+BAA+B;QAC3D,mEAAmE;QACnE,yBAAyB;QACzB,oEAAoE;QACpE,mEAAmE;QACnE,iEAAiE;QACjE,QAAQ;QACR,iEAAiE;QACjE,mEAAmE;QACnE,iEAAiE;QACjE,6BAA6B;QAC7B,OAAO,2QAAA,CAAA,OAAI,CAAC,OAAO;IACvB,OACK,IAAI,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,IAAI,0BACjC,EAAE,IAAI,IAAI,8BACV,EAAE,IAAI,IAAI,eAAe;QACzB,wEAAwE;QACxE,oFAAoF;QACpF,OAAO,2QAAA,CAAA,OAAI,CAAC,OAAO;IACvB,OACK,IAAI,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,IAAI,eACjC,EAAE,IAAI,IAAI,eACV,EAAE,IAAI,IAAI,eACV,EAAE,IAAI,IAAI,iBAAiB;QAC3B,gEAAgE;QAChE,gBAAgB;QAChB,wEAAwE;QACxE,OAAO,2QAAA,CAAA,OAAI,CAAC,WAAW;IAC3B;IACA,MAAM,KAAK,uRAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;IACrC,IAAI,OAAO,QAAQ;QACf,GAAG,KAAK,GAAG;IACf;IACA,OAAO;AACX;AAMO,SAAS,qBAAqB,MAAM;IACvC,MAAM,QAAQ,EAAE;IAChB,OAAS;QACL,IAAI,CAAC,CAAC,kBAAkB,KAAK,GAAG;YAC5B;QACJ;QACA,IAAI,MAAM,QAAQ,CAAC,SAAS;YAExB;QACJ;QACA,MAAM,IAAI,CAAC;QACX,IAAI,CAAC,CAAC,WAAW,MAAM,GAAG;YACtB;QACJ;QACA,SAAS,OAAO,KAAK;IACzB;IACA,OAAO;AACX;AAUO,SAAS,kBAAkB,MAAM;IACpC,MAAM,QAAQ,CAAC;IACf,IAAI,kBAAkB,OAAO;QACzB,IAAI,UAAU,UAAU,OAAO,OAAO,IAAI,IAAI,UAAU;YACpD,MAAM,IAAI,GAAG,OAAO,IAAI;QAC5B;QACA,IAAI,aAAa,UAAU,OAAO,OAAO,OAAO,IAAI,UAAU;YAC1D,MAAM,OAAO,GAAG,OAAO,OAAO;QAClC;IACJ;IACA,OAAO;AACX;AAIO,SAAS,4BAA4B,OAAO;IAC/C,OAAQ;QACJ,KAAK,OAAO,cAAc;QAC1B,KAAK,OAAO,cAAc;QAC1B,KAAK,OAAO,kBAAkB;QAC9B,KAAK,OAAO,gBAAgB;QAC5B,KAAK,OAAO,gBAAgB;QAC5B,KAAK,OAAO,iBAAiB;QAC7B,KAAK,OAAO,aAAa;YACrB,OAAO,IAAI,uRAAA,CAAA,eAAY,CAAC,CAAC,qCAAqC,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,2QAAA,CAAA,OAAI,CAAC,QAAQ;QAChI,KAAK,OAAO,cAAc;YACtB,OAAO,IAAI,uRAAA,CAAA,eAAY,CAAC,CAAC,qCAAqC,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,2QAAA,CAAA,OAAI,CAAC,WAAW;QACnI,KAAK,OAAO,MAAM;YACd,OAAO,IAAI,uRAAA,CAAA,eAAY,CAAC,CAAC,qCAAqC,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,2QAAA,CAAA,OAAI,CAAC,QAAQ;QAChI,KAAK,OAAO,iBAAiB;YACzB,OAAO,IAAI,uRAAA,CAAA,eAAY,CAAC,CAAC,qCAAqC,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,2QAAA,CAAA,OAAI,CAAC,iBAAiB;QACzI,KAAK,OAAO,mBAAmB;YAC3B,OAAO,IAAI,uRAAA,CAAA,eAAY,CAAC,CAAC,qCAAqC,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,2QAAA,CAAA,OAAI,CAAC,gBAAgB;QACxI,KAAK,OAAO,iBAAiB;YACzB,OAAO,IAAI,uRAAA,CAAA,eAAY,CAAC,CAAC,qCAAqC,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,2QAAA,CAAA,OAAI,CAAC,gBAAgB;QACxI,KAAK,OAAO,aAAa;QACzB;YAEI;IACR;IACA,OAAO;AACX;AACO,IAAI;AACX,CAAC,SAAU,MAAM;IACb,MAAM,CAAC,MAAM,CAAC,iBAAiB,GAAG,EAAE,GAAG;IACvC,MAAM,CAAC,MAAM,CAAC,iBAAiB,GAAG,EAAE,GAAG;IACvC,MAAM,CAAC,MAAM,CAAC,qBAAqB,GAAG,EAAE,GAAG;IAC3C,MAAM,CAAC,MAAM,CAAC,mBAAmB,GAAG,EAAE,GAAG;IACzC,MAAM,CAAC,MAAM,CAAC,gBAAgB,GAAG,EAAE,GAAG;IACtC,MAAM,CAAC,MAAM,CAAC,mBAAmB,GAAG,EAAE,GAAG;IACzC,MAAM,CAAC,MAAM,CAAC,iBAAiB,GAAG,EAAE,GAAG;IACvC,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,EAAE,GAAG;IAC/B,MAAM,CAAC,MAAM,CAAC,oBAAoB,GAAG,EAAE,GAAG;IAC1C,MAAM,CAAC,MAAM,CAAC,gBAAgB,GAAG,GAAG,GAAG;IACvC,MAAM,CAAC,MAAM,CAAC,oBAAoB,GAAG,GAAG,GAAG;IAC3C,MAAM,CAAC,MAAM,CAAC,sBAAsB,GAAG,GAAG,GAAG;IAC7C,MAAM,CAAC,MAAM,CAAC,oBAAoB,GAAG,GAAG,GAAG;AAC/C,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40connectrpc%2Bconnect-node%401._90f7088c2e6395363396c6520b160400/node_modules/%40connectrpc/connect-node/dist/esm/compression.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport * as zlib from \"zlib\";\nimport { promisify } from \"util\";\nimport { Code, ConnectError } from \"@connectrpc/connect\";\nimport { getNodeErrorProps } from \"./node-error.js\";\nconst gzip = promisify(zlib.gzip);\nconst gunzip = promisify(zlib.gunzip);\nconst brotliCompress = promisify(zlib.brotliCompress);\nconst brotliDecompress = promisify(zlib.brotliDecompress);\n/**\n * The gzip compression algorithm, implemented with the Node.js built-in module\n * zlib. This value can be used for the `sendCompression` and `acceptCompression`\n * option of client transports, or for the `acceptCompression` option of server\n * plugins like `fastifyConnectPlugin` from @connectrpc/connect-fastify.\n */\nexport const compressionGzip = {\n    name: \"gzip\",\n    compress(bytes) {\n        return gzip(bytes, {});\n    },\n    decompress(bytes, readMaxBytes) {\n        if (bytes.length == 0) {\n            return Promise.resolve(new Uint8Array(0));\n        }\n        return wrapZLibErrors(gunzip(bytes, {\n            maxOutputLength: readMaxBytes,\n        }), readMaxBytes);\n    },\n};\n/**\n * The brotli compression algorithm, implemented with the Node.js built-in module\n * zlib. This value can be used for the `sendCompression` and `acceptCompression`\n * option of client transports, or for the `acceptCompression` option of server\n * plugins like `fastifyConnectPlugin` from @connectrpc/connect-fastify.\n */\nexport const compressionBrotli = {\n    name: \"br\",\n    compress(bytes) {\n        return brotliCompress(bytes, {});\n    },\n    decompress(bytes, readMaxBytes) {\n        if (bytes.length == 0) {\n            return Promise.resolve(new Uint8Array(0));\n        }\n        return wrapZLibErrors(brotliDecompress(bytes, {\n            maxOutputLength: readMaxBytes,\n        }), readMaxBytes);\n    },\n};\nfunction wrapZLibErrors(promise, readMaxBytes) {\n    return promise.catch((e) => {\n        const props = getNodeErrorProps(e);\n        let code = Code.Internal;\n        let message = \"decompression failed\";\n        // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check\n        switch (props.code) {\n            case \"ERR_BUFFER_TOO_LARGE\":\n                code = Code.ResourceExhausted;\n                message = `message is larger than configured readMaxBytes ${readMaxBytes} after decompression`;\n                break;\n            case \"Z_DATA_ERROR\":\n            case \"ERR_PADDING_2\":\n                code = Code.InvalidArgument;\n                break;\n            default:\n                if (props.code !== undefined &&\n                    props.code.startsWith(\"ERR__ERROR_FORMAT_\")) {\n                    code = Code.InvalidArgument;\n                }\n                break;\n        }\n        return Promise.reject(new ConnectError(message, code, undefined, undefined, e));\n    });\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;AACjC;AACA;AACA;AAAA;AACA;;;;;AACA,MAAM,OAAO,CAAA,GAAA,iGAAA,CAAA,YAAS,AAAD,EAAE,iGAAA,CAAA,OAAS;AAChC,MAAM,SAAS,CAAA,GAAA,iGAAA,CAAA,YAAS,AAAD,EAAE,iGAAA,CAAA,SAAW;AACpC,MAAM,iBAAiB,CAAA,GAAA,iGAAA,CAAA,YAAS,AAAD,EAAE,iGAAA,CAAA,iBAAmB;AACpD,MAAM,mBAAmB,CAAA,GAAA,iGAAA,CAAA,YAAS,AAAD,EAAE,iGAAA,CAAA,mBAAqB;AAOjD,MAAM,kBAAkB;IAC3B,MAAM;IACN,UAAS,KAAK;QACV,OAAO,KAAK,OAAO,CAAC;IACxB;IACA,YAAW,KAAK,EAAE,YAAY;QAC1B,IAAI,MAAM,MAAM,IAAI,GAAG;YACnB,OAAO,QAAQ,OAAO,CAAC,IAAI,WAAW;QAC1C;QACA,OAAO,eAAe,OAAO,OAAO;YAChC,iBAAiB;QACrB,IAAI;IACR;AACJ;AAOO,MAAM,oBAAoB;IAC7B,MAAM;IACN,UAAS,KAAK;QACV,OAAO,eAAe,OAAO,CAAC;IAClC;IACA,YAAW,KAAK,EAAE,YAAY;QAC1B,IAAI,MAAM,MAAM,IAAI,GAAG;YACnB,OAAO,QAAQ,OAAO,CAAC,IAAI,WAAW;QAC1C;QACA,OAAO,eAAe,iBAAiB,OAAO;YAC1C,iBAAiB;QACrB,IAAI;IACR;AACJ;AACA,SAAS,eAAe,OAAO,EAAE,YAAY;IACzC,OAAO,QAAQ,KAAK,CAAC,CAAC;QAClB,MAAM,QAAQ,CAAA,GAAA,sRAAA,CAAA,oBAAiB,AAAD,EAAE;QAChC,IAAI,OAAO,2QAAA,CAAA,OAAI,CAAC,QAAQ;QACxB,IAAI,UAAU;QACd,0EAA0E;QAC1E,OAAQ,MAAM,IAAI;YACd,KAAK;gBACD,OAAO,2QAAA,CAAA,OAAI,CAAC,iBAAiB;gBAC7B,UAAU,CAAC,+CAA+C,EAAE,aAAa,oBAAoB,CAAC;gBAC9F;YACJ,KAAK;YACL,KAAK;gBACD,OAAO,2QAAA,CAAA,OAAI,CAAC,eAAe;gBAC3B;YACJ;gBACI,IAAI,MAAM,IAAI,KAAK,aACf,MAAM,IAAI,CAAC,UAAU,CAAC,uBAAuB;oBAC7C,OAAO,2QAAA,CAAA,OAAI,CAAC,eAAe;gBAC/B;gBACA;QACR;QACA,OAAO,QAAQ,MAAM,CAAC,IAAI,uRAAA,CAAA,eAAY,CAAC,SAAS,MAAM,WAAW,WAAW;IAChF;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40connectrpc%2Bconnect-node%401._90f7088c2e6395363396c6520b160400/node_modules/%40connectrpc/connect-node/dist/esm/node-universal-header.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Convert a Node.js header object to a fetch API Headers object.\n *\n * This function works for Node.js incoming and outgoing headers, and for the\n * http and the http2 package.\n *\n * HTTP/2 pseudo-headers (:method, :path, etc.) are stripped.\n */\nexport function nodeHeaderToWebHeader(nodeHeaders) {\n    const header = new Headers();\n    for (const [k, v] of Object.entries(nodeHeaders)) {\n        if (k.startsWith(\":\")) {\n            continue;\n        }\n        if (v === undefined) {\n            continue;\n        }\n        if (typeof v == \"string\") {\n            header.append(k, v);\n        }\n        else if (typeof v == \"number\") {\n            header.append(k, String(v));\n        }\n        else {\n            for (const e of v) {\n                header.append(k, e);\n            }\n        }\n    }\n    return header;\n}\nexport function webHeaderToNodeHeaders(headersInit, defaultNodeHeaders) {\n    if (headersInit === undefined && defaultNodeHeaders === undefined) {\n        return undefined;\n    }\n    const o = Object.create(null);\n    if (defaultNodeHeaders !== undefined) {\n        for (const [key, value] of Object.entries(defaultNodeHeaders)) {\n            if (Array.isArray(value)) {\n                o[key] = value.concat();\n            }\n            else if (value !== undefined) {\n                o[key] = value;\n            }\n        }\n    }\n    if (headersInit !== undefined) {\n        if (Array.isArray(headersInit)) {\n            for (const [key, value] of headersInit) {\n                appendWebHeader(o, key, value);\n            }\n        }\n        else if (\"forEach\" in headersInit) {\n            if (typeof headersInit.forEach == \"function\") {\n                headersInit.forEach((value, key) => {\n                    appendWebHeader(o, key, value);\n                });\n            }\n        }\n        else {\n            for (const [key, value] of Object.entries(headersInit)) {\n                appendWebHeader(o, key, value);\n            }\n        }\n    }\n    return o;\n}\nfunction appendWebHeader(o, key, value) {\n    key = key.toLowerCase();\n    const existing = o[key];\n    if (Array.isArray(existing)) {\n        existing.push(value);\n    }\n    else if (existing === undefined) {\n        o[key] = value;\n    }\n    else {\n        o[key] = [existing.toString(), value];\n    }\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC;;;;;;;CAOC;;;;AACM,SAAS,sBAAsB,WAAW;IAC7C,MAAM,SAAS,IAAI;IACnB,KAAK,MAAM,CAAC,GAAG,EAAE,IAAI,OAAO,OAAO,CAAC,aAAc;QAC9C,IAAI,EAAE,UAAU,CAAC,MAAM;YACnB;QACJ;QACA,IAAI,MAAM,WAAW;YACjB;QACJ;QACA,IAAI,OAAO,KAAK,UAAU;YACtB,OAAO,MAAM,CAAC,GAAG;QACrB,OACK,IAAI,OAAO,KAAK,UAAU;YAC3B,OAAO,MAAM,CAAC,GAAG,OAAO;QAC5B,OACK;YACD,KAAK,MAAM,KAAK,EAAG;gBACf,OAAO,MAAM,CAAC,GAAG;YACrB;QACJ;IACJ;IACA,OAAO;AACX;AACO,SAAS,uBAAuB,WAAW,EAAE,kBAAkB;IAClE,IAAI,gBAAgB,aAAa,uBAAuB,WAAW;QAC/D,OAAO;IACX;IACA,MAAM,IAAI,OAAO,MAAM,CAAC;IACxB,IAAI,uBAAuB,WAAW;QAClC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,oBAAqB;YAC3D,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACtB,CAAC,CAAC,IAAI,GAAG,MAAM,MAAM;YACzB,OACK,IAAI,UAAU,WAAW;gBAC1B,CAAC,CAAC,IAAI,GAAG;YACb;QACJ;IACJ;IACA,IAAI,gBAAgB,WAAW;QAC3B,IAAI,MAAM,OAAO,CAAC,cAAc;YAC5B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,YAAa;gBACpC,gBAAgB,GAAG,KAAK;YAC5B;QACJ,OACK,IAAI,aAAa,aAAa;YAC/B,IAAI,OAAO,YAAY,OAAO,IAAI,YAAY;gBAC1C,YAAY,OAAO,CAAC,CAAC,OAAO;oBACxB,gBAAgB,GAAG,KAAK;gBAC5B;YACJ;QACJ,OACK;YACD,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,aAAc;gBACpD,gBAAgB,GAAG,KAAK;YAC5B;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,gBAAgB,CAAC,EAAE,GAAG,EAAE,KAAK;IAClC,MAAM,IAAI,WAAW;IACrB,MAAM,WAAW,CAAC,CAAC,IAAI;IACvB,IAAI,MAAM,OAAO,CAAC,WAAW;QACzB,SAAS,IAAI,CAAC;IAClB,OACK,IAAI,aAAa,WAAW;QAC7B,CAAC,CAAC,IAAI,GAAG;IACb,OACK;QACD,CAAC,CAAC,IAAI,GAAG;YAAC,SAAS,QAAQ;YAAI;SAAM;IACzC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40connectrpc%2Bconnect-node%401._90f7088c2e6395363396c6520b160400/node_modules/%40connectrpc/connect-node/dist/esm/http2-session-manager.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport * as http2 from \"http2\";\nimport { Code, ConnectError } from \"@connectrpc/connect\";\nimport { connectErrorFromNodeReason } from \"./node-error.js\";\n/**\n * Manage an HTTP/2 connection and keep it alive with PING frames.\n *\n * The logic is based on \"Basic Keepalive\" described in\n * https://github.com/grpc/proposal/blob/0ba0c1905050525f9b0aee46f3f23c8e1e515489/A8-client-side-keepalive.md#basic-keepalive\n * as well as the client channel arguments described in\n * https://github.com/grpc/grpc/blob/8e137e524a1b1da7bbf4603662876d5719563b57/doc/keepalive.md\n *\n * Usually, the managers tracks exactly one connection, but if a connection\n * receives a GOAWAY frame with NO_ERROR, the connection is maintained until\n * all streams have finished, and new requests will open a new connection.\n */\nexport class Http2SessionManager {\n    /**\n     * The current state of the connection:\n     *\n     * - \"closed\"\n     *   The connection is closed, or no connection has been opened yet.\n     * - connecting\n     *   Currently establishing a connection.\n     *\n     * - \"open\"\n     *   A connection is open and has open streams. PING frames are sent every\n     *   pingIntervalMs, unless a stream received data.\n     *   If a PING frame is not responded to within pingTimeoutMs, the connection\n     *   and all open streams close.\n     *\n     * - \"idle\"\n     *   A connection is open, but it does not have any open streams.\n     *   If pingIdleConnection is enabled, PING frames are used to keep the\n     *   connection alive, similar to an \"open\" connection.\n     *   If a connection is idle for longer than idleConnectionTimeoutMs, it closes.\n     *   If a request is made on an idle connection that has not been used for\n     *   longer than pingIntervalMs, the connection is verified.\n     *\n     * - \"verifying\"\n     *   Verifying a connection after a long period of inactivity before issuing a\n     *   request. A PING frame is sent, and if it times out within pingTimeoutMs, a\n     *   new connection is opened.\n     *\n     * - \"error\"\n     *   The connection is closed because of a transient error. A connection\n     *   may have failed to reach the host, or the connection may have died,\n     *   or it may have been aborted.\n     */\n    state() {\n        if (this.s.t == \"ready\") {\n            if (this.verifying !== undefined) {\n                return \"verifying\";\n            }\n            return this.s.streamCount() > 0 ? \"open\" : \"idle\";\n        }\n        return this.s.t;\n    }\n    /**\n     * Returns the error object if the connection is in the \"error\" state,\n     * `undefined` otherwise.\n     */\n    error() {\n        if (this.s.t == \"error\") {\n            return this.s.reason;\n        }\n        return undefined;\n    }\n    constructor(url, pingOptions, http2SessionOptions) {\n        var _a, _b, _c, _d;\n        this.s = closed();\n        this.shuttingDown = [];\n        this.authority = new URL(url).origin;\n        this.http2SessionOptions = http2SessionOptions;\n        this.options = {\n            pingIntervalMs: (_a = pingOptions === null || pingOptions === void 0 ? void 0 : pingOptions.pingIntervalMs) !== null && _a !== void 0 ? _a : Number.POSITIVE_INFINITY,\n            pingTimeoutMs: (_b = pingOptions === null || pingOptions === void 0 ? void 0 : pingOptions.pingTimeoutMs) !== null && _b !== void 0 ? _b : 1000 * 15,\n            pingIdleConnection: (_c = pingOptions === null || pingOptions === void 0 ? void 0 : pingOptions.pingIdleConnection) !== null && _c !== void 0 ? _c : false,\n            idleConnectionTimeoutMs: (_d = pingOptions === null || pingOptions === void 0 ? void 0 : pingOptions.idleConnectionTimeoutMs) !== null && _d !== void 0 ? _d : 1000 * 60 * 15,\n        };\n    }\n    /**\n     * Open a connection if none exists, verify an existing connection if\n     * necessary.\n     */\n    async connect() {\n        try {\n            const ready = await this.gotoReady();\n            return ready.streamCount() > 0 ? \"open\" : \"idle\";\n        }\n        catch (e) {\n            return \"error\";\n        }\n    }\n    /**\n     * Issue a request.\n     *\n     * This method automatically opens a connection if none exists, and verifies\n     * an existing connection if necessary. It calls http2.ClientHttp2Session.request(),\n     * and keeps track of all open http2.ClientHttp2Stream.\n     *\n     * Clients must call notifyResponseByteRead() whenever they successfully read\n     * data from the http2.ClientHttp2Stream.\n     */\n    async request(method, path, headers, options) {\n        // Request sometimes fails with goaway/destroyed\n        // errors, we use a loop to retry.\n        //\n        // This is not expected to happen often, but it is possible that a\n        // connection is closed while we are trying to open a stream.\n        //\n        // Ref: https://github.com/nodejs/help/issues/2105\n        for (;;) {\n            const ready = await this.gotoReady();\n            try {\n                const stream = ready.conn.request(Object.assign(Object.assign({}, headers), { \":method\": method, \":path\": path }), options);\n                ready.registerRequest(stream);\n                return stream;\n            }\n            catch (e) {\n                // Check to see if the connection is closed or destroyed\n                // and if so, we try again.\n                if (ready.conn.closed || ready.conn.destroyed) {\n                    continue;\n                }\n                throw e;\n            }\n        }\n    }\n    /**\n     * Notify the manager of a successful read from a http2.ClientHttp2Stream.\n     *\n     * Clients must call this function whenever they successfully read data from\n     * a http2.ClientHttp2Stream obtained from request(). This informs the\n     * keep-alive logic that the connection is alive, and prevents it from sending\n     * unnecessary PING frames.\n     */\n    notifyResponseByteRead(stream) {\n        if (this.s.t == \"ready\") {\n            this.s.responseByteRead(stream);\n        }\n        for (const s of this.shuttingDown) {\n            s.responseByteRead(stream);\n        }\n    }\n    /**\n     * If there is an open connection, close it. This also closes any open streams.\n     */\n    abort(reason) {\n        var _a, _b, _c;\n        const err = reason !== null && reason !== void 0 ? reason : new ConnectError(\"connection aborted\", Code.Canceled);\n        (_b = (_a = this.s).abort) === null || _b === void 0 ? void 0 : _b.call(_a, err);\n        for (const s of this.shuttingDown) {\n            (_c = s.abort) === null || _c === void 0 ? void 0 : _c.call(s, err);\n        }\n        this.setState(closedOrError(err));\n    }\n    async gotoReady() {\n        if (this.s.t == \"ready\") {\n            if (this.s.isShuttingDown() ||\n                this.s.conn.closed ||\n                this.s.conn.destroyed) {\n                this.setState(connect(this.authority, this.http2SessionOptions));\n            }\n            else if (this.s.requiresVerify()) {\n                await this.verify(this.s);\n            }\n        }\n        else if (this.s.t == \"closed\" || this.s.t == \"error\") {\n            this.setState(connect(this.authority, this.http2SessionOptions));\n        }\n        while (this.s.t !== \"ready\") {\n            if (this.s.t === \"error\") {\n                throw this.s.reason;\n            }\n            if (this.s.t === \"connecting\") {\n                await this.s.conn;\n            }\n        }\n        return this.s;\n    }\n    setState(state) {\n        var _a, _b;\n        (_b = (_a = this.s).onExitState) === null || _b === void 0 ? void 0 : _b.call(_a);\n        if (this.s.t == \"ready\" && this.s.isShuttingDown()) {\n            // Maintain connections that have been asked to shut down.\n            const sd = this.s;\n            this.shuttingDown.push(sd);\n            sd.onClose = sd.onError = () => {\n                const i = this.shuttingDown.indexOf(sd);\n                if (i !== -1) {\n                    this.shuttingDown.splice(i, 1);\n                }\n            };\n        }\n        switch (state.t) {\n            case \"connecting\":\n                state.conn.then((value) => {\n                    this.setState(ready(value, this.options));\n                }, (reason) => {\n                    this.setState(closedOrError(reason));\n                });\n                break;\n            case \"ready\":\n                state.onClose = () => this.setState(closed());\n                state.onError = (err) => this.setState(closedOrError(err));\n                break;\n            case \"closed\":\n                break;\n            case \"error\":\n                break;\n        }\n        this.s = state;\n    }\n    verify(stateReady) {\n        if (this.verifying !== undefined) {\n            return this.verifying;\n        }\n        this.verifying = stateReady\n            .verify()\n            .then((success) => {\n            if (success) {\n                return;\n            }\n            // verify() has destroyed the old connection\n            this.setState(connect(this.authority, this.http2SessionOptions));\n        }, (reason) => {\n            this.setState(closedOrError(reason));\n        })\n            .finally(() => {\n            this.verifying = undefined;\n        });\n        return this.verifying;\n    }\n}\nfunction closed() {\n    return {\n        t: \"closed\",\n    };\n}\nfunction error(reason) {\n    return {\n        t: \"error\",\n        reason,\n    };\n}\nfunction closedOrError(reason) {\n    const isCancel = reason instanceof ConnectError &&\n        ConnectError.from(reason).code == Code.Canceled;\n    return isCancel ? closed() : error(reason);\n}\nfunction connect(authority, http2SessionOptions) {\n    let resolve;\n    let reject;\n    const conn = new Promise((res, rej) => {\n        resolve = res;\n        reject = rej;\n    });\n    const newConn = http2.connect(authority, http2SessionOptions);\n    newConn.on(\"connect\", onConnect);\n    newConn.on(\"error\", onError);\n    function onConnect() {\n        resolve === null || resolve === void 0 ? void 0 : resolve(newConn);\n        cleanup();\n    }\n    function onError(err) {\n        reject === null || reject === void 0 ? void 0 : reject(connectErrorFromNodeReason(err));\n        cleanup();\n    }\n    function cleanup() {\n        newConn.off(\"connect\", onConnect);\n        newConn.off(\"error\", onError);\n    }\n    return {\n        t: \"connecting\",\n        conn,\n        abort(reason) {\n            if (!newConn.destroyed) {\n                newConn.destroy(undefined, http2.constants.NGHTTP2_CANCEL);\n            }\n            // According to the documentation, destroy() should immediately terminate\n            // the session and the socket, but we still receive a \"connect\" event.\n            // We must not resolve a broken connection, so we reject it manually here.\n            reject === null || reject === void 0 ? void 0 : reject(reason);\n        },\n        onExitState() {\n            cleanup();\n        },\n    };\n}\nfunction ready(conn, options) {\n    // Users have reported an error \"The session has been destroyed\" raised\n    // from H2SessionManager.request(), see https://github.com/connectrpc/connect-es/issues/683\n    // This assertion will show whether the session already died in the\n    // \"connecting\" state.\n    assertSessionOpen(conn);\n    // Do not block Node.js from exiting on an idle connection.\n    // Note that we ref() again for the first stream to open, and unref() again\n    // for the last stream to close.\n    conn.unref();\n    // the last time we were sure that the connection is alive, via a PING\n    // response, or via received response bytes\n    let lastAliveAt = Date.now();\n    // how many streams are currently open on this session\n    let streamCount = 0;\n    // timer for the keep-alive interval\n    let pingIntervalId;\n    // timer for waiting for a PING response\n    let pingTimeoutId;\n    // keep track of GOAWAY - gracefully shut down open streams / wait for connection to error\n    let receivedGoAway = false;\n    // keep track of GOAWAY with ENHANCE_YOUR_CALM and with debug data too_many_pings\n    let receivedGoAwayEnhanceYourCalmTooManyPings = false;\n    // timer for closing connections without open streams, must be initialized\n    let idleTimeoutId;\n    resetIdleTimeout();\n    const state = {\n        t: \"ready\",\n        conn,\n        streamCount() {\n            return streamCount;\n        },\n        requiresVerify() {\n            const elapsedMs = Date.now() - lastAliveAt;\n            return elapsedMs > options.pingIntervalMs;\n        },\n        isShuttingDown() {\n            return receivedGoAway;\n        },\n        onClose: undefined,\n        onError: undefined,\n        registerRequest(stream) {\n            streamCount++;\n            if (streamCount == 1) {\n                conn.ref();\n                resetPingInterval(); // reset to ping with the appropriate interval for \"open\"\n                stopIdleTimeout();\n            }\n            stream.once(\"response\", () => {\n                lastAliveAt = Date.now();\n                resetPingInterval();\n            });\n            stream.once(\"close\", () => {\n                streamCount--;\n                if (streamCount == 0) {\n                    conn.unref();\n                    resetPingInterval(); // reset to ping with the appropriate interval for \"idle\"\n                    resetIdleTimeout();\n                }\n            });\n        },\n        responseByteRead(stream) {\n            if (stream.session !== conn) {\n                return;\n            }\n            if (conn.closed || conn.destroyed) {\n                return;\n            }\n            if (streamCount <= 0) {\n                return;\n            }\n            lastAliveAt = Date.now();\n            resetPingInterval();\n        },\n        verify() {\n            conn.ref();\n            return new Promise((resolve) => {\n                commonPing(() => {\n                    if (streamCount == 0)\n                        conn.unref();\n                    resolve(true);\n                });\n                conn.once(\"error\", () => resolve(false));\n            });\n        },\n        abort(reason) {\n            if (!conn.destroyed) {\n                conn.once(\"error\", () => {\n                    // conn.destroy() may raise an error after onExitState() was called\n                    // and our error listeners are removed.\n                    // We attach this one to swallow uncaught exceptions.\n                });\n                conn.destroy(reason, http2.constants.NGHTTP2_CANCEL);\n            }\n        },\n        onExitState() {\n            if (state.isShuttingDown()) {\n                // Per the interface, this method is called when the manager is leaving\n                // the state. We maintain this connection in the session manager until\n                // all streams have finished, so we do not detach event listeners here.\n                return;\n            }\n            cleanup();\n            this.onError = undefined;\n            this.onClose = undefined;\n        },\n    };\n    // start or restart the ping interval\n    function resetPingInterval() {\n        stopPingInterval();\n        if (streamCount > 0 || options.pingIdleConnection) {\n            pingIntervalId = safeSetTimeout(onPingInterval, options.pingIntervalMs);\n        }\n    }\n    function stopPingInterval() {\n        clearTimeout(pingIntervalId);\n        clearTimeout(pingTimeoutId);\n    }\n    function onPingInterval() {\n        commonPing(resetPingInterval);\n    }\n    function commonPing(onSuccess) {\n        clearTimeout(pingTimeoutId);\n        pingTimeoutId = safeSetTimeout(() => {\n            conn.destroy(new ConnectError(\"PING timed out\", Code.Unavailable), http2.constants.NGHTTP2_CANCEL);\n        }, options.pingTimeoutMs);\n        conn.ping((err, duration) => {\n            clearTimeout(pingTimeoutId);\n            if (err !== null) {\n                // We will receive an ERR_HTTP2_PING_CANCEL here if we destroy the\n                // connection with a pending ping.\n                // We might also see other errors, but they should be picked up by the\n                // \"error\" event listener.\n                return;\n            }\n            if (duration > options.pingTimeoutMs) {\n                // setTimeout is not precise, and HTTP/2 pings take less than 1ms in\n                // tests.\n                conn.destroy(new ConnectError(\"PING timed out\", Code.Unavailable), http2.constants.NGHTTP2_CANCEL);\n                return;\n            }\n            lastAliveAt = Date.now();\n            onSuccess();\n        });\n    }\n    function stopIdleTimeout() {\n        clearTimeout(idleTimeoutId);\n    }\n    function resetIdleTimeout() {\n        idleTimeoutId = safeSetTimeout(onIdleTimeout, options.idleConnectionTimeoutMs);\n    }\n    function onIdleTimeout() {\n        conn.close();\n        onClose(); // trigger a state change right away, so we are not open to races\n    }\n    function onGoaway(errorCode, lastStreamID, opaqueData) {\n        receivedGoAway = true;\n        const tooManyPingsAscii = Buffer.from(\"too_many_pings\", \"ascii\");\n        if (errorCode === http2.constants.NGHTTP2_ENHANCE_YOUR_CALM &&\n            opaqueData != null &&\n            opaqueData.equals(tooManyPingsAscii)) {\n            // double pingIntervalMs, following the last paragraph of https://github.com/grpc/proposal/blob/0ba0c1905050525f9b0aee46f3f23c8e1e515489/A8-client-side-keepalive.md#basic-keepalive\n            options.pingIntervalMs = options.pingIntervalMs * 2;\n            receivedGoAwayEnhanceYourCalmTooManyPings = true;\n        }\n        if (errorCode === http2.constants.NGHTTP2_NO_ERROR) {\n            const nodeMajor = parseInt(process.versions.node.split(\".\")[0], 10);\n            // Node.js v16 closes a connection on its own when it receives a GOAWAY\n            // frame and there are no open streams (emitting a \"close\" event and\n            // destroying the session), but more recent versions do not.\n            // Calling close() ourselves is ineffective here - it appears that the\n            // method is already being called, see https://github.com/nodejs/node/blob/198affc63973805ce5102d246f6b7822be57f5fc/lib/internal/http2/core.js#L681\n            if (streamCount == 0 && nodeMajor >= 18) {\n                conn.destroy(new ConnectError(\"received GOAWAY without any open streams\", Code.Canceled), http2.constants.NGHTTP2_NO_ERROR);\n            }\n        }\n    }\n    function onClose() {\n        var _a;\n        cleanup();\n        (_a = state.onClose) === null || _a === void 0 ? void 0 : _a.call(state);\n    }\n    function onError(err) {\n        var _a, _b;\n        cleanup();\n        if (receivedGoAwayEnhanceYourCalmTooManyPings) {\n            // We cannot prevent node from destroying session and streams with its own\n            // error that does not carry debug data, but at least we can wrap the error\n            // we surface on the manager.\n            const ce = new ConnectError(`http/2 connection closed with error code ENHANCE_YOUR_CALM (0x${http2.constants.NGHTTP2_ENHANCE_YOUR_CALM.toString(16)}), too_many_pings, doubled the interval`, Code.ResourceExhausted);\n            (_a = state.onError) === null || _a === void 0 ? void 0 : _a.call(state, ce);\n        }\n        else {\n            (_b = state.onError) === null || _b === void 0 ? void 0 : _b.call(state, connectErrorFromNodeReason(err));\n        }\n    }\n    function cleanup() {\n        stopPingInterval();\n        stopIdleTimeout();\n        conn.off(\"error\", onError);\n        conn.off(\"close\", onClose);\n        conn.off(\"goaway\", onGoaway);\n    }\n    conn.on(\"error\", onError);\n    conn.on(\"close\", onClose);\n    conn.on(\"goaway\", onGoaway);\n    return state;\n}\n/**\n * setTimeout(), but simply ignores values larger than the maximum supported\n * value (signed 32-bit integer) instead of calling the callback right away,\n * and does not block Node.js from exiting.\n */\nfunction safeSetTimeout(callback, ms) {\n    if (ms > 0x7fffffff) {\n        return;\n    }\n    return setTimeout(callback, ms).unref();\n}\nfunction assertSessionOpen(conn) {\n    if (conn.connecting) {\n        throw new ConnectError(\"expected open session, but it is connecting\", Code.Internal);\n    }\n    if (conn.destroyed) {\n        throw new ConnectError(\"expected open session, but it is destroyed\", Code.Internal);\n    }\n    if (conn.closed) {\n        throw new ConnectError(\"expected open session, but it is closed\", Code.Internal);\n    }\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;AACA;AAAA;AACA;;;;AAaO,MAAM;IACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+BC,GACD,QAAQ;QACJ,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,SAAS;YACrB,IAAI,IAAI,CAAC,SAAS,KAAK,WAAW;gBAC9B,OAAO;YACX;YACA,OAAO,IAAI,CAAC,CAAC,CAAC,WAAW,KAAK,IAAI,SAAS;QAC/C;QACA,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;IACnB;IACA;;;KAGC,GACD,QAAQ;QACJ,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,SAAS;YACrB,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM;QACxB;QACA,OAAO;IACX;IACA,YAAY,GAAG,EAAE,WAAW,EAAE,mBAAmB,CAAE;QAC/C,IAAI,IAAI,IAAI,IAAI;QAChB,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,YAAY,GAAG,EAAE;QACtB,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,KAAK,MAAM;QACpC,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,OAAO,GAAG;YACX,gBAAgB,CAAC,KAAK,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,OAAO,iBAAiB;YACrK,eAAe,CAAC,KAAK,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,aAAa,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,OAAO;YAClJ,oBAAoB,CAAC,KAAK,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,kBAAkB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;YACrJ,yBAAyB,CAAC,KAAK,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,uBAAuB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,OAAO,KAAK;QAC/K;IACJ;IACA;;;KAGC,GACD,MAAM,UAAU;QACZ,IAAI;YACA,MAAM,QAAQ,MAAM,IAAI,CAAC,SAAS;YAClC,OAAO,MAAM,WAAW,KAAK,IAAI,SAAS;QAC9C,EACA,OAAO,GAAG;YACN,OAAO;QACX;IACJ;IACA;;;;;;;;;KASC,GACD,MAAM,QAAQ,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;QAC1C,gDAAgD;QAChD,kCAAkC;QAClC,EAAE;QACF,kEAAkE;QAClE,6DAA6D;QAC7D,EAAE;QACF,kDAAkD;QAClD,OAAS;YACL,MAAM,QAAQ,MAAM,IAAI,CAAC,SAAS;YAClC,IAAI;gBACA,MAAM,SAAS,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU;oBAAE,WAAW;oBAAQ,SAAS;gBAAK,IAAI;gBACnH,MAAM,eAAe,CAAC;gBACtB,OAAO;YACX,EACA,OAAO,GAAG;gBACN,wDAAwD;gBACxD,2BAA2B;gBAC3B,IAAI,MAAM,IAAI,CAAC,MAAM,IAAI,MAAM,IAAI,CAAC,SAAS,EAAE;oBAC3C;gBACJ;gBACA,MAAM;YACV;QACJ;IACJ;IACA;;;;;;;KAOC,GACD,uBAAuB,MAAM,EAAE;QAC3B,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,SAAS;YACrB,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC;QAC5B;QACA,KAAK,MAAM,KAAK,IAAI,CAAC,YAAY,CAAE;YAC/B,EAAE,gBAAgB,CAAC;QACvB;IACJ;IACA;;KAEC,GACD,MAAM,MAAM,EAAE;QACV,IAAI,IAAI,IAAI;QACZ,MAAM,MAAM,WAAW,QAAQ,WAAW,KAAK,IAAI,SAAS,IAAI,uRAAA,CAAA,eAAY,CAAC,sBAAsB,2QAAA,CAAA,OAAI,CAAC,QAAQ;QAChH,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI;QAC5E,KAAK,MAAM,KAAK,IAAI,CAAC,YAAY,CAAE;YAC/B,CAAC,KAAK,EAAE,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,GAAG;QACnE;QACA,IAAI,CAAC,QAAQ,CAAC,cAAc;IAChC;IACA,MAAM,YAAY;QACd,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,SAAS;YACrB,IAAI,IAAI,CAAC,CAAC,CAAC,cAAc,MACrB,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,IAClB,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE;gBACvB,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,mBAAmB;YAClE,OACK,IAAI,IAAI,CAAC,CAAC,CAAC,cAAc,IAAI;gBAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC5B;QACJ,OACK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,SAAS;YAClD,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,mBAAmB;QAClE;QACA,MAAO,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,QAAS;YACzB,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS;gBACtB,MAAM,IAAI,CAAC,CAAC,CAAC,MAAM;YACvB;YACA,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,cAAc;gBAC3B,MAAM,IAAI,CAAC,CAAC,CAAC,IAAI;YACrB;QACJ;QACA,OAAO,IAAI,CAAC,CAAC;IACjB;IACA,SAAS,KAAK,EAAE;QACZ,IAAI,IAAI;QACR,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC;QAC9E,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,IAAI,CAAC,CAAC,CAAC,cAAc,IAAI;YAChD,0DAA0D;YAC1D,MAAM,KAAK,IAAI,CAAC,CAAC;YACjB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YACvB,GAAG,OAAO,GAAG,GAAG,OAAO,GAAG;gBACtB,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;gBACpC,IAAI,MAAM,CAAC,GAAG;oBACV,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG;gBAChC;YACJ;QACJ;QACA,OAAQ,MAAM,CAAC;YACX,KAAK;gBACD,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC;oBACb,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO,IAAI,CAAC,OAAO;gBAC3C,GAAG,CAAC;oBACA,IAAI,CAAC,QAAQ,CAAC,cAAc;gBAChC;gBACA;YACJ,KAAK;gBACD,MAAM,OAAO,GAAG,IAAM,IAAI,CAAC,QAAQ,CAAC;gBACpC,MAAM,OAAO,GAAG,CAAC,MAAQ,IAAI,CAAC,QAAQ,CAAC,cAAc;gBACrD;YACJ,KAAK;gBACD;YACJ,KAAK;gBACD;QACR;QACA,IAAI,CAAC,CAAC,GAAG;IACb;IACA,OAAO,UAAU,EAAE;QACf,IAAI,IAAI,CAAC,SAAS,KAAK,WAAW;YAC9B,OAAO,IAAI,CAAC,SAAS;QACzB;QACA,IAAI,CAAC,SAAS,GAAG,WACZ,MAAM,GACN,IAAI,CAAC,CAAC;YACP,IAAI,SAAS;gBACT;YACJ;YACA,4CAA4C;YAC5C,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,mBAAmB;QAClE,GAAG,CAAC;YACA,IAAI,CAAC,QAAQ,CAAC,cAAc;QAChC,GACK,OAAO,CAAC;YACT,IAAI,CAAC,SAAS,GAAG;QACrB;QACA,OAAO,IAAI,CAAC,SAAS;IACzB;AACJ;AACA,SAAS;IACL,OAAO;QACH,GAAG;IACP;AACJ;AACA,SAAS,MAAM,MAAM;IACjB,OAAO;QACH,GAAG;QACH;IACJ;AACJ;AACA,SAAS,cAAc,MAAM;IACzB,MAAM,WAAW,kBAAkB,uRAAA,CAAA,eAAY,IAC3C,uRAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,2QAAA,CAAA,OAAI,CAAC,QAAQ;IACnD,OAAO,WAAW,WAAW,MAAM;AACvC;AACA,SAAS,QAAQ,SAAS,EAAE,mBAAmB;IAC3C,IAAI;IACJ,IAAI;IACJ,MAAM,OAAO,IAAI,QAAQ,CAAC,KAAK;QAC3B,UAAU;QACV,SAAS;IACb;IACA,MAAM,UAAU,CAAA,GAAA,mGAAA,CAAA,UAAa,AAAD,EAAE,WAAW;IACzC,QAAQ,EAAE,CAAC,WAAW;IACtB,QAAQ,EAAE,CAAC,SAAS;IACpB,SAAS;QACL,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ;QAC1D;IACJ;IACA,SAAS,QAAQ,GAAG;QAChB,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,CAAA,GAAA,sRAAA,CAAA,6BAA0B,AAAD,EAAE;QAClF;IACJ;IACA,SAAS;QACL,QAAQ,GAAG,CAAC,WAAW;QACvB,QAAQ,GAAG,CAAC,SAAS;IACzB;IACA,OAAO;QACH,GAAG;QACH;QACA,OAAM,MAAM;YACR,IAAI,CAAC,QAAQ,SAAS,EAAE;gBACpB,QAAQ,OAAO,CAAC,WAAW,mGAAA,CAAA,YAAe,CAAC,cAAc;YAC7D;YACA,yEAAyE;YACzE,sEAAsE;YACtE,0EAA0E;YAC1E,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO;QAC3D;QACA;YACI;QACJ;IACJ;AACJ;AACA,SAAS,MAAM,IAAI,EAAE,OAAO;IACxB,uEAAuE;IACvE,2FAA2F;IAC3F,mEAAmE;IACnE,sBAAsB;IACtB,kBAAkB;IAClB,2DAA2D;IAC3D,2EAA2E;IAC3E,gCAAgC;IAChC,KAAK,KAAK;IACV,sEAAsE;IACtE,2CAA2C;IAC3C,IAAI,cAAc,KAAK,GAAG;IAC1B,sDAAsD;IACtD,IAAI,cAAc;IAClB,oCAAoC;IACpC,IAAI;IACJ,wCAAwC;IACxC,IAAI;IACJ,0FAA0F;IAC1F,IAAI,iBAAiB;IACrB,iFAAiF;IACjF,IAAI,4CAA4C;IAChD,0EAA0E;IAC1E,IAAI;IACJ;IACA,MAAM,QAAQ;QACV,GAAG;QACH;QACA;YACI,OAAO;QACX;QACA;YACI,MAAM,YAAY,KAAK,GAAG,KAAK;YAC/B,OAAO,YAAY,QAAQ,cAAc;QAC7C;QACA;YACI,OAAO;QACX;QACA,SAAS;QACT,SAAS;QACT,iBAAgB,MAAM;YAClB;YACA,IAAI,eAAe,GAAG;gBAClB,KAAK,GAAG;gBACR,qBAAqB,yDAAyD;gBAC9E;YACJ;YACA,OAAO,IAAI,CAAC,YAAY;gBACpB,cAAc,KAAK,GAAG;gBACtB;YACJ;YACA,OAAO,IAAI,CAAC,SAAS;gBACjB;gBACA,IAAI,eAAe,GAAG;oBAClB,KAAK,KAAK;oBACV,qBAAqB,yDAAyD;oBAC9E;gBACJ;YACJ;QACJ;QACA,kBAAiB,MAAM;YACnB,IAAI,OAAO,OAAO,KAAK,MAAM;gBACzB;YACJ;YACA,IAAI,KAAK,MAAM,IAAI,KAAK,SAAS,EAAE;gBAC/B;YACJ;YACA,IAAI,eAAe,GAAG;gBAClB;YACJ;YACA,cAAc,KAAK,GAAG;YACtB;QACJ;QACA;YACI,KAAK,GAAG;YACR,OAAO,IAAI,QAAQ,CAAC;gBAChB,WAAW;oBACP,IAAI,eAAe,GACf,KAAK,KAAK;oBACd,QAAQ;gBACZ;gBACA,KAAK,IAAI,CAAC,SAAS,IAAM,QAAQ;YACrC;QACJ;QACA,OAAM,MAAM;YACR,IAAI,CAAC,KAAK,SAAS,EAAE;gBACjB,KAAK,IAAI,CAAC,SAAS;gBACf,mEAAmE;gBACnE,uCAAuC;gBACvC,qDAAqD;gBACzD;gBACA,KAAK,OAAO,CAAC,QAAQ,mGAAA,CAAA,YAAe,CAAC,cAAc;YACvD;QACJ;QACA;YACI,IAAI,MAAM,cAAc,IAAI;gBACxB,uEAAuE;gBACvE,sEAAsE;gBACtE,uEAAuE;gBACvE;YACJ;YACA;YACA,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,OAAO,GAAG;QACnB;IACJ;IACA,qCAAqC;IACrC,SAAS;QACL;QACA,IAAI,cAAc,KAAK,QAAQ,kBAAkB,EAAE;YAC/C,iBAAiB,eAAe,gBAAgB,QAAQ,cAAc;QAC1E;IACJ;IACA,SAAS;QACL,aAAa;QACb,aAAa;IACjB;IACA,SAAS;QACL,WAAW;IACf;IACA,SAAS,WAAW,SAAS;QACzB,aAAa;QACb,gBAAgB,eAAe;YAC3B,KAAK,OAAO,CAAC,IAAI,uRAAA,CAAA,eAAY,CAAC,kBAAkB,2QAAA,CAAA,OAAI,CAAC,WAAW,GAAG,mGAAA,CAAA,YAAe,CAAC,cAAc;QACrG,GAAG,QAAQ,aAAa;QACxB,KAAK,IAAI,CAAC,CAAC,KAAK;YACZ,aAAa;YACb,IAAI,QAAQ,MAAM;gBACd,kEAAkE;gBAClE,kCAAkC;gBAClC,sEAAsE;gBACtE,0BAA0B;gBAC1B;YACJ;YACA,IAAI,WAAW,QAAQ,aAAa,EAAE;gBAClC,oEAAoE;gBACpE,SAAS;gBACT,KAAK,OAAO,CAAC,IAAI,uRAAA,CAAA,eAAY,CAAC,kBAAkB,2QAAA,CAAA,OAAI,CAAC,WAAW,GAAG,mGAAA,CAAA,YAAe,CAAC,cAAc;gBACjG;YACJ;YACA,cAAc,KAAK,GAAG;YACtB;QACJ;IACJ;IACA,SAAS;QACL,aAAa;IACjB;IACA,SAAS;QACL,gBAAgB,eAAe,eAAe,QAAQ,uBAAuB;IACjF;IACA,SAAS;QACL,KAAK,KAAK;QACV,WAAW,iEAAiE;IAChF;IACA,SAAS,SAAS,SAAS,EAAE,YAAY,EAAE,UAAU;QACjD,iBAAiB;QACjB,MAAM,oBAAoB,OAAO,IAAI,CAAC,kBAAkB;QACxD,IAAI,cAAc,mGAAA,CAAA,YAAe,CAAC,yBAAyB,IACvD,cAAc,QACd,WAAW,MAAM,CAAC,oBAAoB;YACtC,oLAAoL;YACpL,QAAQ,cAAc,GAAG,QAAQ,cAAc,GAAG;YAClD,4CAA4C;QAChD;QACA,IAAI,cAAc,mGAAA,CAAA,YAAe,CAAC,gBAAgB,EAAE;YAChD,MAAM,YAAY,SAAS,QAAQ,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;YAChE,uEAAuE;YACvE,oEAAoE;YACpE,4DAA4D;YAC5D,sEAAsE;YACtE,mJAAmJ;YACnJ,IAAI,eAAe,KAAK,aAAa,IAAI;gBACrC,KAAK,OAAO,CAAC,IAAI,uRAAA,CAAA,eAAY,CAAC,4CAA4C,2QAAA,CAAA,OAAI,CAAC,QAAQ,GAAG,mGAAA,CAAA,YAAe,CAAC,gBAAgB;YAC9H;QACJ;IACJ;IACA,SAAS;QACL,IAAI;QACJ;QACA,CAAC,KAAK,MAAM,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC;IACtE;IACA,SAAS,QAAQ,GAAG;QAChB,IAAI,IAAI;QACR;QACA,IAAI,2CAA2C;YAC3C,0EAA0E;YAC1E,2EAA2E;YAC3E,6BAA6B;YAC7B,MAAM,KAAK,IAAI,uRAAA,CAAA,eAAY,CAAC,CAAC,8DAA8D,EAAE,mGAAA,CAAA,YAAe,CAAC,yBAAyB,CAAC,QAAQ,CAAC,IAAI,uCAAuC,CAAC,EAAE,2QAAA,CAAA,OAAI,CAAC,iBAAiB;YACpN,CAAC,KAAK,MAAM,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,OAAO;QAC7E,OACK;YACD,CAAC,KAAK,MAAM,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,OAAO,CAAA,GAAA,sRAAA,CAAA,6BAA0B,AAAD,EAAE;QACxG;IACJ;IACA,SAAS;QACL;QACA;QACA,KAAK,GAAG,CAAC,SAAS;QAClB,KAAK,GAAG,CAAC,SAAS;QAClB,KAAK,GAAG,CAAC,UAAU;IACvB;IACA,KAAK,EAAE,CAAC,SAAS;IACjB,KAAK,EAAE,CAAC,SAAS;IACjB,KAAK,EAAE,CAAC,UAAU;IAClB,OAAO;AACX;AACA;;;;CAIC,GACD,SAAS,eAAe,QAAQ,EAAE,EAAE;IAChC,IAAI,KAAK,YAAY;QACjB;IACJ;IACA,OAAO,WAAW,UAAU,IAAI,KAAK;AACzC;AACA,SAAS,kBAAkB,IAAI;IAC3B,IAAI,KAAK,UAAU,EAAE;QACjB,MAAM,IAAI,uRAAA,CAAA,eAAY,CAAC,+CAA+C,2QAAA,CAAA,OAAI,CAAC,QAAQ;IACvF;IACA,IAAI,KAAK,SAAS,EAAE;QAChB,MAAM,IAAI,uRAAA,CAAA,eAAY,CAAC,8CAA8C,2QAAA,CAAA,OAAI,CAAC,QAAQ;IACtF;IACA,IAAI,KAAK,MAAM,EAAE;QACb,MAAM,IAAI,uRAAA,CAAA,eAAY,CAAC,2CAA2C,2QAAA,CAAA,OAAI,CAAC,QAAQ;IACnF;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 872, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40connectrpc%2Bconnect-node%401._90f7088c2e6395363396c6520b160400/node_modules/%40connectrpc/connect-node/dist/esm/node-universal-client.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport * as http2 from \"http2\";\nimport * as http from \"http\";\nimport * as https from \"https\";\nimport { Code, ConnectError } from \"@connectrpc/connect\";\nimport { nodeHeaderToWebHeader, webHeaderToNodeHeaders, } from \"./node-universal-header.js\";\nimport { connectErrorFromH2ResetCode, connectErrorFromNodeReason, getNodeErrorProps, H2Code, unwrapNodeErrorChain, } from \"./node-error.js\";\nimport { getAbortSignalReason } from \"@connectrpc/connect/protocol\";\nimport { Http2SessionManager } from \"./http2-session-manager.js\";\n/**\n * Create a universal client function, a minimal abstraction of an HTTP client,\n * using the Node.js `http`, `https`, or `http2` module.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function createNodeHttpClient(options) {\n    var _a;\n    if (options.httpVersion == \"1.1\") {\n        return createNodeHttp1Client(options.nodeOptions);\n    }\n    const sessionProvider = (_a = options.sessionProvider) !== null && _a !== void 0 ? _a : ((url) => new Http2SessionManager(url));\n    return createNodeHttp2Client(sessionProvider);\n}\n/**\n * Create an HTTP client using the Node.js `http` or `https` package.\n *\n * The HTTP client is a simple function conforming to the type UniversalClientFn.\n * It takes an UniversalClientRequest as an argument, and returns a promise for\n * an UniversalClientResponse.\n */\nfunction createNodeHttp1Client(httpOptions) {\n    return async function request(req) {\n        const sentinel = createSentinel(req.signal);\n        return new Promise((resolve, reject) => {\n            sentinel.catch((e) => {\n                reject(e);\n            });\n            h1Request(sentinel, req.url, Object.assign(Object.assign({}, httpOptions), { headers: webHeaderToNodeHeaders(req.header, httpOptions === null || httpOptions === void 0 ? void 0 : httpOptions.headers), method: req.method }), (request) => {\n                void sinkRequest(req, request, sentinel);\n                request.on(\"response\", (response) => {\n                    var _a;\n                    response.on(\"error\", sentinel.reject);\n                    sentinel.catch((reason) => response.destroy(connectErrorFromNodeReason(reason)));\n                    const trailer = new Headers();\n                    resolve({\n                        status: (_a = response.statusCode) !== null && _a !== void 0 ? _a : 0,\n                        header: nodeHeaderToWebHeader(response.headers),\n                        body: h1ResponseIterable(sentinel, response, trailer),\n                        trailer,\n                    });\n                });\n            });\n        });\n    };\n}\n/**\n * Create an HTTP client using the Node.js `http2` package.\n *\n * The HTTP client is a simple function conforming to the type UniversalClientFn.\n * It takes an UniversalClientRequest as an argument, and returns a promise for\n * an UniversalClientResponse.\n */\nfunction createNodeHttp2Client(sessionProvider) {\n    return function request(req) {\n        const sentinel = createSentinel(req.signal);\n        const sessionManager = sessionProvider(req.url);\n        return new Promise((resolve, reject) => {\n            sentinel.catch((e) => {\n                reject(e);\n            });\n            h2Request(sentinel, sessionManager, req.url, req.method, webHeaderToNodeHeaders(req.header), {}, (stream) => {\n                void sinkRequest(req, stream, sentinel);\n                stream.on(\"response\", (headers) => {\n                    var _a;\n                    const response = {\n                        status: (_a = headers[\":status\"]) !== null && _a !== void 0 ? _a : 0,\n                        header: nodeHeaderToWebHeader(headers),\n                        body: h2ResponseIterable(sentinel, stream, sessionManager),\n                        trailer: h2ResponseTrailer(stream),\n                    };\n                    resolve(response);\n                });\n            });\n        });\n    };\n}\nfunction h1Request(sentinel, url, options, onRequest) {\n    let request;\n    if (new URL(url).protocol.startsWith(\"https\")) {\n        request = https.request(url, options);\n    }\n    else {\n        request = http.request(url, options);\n    }\n    sentinel.catch((reason) => request.destroy(connectErrorFromNodeReason(reason)));\n    // Node.js will only send headers with the first request body byte by default.\n    // We force it to send headers right away for consistent behavior between\n    // HTTP/1.1 and HTTP/2.2 clients.\n    request.flushHeaders();\n    request.on(\"error\", sentinel.reject);\n    request.on(\"socket\", function onRequestSocket(socket) {\n        function onSocketConnect() {\n            socket.off(\"connect\", onSocketConnect);\n            onRequest(request);\n        }\n        // If readyState is open, then socket is already open due to keepAlive, so\n        // the 'connect' event will never fire so call onRequest explicitly\n        if (socket.readyState === \"open\") {\n            onRequest(request);\n        }\n        else {\n            socket.on(\"connect\", onSocketConnect);\n        }\n    });\n}\nfunction h1ResponseIterable(sentinel, response, trailer) {\n    const inner = response[Symbol.asyncIterator]();\n    return {\n        [Symbol.asyncIterator]() {\n            return {\n                async next() {\n                    const r = await sentinel.race(inner.next());\n                    if (r.done === true) {\n                        nodeHeaderToWebHeader(response.trailers).forEach((value, key) => {\n                            trailer.set(key, value);\n                        });\n                        sentinel.resolve();\n                        await sentinel;\n                    }\n                    return r;\n                },\n                throw(e) {\n                    sentinel.reject(e);\n                    throw e;\n                },\n            };\n        },\n    };\n}\nfunction h2Request(sentinel, sm, url, method, headers, options, onStream) {\n    const requestUrl = new URL(url);\n    if (requestUrl.origin !== sm.authority) {\n        const message = `cannot make a request to ${requestUrl.origin}: the http2 session is connected to ${sm.authority}`;\n        sentinel.reject(new ConnectError(message, Code.Internal));\n        return;\n    }\n    sm.request(method, requestUrl.pathname + requestUrl.search, headers, {}).then((stream) => {\n        sentinel.catch((reason) => {\n            if (stream.closed) {\n                return;\n            }\n            // Node.js http2 streams that are aborted via an AbortSignal close with\n            // an RST_STREAM with code INTERNAL_ERROR.\n            // To comply with the mapping between gRPC and HTTP/2 codes, we need to\n            // close with code CANCEL.\n            // See https://github.com/grpc/grpc/blob/master/doc/PROTOCOL-HTTP2.md#errors\n            // See https://www.rfc-editor.org/rfc/rfc7540#section-7\n            const rstCode = reason instanceof ConnectError && reason.code == Code.Canceled\n                ? H2Code.CANCEL\n                : H2Code.INTERNAL_ERROR;\n            return new Promise((resolve) => stream.close(rstCode, resolve));\n        });\n        stream.on(\"error\", function h2StreamError(e) {\n            if (stream.writableEnded &&\n                unwrapNodeErrorChain(e)\n                    .map(getNodeErrorProps)\n                    .some((p) => p.code == \"ERR_STREAM_WRITE_AFTER_END\")) {\n                return;\n            }\n            sentinel.reject(e);\n        });\n        stream.on(\"close\", function h2StreamClose() {\n            const err = connectErrorFromH2ResetCode(stream.rstCode);\n            if (err) {\n                sentinel.reject(err);\n            }\n        });\n        onStream(stream);\n    }, (reason) => {\n        sentinel.reject(reason);\n    });\n}\nfunction h2ResponseTrailer(response) {\n    const trailer = new Headers();\n    response.on(\"trailers\", (args) => {\n        nodeHeaderToWebHeader(args).forEach((value, key) => {\n            trailer.set(key, value);\n        });\n    });\n    return trailer;\n}\nfunction h2ResponseIterable(sentinel, response, sm) {\n    const inner = response[Symbol.asyncIterator]();\n    return {\n        [Symbol.asyncIterator]() {\n            return {\n                async next() {\n                    const r = await sentinel.race(inner.next());\n                    if (r.done === true) {\n                        sentinel.resolve();\n                        await sentinel;\n                    }\n                    sm === null || sm === void 0 ? void 0 : sm.notifyResponseByteRead(response);\n                    return r;\n                },\n                throw(e) {\n                    sentinel.reject(e);\n                    throw e;\n                },\n            };\n        },\n    };\n}\nasync function sinkRequest(request, nodeRequest, sentinel) {\n    if (request.body === undefined) {\n        await new Promise((resolve) => nodeRequest.end(resolve));\n        return;\n    }\n    const it = request.body[Symbol.asyncIterator]();\n    return new Promise((resolve) => {\n        writeNext();\n        function writeNext() {\n            if (sentinel.isRejected()) {\n                return;\n            }\n            it.next().then((r) => {\n                if (r.done === true) {\n                    nodeRequest.end(resolve);\n                    return;\n                }\n                nodeRequest.write(r.value, \"binary\", function (e) {\n                    if (e === null || e === undefined) {\n                        writeNext();\n                        return;\n                    }\n                    if (it.throw !== undefined) {\n                        it.throw(connectErrorFromNodeReason(e)).catch(() => {\n                            //\n                        });\n                    }\n                    // If the server responds and closes the connection before the client has written the entire response\n                    // body, we get an ERR_STREAM_WRITE_AFTER_END error code from Node.js here.\n                    // We do want to notify the iterable of the error condition, but we do not want to reject our sentinel,\n                    // because that would also affect the reading side.\n                    if (nodeRequest.writableEnded &&\n                        unwrapNodeErrorChain(e)\n                            .map(getNodeErrorProps)\n                            .some((p) => p.code == \"ERR_STREAM_WRITE_AFTER_END\")) {\n                        return;\n                    }\n                    sentinel.reject(e);\n                });\n            }, (e) => {\n                sentinel.reject(e);\n            });\n        }\n    });\n}\nfunction createSentinel(signal) {\n    let res;\n    let rej;\n    let resolved = false;\n    let rejected = false;\n    const p = new Promise((resolve, reject) => {\n        res = resolve;\n        rej = reject;\n    });\n    const c = {\n        resolve() {\n            if (!resolved && !rejected) {\n                resolved = true;\n                res === null || res === void 0 ? void 0 : res();\n            }\n        },\n        isResolved() {\n            return resolved;\n        },\n        reject(reason) {\n            if (!resolved && !rejected) {\n                rejected = true;\n                rej === null || rej === void 0 ? void 0 : rej(connectErrorFromNodeReason(reason));\n            }\n        },\n        isRejected() {\n            return rejected;\n        },\n        async race(promise) {\n            const r = await Promise.race([promise, p]);\n            if (r === undefined && resolved) {\n                throw new ConnectError(\"sentinel completed early\", Code.Internal);\n            }\n            return r;\n        },\n    };\n    const s = Object.assign(p, c);\n    function onSignalAbort() {\n        c.reject(getAbortSignalReason(this));\n    }\n    if (signal) {\n        if (signal.aborted) {\n            c.reject(getAbortSignalReason(signal));\n        }\n        else {\n            signal.addEventListener(\"abort\", onSignalAbort);\n        }\n        p.finally(() => signal.removeEventListener(\"abort\", onSignalAbort)).catch(() => {\n            // We intentionally swallow sentinel rejection - errors must\n            // propagate through the request or response iterables.\n        });\n    }\n    return s;\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;AAOO,SAAS,qBAAqB,OAAO;IACxC,IAAI;IACJ,IAAI,QAAQ,WAAW,IAAI,OAAO;QAC9B,OAAO,sBAAsB,QAAQ,WAAW;IACpD;IACA,MAAM,kBAAkB,CAAC,KAAK,QAAQ,eAAe,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAM,CAAC,MAAQ,IAAI,oSAAA,CAAA,sBAAmB,CAAC;IAC1H,OAAO,sBAAsB;AACjC;AACA;;;;;;CAMC,GACD,SAAS,sBAAsB,WAAW;IACtC,OAAO,eAAe,QAAQ,GAAG;QAC7B,MAAM,WAAW,eAAe,IAAI,MAAM;QAC1C,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,SAAS,KAAK,CAAC,CAAC;gBACZ,OAAO;YACX;YACA,UAAU,UAAU,IAAI,GAAG,EAAE,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,cAAc;gBAAE,SAAS,CAAA,GAAA,oSAAA,CAAA,yBAAsB,AAAD,EAAE,IAAI,MAAM,EAAE,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,OAAO;gBAAG,QAAQ,IAAI,MAAM;YAAC,IAAI,CAAC;gBAC7N,KAAK,YAAY,KAAK,SAAS;gBAC/B,QAAQ,EAAE,CAAC,YAAY,CAAC;oBACpB,IAAI;oBACJ,SAAS,EAAE,CAAC,SAAS,SAAS,MAAM;oBACpC,SAAS,KAAK,CAAC,CAAC,SAAW,SAAS,OAAO,CAAC,CAAA,GAAA,sRAAA,CAAA,6BAA0B,AAAD,EAAE;oBACvE,MAAM,UAAU,IAAI;oBACpB,QAAQ;wBACJ,QAAQ,CAAC,KAAK,SAAS,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;wBACpE,QAAQ,CAAA,GAAA,oSAAA,CAAA,wBAAqB,AAAD,EAAE,SAAS,OAAO;wBAC9C,MAAM,mBAAmB,UAAU,UAAU;wBAC7C;oBACJ;gBACJ;YACJ;QACJ;IACJ;AACJ;AACA;;;;;;CAMC,GACD,SAAS,sBAAsB,eAAe;IAC1C,OAAO,SAAS,QAAQ,GAAG;QACvB,MAAM,WAAW,eAAe,IAAI,MAAM;QAC1C,MAAM,iBAAiB,gBAAgB,IAAI,GAAG;QAC9C,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,SAAS,KAAK,CAAC,CAAC;gBACZ,OAAO;YACX;YACA,UAAU,UAAU,gBAAgB,IAAI,GAAG,EAAE,IAAI,MAAM,EAAE,CAAA,GAAA,oSAAA,CAAA,yBAAsB,AAAD,EAAE,IAAI,MAAM,GAAG,CAAC,GAAG,CAAC;gBAC9F,KAAK,YAAY,KAAK,QAAQ;gBAC9B,OAAO,EAAE,CAAC,YAAY,CAAC;oBACnB,IAAI;oBACJ,MAAM,WAAW;wBACb,QAAQ,CAAC,KAAK,OAAO,CAAC,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;wBACnE,QAAQ,CAAA,GAAA,oSAAA,CAAA,wBAAqB,AAAD,EAAE;wBAC9B,MAAM,mBAAmB,UAAU,QAAQ;wBAC3C,SAAS,kBAAkB;oBAC/B;oBACA,QAAQ;gBACZ;YACJ;QACJ;IACJ;AACJ;AACA,SAAS,UAAU,QAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,SAAS;IAChD,IAAI;IACJ,IAAI,IAAI,IAAI,KAAK,QAAQ,CAAC,UAAU,CAAC,UAAU;QAC3C,UAAU,CAAA,GAAA,mGAAA,CAAA,UAAa,AAAD,EAAE,KAAK;IACjC,OACK;QACD,UAAU,CAAA,GAAA,iGAAA,CAAA,UAAY,AAAD,EAAE,KAAK;IAChC;IACA,SAAS,KAAK,CAAC,CAAC,SAAW,QAAQ,OAAO,CAAC,CAAA,GAAA,sRAAA,CAAA,6BAA0B,AAAD,EAAE;IACtE,8EAA8E;IAC9E,yEAAyE;IACzE,iCAAiC;IACjC,QAAQ,YAAY;IACpB,QAAQ,EAAE,CAAC,SAAS,SAAS,MAAM;IACnC,QAAQ,EAAE,CAAC,UAAU,SAAS,gBAAgB,MAAM;QAChD,SAAS;YACL,OAAO,GAAG,CAAC,WAAW;YACtB,UAAU;QACd;QACA,0EAA0E;QAC1E,mEAAmE;QACnE,IAAI,OAAO,UAAU,KAAK,QAAQ;YAC9B,UAAU;QACd,OACK;YACD,OAAO,EAAE,CAAC,WAAW;QACzB;IACJ;AACJ;AACA,SAAS,mBAAmB,QAAQ,EAAE,QAAQ,EAAE,OAAO;IACnD,MAAM,QAAQ,QAAQ,CAAC,OAAO,aAAa,CAAC;IAC5C,OAAO;QACH,CAAC,OAAO,aAAa,CAAC;YAClB,OAAO;gBACH,MAAM;oBACF,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,MAAM,IAAI;oBACxC,IAAI,EAAE,IAAI,KAAK,MAAM;wBACjB,CAAA,GAAA,oSAAA,CAAA,wBAAqB,AAAD,EAAE,SAAS,QAAQ,EAAE,OAAO,CAAC,CAAC,OAAO;4BACrD,QAAQ,GAAG,CAAC,KAAK;wBACrB;wBACA,SAAS,OAAO;wBAChB,MAAM;oBACV;oBACA,OAAO;gBACX;gBACA,OAAM,CAAC;oBACH,SAAS,MAAM,CAAC;oBAChB,MAAM;gBACV;YACJ;QACJ;IACJ;AACJ;AACA,SAAS,UAAU,QAAQ,EAAE,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ;IACpE,MAAM,aAAa,IAAI,IAAI;IAC3B,IAAI,WAAW,MAAM,KAAK,GAAG,SAAS,EAAE;QACpC,MAAM,UAAU,CAAC,yBAAyB,EAAE,WAAW,MAAM,CAAC,oCAAoC,EAAE,GAAG,SAAS,EAAE;QAClH,SAAS,MAAM,CAAC,IAAI,uRAAA,CAAA,eAAY,CAAC,SAAS,2QAAA,CAAA,OAAI,CAAC,QAAQ;QACvD;IACJ;IACA,GAAG,OAAO,CAAC,QAAQ,WAAW,QAAQ,GAAG,WAAW,MAAM,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC;QAC3E,SAAS,KAAK,CAAC,CAAC;YACZ,IAAI,OAAO,MAAM,EAAE;gBACf;YACJ;YACA,uEAAuE;YACvE,0CAA0C;YAC1C,uEAAuE;YACvE,0BAA0B;YAC1B,4EAA4E;YAC5E,uDAAuD;YACvD,MAAM,UAAU,kBAAkB,uRAAA,CAAA,eAAY,IAAI,OAAO,IAAI,IAAI,2QAAA,CAAA,OAAI,CAAC,QAAQ,GACxE,sRAAA,CAAA,SAAM,CAAC,MAAM,GACb,sRAAA,CAAA,SAAM,CAAC,cAAc;YAC3B,OAAO,IAAI,QAAQ,CAAC,UAAY,OAAO,KAAK,CAAC,SAAS;QAC1D;QACA,OAAO,EAAE,CAAC,SAAS,SAAS,cAAc,CAAC;YACvC,IAAI,OAAO,aAAa,IACpB,CAAA,GAAA,sRAAA,CAAA,uBAAoB,AAAD,EAAE,GAChB,GAAG,CAAC,sRAAA,CAAA,oBAAiB,EACrB,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,IAAI,+BAA+B;gBAC1D;YACJ;YACA,SAAS,MAAM,CAAC;QACpB;QACA,OAAO,EAAE,CAAC,SAAS,SAAS;YACxB,MAAM,MAAM,CAAA,GAAA,sRAAA,CAAA,8BAA2B,AAAD,EAAE,OAAO,OAAO;YACtD,IAAI,KAAK;gBACL,SAAS,MAAM,CAAC;YACpB;QACJ;QACA,SAAS;IACb,GAAG,CAAC;QACA,SAAS,MAAM,CAAC;IACpB;AACJ;AACA,SAAS,kBAAkB,QAAQ;IAC/B,MAAM,UAAU,IAAI;IACpB,SAAS,EAAE,CAAC,YAAY,CAAC;QACrB,CAAA,GAAA,oSAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM,OAAO,CAAC,CAAC,OAAO;YACxC,QAAQ,GAAG,CAAC,KAAK;QACrB;IACJ;IACA,OAAO;AACX;AACA,SAAS,mBAAmB,QAAQ,EAAE,QAAQ,EAAE,EAAE;IAC9C,MAAM,QAAQ,QAAQ,CAAC,OAAO,aAAa,CAAC;IAC5C,OAAO;QACH,CAAC,OAAO,aAAa,CAAC;YAClB,OAAO;gBACH,MAAM;oBACF,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,MAAM,IAAI;oBACxC,IAAI,EAAE,IAAI,KAAK,MAAM;wBACjB,SAAS,OAAO;wBAChB,MAAM;oBACV;oBACA,OAAO,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,sBAAsB,CAAC;oBAClE,OAAO;gBACX;gBACA,OAAM,CAAC;oBACH,SAAS,MAAM,CAAC;oBAChB,MAAM;gBACV;YACJ;QACJ;IACJ;AACJ;AACA,eAAe,YAAY,OAAO,EAAE,WAAW,EAAE,QAAQ;IACrD,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC5B,MAAM,IAAI,QAAQ,CAAC,UAAY,YAAY,GAAG,CAAC;QAC/C;IACJ;IACA,MAAM,KAAK,QAAQ,IAAI,CAAC,OAAO,aAAa,CAAC;IAC7C,OAAO,IAAI,QAAQ,CAAC;QAChB;QACA,SAAS;YACL,IAAI,SAAS,UAAU,IAAI;gBACvB;YACJ;YACA,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;gBACZ,IAAI,EAAE,IAAI,KAAK,MAAM;oBACjB,YAAY,GAAG,CAAC;oBAChB;gBACJ;gBACA,YAAY,KAAK,CAAC,EAAE,KAAK,EAAE,UAAU,SAAU,CAAC;oBAC5C,IAAI,MAAM,QAAQ,MAAM,WAAW;wBAC/B;wBACA;oBACJ;oBACA,IAAI,GAAG,KAAK,KAAK,WAAW;wBACxB,GAAG,KAAK,CAAC,CAAA,GAAA,sRAAA,CAAA,6BAA0B,AAAD,EAAE,IAAI,KAAK,CAAC;wBAC1C,EAAE;wBACN;oBACJ;oBACA,qGAAqG;oBACrG,2EAA2E;oBAC3E,uGAAuG;oBACvG,mDAAmD;oBACnD,IAAI,YAAY,aAAa,IACzB,CAAA,GAAA,sRAAA,CAAA,uBAAoB,AAAD,EAAE,GAChB,GAAG,CAAC,sRAAA,CAAA,oBAAiB,EACrB,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,IAAI,+BAA+B;wBAC1D;oBACJ;oBACA,SAAS,MAAM,CAAC;gBACpB;YACJ,GAAG,CAAC;gBACA,SAAS,MAAM,CAAC;YACpB;QACJ;IACJ;AACJ;AACA,SAAS,eAAe,MAAM;IAC1B,IAAI;IACJ,IAAI;IACJ,IAAI,WAAW;IACf,IAAI,WAAW;IACf,MAAM,IAAI,IAAI,QAAQ,CAAC,SAAS;QAC5B,MAAM;QACN,MAAM;IACV;IACA,MAAM,IAAI;QACN;YACI,IAAI,CAAC,YAAY,CAAC,UAAU;gBACxB,WAAW;gBACX,QAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI;YAC9C;QACJ;QACA;YACI,OAAO;QACX;QACA,QAAO,MAAM;YACT,IAAI,CAAC,YAAY,CAAC,UAAU;gBACxB,WAAW;gBACX,QAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,CAAA,GAAA,sRAAA,CAAA,6BAA0B,AAAD,EAAE;YAC7E;QACJ;QACA;YACI,OAAO;QACX;QACA,MAAM,MAAK,OAAO;YACd,MAAM,IAAI,MAAM,QAAQ,IAAI,CAAC;gBAAC;gBAAS;aAAE;YACzC,IAAI,MAAM,aAAa,UAAU;gBAC7B,MAAM,IAAI,uRAAA,CAAA,eAAY,CAAC,4BAA4B,2QAAA,CAAA,OAAI,CAAC,QAAQ;YACpE;YACA,OAAO;QACX;IACJ;IACA,MAAM,IAAI,OAAO,MAAM,CAAC,GAAG;IAC3B,SAAS;QACL,EAAE,MAAM,CAAC,CAAA,GAAA,0RAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI;IACtC;IACA,IAAI,QAAQ;QACR,IAAI,OAAO,OAAO,EAAE;YAChB,EAAE,MAAM,CAAC,CAAA,GAAA,0RAAA,CAAA,uBAAoB,AAAD,EAAE;QAClC,OACK;YACD,OAAO,gBAAgB,CAAC,SAAS;QACrC;QACA,EAAE,OAAO,CAAC,IAAM,OAAO,mBAAmB,CAAC,SAAS,gBAAgB,KAAK,CAAC;QACtE,4DAA4D;QAC5D,uDAAuD;QAC3D;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40connectrpc%2Bconnect-node%401._90f7088c2e6395363396c6520b160400/node_modules/%40connectrpc/connect-node/dist/esm/node-transport-options.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { validateReadWriteMaxBytes } from \"@connectrpc/connect/protocol\";\nimport { compressionBrotli, compressionGzip } from \"./compression.js\";\nimport { createNodeHttpClient } from \"./node-universal-client.js\";\nimport { Http2SessionManager } from \"./http2-session-manager.js\";\nimport * as http2 from \"http2\";\nimport * as http from \"http\";\nimport * as https from \"https\";\n/**\n * Asserts that the options are within sane limits, and returns default values\n * where no value is provided.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function validateNodeTransportOptions(options) {\n    var _a, _b, _c, _d;\n    let httpClient;\n    if (options.httpVersion == \"2\") {\n        let sessionManager;\n        if (options.sessionManager) {\n            sessionManager = options.sessionManager;\n        }\n        else {\n            sessionManager = new Http2SessionManager(options.baseUrl, {\n                pingIntervalMs: options.pingIntervalMs,\n                pingIdleConnection: options.pingIdleConnection,\n                pingTimeoutMs: options.pingTimeoutMs,\n                idleConnectionTimeoutMs: options.idleConnectionTimeoutMs,\n            }, options.nodeOptions);\n        }\n        httpClient = createNodeHttpClient({\n            httpVersion: \"2\",\n            sessionProvider: () => sessionManager,\n        });\n    }\n    else {\n        httpClient = createNodeHttpClient({\n            httpVersion: \"1.1\",\n            nodeOptions: options.nodeOptions,\n        });\n    }\n    return Object.assign(Object.assign(Object.assign({}, options), { httpClient, useBinaryFormat: (_a = options.useBinaryFormat) !== null && _a !== void 0 ? _a : true, interceptors: (_b = options.interceptors) !== null && _b !== void 0 ? _b : [], sendCompression: (_c = options.sendCompression) !== null && _c !== void 0 ? _c : null, acceptCompression: (_d = options.acceptCompression) !== null && _d !== void 0 ? _d : [\n            compressionGzip,\n            compressionBrotli,\n        ] }), validateReadWriteMaxBytes(options.readMaxBytes, options.writeMaxBytes, options.compressMinBytes));\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAOO,SAAS,6BAA6B,OAAO;IAChD,IAAI,IAAI,IAAI,IAAI;IAChB,IAAI;IACJ,IAAI,QAAQ,WAAW,IAAI,KAAK;QAC5B,IAAI;QACJ,IAAI,QAAQ,cAAc,EAAE;YACxB,iBAAiB,QAAQ,cAAc;QAC3C,OACK;YACD,iBAAiB,IAAI,oSAAA,CAAA,sBAAmB,CAAC,QAAQ,OAAO,EAAE;gBACtD,gBAAgB,QAAQ,cAAc;gBACtC,oBAAoB,QAAQ,kBAAkB;gBAC9C,eAAe,QAAQ,aAAa;gBACpC,yBAAyB,QAAQ,uBAAuB;YAC5D,GAAG,QAAQ,WAAW;QAC1B;QACA,aAAa,CAAA,GAAA,oSAAA,CAAA,uBAAoB,AAAD,EAAE;YAC9B,aAAa;YACb,iBAAiB,IAAM;QAC3B;IACJ,OACK;QACD,aAAa,CAAA,GAAA,oSAAA,CAAA,uBAAoB,AAAD,EAAE;YAC9B,aAAa;YACb,aAAa,QAAQ,WAAW;QACpC;IACJ;IACA,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU;QAAE;QAAY,iBAAiB,CAAC,KAAK,QAAQ,eAAe,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAAM,cAAc,CAAC,KAAK,QAAQ,YAAY,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QAAE,iBAAiB,CAAC,KAAK,QAAQ,eAAe,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAAM,mBAAmB,CAAC,KAAK,QAAQ,iBAAiB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;YACvZ,oRAAA,CAAA,kBAAe;YACf,oRAAA,CAAA,oBAAiB;SACpB;IAAC,IAAI,CAAA,GAAA,8RAAA,CAAA,4BAAyB,AAAD,EAAE,QAAQ,YAAY,EAAE,QAAQ,aAAa,EAAE,QAAQ,gBAAgB;AAC7G", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40connectrpc%2Bconnect-node%401._90f7088c2e6395363396c6520b160400/node_modules/%40connectrpc/connect-node/dist/esm/grpc-web-transport.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { createTransport } from \"@connectrpc/connect/protocol-grpc-web\";\nimport { validateNodeTransportOptions } from \"./node-transport-options.js\";\n/**\n * Create a Transport for the gRPC-web protocol using the Node.js `http`,\n * `http2`, or `http2` module.\n */\nexport function createGrpcWebTransport(options) {\n    return createTransport(validateNodeTransportOptions(options));\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;AACA;;;AAKO,SAAS,uBAAuB,OAAO;IAC1C,OAAO,CAAA,GAAA,2SAAA,CAAA,kBAAe,AAAD,EAAE,CAAA,GAAA,qSAAA,CAAA,+BAA4B,AAAD,EAAE;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40connectrpc%2Bconnect-node%401._90f7088c2e6395363396c6520b160400/node_modules/%40connectrpc/connect-node/dist/esm/grpc-transport.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { createTransport } from \"@connectrpc/connect/protocol-grpc\";\nimport { validateNodeTransportOptions } from \"./node-transport-options.js\";\n/**\n * Create a Transport for the gRPC protocol using the Node.js `http`, `http2`,\n * or `http2` module.\n */\nexport function createGrpcTransport(options) {\n    return createTransport(validateNodeTransportOptions(options));\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;AACA;;;AAKO,SAAS,oBAAoB,OAAO;IACvC,OAAO,CAAA,GAAA,oSAAA,CAAA,kBAAe,AAAD,EAAE,CAAA,GAAA,qSAAA,CAAA,+BAA4B,AAAD,EAAE;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40connectrpc%2Bconnect-node%401._90f7088c2e6395363396c6520b160400/node_modules/%40connectrpc/connect-node/dist/esm/connect-transport.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { createTransport } from \"@connectrpc/connect/protocol-connect\";\nimport { validateNodeTransportOptions } from \"./node-transport-options.js\";\n/**\n * Create a Transport for the Connect protocol using the Node.js `http`, `http2`,\n * or `http2` module.\n */\nexport function createConnectTransport(options) {\n    return createTransport(validateNodeTransportOptions(options));\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;AACA;;;AAKO,SAAS,uBAAuB,OAAO;IAC1C,OAAO,CAAA,GAAA,uSAAA,CAAA,kBAAe,AAAD,EAAE,CAAA,GAAA,qSAAA,CAAA,+BAA4B,AAAD,EAAE;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40connectrpc%2Bconnect-node%401._90f7088c2e6395363396c6520b160400/node_modules/%40connectrpc/connect-node/dist/esm/node-universal-handler.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __asyncValues = (this && this.__asyncValues) || function (o) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var m = o[Symbol.asyncIterator], i;\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n};\nvar __await = (this && this.__await) || function (v) { return this instanceof __await ? (this.v = v, this) : new __await(v); }\nvar __asyncGenerator = (this && this.__asyncGenerator) || function (thisArg, _arguments, generator) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n    function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n    function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n    function fulfill(value) { resume(\"next\", value); }\n    function reject(value) { resume(\"throw\", value); }\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n};\nimport { Code, ConnectError } from \"@connectrpc/connect\";\nimport { nodeHeaderToWebHeader, webHeaderToNodeHeaders, } from \"./node-universal-header.js\";\nimport { connectErrorFromH2ResetCode, connectErrorFromNodeReason, } from \"./node-error.js\";\nexport function universalRequestFromNodeRequest(nodeRequest, ...rest) {\n    var _a, _b;\n    const nodeResponse = rest.length === 3 ? rest[0] : undefined;\n    const parsedJsonBody = rest.length === 3 ? rest[1] : rest[0];\n    const contextValues = rest.length === 3 ? rest[2] : rest[1];\n    const encrypted = \"encrypted\" in nodeRequest.socket && nodeRequest.socket.encrypted;\n    const protocol = encrypted ? \"https\" : \"http\";\n    const authority = \"authority\" in nodeRequest\n        ? nodeRequest.authority\n        : nodeRequest.headers.host;\n    const pathname = (_a = nodeRequest.url) !== null && _a !== void 0 ? _a : \"\";\n    if (authority === undefined) {\n        throw new ConnectError(\"unable to determine request authority from Node.js server request\", Code.Internal);\n    }\n    const body = parsedJsonBody !== undefined\n        ? parsedJsonBody\n        : asyncIterableFromNodeServerRequest(nodeRequest);\n    const abortController = new AbortController();\n    if (\"stream\" in nodeRequest) {\n        // HTTP/2 has error codes we want to honor\n        nodeRequest.once(\"close\", () => {\n            const err = connectErrorFromH2ResetCode(nodeRequest.stream.rstCode);\n            if (err !== undefined) {\n                abortController.abort(err);\n            }\n            else {\n                abortController.abort();\n            }\n        });\n    }\n    else {\n        // HTTP/1.1 does not have error codes, but Node.js has ECONNRESET\n        const nodeResponsOrRequest = nodeResponse !== null && nodeResponse !== void 0 ? nodeResponse : nodeRequest;\n        const onH1Error = (e) => {\n            nodeRequest.off(\"error\", onH1Error);\n            nodeResponsOrRequest.off(\"close\", onH1Close);\n            abortController.abort(connectErrorFromNodeReason(e));\n        };\n        const onH1Close = () => {\n            nodeRequest.off(\"error\", onH1Error);\n            nodeResponsOrRequest.off(\"close\", onH1Close);\n            // When subscribed to the response, this can get called before \"error\"\n            abortController.abort(nodeRequest.errored\n                ? connectErrorFromNodeReason(nodeRequest.errored)\n                : undefined);\n        };\n        nodeRequest.once(\"error\", onH1Error);\n        // Node emits close on the request as soon as all data is read.\n        // We instead subscribe to the response (if available)\n        //\n        // Ref: https://github.com/nodejs/node/issues/40775\n        nodeResponsOrRequest.once(\"close\", onH1Close);\n    }\n    return {\n        httpVersion: nodeRequest.httpVersion,\n        method: (_b = nodeRequest.method) !== null && _b !== void 0 ? _b : \"\",\n        url: new URL(pathname, `${protocol}://${authority}`).toString(),\n        header: nodeHeaderToWebHeader(nodeRequest.headers),\n        body,\n        signal: abortController.signal,\n        contextValues: contextValues,\n    };\n}\n/**\n * Writes a UniversalServerResponse to a Node.js server response.\n * This function helps to implement adapters to server frameworks running\n * on Node.js. Please be careful using this function in your own code, as we\n * may have to make changes to it in the future.\n */\nexport async function universalResponseToNodeResponse(universalResponse, nodeResponse) {\n    var _a, _b, _c;\n    const it = (_a = universalResponse.body) === null || _a === void 0 ? void 0 : _a[Symbol.asyncIterator]();\n    let isWriteError = false;\n    try {\n        if (it !== undefined) {\n            let chunk = await it.next();\n            isWriteError = true;\n            // we deliberately send headers after first read, not before,\n            // because we have to give the implementation a chance to\n            // set response headers\n            nodeResponse.writeHead(universalResponse.status, webHeaderToNodeHeaders(universalResponse.header));\n            isWriteError = false;\n            for (; chunk.done !== true; chunk = await it.next()) {\n                isWriteError = true;\n                await write(nodeResponse, chunk.value);\n                if (\"flush\" in nodeResponse &&\n                    typeof nodeResponse.flush == \"function\") {\n                    // The npm package \"compression\" is an express middleware that is widely used,\n                    // for example in next.js. It uses the npm package \"compressible\" to determine\n                    // whether to apply compression to a response. Unfortunately, \"compressible\"\n                    // matches every mime type that ends with \"+json\", causing our server-streaming\n                    // RPCs to be buffered.\n                    // The package modifies the response object, and adds a flush() method, which\n                    // flushes the underlying gzip or deflate stream from the Node.js zlib module.\n                    // The method is added here:\n                    // https://github.com/expressjs/compression/blob/ad5113b98cafe1382a0ece30bb4673707ac59ce7/index.js#L70\n                    nodeResponse.flush();\n                }\n                isWriteError = false;\n            }\n        }\n        if (!nodeResponse.headersSent) {\n            nodeResponse.writeHead(universalResponse.status, webHeaderToNodeHeaders(universalResponse.header));\n        }\n        if (universalResponse.trailer) {\n            nodeResponse.addTrailers(webHeaderToNodeHeaders(universalResponse.trailer));\n        }\n        await new Promise((resolve) => {\n            // The npm package \"compression\" crashes when a callback is passed to end()\n            // https://github.com/expressjs/compression/blob/ad5113b98cafe1382a0ece30bb4673707ac59ce7/index.js#L115\n            nodeResponse.once(\"end\", resolve);\n            nodeResponse.end();\n        });\n    }\n    catch (e) {\n        // Report write errors to the handler.\n        if (isWriteError) {\n            (_b = it === null || it === void 0 ? void 0 : it.throw) === null || _b === void 0 ? void 0 : _b.call(it, e).catch(() => { });\n        }\n        throw connectErrorFromNodeReason(e);\n    }\n    finally {\n        (_c = it === null || it === void 0 ? void 0 : it.return) === null || _c === void 0 ? void 0 : _c.call(it).catch(() => { });\n    }\n}\nfunction asyncIterableFromNodeServerRequest(request) {\n    return __asyncGenerator(this, arguments, function* asyncIterableFromNodeServerRequest_1() {\n        var _a, e_1, _b, _c;\n        const it = request.iterator({\n            // Node.js v16 closes request and response when this option isn't disabled.\n            // When one of our handlers receives invalid data (such as an unexpected\n            // compression flag in a streaming request), we're unable to write the error\n            // response.\n            // Later major versions have a more sensible behavior - we can revert this\n            // workaround once we stop supporting v16.\n            destroyOnReturn: false,\n        });\n        try {\n            for (var _d = true, it_1 = __asyncValues(it), it_1_1; it_1_1 = yield __await(it_1.next()), _a = it_1_1.done, !_a; _d = true) {\n                _c = it_1_1.value;\n                _d = false;\n                const chunk = _c;\n                yield yield __await(chunk);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = it_1.return)) yield __await(_b.call(it_1));\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    });\n}\nfunction write(stream, data) {\n    return new Promise((resolve, reject) => {\n        if (stream.errored) {\n            return error(stream.errored);\n        }\n        stream.once(\"error\", error);\n        stream.once(\"drain\", drain);\n        // flushed == false: the stream wishes for the calling code to wait for\n        // the 'drain' event to be emitted before continuing to write additional\n        // data.\n        const flushed = stream.write(data, \"binary\", function (err) {\n            if (err && !flushed) {\n                // We are never getting a \"drain\" nor an \"error\" event if the stream\n                // has already ended (ERR_STREAM_WRITE_AFTER_END), so we have to\n                // resolve our promise in this callback.\n                error(err);\n                // However, once we do that (and remove our event listeners), we _do_\n                // get an \"error\" event, which ends up as an uncaught exception.\n                // We silence this error specifically with the following listener.\n                // All of this seems very fragile.\n                stream.once(\"error\", () => {\n                    //\n                });\n            }\n        });\n        if (flushed) {\n            drain();\n        }\n        function error(err) {\n            stream.off(\"error\", error);\n            stream.off(\"drain\", drain);\n            reject(err);\n        }\n        function drain() {\n            stream.off(\"error\", error);\n            stream.off(\"drain\", drain);\n            resolve();\n        }\n    });\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;AAqBjC;AAAA;AACA;AACA;AAtBA,IAAI,gBAAgB,AAAC,IAAI,IAAI,IAAI,CAAC,aAAa,IAAK,SAAU,CAAC;IAC3D,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,CAAC,CAAC,OAAO,aAAa,CAAC,EAAE;IACjC,OAAO,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,aAAa,aAAa,SAAS,KAAK,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,WAAW,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG,CAAC;;IAC/M,SAAS,KAAK,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,SAAU,CAAC;YAAI,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;gBAAI,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,OAAO,SAAS,QAAQ,EAAE,IAAI,EAAE,EAAE,KAAK;YAAG;QAAI;IAAG;IAC/J,SAAS,OAAO,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QAAI,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;YAAI,QAAQ;gBAAE,OAAO;gBAAG,MAAM;YAAE;QAAI,GAAG;IAAS;AAC/H;AACA,IAAI,UAAU,AAAC,IAAI,IAAI,IAAI,CAAC,OAAO,IAAK,SAAU,CAAC;IAAI,OAAO,IAAI,YAAY,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,QAAQ;AAAI;AAC7H,IAAI,mBAAmB,AAAC,IAAI,IAAI,IAAI,CAAC,gBAAgB,IAAK,SAAU,OAAO,EAAE,UAAU,EAAE,SAAS;IAC9F,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,GAAG,GAAG,IAAI,EAAE;IAC7D,OAAO,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,UAAU,cAAc,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IACjI,SAAS,YAAY,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG;QAAS;IAAG;IAC9F,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,IAAI,CAAC,CAAC,EAAE,EAAE;YAAE,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;gBAAI,OAAO,IAAI,QAAQ,SAAU,CAAC,EAAE,CAAC;oBAAI,EAAE,IAAI,CAAC;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE,IAAI,KAAK,OAAO,GAAG;gBAAI;YAAI;YAAG,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE;QAAG;IAAE;IACvK,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI;YAAE,KAAK,CAAC,CAAC,EAAE,CAAC;QAAK,EAAE,OAAO,GAAG;YAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAAI;IAAE;IACjF,SAAS,KAAK,CAAC;QAAI,EAAE,KAAK,YAAY,UAAU,QAAQ,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,UAAU,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IAAI;IACvH,SAAS,QAAQ,KAAK;QAAI,OAAO,QAAQ;IAAQ;IACjD,SAAS,OAAO,KAAK;QAAI,OAAO,SAAS;IAAQ;IACjD,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IAAG;AACrF;;;;AAIO,SAAS,gCAAgC,WAAW,EAAE,GAAG,IAAI;IAChE,IAAI,IAAI;IACR,MAAM,eAAe,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,EAAE,GAAG;IACnD,MAAM,iBAAiB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;IAC5D,MAAM,gBAAgB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;IAC3D,MAAM,YAAY,eAAe,YAAY,MAAM,IAAI,YAAY,MAAM,CAAC,SAAS;IACnF,MAAM,WAAW,YAAY,UAAU;IACvC,MAAM,YAAY,eAAe,cAC3B,YAAY,SAAS,GACrB,YAAY,OAAO,CAAC,IAAI;IAC9B,MAAM,WAAW,CAAC,KAAK,YAAY,GAAG,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IACzE,IAAI,cAAc,WAAW;QACzB,MAAM,IAAI,uRAAA,CAAA,eAAY,CAAC,qEAAqE,2QAAA,CAAA,OAAI,CAAC,QAAQ;IAC7G;IACA,MAAM,OAAO,mBAAmB,YAC1B,iBACA,mCAAmC;IACzC,MAAM,kBAAkB,IAAI;IAC5B,IAAI,YAAY,aAAa;QACzB,0CAA0C;QAC1C,YAAY,IAAI,CAAC,SAAS;YACtB,MAAM,MAAM,CAAA,GAAA,sRAAA,CAAA,8BAA2B,AAAD,EAAE,YAAY,MAAM,CAAC,OAAO;YAClE,IAAI,QAAQ,WAAW;gBACnB,gBAAgB,KAAK,CAAC;YAC1B,OACK;gBACD,gBAAgB,KAAK;YACzB;QACJ;IACJ,OACK;QACD,iEAAiE;QACjE,MAAM,uBAAuB,iBAAiB,QAAQ,iBAAiB,KAAK,IAAI,eAAe;QAC/F,MAAM,YAAY,CAAC;YACf,YAAY,GAAG,CAAC,SAAS;YACzB,qBAAqB,GAAG,CAAC,SAAS;YAClC,gBAAgB,KAAK,CAAC,CAAA,GAAA,sRAAA,CAAA,6BAA0B,AAAD,EAAE;QACrD;QACA,MAAM,YAAY;YACd,YAAY,GAAG,CAAC,SAAS;YACzB,qBAAqB,GAAG,CAAC,SAAS;YAClC,sEAAsE;YACtE,gBAAgB,KAAK,CAAC,YAAY,OAAO,GACnC,CAAA,GAAA,sRAAA,CAAA,6BAA0B,AAAD,EAAE,YAAY,OAAO,IAC9C;QACV;QACA,YAAY,IAAI,CAAC,SAAS;QAC1B,+DAA+D;QAC/D,sDAAsD;QACtD,EAAE;QACF,mDAAmD;QACnD,qBAAqB,IAAI,CAAC,SAAS;IACvC;IACA,OAAO;QACH,aAAa,YAAY,WAAW;QACpC,QAAQ,CAAC,KAAK,YAAY,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QACnE,KAAK,IAAI,IAAI,UAAU,GAAG,SAAS,GAAG,EAAE,WAAW,EAAE,QAAQ;QAC7D,QAAQ,CAAA,GAAA,oSAAA,CAAA,wBAAqB,AAAD,EAAE,YAAY,OAAO;QACjD;QACA,QAAQ,gBAAgB,MAAM;QAC9B,eAAe;IACnB;AACJ;AAOO,eAAe,gCAAgC,iBAAiB,EAAE,YAAY;IACjF,IAAI,IAAI,IAAI;IACZ,MAAM,KAAK,CAAC,KAAK,kBAAkB,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,OAAO,aAAa,CAAC;IACtG,IAAI,eAAe;IACnB,IAAI;QACA,IAAI,OAAO,WAAW;YAClB,IAAI,QAAQ,MAAM,GAAG,IAAI;YACzB,eAAe;YACf,6DAA6D;YAC7D,yDAAyD;YACzD,uBAAuB;YACvB,aAAa,SAAS,CAAC,kBAAkB,MAAM,EAAE,CAAA,GAAA,oSAAA,CAAA,yBAAsB,AAAD,EAAE,kBAAkB,MAAM;YAChG,eAAe;YACf,MAAO,MAAM,IAAI,KAAK,MAAM,QAAQ,MAAM,GAAG,IAAI,GAAI;gBACjD,eAAe;gBACf,MAAM,MAAM,cAAc,MAAM,KAAK;gBACrC,IAAI,WAAW,gBACX,OAAO,aAAa,KAAK,IAAI,YAAY;oBACzC,8EAA8E;oBAC9E,8EAA8E;oBAC9E,4EAA4E;oBAC5E,+EAA+E;oBAC/E,uBAAuB;oBACvB,6EAA6E;oBAC7E,8EAA8E;oBAC9E,4BAA4B;oBAC5B,sGAAsG;oBACtG,aAAa,KAAK;gBACtB;gBACA,eAAe;YACnB;QACJ;QACA,IAAI,CAAC,aAAa,WAAW,EAAE;YAC3B,aAAa,SAAS,CAAC,kBAAkB,MAAM,EAAE,CAAA,GAAA,oSAAA,CAAA,yBAAsB,AAAD,EAAE,kBAAkB,MAAM;QACpG;QACA,IAAI,kBAAkB,OAAO,EAAE;YAC3B,aAAa,WAAW,CAAC,CAAA,GAAA,oSAAA,CAAA,yBAAsB,AAAD,EAAE,kBAAkB,OAAO;QAC7E;QACA,MAAM,IAAI,QAAQ,CAAC;YACf,2EAA2E;YAC3E,uGAAuG;YACvG,aAAa,IAAI,CAAC,OAAO;YACzB,aAAa,GAAG;QACpB;IACJ,EACA,OAAO,GAAG;QACN,sCAAsC;QACtC,IAAI,cAAc;YACd,CAAC,KAAK,OAAO,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,KAAQ;QAC9H;QACA,MAAM,CAAA,GAAA,sRAAA,CAAA,6BAA0B,AAAD,EAAE;IACrC,SACQ;QACJ,CAAC,KAAK,OAAO,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,CAAC,KAAQ;IAC5H;AACJ;AACA,SAAS,mCAAmC,OAAO;IAC/C,OAAO,iBAAiB,IAAI,EAAE,WAAW,UAAU;QAC/C,IAAI,IAAI,KAAK,IAAI;QACjB,MAAM,KAAK,QAAQ,QAAQ,CAAC;YACxB,2EAA2E;YAC3E,wEAAwE;YACxE,4EAA4E;YAC5E,YAAY;YACZ,0EAA0E;YAC1E,0CAA0C;YAC1C,iBAAiB;QACrB;QACA,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,OAAO,cAAc,KAAK,QAAQ,SAAS,MAAM,QAAQ,KAAK,IAAI,KAAK,KAAK,OAAO,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBACzH,KAAK,OAAO,KAAK;gBACjB,KAAK;gBACL,MAAM,QAAQ;gBACd,MAAM,MAAM,QAAQ;YACxB;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,KAAK,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;YAChE,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;IACJ;AACJ;AACA,SAAS,MAAM,MAAM,EAAE,IAAI;IACvB,OAAO,IAAI,QAAQ,CAAC,SAAS;QACzB,IAAI,OAAO,OAAO,EAAE;YAChB,OAAO,MAAM,OAAO,OAAO;QAC/B;QACA,OAAO,IAAI,CAAC,SAAS;QACrB,OAAO,IAAI,CAAC,SAAS;QACrB,uEAAuE;QACvE,wEAAwE;QACxE,QAAQ;QACR,MAAM,UAAU,OAAO,KAAK,CAAC,MAAM,UAAU,SAAU,GAAG;YACtD,IAAI,OAAO,CAAC,SAAS;gBACjB,oEAAoE;gBACpE,gEAAgE;gBAChE,wCAAwC;gBACxC,MAAM;gBACN,qEAAqE;gBACrE,gEAAgE;gBAChE,kEAAkE;gBAClE,kCAAkC;gBAClC,OAAO,IAAI,CAAC,SAAS;gBACjB,EAAE;gBACN;YACJ;QACJ;QACA,IAAI,SAAS;YACT;QACJ;QACA,SAAS,MAAM,GAAG;YACd,OAAO,GAAG,CAAC,SAAS;YACpB,OAAO,GAAG,CAAC,SAAS;YACpB,OAAO;QACX;QACA,SAAS;YACL,OAAO,GAAG,CAAC,SAAS;YACpB,OAAO,GAAG,CAAC,SAAS;YACpB;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1643, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40connectrpc%2Bconnect-node%401._90f7088c2e6395363396c6520b160400/node_modules/%40connectrpc/connect-node/dist/esm/connect-node-adapter.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { Code, ConnectError, createConnectRouter } from \"@connectrpc/connect\";\nimport { uResponseNotFound } from \"@connectrpc/connect/protocol\";\nimport { universalRequestFromNodeRequest, universalResponseToNodeResponse, } from \"./node-universal-handler.js\";\nimport { compressionBrotli, compressionGzip } from \"./compression.js\";\n/**\n * Create a Node.js request handler from a ConnectRouter.\n *\n * The returned function is compatible with http.RequestListener and its equivalent for http2.\n */\nexport function connectNodeAdapter(options) {\n    var _a;\n    if (options.acceptCompression === undefined) {\n        options.acceptCompression = [compressionGzip, compressionBrotli];\n    }\n    const router = createConnectRouter(options);\n    options.routes(router);\n    const prefix = (_a = options.requestPathPrefix) !== null && _a !== void 0 ? _a : \"\";\n    const paths = new Map();\n    for (const uHandler of router.handlers) {\n        paths.set(prefix + uHandler.requestPath, uHandler);\n    }\n    return function nodeRequestHandler(req, res) {\n        var _a, _b, _c, _d;\n        // Strip the query parameter when matching paths.\n        const uHandler = paths.get((_b = (_a = req.url) === null || _a === void 0 ? void 0 : _a.split(\"?\", 2)[0]) !== null && _b !== void 0 ? _b : \"\");\n        if (!uHandler) {\n            ((_c = options.fallback) !== null && _c !== void 0 ? _c : fallback)(req, res);\n            return;\n        }\n        const uReq = universalRequestFromNodeRequest(req, res, undefined, (_d = options.contextValues) === null || _d === void 0 ? void 0 : _d.call(options, req));\n        uHandler(uReq)\n            .then((uRes) => universalResponseToNodeResponse(uRes, res))\n            .catch((reason) => {\n            if (ConnectError.from(reason).code == Code.Aborted) {\n                return;\n            }\n            // eslint-disable-next-line no-console\n            console.error(`handler for rpc ${uHandler.method.name} of ${uHandler.service.typeName} failed`, reason);\n        });\n    };\n}\nconst fallback = (request, response) => {\n    response.writeHead(uResponseNotFound.status);\n    response.end();\n};\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;AAAA;AAAA;AACA;AACA;AACA;;;;;AAMO,SAAS,mBAAmB,OAAO;IACtC,IAAI;IACJ,IAAI,QAAQ,iBAAiB,KAAK,WAAW;QACzC,QAAQ,iBAAiB,GAAG;YAAC,oRAAA,CAAA,kBAAe;YAAE,oRAAA,CAAA,oBAAiB;SAAC;IACpE;IACA,MAAM,SAAS,CAAA,GAAA,6QAAA,CAAA,sBAAmB,AAAD,EAAE;IACnC,QAAQ,MAAM,CAAC;IACf,MAAM,SAAS,CAAC,KAAK,QAAQ,iBAAiB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IACjF,MAAM,QAAQ,IAAI;IAClB,KAAK,MAAM,YAAY,OAAO,QAAQ,CAAE;QACpC,MAAM,GAAG,CAAC,SAAS,SAAS,WAAW,EAAE;IAC7C;IACA,OAAO,SAAS,mBAAmB,GAAG,EAAE,GAAG;QACvC,IAAI,IAAI,IAAI,IAAI;QAChB,iDAAiD;QACjD,MAAM,WAAW,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,GAAG,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAC3I,IAAI,CAAC,UAAU;YACX,CAAC,CAAC,KAAK,QAAQ,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,QAAQ,EAAE,KAAK;YACzE;QACJ;QACA,MAAM,OAAO,CAAA,GAAA,qSAAA,CAAA,kCAA+B,AAAD,EAAE,KAAK,KAAK,WAAW,CAAC,KAAK,QAAQ,aAAa,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS;QACrJ,SAAS,MACJ,IAAI,CAAC,CAAC,OAAS,CAAA,GAAA,qSAAA,CAAA,kCAA+B,AAAD,EAAE,MAAM,MACrD,KAAK,CAAC,CAAC;YACR,IAAI,uRAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,2QAAA,CAAA,OAAI,CAAC,OAAO,EAAE;gBAChD;YACJ;YACA,sCAAsC;YACtC,QAAQ,KAAK,CAAC,CAAC,gBAAgB,EAAE,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;QACpG;IACJ;AACJ;AACA,MAAM,WAAW,CAAC,SAAS;IACvB,SAAS,SAAS,CAAC,4RAAA,CAAA,oBAAiB,CAAC,MAAM;IAC3C,SAAS,GAAG;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1712, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40connectrpc%2Bconnect-node%401._90f7088c2e6395363396c6520b160400/node_modules/%40connectrpc/connect-node/dist/esm/index.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n// Polyfill the Headers API for Node versions < 18\nimport \"./node-headers-polyfill.js\";\nexport { createGrpcWebTransport } from \"./grpc-web-transport.js\";\nexport { createGrpcTransport } from \"./grpc-transport.js\";\nexport { createConnectTransport } from \"./connect-transport.js\";\nexport { compressionBrotli, compressionGzip } from \"./compression.js\";\nexport { connectNodeAdapter } from \"./connect-node-adapter.js\";\nexport { universalRequestFromNodeRequest, universalResponseToNodeResponse, } from \"./node-universal-handler.js\";\nexport { createNodeHttpClient } from \"./node-universal-client.js\";\nexport { Http2SessionManager } from \"./http2-session-manager.js\";\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,kDAAkD;;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": [0], "debugId": null}}]}