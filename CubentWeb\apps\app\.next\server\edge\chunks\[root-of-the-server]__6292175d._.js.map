{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/security/middleware.ts"], "sourcesContent": ["import {\n  type NoseconeOptions,\n  defaults,\n  withVercelToolbar,\n} from '@nosecone/next';\nexport { createMiddleware as noseconeMiddleware } from '@nosecone/next';\n\n// Nosecone security headers configuration\n// https://docs.arcjet.com/nosecone/quick-start\nexport const noseconeOptions: NoseconeOptions = {\n  ...defaults,\n  // Content Security Policy (CSP) is disabled by default because the values\n  // depend on which Next Forge features are enabled. See\n  // https://www.next-forge.com/packages/security/headers for guidance on how\n  // to configure it.\n  contentSecurityPolicy: false,\n  // Relax COEP to allow Stripe and other third-party services to load\n  crossOriginEmbedderPolicy: false,\n};\n\nexport const noseconeOptionsWithToolbar: NoseconeOptions =\n  withVercelToolbar(noseconeOptions);\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;;;AASO,MAAM,kBAAmC;IAC9C,GAAG,gRAAA,CAAA,WAAQ;IACX,0EAA0E;IAC1E,uDAAuD;IACvD,2EAA2E;IAC3E,mBAAmB;IACnB,uBAAuB;IACvB,oEAAoE;IACpE,2BAA2B;AAC7B;AAEO,MAAM,6BACX,CAAA,GAAA,2MAAA,CAAA,oBAAiB,AAAD,EAAE"}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/analytics/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    client: {\n      NEXT_PUBLIC_POSTHOG_KEY: z.string().startsWith('phc_').optional(),\n      NEXT_PUBLIC_POSTHOG_HOST: z.string().url().optional(),\n      NEXT_PUBLIC_GA_MEASUREMENT_ID: z.string().startsWith('G-').optional(),\n    },\n    runtimeEnv: {\n      NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,\n      NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,\n      NEXT_PUBLIC_GA_MEASUREMENT_ID: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,yBAAyB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC,QAAQ,QAAQ;YAC/D,0BAA0B,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;YACnD,+BAA+B,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,QAAQ;QACrE;QACA,YAAY;YACV,uBAAuB;YACvB,wBAAwB;YACxB,6BAA6B;QAC/B;IACF"}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/auth/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      CLERK_SECRET_KEY: z.string().startsWith('sk_'),\n      CLERK_WEBHOOK_SECRET: z.string().startsWith('whsec_').optional(),\n    },\n    client: {\n      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: z.string().startsWith('pk_'),\n      NEXT_PUBLIC_CLERK_DOMAIN: z.string().optional(),\n      NEXT_PUBLIC_CLERK_SIGN_IN_URL: z.string().startsWith('/'),\n      NEXT_PUBLIC_CLERK_SIGN_UP_URL: z.string().startsWith('/'),\n      NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL: z.string().startsWith('/'),\n      NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL: z.string().startsWith('/'),\n    },\n    runtimeEnv: {\n      CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY,\n      CLERK_WEBHOOK_SECRET: process.env.CLERK_WEBHOOK_SECRET,\n      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:\n        process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY,\n      NEXT_PUBLIC_CLERK_DOMAIN: process.env.NEXT_PUBLIC_CLERK_DOMAIN,\n      NEXT_PUBLIC_CLERK_SIGN_IN_URL: process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL,\n      NEXT_PUBLIC_CLERK_SIGN_UP_URL: process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL,\n      NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL:\n        process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL,\n      NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL:\n        process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,kBAAkB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC;YACxC,sBAAsB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC,UAAU,QAAQ;QAChE;QACA,QAAQ;YACN,mCAAmC,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC;YACzD,0BAA0B,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC7C,+BAA+B,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC;YACrD,+BAA+B,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC;YACrD,qCAAqC,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC;YAC3D,qCAAqC,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC;QAC7D;QACA,YAAY;YACV,kBAAkB,QAAQ,GAAG,CAAC,gBAAgB;YAC9C,sBAAsB,QAAQ,GAAG,CAAC,oBAAoB;YACtD,iCAAiC;YAEjC,wBAAwB;YACxB,6BAA6B;YAC7B,6BAA6B;YAC7B,mCAAmC;YAEnC,mCAAmC;QAErC;IACF"}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/collaboration/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      LIVEBLOCKS_SECRET: z.string().startsWith('sk_').optional(),\n    },\n    runtimeEnv: {\n      LIVEBLOCKS_SECRET: process.env.LIVEBLOCKS_SECRET,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,mBAAmB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC,OAAO,QAAQ;QAC1D;QACA,YAAY;YACV,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAClD;IACF"}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/database/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      DATABASE_URL: z.string().url(),\n    },\n    runtimeEnv: {\n      DATABASE_URL: process.env.DATABASE_URL,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,cAAc,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;QAC9B;QACA,YAAY;YACV,cAAc,QAAQ,GAAG,CAAC,YAAY;QACxC;IACF"}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/email/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      RESEND_FROM: z.string().email(),\n      RESEND_TOKEN: z.string().startsWith('re_'),\n    },\n    runtimeEnv: {\n      RESEND_FROM: process.env.RESEND_FROM,\n      RESEND_TOKEN: process.env.RESEND_TOKEN,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,aAAa,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK;YAC7B,cAAc,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC;QACtC;QACA,YAAY;YACV,aAAa,QAAQ,GAAG,CAAC,WAAW;YACpC,cAAc,QAAQ,GAAG,CAAC,YAAY;QACxC;IACF"}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/feature-flags/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      FLAGS_SECRET: z.string().optional(),\n    },\n    runtimeEnv: {\n      FLAGS_SECRET: process.env.FLAGS_SECRET,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,cAAc,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC;QACA,YAAY;YACV,cAAc,QAAQ,GAAG,CAAC,YAAY;QACxC;IACF"}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/next-config/keys.ts"], "sourcesContent": ["import { vercel } from '@t3-oss/env-core/presets-zod';\nimport { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    extends: [vercel()],\n    server: {\n      ANALYZE: z.string().optional(),\n\n      // Added by Vercel\n      NEXT_RUNTIME: z.enum(['nodejs', 'edge']).optional(),\n    },\n    client: {\n      NEXT_PUBLIC_APP_URL: z.string().url(),\n      NEXT_PUBLIC_WEB_URL: z.string().url(),\n      NEXT_PUBLIC_API_URL: z.string().url().optional(),\n      NEXT_PUBLIC_DOCS_URL: z.string().url().optional(),\n    },\n    runtimeEnv: {\n      ANALYZE: process.env.ANALYZE,\n      NEXT_RUNTIME: process.env.NEXT_RUNTIME,\n      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,\n      NEXT_PUBLIC_WEB_URL: process.env.NEXT_PUBLIC_WEB_URL,\n      NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,\n      NEXT_PUBLIC_DOCS_URL: process.env.NEXT_PUBLIC_DOCS_URL,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;YAAC,CAAA,GAAA,yRAAA,CAAA,SAAM,AAAD;SAAI;QACnB,QAAQ;YACN,SAAS,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAE5B,kBAAkB;YAClB,cAAc,2OAAA,CAAA,IAAC,CAAC,IAAI,CAAC;gBAAC;gBAAU;aAAO,EAAE,QAAQ;QACnD;QACA,QAAQ;YACN,qBAAqB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;YACnC,qBAAqB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;YACnC,qBAAqB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;YAC9C,sBAAsB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;QACjD;QACA,YAAY;YACV,SAAS,QAAQ,GAAG,CAAC,OAAO;YAC5B,YAAY;YACZ,mBAAmB;YACnB,mBAAmB;YACnB,qBAAqB,QAAQ,GAAG,CAAC,mBAAmB;YACpD,oBAAoB;QACtB;IACF"}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/notifications/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      KNOCK_SECRET_API_KEY: z.string().optional(),\n    },\n    client: {\n      NEXT_PUBLIC_KNOCK_API_KEY: z.string().optional(),\n      NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID: z.string().optional(),\n    },\n    runtimeEnv: {\n      NEXT_PUBLIC_KNOCK_API_KEY: process.env.NEXT_PUBLIC_KNOCK_API_KEY,\n      NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID:\n        process.env.NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID,\n      KNOCK_SECRET_API_KEY: process.env.KNOCK_SECRET_API_KEY,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,sBAAsB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC3C;QACA,QAAQ;YACN,2BAA2B,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC9C,mCAAmC,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACxD;QACA,YAAY;YACV,yBAAyB;YACzB,iCAAiC;YAEjC,sBAAsB,QAAQ,GAAG,CAAC,oBAAoB;QACxD;IACF"}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/observability/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      BETTERSTACK_API_KEY: z.string().optional(),\n      BETTERSTACK_URL: z.string().optional(),\n\n      // Added by Sentry Integration, Vercel Marketplace\n      SENTRY_ORG: z.string().optional(),\n      SENTRY_PROJECT: z.string().optional(),\n    },\n    client: {\n      // Added by Sentry Integration, Vercel Marketplace\n      NEXT_PUBLIC_SENTRY_DSN: z.string().url().optional(),\n    },\n    runtimeEnv: {\n      BETTERSTACK_API_KEY: process.env.BETTERSTACK_API_KEY,\n      BETTERSTACK_URL: process.env.BETTERSTACK_URL,\n      SENTRY_ORG: process.env.SENTRY_ORG,\n      SENTRY_PROJECT: process.env.SENTRY_PROJECT,\n      NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,qBAAqB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YACxC,iBAAiB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAEpC,kDAAkD;YAClD,YAAY,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC/B,gBAAgB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACrC;QACA,QAAQ;YACN,kDAAkD;YAClD,wBAAwB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;QACnD;QACA,YAAY;YACV,qBAAqB,QAAQ,GAAG,CAAC,mBAAmB;YACpD,iBAAiB,QAAQ,GAAG,CAAC,eAAe;YAC5C,YAAY,QAAQ,GAAG,CAAC,UAAU;YAClC,gBAAgB,QAAQ,GAAG,CAAC,cAAc;YAC1C,wBAAwB,QAAQ,GAAG,CAAC,sBAAsB;QAC5D;IACF"}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/payments/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      STRIPE_SECRET_KEY: z.string().startsWith('sk_'),\n      STRIPE_WEBHOOK_SECRET: z.string().startsWith('whsec_').optional(),\n    },\n    runtimeEnv: {\n      STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,\n      STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,mBAAmB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC;YACzC,uBAAuB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC,UAAU,QAAQ;QACjE;QACA,YAAY;YACV,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;YAChD,uBAAuB,QAAQ,GAAG,CAAC,qBAAqB;QAC1D;IACF"}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/security/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      ARCJET_KEY: z.string().startsWith('ajkey_').optional(),\n    },\n    runtimeEnv: {\n      ARCJET_KEY: process.env.ARCJET_KEY,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,YAAY,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC,UAAU,QAAQ;QACtD;QACA,YAAY;YACV,YAAY,QAAQ,GAAG,CAAC,UAAU;QACpC;IACF"}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/webhooks/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      SVIX_TOKEN: z\n        .union([z.string().startsWith('sk_'), z.string().startsWith('testsk_')])\n        .optional(),\n    },\n    runtimeEnv: {\n      SVIX_TOKEN: process.env.SVIX_TOKEN,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,YAAY,2OAAA,CAAA,IAAC,CACV,KAAK,CAAC;gBAAC,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC;gBAAQ,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC;aAAW,EACtE,QAAQ;QACb;QACA,YAAY;YACV,YAAY,QAAQ,GAAG,CAAC,UAAU;QACpC;IACF"}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/app/env.ts"], "sourcesContent": ["import { keys as analytics } from '@repo/analytics/keys';\nimport { keys as auth } from '@repo/auth/keys';\nimport { keys as collaboration } from '@repo/collaboration/keys';\nimport { keys as database } from '@repo/database/keys';\nimport { keys as email } from '@repo/email/keys';\nimport { keys as flags } from '@repo/feature-flags/keys';\nimport { keys as core } from '@repo/next-config/keys';\nimport { keys as notifications } from '@repo/notifications/keys';\nimport { keys as observability } from '@repo/observability/keys';\nimport { keys as payments } from '@repo/payments/keys';\nimport { keys as security } from '@repo/security/keys';\nimport { keys as webhooks } from '@repo/webhooks/keys';\nimport { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const env = createEnv({\n  extends: [\n    auth(),\n    analytics(),\n    collaboration(),\n    core(),\n    database(),\n    email(),\n    flags(),\n    notifications(),\n    observability(),\n    payments(),\n    security(),\n    webhooks(),\n  ],\n  server: {\n    CRON_SECRET: z.string().optional(),\n  },\n  client: {},\n  runtimeEnv: {\n    CRON_SECRET: process.env.CRON_SECRET,\n  },\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;AAEO,MAAM,MAAM,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;IAC3B,SAAS;QACP,CAAA,GAAA,gIAAA,CAAA,OAAI,AAAD;QACH,CAAA,GAAA,qIAAA,CAAA,OAAS,AAAD;QACR,CAAA,GAAA,yIAAA,CAAA,OAAa,AAAD;QACZ,CAAA,GAAA,0IAAA,CAAA,OAAI,AAAD;QACH,CAAA,GAAA,oIAAA,CAAA,OAAQ,AAAD;QACP,CAAA,GAAA,iIAAA,CAAA,OAAK,AAAD;QACJ,CAAA,GAAA,4IAAA,CAAA,OAAK,AAAD;QACJ,CAAA,GAAA,yIAAA,CAAA,OAAa,AAAD;QACZ,CAAA,GAAA,yIAAA,CAAA,OAAa,AAAD;QACZ,CAAA,GAAA,oIAAA,CAAA,OAAQ,AAAD;QACP,CAAA,GAAA,oIAAA,CAAA,OAAQ,AAAD;QACP,CAAA,GAAA,oIAAA,CAAA,OAAQ,AAAD;KACR;IACD,QAAQ;QACN,aAAa,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAClC;IACA,QAAQ,CAAC;IACT,YAAY;QACV,aAAa,QAAQ,GAAG,CAAC,WAAW;IACtC;AACF"}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/app/middleware.ts"], "sourcesContent": ["import { authMiddleware } from '@repo/auth/middleware';\nimport {\n  noseconeMiddleware,\n  noseconeOptions,\n  noseconeOptionsWithToolbar,\n} from '@repo/security/middleware';\nimport type { NextMiddleware } from 'next/server';\nimport { env } from './env';\n\nconst securityHeaders = env.FLAGS_SECRET\n  ? noseconeMiddleware(noseconeOptionsWithToolbar)\n  : noseconeMiddleware(noseconeOptions);\n\nexport default authMiddleware(() =>\n  securityHeaders()\n) as unknown as NextMiddleware;\n\nexport const config = {\n  matcher: [\n    // Skip Next.js internals and all static files, unless found in search params\n    '/((?!_next|[^?]*\\\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',\n    // Always run for API routes\n    '/(api|trpc)(.*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAAA;AAAA;AAMA;;;;AAEA,MAAM,kBAAkB,0HAAA,CAAA,MAAG,CAAC,YAAY,GACpC,CAAA,GAAA,0UAAA,CAAA,qBAAkB,AAAD,EAAE,0JAAA,CAAA,6BAA0B,IAC7C,CAAA,GAAA,0UAAA,CAAA,qBAAkB,AAAD,EAAE,0JAAA,CAAA,kBAAe;uCAEvB,CAAA,GAAA,oVAAA,CAAA,iBAAc,AAAD,EAAE,IAC5B;AAGK,MAAM,SAAS;IACpB,SAAS;QACP,6EAA6E;QAC7E;QACA,4BAA4B;QAC5B;KACD;AACH"}}]}