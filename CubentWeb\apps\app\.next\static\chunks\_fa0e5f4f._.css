/* [project]/node_modules/.pnpm/@knocklabs+react@0.7.11_@ty_6fc524c218170b7480adccae4612663b/node_modules/@knocklabs/react/dist/index.css [app-client] (css) */
:root {
  --knock-border-style-solid: solid;
  --knock-border-style-dashed: dashed;
  --knock-transparent: transparent;
  --knock-white: #fff;
  --knock-black: #000;
  --knock-alpha-white-1: #ffffff0d;
  --knock-alpha-white-2: #ffffff1a;
  --knock-alpha-white-3: #ffffff26;
  --knock-alpha-white-4: #fff3;
  --knock-alpha-white-5: #ffffff4d;
  --knock-alpha-white-6: #fff6;
  --knock-alpha-white-7: #ffffff80;
  --knock-alpha-white-8: #fff9;
  --knock-alpha-white-9: #ffffffb3;
  --knock-alpha-white-10: #fffc;
  --knock-alpha-white-11: #ffffffe6;
  --knock-alpha-white-12: #fffffff2;
  --knock-alpha-black-1: #0000000d;
  --knock-alpha-black-2: #0000001a;
  --knock-alpha-black-3: #00000026;
  --knock-alpha-black-4: #0003;
  --knock-alpha-black-5: #0000004d;
  --knock-alpha-black-6: #0006;
  --knock-alpha-black-7: #00000080;
  --knock-alpha-black-8: #0009;
  --knock-alpha-black-9: #000000b3;
  --knock-alpha-black-10: #000c;
  --knock-alpha-black-11: #000000e6;
  --knock-alpha-black-12: #000000f2;
  --knock-rounded-0: 0px;
  --knock-rounded-1: .125rem;
  --knock-rounded-2: .25rem;
  --knock-rounded-3: .375rem;
  --knock-rounded-4: .5rem;
  --knock-rounded-5: .75rem;
  --knock-rounded-6: 1rem;
  --knock-rounded-full: 9999px;
  --knock-shadow-0: 0px 0px 0px 0px #0000;
  --knock-shadow-1: 0px 5px 2px 0px #1c202403, 0px 3px 2px 0px #1c202408, 0px 1px 1px 0px #1c20240d, 0px 0px 1px 0px #1c20240f;
  --knock-shadow-2: 0px 16px 7px 0px #1c202403, 0px 9px 6px 0px #1c202408, 0px 4px 4px 0px #1c20240d, 0px 1px 2px 0px #1c20240f;
  --knock-shadow-3: 0px 29px 12px 0px #1c202403, 0px 16px 10px 0px #1c202408, 0px 7px 7px 0px #1c20240d, 0px 2px 4px 0px #1c20240f;
  --knock-shadow-inner: 0px 5px 2px 0px #1c202403 inset, 0px 3px 2px 0px #1c202408 inset, 0px 1px 1px 0px #1c20240d inset, 0px 0px 1px 0px #1c20240f inset;
  --knock-spacing-0: 0px;
  --knock-spacing-1: .25rem;
  --knock-spacing-2: .5rem;
  --knock-spacing-3: .75rem;
  --knock-spacing-4: 1rem;
  --knock-spacing-5: 1.25rem;
  --knock-spacing-6: 1.5rem;
  --knock-spacing-7: 1.75rem;
  --knock-spacing-8: 2rem;
  --knock-spacing-9: 2.25rem;
  --knock-spacing-10: 2.5rem;
  --knock-spacing-11: 2.75rem;
  --knock-spacing-12: 3rem;
  --knock-spacing-14: 3.5rem;
  --knock-spacing-16: 4rem;
  --knock-spacing-20: 5rem;
  --knock-spacing-24: 6rem;
  --knock-spacing-28: 7rem;
  --knock-spacing-32: 8rem;
  --knock-spacing-36: 9rem;
  --knock-spacing-40: 10rem;
  --knock-spacing-44: 11rem;
  --knock-spacing-48: 12rem;
  --knock-spacing-52: 13rem;
  --knock-spacing-56: 14rem;
  --knock-spacing-60: 15rem;
  --knock-spacing-64: 16rem;
  --knock-spacing-72: 18rem;
  --knock-spacing-80: 20rem;
  --knock-spacing-96: 24rem;
  --knock-spacing-140: 35rem;
  --knock-spacing-160: 40rem;
  --knock-spacing-px: 1px;
  --knock-spacing-full: 100%;
  --knock-spacing-auto: auto;
  --knock-family-sans: Inter, -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
  --knock-family-mono: Menlo, Consolas, Monaco, Liberation Mono, Lucida Console, monospace;
  --knock-leading-0: 1rem;
  --knock-leading-1: 1rem;
  --knock-leading-2: 1.25rem;
  --knock-leading-3: 1.5rem;
  --knock-leading-4: 1.75rem;
  --knock-leading-5: 1.75rem;
  --knock-leading-6: 2rem;
  --knock-leading-7: 2.25rem;
  --knock-leading-8: 2.5rem;
  --knock-leading-9: 3.5rem;
  --knock-leading-code-0: 1rem;
  --knock-leading-code-1: 1rem;
  --knock-leading-code-2: 1.25rem;
  --knock-leading-code-3: 1.5rem;
  --knock-leading-code-4: 1.75rem;
  --knock-leading-code-5: 1.75rem;
  --knock-leading-code-6: 2rem;
  --knock-leading-code-7: 2.25rem;
  --knock-leading-code-8: 2.5rem;
  --knock-leading-code-9: 3rem;
  --knock-tracking-0: 0.25%;
  --knock-tracking-1: 0.25%;
  --knock-tracking-2: 0;
  --knock-tracking-3: 0;
  --knock-tracking-4: -0.25%;
  --knock-tracking-5: -0.5%;
  --knock-tracking-6: -0.625%;
  --knock-tracking-7: -0.75%;
  --knock-tracking-8: -1%;
  --knock-tracking-9: -2.5%;
  --knock-text-0: .6875rem;
  --knock-text-1: .75rem;
  --knock-text-2: .875rem;
  --knock-text-3: 1rem;
  --knock-text-4: 1.125rem;
  --knock-text-5: 1.25rem;
  --knock-text-6: 1.5rem;
  --knock-text-7: 1.875rem;
  --knock-text-8: 2.25rem;
  --knock-text-9: 3rem;
  --knock-text-code-0: .625rem;
  --knock-text-code-1: .688rem;
  --knock-text-code-2: .812rem;
  --knock-text-code-4: 1.062rem;
  --knock-text-code-5: 1.188rem;
  --knock-text-code-6: 1.438rem;
  --knock-text-code-7: 1.75rem;
  --knock-text-code-8: 2.125rem;
  --knock-text-code-9: 2.875rem;
  --knock-weight-regular: 400;
  --knock-weight-medium: 500;
  --knock-weight-semi-bold: 600;
  --knock-breakpoint-sm: 640px;
  --knock-breakpoint-md: 768px;
  --knock-breakpoint-lg: 1024px;
  --knock-breakpoint-xl: 1280px;
  --knock-breakpoint-2xl: 1536px;
  --knock-zIndex-hidden: -1;
  --knock-zIndex-base: 0;
  --knock-zIndex-auto: auto;
  --knock-zIndex-dropdown: 1000;
  --knock-zIndex-sticky: 1100;
  --knock-zIndex-banner: 1200;
  --knock-zIndex-overlay: 1300;
  --knock-zIndex-modal: 1400;
  --knock-zIndex-popover: 1500;
  --knock-zIndex-skipLink: 1600;
  --knock-zIndex-toast: 1700;
  --knock-zIndex-tooltip: 1800;
}

[data-knock-color-mode="light"] {
  --knock-surface-1: #fff;
  --knock-surface-2: #f9f9f8;
  --knock-gray-1: #fcfcfd;
  --knock-gray-2: #f9f9fb;
  --knock-gray-3: #f0f0f3;
  --knock-gray-4: #e8e8ec;
  --knock-gray-5: #e0e1e6;
  --knock-gray-6: #d9d9e0;
  --knock-gray-7: #cdced6;
  --knock-gray-8: #b9bbc6;
  --knock-gray-9: #8b8d98;
  --knock-gray-10: #80838d;
  --knock-gray-11: #60646c;
  --knock-gray-12: #1c2024;
  --knock-beige-1: #fdfdfc;
  --knock-beige-2: #f9f9f8;
  --knock-beige-3: #f1f0ef;
  --knock-beige-4: #e9e8e6;
  --knock-beige-5: #e2e1de;
  --knock-beige-6: #dad9d6;
  --knock-beige-7: #cfceca;
  --knock-beige-8: #bcbbb5;
  --knock-beige-9: #8d8d86;
  --knock-beige-10: #82827c;
  --knock-beige-11: #63635e;
  --knock-beige-12: #21201c;
  --knock-orange-1: #fffcfc;
  --knock-orange-2: #fff8f7;
  --knock-orange-3: #feebe7;
  --knock-orange-4: #ffdcd3;
  --knock-orange-5: #ffcdc2;
  --knock-orange-6: #fdbdaf;
  --knock-orange-7: #f5a898;
  --knock-orange-8: #ec8e7b;
  --knock-orange-9: #e54d2e;
  --knock-orange-10: #dd4425;
  --knock-orange-11: #d13415;
  --knock-orange-12: #5c271f;
  --knock-green-1: #fbfefd;
  --knock-green-2: #f4fbf7;
  --knock-green-3: #e6f7ed;
  --knock-green-4: #d6f1e3;
  --knock-green-5: #c3e9d7;
  --knock-green-6: #acdec8;
  --knock-green-7: #8bceb6;
  --knock-green-8: #56ba9f;
  --knock-green-9: #29a383;
  --knock-green-10: #26997b;
  --knock-green-11: #208368;
  --knock-green-12: #1d3b31;
  --knock-yellow-1: #fefdfb;
  --knock-yellow-2: #fefbe9;
  --knock-yellow-3: #fff7c2;
  --knock-yellow-4: #ffee9c;
  --knock-yellow-5: #fbe577;
  --knock-yellow-6: #f3d673;
  --knock-yellow-7: #e9c162;
  --knock-yellow-8: #f3d673;
  --knock-yellow-9: #ffc53d;
  --knock-yellow-10: #ffba18;
  --knock-yellow-11: #ab6400;
  --knock-yellow-12: #4f3422;
  --knock-blue-1: #fdfdfe;
  --knock-blue-2: #f7f9ff;
  --knock-blue-3: #edf2fe;
  --knock-blue-4: #e1e9ff;
  --knock-blue-5: #d2deff;
  --knock-blue-6: #c1d0ff;
  --knock-blue-7: #abbdf9;
  --knock-blue-8: #8da4ef;
  --knock-blue-9: #3e63dd;
  --knock-blue-10: #3358d4;
  --knock-blue-11: #3a5bc7;
  --knock-blue-12: #1f2d5c;
  --knock-red-1: #fffcfd;
  --knock-red-2: #fff7f8;
  --knock-red-3: #feeaed;
  --knock-red-4: #ffdce1;
  --knock-red-5: #ffced6;
  --knock-red-6: #f8bfc8;
  --knock-red-7: #efacb8;
  --knock-red-8: #e592a3;
  --knock-red-9: #e54666;
  --knock-red-10: #dc3b5d;
  --knock-red-11: #ca244d;
  --knock-red-12: #64172b;
  --knock-purple-1: #fdfcfe;
  --knock-purple-2: #faf8ff;
  --knock-purple-3: #f4f0fe;
  --knock-purple-4: #ebe4ff;
  --knock-purple-5: #e1d9ff;
  --knock-purple-6: #d4cafe;
  --knock-purple-7: #c2b5f5;
  --knock-purple-8: #aa99ec;
  --knock-purple-9: #654dc4;
  --knock-purple-10: #654dc4;
  --knock-purple-11: #6550b9;
  --knock-purple-12: #2f265f;
}

[data-knock-color-mode="dark"] {
  --knock-surface-1: #18191b;
  --knock-surface-2: #111110;
  --knock-gray-1: #111113;
  --knock-gray-2: #18191b;
  --knock-gray-3: #212225;
  --knock-gray-4: #272a2d;
  --knock-gray-5: #2e3135;
  --knock-gray-6: #363a3f;
  --knock-gray-7: #43484e;
  --knock-gray-8: #5a6169;
  --knock-gray-9: #696e77;
  --knock-gray-10: #777b84;
  --knock-gray-11: #b0b4ba;
  --knock-gray-12: #edeef0;
  --knock-beige-1: #111110;
  --knock-beige-2: #191918;
  --knock-beige-3: #222221;
  --knock-beige-4: #2a2a28;
  --knock-beige-5: #31312e;
  --knock-beige-6: #3b3a37;
  --knock-beige-7: #494844;
  --knock-beige-8: #62605b;
  --knock-beige-9: #6f6d66;
  --knock-beige-10: #7c7b74;
  --knock-beige-11: #b5b3ad;
  --knock-beige-12: #eeeeec;
  --knock-orange-1: #181111;
  --knock-orange-2: #1f1513;
  --knock-orange-3: #391714;
  --knock-orange-4: #4e1511;
  --knock-orange-5: #5e1c16;
  --knock-orange-6: #6e2920;
  --knock-orange-7: #853a2d;
  --knock-orange-8: #ac4d39;
  --knock-orange-9: #e54d2e;
  --knock-orange-10: #ec6142;
  --knock-orange-11: #ff977d;
  --knock-orange-12: #fbd3cb;
  --knock-green-1: #0d1512;
  --knock-green-2: #121c18;
  --knock-green-3: #0f2e22;
  --knock-green-4: #0b3b2c;
  --knock-green-5: #114837;
  --knock-green-6: #1b5745;
  --knock-green-7: #246854;
  --knock-green-8: #2a7e68;
  --knock-green-9: #29a383;
  --knock-green-10: #27b08b;
  --knock-green-11: #1fd8a4;
  --knock-green-12: #adf0d4;
  --knock-yellow-1: #16120c;
  --knock-yellow-2: #1d180f;
  --knock-yellow-3: #302008;
  --knock-yellow-4: #3f2700;
  --knock-yellow-5: #4d3000;
  --knock-yellow-6: #5c3d05;
  --knock-yellow-7: #714f19;
  --knock-yellow-8: #8f6424;
  --knock-yellow-9: #ffc53d;
  --knock-yellow-10: #ffd60a;
  --knock-yellow-11: #ffca16;
  --knock-yellow-12: #ffe7b3;
  --knock-blue-1: #11131f;
  --knock-blue-2: #141726;
  --knock-blue-3: #182449;
  --knock-blue-4: #1d2e62;
  --knock-blue-5: #253974;
  --knock-blue-6: #304384;
  --knock-blue-7: #3a4f97;
  --knock-blue-8: #435db1;
  --knock-blue-9: #3e63dd;
  --knock-blue-10: #5472e4;
  --knock-blue-11: #9eb1ff;
  --knock-blue-12: #d6e1ff;
  --knock-red-1: #191113;
  --knock-red-2: #1e1517;
  --knock-red-3: #3a141e;
  --knock-red-4: #4e1325;
  --knock-red-5: #5e1a2e;
  --knock-red-6: #6f2539;
  --knock-red-7: #883447;
  --knock-red-8: #b3445a;
  --knock-red-9: #e54666;
  --knock-red-10: #ec5a72;
  --knock-red-11: #ff949d;
  --knock-red-12: #fed2e1;
  --knock-purple-1: #14121f;
  --knock-purple-2: #1b1525;
  --knock-purple-3: #291f43;
  --knock-purple-4: #33255b;
  --knock-purple-5: #3c2e69;
  --knock-purple-6: #473876;
  --knock-purple-7: #56468b;
  --knock-purple-8: #6958ad;
  --knock-purple-9: #6e56cf;
  --knock-purple-10: #7d66d9;
  --knock-purple-11: #baa7ff;
  --knock-purple-12: #e2ddfe;
}

[data-knock-color-mode="light"] {
  --knock-guide-accent: var(--knock-gray-12);
  --knock-guide-accent-light: var(--knock-gray-4);
  --knock-guide-accent-dark: var(--knock-gray-12);
  --knock-guide-secondary: var(--knock-gray-9);
  --knock-guide-secondary-light: var(--knock-gray-3);
  --knock-guide-secondary-dark: var(--knock-gray-11);
  --knock-guide-content: var(--knock-gray-12);
  --knock-guide-content-light: var(--knock-gray-11);
  --knock-guide-content-disabled: var(--knock-gray-9);
  --knock-guide-content-contrast: var(--knock-white);
  --knock-guide-border: var(--knock-gray-5);
  --knock-guide-border-light: var(--knock-gray-4);
  --knock-guide-border-dark: var(--knock-gray-7);
  --knock-guide-surface: var(--knock-white);
  --knock-guide-surface-2: var(--knock-gray-2);
}

[data-knock-color-mode="dark"] {
  --knock-guide-surface: var(--knock-gray-2);
  --knock-guide-surface-2: var(--knock-gray-1);
  --knock-guide-accent: var(--knock-white);
  --knock-guide-accent-light: var(--knock-gray-4);
  --knock-guide-accent-dark: var(--knock-gray-12);
  --knock-guide-secondary: var(--knock-gray-9);
  --knock-guide-secondary-light: var(--knock-gray-3);
  --knock-guide-secondary-dark: var(--knock-gray-11);
  --knock-guide-content: var(--knock-gray-12);
  --knock-guide-content-light: var(--knock-gray-11);
  --knock-guide-content-disabled: var(--knock-gray-9);
  --knock-guide-content-contrast: var(--knock-gray-1);
  --knock-guide-border: var(--knock-gray-5);
  --knock-guide-border-light: var(--knock-gray-4);
  --knock-guide-border-dark: var(--knock-gray-7);
}

:root {
  --rnf-font-size-xs: .75rem;
  --rnf-font-size-sm: .875rem;
  --rnf-font-size-md: 1rem;
  --rnf-font-size-lg: 1.125rem;
  --rnf-font-size-xl: 1.266rem;
  --rnf-font-size-2xl: 1.5rem;
  --rnf-font-size-3xl: 1.75rem;
  --rnf-spacing-0: 0;
  --rnf-spacing-1: 4px;
  --rnf-spacing-2: 8px;
  --rnf-spacing-3: 12px;
  --rnf-spacing-4: 16px;
  --rnf-spacing-5: 20px;
  --rnf-spacing-6: 24px;
  --rnf-spacing-7: 32px;
  --rnf-spacing-8: 42px;
  --rnf-font-weight-normal: 400;
  --rnf-font-weight-medium: 500;
  --rnf-font-weight-semibold: 600;
  --rnf-font-weight-bold: 700;
  --rnf-font-family-sanserif: Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Ubuntu, "Helvetica Neue", sans-serif;
  --rnf-border-radius-sm: 2px;
  --rnf-border-radius-md: 4px;
  --rnf-border-radius-lg: 8px;
  --rnf-shadow-sm: 0px 5px 10px #0000001f;
  --rnf-shadow-md: 0px 8px 30px #0000003d;
  --rnf-color-white: #fff;
  --rnf-color-white-a-75: #ffffffbf;
  --rnf-color-black: #000;
  --rnf-color-gray-900: #1a1f36;
  --rnf-color-gray-800: #3c4257;
  --rnf-color-gray-700: #3c4257;
  --rnf-color-gray-600: #515669;
  --rnf-color-gray-500: #697386;
  --rnf-color-gray-400: #9ea0aa;
  --rnf-color-gray-300: #a5acb8;
  --rnf-color-gray-200: #dddee1;
  --rnf-color-gray-100: #e4e8ee;
  --rnf-color-brand-500: #e95744;
  --rnf-color-brand-700: #e4321b;
  --rnf-color-brand-900: #891e10;
  --rnf-unread-badge-bg-color: #dd514c;
  --rnf-avatar-bg-color: #ef8476;
  --rnf-message-cell-unread-dot-bg-color: #f4ada4;
  --rnf-message-cell-hover-bg-color: #f1f6fc;
  --rnf-button-padding-x: 8px;
  --rnf-button-padding-y: 4px;
  --rnf-button-border-radius: 4px;
  --rnf-button-font-weight: var(--rnf-font-weight-medium);
  --rnf-button-font-size: var(--rnf-font-size-sm);
  --rnf-button-primary-bg-color: var(--rnf-color-brand-500);
  --rnf-button-primary-hover-bg-color: var(--rnf-color-brand-700);
  --rnf-button-primary-border-color: transparent;
  --rnf-button-primary-text-color: var(--rnf-color-white);
  --rnf-button-secondary-bg-color: var(--rnf-color-white);
  --rnf-button-secondary-hover-bg-color: #dddee1;
  --rnf-button-secondary-border-color: #dddee1;
  --rnf-button-secondary-text-color: var(--rnf-color-gray-700);
}

.rnf-button {
  -webkit-user-select: none;
  user-select: none;
  white-space: nowrap;
  vertical-align: middle;
  width: auto;
  padding: var(--rnf-button-padding-y) var(--rnf-button-padding-x);
  border-radius: var(--rnf-button-border-radius);
  font-size: var(--rnf-button-font-size);
  line-height: var(--rnf-font-size-lg);
  font-weight: var(--rnf-button-font-weight);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
  border: 1px solid;
  justify-content: center;
  align-items: center;
  transition: all .1s ease-in-out;
  display: inline-flex;
}

.rnf-button--full-width {
  width: 100%;
}

.rnf-button--primary {
  background-color: var(--rnf-button-primary-bg-color);
  color: var(--rnf-button-primary-text-color);
  border-color: var(--rnf-button-primary-border-color);
}

.rnf-button--primary:hover:not(:disabled), .rnf-button--primary:active:not(:disabled) {
  background-color: var(--rnf-button-primary-hover-bg-color);
}

.rnf-button:disabled {
  opacity: .4;
  cursor: not-allowed;
}

.rnf-button--secondary {
  background-color: var(--rnf-button-secondary-bg-color);
  color: var(--rnf-button-secondary-text-color);
  border-color: var(--rnf-button-secondary-border-color);
}

.rnf-button--secondary:hover:not(:disabled), .rnf-button--secondary:active:not(:disabled) {
  background-color: var(--rnf-button-secondary-hover-bg-color);
}

.rnf-button--dark.rnf-button--secondary {
  color: var(--rnf-color-white-a-75);
  background-color: #43464c;
  border-color: #43464c;
}

.rnf-button__button-text-hidden {
  opacity: 0;
}

.rnf-button--dark.rnf-button--secondary:hover:not(:disabled), .rnf-button--dark.rnf-button--secondary:active:not(:disabled) {
  background-color: var(--rnf-color-gray-600);
}

.rnf-button-spinner {
  font-size: 1rem;
  line-height: "normal";
  align-items: center;
  display: flex;
}

.rnf-button-spinner--without-label {
  position: absolute;
}

.rnf-button-spinner--with-label {
  margin-right: 6px;
}

.rnf-button--primary .rnf-button-spinner circle {
  stroke: #fff;
}

.rnf-button--secondary .rnf-button-spinner circle {
  stroke: var(--rnf-button-secondary-text-color);
}

.rnf-button--dark.rnf-button--secondary .rnf-button-spinner circle {
  stroke: var(--rnf-color-white-a-75);
}

.rnf-button-group > .rnf-button + .rnf-button {
  margin-left: 8px;
}

:root {
  --rnf-empty-feed-max-w: 240px;
  --rnf-empty-feed-header-font-size: var(--rnf-font-size-md);
  --rnf-empty-feed-body-font-size: var(--rnf-font-size-sm);
}

.rnf-empty-feed {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  display: flex;
  position: relative;
}

.rnf-empty-feed__inner {
  max-width: var(--rnf-empty-feed-max-w);
  text-align: center;
  margin: -20px auto 0;
}

.rnf-empty-feed__header {
  font-size: var(--rnf-empty-feed-header-font-size);
  font-weight: var(--rnf-font-weight-medium);
  color: var(--rnf-color-gray-900);
  margin: 0 0 var(--rnf-spacing-1);
}

.rnf-empty-feed__body {
  font-size: var(--rnf-empty-feed-body-font-size);
  color: var(--rnf-color-gray-300);
  margin: 0;
}

.rnf-empty-feed--dark .rnf-empty-feed__header {
  color: var(--rnf-color-white-a-75);
}

.rnf-empty-feed--dark .rnf-empty-feed__body {
  color: var(--rnf-color-gray-400);
}

:root {
  --rnf-avatar-bg-color: #ef8476;
  --rnf-avatar-size: 32px;
  --rnf-avatar-initials-font-size: var(--rnf-font-size-md);
  --rnf-avatar-initials-line-height: var(--rnf-font-size-lg);
  --rnf-avatar-initials-color: #fff;
  --rnf-notification-cell-border-bottom-color: #e4e8ee;
  --rnf-notification-cell-padding: var(--rnf-spacing-3);
  --rnf-notification-cell-active-bg-color: #f1f6fc;
  --rnf-notification-cell-unread-dot-size: 6px;
  --rnf-notification-cell-unread-dot-bg-color: #80c7f5;
  --rnf-notification-cell-unread-dot-border-color: #3192e3;
  --rnf-notification-cell-content-color: var(--rnf-color-gray-900);
  --rnf-notification-cell-content-font-size: var(--rnf-font-size-sm);
  --rnf-notification-cell-content-line-height: var(--rnf-font-size-lg);
  --rnf-archive-notification-btn-bg-color: var(--rnf-color-gray-400);
  --rnf-archive-notification-btn-bg-color-active: var(--rnf-color-gray-500);
}

.rnf-avatar {
  background-color: var(--rnf-avatar-bg-color);
  border-radius: var(--rnf-avatar-size);
  width: var(--rnf-avatar-size);
  height: var(--rnf-avatar-size);
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  display: flex;
  overflow: hidden;
}

.rnf-avatar__initials {
  font-size: var(--rnf-avatar-initials-font-size);
  line-height: var(--rnf-avatar-initials-line-height);
  color: var(--rnf-avatar-initials-color);
}

.rnf-avatar__image {
  object-fit: cover;
  width: var(--rnf-avatar-size);
  height: var(--rnf-avatar-size);
}

.rnf-notification-cell {
  border-bottom: 1px solid var(--rnf-notification-cell-border-bottom-color);
  background-color: #0000;
  position: relative;
}

.rnf-notification-cell:last-child {
  border-bottom-color: #0000;
}

.rnf-notification-cell:hover, .rnf-notification-cell:focus, .rnf-notification-cell:active {
  background-color: var(--rnf-notification-cell-active-bg-color);
  outline: none;
}

.rnf-notification-cell__inner {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 100%;
  padding: var(--rnf-notification-cell-padding);
  cursor: pointer;
  text-align: left;
  border: none;
  justify-content: flex-start;
  margin: 0;
  text-decoration: none;
  display: flex;
}

.rnf-notification-cell__unread-dot {
  top: var(--rnf-notification-cell-unread-dot-size);
  left: var(--rnf-notification-cell-unread-dot-size);
  width: var(--rnf-notification-cell-unread-dot-size);
  height: var(--rnf-notification-cell-unread-dot-size);
  border-radius: var(--rnf-notification-cell-unread-dot-size);
  background-color: var(--rnf-notification-cell-unread-dot-bg-color);
  border: 1px solid var(--rnf-notification-cell-unread-dot-border-color);
  position: absolute;
}

.rnf-notification-cell__content-outer {
  margin-left: var(--rnf-spacing-3);
}

.rnf-notification-cell__content {
  color: var(--rnf-notification-cell-content-color);
  font-weight: var(--rnf-font-weight-normal);
  font-size: var(--rnf-notification-cell-content-font-size);
  line-height: var(--rnf-notification-cell-content-line-height);
  margin-bottom: var(--rnf-spacing-1);
  word-break: break-word;
  word-wrap: break-word;
  display: block;
}

.rnf-notification-cell__content h1, .rnf-notification-cell__content h2, .rnf-notification-cell__content h3, .rnf-notification-cell__content h4 {
  font-weight: var(--rnf-font-weight-semibold);
  margin-bottom: .5em;
}

.rnf-notification-cell__content h1 {
  font-size: var(--rnf-font-size-2xl);
}

.rnf-notification-cell__content h2 {
  font-size: var(--rnf-font-size-xl);
}

.rnf-notification-cell__content h3 {
  font-size: var(--rnf-font-size-lg);
}

.rnf-notification-cell__content h4 {
  font-size: var(--rnf-font-size-md);
}

.rnf-notification-cell__content p {
  margin: 0 0 .75em;
}

.rnf-notification-cell__content p:last-child {
  margin-bottom: 0;
}

.rnf-notification-cell__content blockquote {
  border-left: 3px solid var(--rnf-color-gray-300);
  padding-left: var(--rnf-spacing-3);
  line-height: var(--rnf-font-size-xl);
  margin: 0;
}

.rnf-notification-cell__content strong {
  font-weight: var(--rnf-font-weight-semibold);
}

.rnf-notification-cell__timestamp {
  color: var(--rnf-color-gray-300);
  font-size: var(--rnf-font-size-sm);
  font-weight: var(--rnf-font-weight-normal);
  line-height: var(--rnf-font-size-lg);
  display: block;
}

.rnf-notification-cell__child-content, .rnf-notification-cell__button-group {
  margin: .75em 0 .5em;
}

.rnf-archive-notification-btn {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  -webkit-user-select: none;
  user-select: none;
  opacity: 0;
  cursor: pointer;
  width: 24px;
  height: 24px;
  color: var(--rnf-archive-notification-btn-bg-color);
  padding: var(--rnf-spacing-1) var(--rnf-spacing-2);
  background-color: #0000;
  border: none;
  margin-left: auto;
  transition: color .1s ease-in-out, opacity .2s ease-in-out;
}

.rnf-notification-cell:focus .rnf-archive-notification-btn, .rnf-notification-cell:hover .rnf-archive-notification-btn, .rnf-notification-cell:active .rnf-archive-notification-btn {
  opacity: 1;
}

.rnf-archive-notification-btn:focus, .rnf-archive-notification-btn:hover, .rnf-archive-notification-btn:active {
  opacity: 1;
  color: var(--rnf-archive-notification-btn-bg-color-active);
  outline: none;
}

.rnf-tooltip {
  background-color: var(--rnf-color-gray-700);
  color: #fff;
  padding: var(--rnf-spacing-1) var(--rnf-spacing-2);
  font-size: var(--rnf-font-size-xs);
  line-height: var(--rnf-font-size-s);
  font-weight: var(--rnf-font-weight-medium);
  z-index: 9999;
  border-radius: 4px;
  flex-direction: column;
  transition: opacity .3s;
  display: flex;
  box-shadow: 0 2px 4px #0000002e;
}

.rnf-notification-cell--dark {
  --rnf-notification-cell-border-bottom-color: #697386a6;
  --rnf-notification-cell-active-bg-color: #393b40;
  --rnf-notification-cell-content-color: var(--rnf-color-white-a-75);
}

.rnf-notification-cell--dark:last-child {
  border-bottom-color: #0000;
}

.rnf-notification-cell--dark .rnf-notification-cell__timestamp {
  color: var(--rnf-color-gray-500);
}

.rnf-archive-notification-btn--dark {
  --rnf-archive-notification-btn-bg-color: var(--rnf-color-gray-500);
  --rnf-archive-notification-btn-bg-color-active: var(--rnf-color-gray-400);
}

.rnf-tooltip--dark {
  background-color: #565a61;
}

@media screen and (hover: none) {
  .rnf-archive-notification-btn {
    opacity: 1;
  }
}

:root {
  --rnf-notification-feed-header-height: 45px;
}

.rnf-notification-feed {
  background-color: var(--rnf-color-white);
  flex-direction: column;
  height: 100%;
  display: flex;
}

.rnf-dropdown {
  font-size: var(--rnf-font-size-md);
  font-weight: var(--rnf-font-weight-medium);
  color: var(--rnf-color-gray-400);
  position: relative;
}

.rnf-dropdown select {
  padding-right: var(--rnf-spacing-3);
  color: currentColor;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  font-size: var(--rnf-font-size-sm);
  text-align: right;
  z-index: 2;
  background: none;
  border: none;
  position: relative;
}

.rnf-dropdown svg {
  z-index: 1;
  margin-top: -2px;
  position: absolute;
  top: 50%;
  right: 0;
}

.rnf-mark-all-as-read {
  font-size: var(--rnf-font-size-sm);
  color: var(--rnf-color-gray-400);
  cursor: pointer;
  background: none;
  border: none;
  align-items: center;
  margin-left: auto;
  padding: 0;
  display: flex;
}

.rnf-mark-all-as-read:disabled {
  color: var(--rnf-color-gray-200);
  cursor: not-allowed;
}

.rnf-mark-all-as-read svg {
  margin-top: 1px;
  margin-left: var(--rnf-spacing-1);
}

.rnf-notification-feed__header {
  padding: var(--rnf-spacing-3) var(--rnf-spacing-4);
  height: var(--rnf-notification-feed-header-height);
  align-items: center;
  display: flex;
}

.rnf-notification-feed__selector {
  align-items: center;
  display: flex;
}

.rnf-notification-feed__type {
  font-size: var(--rnf-font-size-sm);
  font-weight: var(--rnf-font-weight-medium);
  color: var(--rnf-color-gray-900);
  margin-right: var(--rnf-spacing-2);
}

.rnf-notification-feed__container {
  flex: 1;
  overflow-y: auto;
}

.rnf-notification-feed__spinner-container {
  padding: var(--rnf-spacing-3) var(--rnf-spacing-4);
}

.rnf-notification-feed__spinner-container svg {
  margin: 0 auto;
  display: block;
}

.rnf-notification-feed__knock-branding {
  text-align: center;
}

.rnf-notification-feed__knock-branding a {
  font-size: var(--rnf-font-size-sm);
  color: var(--rnf-color-gray-500);
  border-top: 1px solid var(--rnf-color-gray-100);
  padding: 6px;
  display: block;
}

.rnf-notification-feed__knock-branding a:hover {
  background-color: #f1f6fc;
}

.rnf-notification-feed--dark {
  background-color: #2e2f34;
}

.rnf-notification-feed--dark .rnf-notification-feed__type {
  color: var(--rnf-color-white-a-75);
}

.rnf-dropdown--dark {
  color: var(--rnf-color-gray-400);
}

.rnf-mark-all-as-read--dark:disabled {
  color: var(--rnf-color-gray-500);
}

.rnf-notification-feed--dark .rnf-notification-feed__knock-branding a {
  color: var(--rnf-color-gray-400);
  border-top-color: #697386a6;
}

.rnf-notification-feed--dark .rnf-notification-feed__knock-branding a:hover {
  background-color: #393b40;
}

.rnf-feed-provider {
  font-family: var(--rnf-font-family-sanserif) !important;
  margin: 0 !important;
  padding: 0 !important;
}

.rnf-feed-provider [class^="rnf-"] {
  box-sizing: border-box;
  font-family: var(--rnf-font-family-sanserif) !important;
}

:root {
  --rnf-notification-feed-popover-max-w: 400px;
  --rnf-notification-feed-popover-min-w: 280px;
  --rnf-notification-feed-popover-height: 400px;
  --rnf-notification-feed-popover-shadow: drop-shadow(0px 5px 15px #0003);
  --rnf-notification-feed-popover-shadow-color: #0003;
  --rnf-notification-feed-popover-bg-color: #fff;
  --rnf-notification-feed-popover-z-index: 999;
  --rnf-notification-feed-popover-arrow-size: 10px;
  --rnf-notification-feed-popover-border-radius: 4px;
}

.rnf-notification-feed-popover {
  width: 100%;
  max-width: var(--rnf-notification-feed-popover-max-w);
  min-width: var(--rnf-notification-feed-popover-min-w);
  height: var(--rnf-notification-feed-popover-height);
  z-index: var(--rnf-notification-feed-popover-z-index);
}

.rnf-notification-feed-popover__inner {
  background-color: var(--rnf-notification-feed-popover-bg-color);
  border-radius: var(--rnf-notification-feed-popover-border-radius);
  filter: var(--rnf-notification-feed-popover-shadow);
  height: 100%;
  overflow: hidden;
}

.rnf-notification-feed-popover__arrow {
  width: var(--rnf-notification-feed-popover-arrow-size);
  height: var(--rnf-notification-feed-popover-arrow-size);
  position: absolute;
}

.rnf-notification-feed-popover__arrow:after {
  content: " ";
  background-color: var(--rnf-notification-feed-popover-bg-color);
  box-shadow: -1px -1px 1px var(--rnf-notification-feed-popover-shadow-color);
  width: var(--rnf-notification-feed-popover-arrow-size);
  height: var(--rnf-notification-feed-popover-arrow-size);
  display: block;
  position: absolute;
  top: -5px;
  left: 0;
  transform: rotate(45deg);
}

.rnf-notification-feed-popover--dark {
  --rnf-notification-feed-popover-shadow-color: #0003;
}

:root {
  --rnf-unseen-badge-bg-color: #eb5757;
  --rnf-unseen-badge-size: 16px;
  --rnf-unseed-badge-font-size: 9px;
}

.rnf-unseen-badge {
  background-color: var(--rnf-unseen-badge-bg-color);
  width: var(--rnf-unseen-badge-size);
  height: var(--rnf-unseen-badge-size);
  border-radius: var(--rnf-unseen-badge-size);
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
  top: 0;
  right: 0;
}

.rnf-unseen-badge__count {
  font-size: var(--rnf-unseed-badge-font-size);
  font-weight: var(--rnf-font-weight-medium);
  color: var(--rnf-color-white);
  margin-top: -1px;
}

.rnf-unseen-badge--dark {
  --rnf-unseen-badge-bg-color: #ef3434;
}

:root {
  --rnf-notification-icon-button-size: 32px;
  --rnf-notification-icon-button-bg-color: transparent;
}

.rnf-notification-icon-button {
  background-color: var(--rnf-notification-icon-button-bg-color);
  cursor: pointer;
  width: var(--rnf-notification-icon-button-size);
  height: var(--rnf-notification-icon-button-size);
  color: inherit;
  border: none;
  margin: 0;
  padding: 0;
  display: block;
  position: relative;
}

.rnf-notification-icon-button svg {
  margin: 0 auto;
  display: block;
}

.rnf-notification-icon-button--dark {
  color: #fff;
}

.knock-guide-banner {
  background: var(--knock-guide-surface);
  padding: var(--knock-spacing-4) var(--knock-spacing-6);
  border-radius: var(--knock-rounded-4);
  border: .5px solid var(--knock-guide-border);
  box-shadow: var(--knock-shadow-2);
  justify-content: space-between;
  align-items: center;
  gap: var(--knock-spacing-4);
  display: flex;
}

.knock-guide-banner__message {
  min-width: var(--knock-spacing-96);
}

.knock-guide-banner__title {
  color: var(--knock-guide-content);
  font-size: var(--knock-text-4);
  font-weight: var(--knock-weight-medium);
  line-height: var(--knock-leading-4);
}

.knock-guide-banner__body {
  color: var(--knock-guide-content-light);
  font-size: var(--knock-text-3);
  font-weight: var(--knock-weight-regular);
  line-height: var(--knock-leading-3);
}

.knock-guide-banner__body > :first-child {
  margin-top: 0;
}

.knock-guide-banner__body > :last-child {
  margin-bottom: 0;
}

.knock-guide-banner__actions {
  justify-content: space-between;
  align-items: center;
  gap: var(--knock-spacing-3);
  display: flex;
}

.knock-guide-banner__action {
  font-size: var(--knock-text-3);
  font-weight: var(--knock-weight-regular);
  line-height: var(--knock-leading-3);
  border-radius: var(--knock-rounded-3);
  padding: 0 var(--knock-spacing-4);
  box-sizing: border-box;
  height: var(--knock-spacing-10);
  border: .5px solid var(--knock-guide-accent);
  background: var(--knock-guide-accent);
  color: var(--knock-guide-content-contrast);
  cursor: pointer;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  display: flex;
}

.knock-guide-banner__action--secondary {
  border-color: var(--knock-guide-border-dark);
  background: var(--knock-guide-surface);
  color: var(--knock-guide-content);
}

.knock-guide-banner__close {
  padding: var(--knock-spacing-3);
  cursor: pointer;
  background: none;
  border: none;
  line-height: 0;
}

.knock-guide-card {
  background: var(--knock-guide-surface);
  padding: var(--knock-spacing-4);
  border-radius: var(--knock-rounded-2);
  border: .5px solid var(--knock-guide-border);
  gap: var(--knock-spacing-4);
  max-width: var(--knock-spacing-96);
  flex-direction: column;
  display: flex;
}

.knock-guide-card__header {
  justify-content: space-between;
  align-self: stretch;
  align-items: center;
  gap: var(--knock-spacing-2);
  display: flex;
}

.knock-guide-card__headline {
  color: var(--knock-guide-accent-dark);
  font-size: var(--knock-text-1);
  font-weight: var(--knock-weight-medium);
  line-height: var(--knock-leading-1);
  letter-spacing: var(--knock-tracking-1);
}

.knock-guide-card__message {
  flex-direction: column;
  align-self: stretch;
  align-items: flex-start;
  display: flex;
}

.knock-guide-card__title {
  color: var(--knock-guide-content);
  font-size: var(--knock-text-4);
  font-weight: var(--knock-weight-medium);
  line-height: var(--knock-leading-4);
}

.knock-guide-card__body {
  color: var(--knock-guide-content-light);
  font-size: var(--knock-text-3);
  font-weight: var(--knock-weight-regular);
  line-height: var(--knock-leading-3);
}

.knock-guide-card__body > :first-child {
  margin-top: 0;
}

.knock-guide-card__body > :last-child {
  margin-bottom: 0;
}

.knock-guide-card__img {
  max-width: 100%;
  height: auto;
  display: block;
}

.knock-guide-card__actions {
  align-items: center;
  gap: var(--knock-spacing-3);
  display: flex;
}

.knock-guide-card__action {
  font-size: var(--knock-text-3);
  font-weight: var(--knock-weight-medium);
  line-height: var(--knock-leading-3);
  border-radius: var(--knock-rounded-3);
  padding: 0 var(--knock-spacing-4);
  box-sizing: border-box;
  height: var(--knock-spacing-10);
  border: .5px solid var(--knock-guide-accent);
  background: var(--knock-guide-accent);
  color: var(--knock-guide-content-contrast);
  cursor: pointer;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  display: flex;
}

.knock-guide-card__action--secondary {
  border-color: var(--knock-guide-border-dark);
  background: var(--knock-guide-surface);
  color: var(--knock-guide-content);
}

.knock-guide-card__close {
  padding: var(--knock-spacing-3);
  cursor: pointer;
  background: none;
  border: none;
  line-height: 0;
}

.knock-guide-modal {
  font-family: var(--knock-family-sans);
  background: var(--knock-guide-surface);
  padding: var(--knock-spacing-4) var(--knock-spacing-6) var(--knock-spacing-6);
  border-radius: var(--knock-rounded-4);
  border: .5px solid var(--knock-guide-border);
  box-shadow: var(--knock-shadow-3);
  max-width: var(--knock-spacing-96);
  max-height: calc(100vh - var(--knock-spacing-32));
  max-width: min(100vw - var(--knock-spacing-8), var(--knock-spacing-140));
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.knock-guide-modal__overlay {
  background-color: var(--knock-alpha-black-6);
  position: fixed;
  inset: 0;
}

.knock-guide-modal__header {
  justify-content: space-between;
  align-items: center;
  gap: var(--knock-spacing-2);
  padding-bottom: var(--knock-spacing-1);
  display: flex;
}

.knock-guide-modal__title {
  color: var(--knock-guide-content);
  font-size: var(--knock-text-4);
  font-weight: var(--knock-weight-medium);
  line-height: var(--knock-leading-4);
  margin: 0;
}

.knock-guide-modal__body {
  color: var(--knock-guide-content-light);
  font-size: var(--knock-text-3);
  font-weight: var(--knock-weight-regular);
  line-height: var(--knock-leading-3);
  margin: 0;
}

.knock-guide-modal__body > :first-child {
  margin-top: 0;
}

.knock-guide-modal__body > :last-child {
  margin-bottom: 0;
}

.knock-guide-modal__actions {
  justify-content: space-between;
  align-items: center;
  gap: var(--knock-spacing-3);
  margin-top: var(--knock-spacing-4);
  display: flex;
}

.knock-guide-modal__img {
  margin-top: var(--knock-spacing-4);
  max-width: 100%;
  height: auto;
  display: block;
}

.knock-guide-modal__action {
  text-align: center;
  font-size: var(--knock-text-3);
  font-weight: var(--knock-weight-medium);
  line-height: var(--knock-leading-3);
  border-radius: var(--knock-rounded-3);
  padding: 0 var(--knock-spacing-4);
  box-sizing: border-box;
  height: var(--knock-spacing-10);
  border: .5px solid var(--knock-guide-accent);
  background: var(--knock-guide-accent);
  color: var(--knock-guide-content-contrast);
  cursor: pointer;
  justify-content: center;
  align-items: center;
  width: 100%;
  text-decoration: none;
  display: flex;
}

.knock-guide-modal__action--secondary {
  border-color: var(--knock-guide-border-dark);
  background: var(--knock-guide-surface);
  color: var(--knock-guide-content);
}

.knock-guide-modal__close {
  padding: var(--knock-spacing-3);
  cursor: pointer;
  background: none;
  border: none;
  line-height: 0;
}

:root {
  --tgph-surface-1: #fff;
  --tgph-surface-2: #f9f9f8;
  --tgph-gray-1: #fcfcfd;
  --tgph-gray-2: #f9f9fb;
  --tgph-gray-3: #f0f0f3;
  --tgph-gray-4: #e8e8ec;
  --tgph-gray-5: #e0e1e6;
  --tgph-gray-6: #d9d9e0;
  --tgph-gray-7: #cdced6;
  --tgph-gray-8: #b9bbc6;
  --tgph-gray-9: #8b8d98;
  --tgph-gray-10: #80838d;
  --tgph-gray-11: #60646c;
  --tgph-gray-12: #1c2024;
  --tgph-beige-1: #fdfdfc;
  --tgph-beige-2: #f9f9f8;
  --tgph-beige-3: #f1f0ef;
  --tgph-beige-4: #e9e8e6;
  --tgph-beige-5: #e2e1de;
  --tgph-beige-6: #dad9d6;
  --tgph-beige-7: #cfceca;
  --tgph-beige-8: #bcbbb5;
  --tgph-beige-9: #8d8d86;
  --tgph-beige-10: #82827c;
  --tgph-beige-11: #63635e;
  --tgph-beige-12: #21201c;
  --tgph-accent-1: #fffcfc;
  --tgph-accent-2: #fff8f7;
  --tgph-accent-3: #feebe7;
  --tgph-accent-4: #ffdcd3;
  --tgph-accent-5: #ffcdc2;
  --tgph-accent-6: #fdbdaf;
  --tgph-accent-7: #f5a898;
  --tgph-accent-8: #ec8e7b;
  --tgph-accent-9: #e54d2e;
  --tgph-accent-10: #dd4425;
  --tgph-accent-11: #d13415;
  --tgph-accent-12: #5c271f;
  --tgph-green-1: #fbfefd;
  --tgph-green-2: #f4fbf7;
  --tgph-green-3: #e6f7ed;
  --tgph-green-4: #d6f1e3;
  --tgph-green-5: #c3e9d7;
  --tgph-green-6: #acdec8;
  --tgph-green-7: #8bceb6;
  --tgph-green-8: #56ba9f;
  --tgph-green-9: #29a383;
  --tgph-green-10: #26997b;
  --tgph-green-11: #208368;
  --tgph-green-12: #1d3b31;
  --tgph-yellow-1: #fefdfb;
  --tgph-yellow-2: #fefbe9;
  --tgph-yellow-3: #fff7c2;
  --tgph-yellow-4: #ffee9c;
  --tgph-yellow-5: #fbe577;
  --tgph-yellow-6: #f3d673;
  --tgph-yellow-7: #e9c162;
  --tgph-yellow-8: #f3d673;
  --tgph-yellow-9: #ffc53d;
  --tgph-yellow-10: #ffba18;
  --tgph-yellow-11: #ab6400;
  --tgph-yellow-12: #4f3422;
  --tgph-blue-1: #fdfdfe;
  --tgph-blue-2: #f7f9ff;
  --tgph-blue-3: #edf2fe;
  --tgph-blue-4: #e1e9ff;
  --tgph-blue-5: #d2deff;
  --tgph-blue-6: #c1d0ff;
  --tgph-blue-7: #abbdf9;
  --tgph-blue-8: #8da4ef;
  --tgph-blue-9: #3e63dd;
  --tgph-blue-10: #3358d4;
  --tgph-blue-11: #3a5bc7;
  --tgph-blue-12: #1f2d5c;
  --tgph-red-1: #fffcfd;
  --tgph-red-2: #fff7f8;
  --tgph-red-3: #feeaed;
  --tgph-red-4: #ffdce1;
  --tgph-red-5: #ffced6;
  --tgph-red-6: #f8bfc8;
  --tgph-red-7: #efacb8;
  --tgph-red-8: #e592a3;
  --tgph-red-9: #e54666;
  --tgph-red-10: #dc3b5d;
  --tgph-red-11: #ca244d;
  --tgph-red-12: #64172b;
  --tgph-purple-1: #fdfcfe;
  --tgph-purple-2: #faf8ff;
  --tgph-purple-3: #f4f0fe;
  --tgph-purple-4: #ebe4ff;
  --tgph-purple-5: #e1d9ff;
  --tgph-purple-6: #d4cafe;
  --tgph-purple-7: #c2b5f5;
  --tgph-purple-8: #aa99ec;
  --tgph-purple-9: #654dc4;
  --tgph-purple-10: #654dc4;
  --tgph-purple-11: #6550b9;
  --tgph-purple-12: #2f265f;
  --tgph-border-style-solid: solid;
  --tgph-border-style-dashed: dashed;
  --tgph-transparent: transparent;
  --tgph-white: #fff;
  --tgph-black: #000;
  --tgph-alpha-white-1: #ffffff0d;
  --tgph-alpha-white-2: #ffffff1a;
  --tgph-alpha-white-3: #ffffff26;
  --tgph-alpha-white-4: #fff3;
  --tgph-alpha-white-5: #ffffff4d;
  --tgph-alpha-white-6: #fff6;
  --tgph-alpha-white-7: #ffffff80;
  --tgph-alpha-white-8: #fff9;
  --tgph-alpha-white-9: #ffffffb3;
  --tgph-alpha-white-10: #fffc;
  --tgph-alpha-white-11: #ffffffe6;
  --tgph-alpha-white-12: #fffffff2;
  --tgph-alpha-black-1: #0000000d;
  --tgph-alpha-black-2: #0000001a;
  --tgph-alpha-black-3: #00000026;
  --tgph-alpha-black-4: #0003;
  --tgph-alpha-black-5: #0000004d;
  --tgph-alpha-black-6: #0006;
  --tgph-alpha-black-7: #00000080;
  --tgph-alpha-black-8: #0009;
  --tgph-alpha-black-9: #000000b3;
  --tgph-alpha-black-10: #000c;
  --tgph-alpha-black-11: #000000e6;
  --tgph-alpha-black-12: #000000f2;
  --tgph-rounded-0: 0px;
  --tgph-rounded-1: .125rem;
  --tgph-rounded-2: .25rem;
  --tgph-rounded-3: .375rem;
  --tgph-rounded-4: .5rem;
  --tgph-rounded-5: .75rem;
  --tgph-rounded-6: 1rem;
  --tgph-rounded-full: 9999px;
  --tgph-shadow-0: 0px 0px 0px 0px #0000;
  --tgph-shadow-1: 0px 5px 2px 0px #1c202403, 0px 3px 2px 0px #1c202408, 0px 1px 1px 0px #1c20240d, 0px 0px 1px 0px #1c20240f;
  --tgph-shadow-2: 0px 16px 7px 0px #1c202403, 0px 9px 6px 0px #1c202408, 0px 4px 4px 0px #1c20240d, 0px 1px 2px 0px #1c20240f;
  --tgph-shadow-3: 0px 29px 12px 0px #1c202403, 0px 16px 10px 0px #1c202408, 0px 7px 7px 0px #1c20240d, 0px 2px 4px 0px #1c20240f;
  --tgph-shadow-inner: 0px 5px 2px 0px #1c202403 inset, 0px 3px 2px 0px #1c202408 inset, 0px 1px 1px 0px #1c20240d inset, 0px 0px 1px 0px #1c20240f inset;
  --tgph-spacing-0: 0px;
  --tgph-spacing-1: .25rem;
  --tgph-spacing-2: .5rem;
  --tgph-spacing-3: .75rem;
  --tgph-spacing-4: 1rem;
  --tgph-spacing-5: 1.25rem;
  --tgph-spacing-6: 1.5rem;
  --tgph-spacing-7: 1.75rem;
  --tgph-spacing-8: 2rem;
  --tgph-spacing-9: 2.25rem;
  --tgph-spacing-10: 2.5rem;
  --tgph-spacing-11: 2.75rem;
  --tgph-spacing-12: 3rem;
  --tgph-spacing-14: 3.5rem;
  --tgph-spacing-16: 4rem;
  --tgph-spacing-20: 5rem;
  --tgph-spacing-24: 6rem;
  --tgph-spacing-28: 7rem;
  --tgph-spacing-32: 8rem;
  --tgph-spacing-36: 9rem;
  --tgph-spacing-40: 10rem;
  --tgph-spacing-44: 11rem;
  --tgph-spacing-48: 12rem;
  --tgph-spacing-52: 13rem;
  --tgph-spacing-56: 14rem;
  --tgph-spacing-60: 15rem;
  --tgph-spacing-64: 16rem;
  --tgph-spacing-72: 18rem;
  --tgph-spacing-80: 20rem;
  --tgph-spacing-96: 24rem;
  --tgph-spacing-140: 35rem;
  --tgph-spacing-160: 40rem;
  --tgph-spacing-px: 1px;
  --tgph-spacing-full: 100%;
  --tgph-spacing-auto: auto;
  --tgph-family-sans: Inter, -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
  --tgph-family-mono: Menlo, Consolas, Monaco, Liberation Mono, Lucida Console, monospace;
  --tgph-leading-0: 1rem;
  --tgph-leading-1: 1rem;
  --tgph-leading-2: 1.25rem;
  --tgph-leading-3: 1.5rem;
  --tgph-leading-4: 1.75rem;
  --tgph-leading-5: 1.75rem;
  --tgph-leading-6: 2rem;
  --tgph-leading-7: 2.25rem;
  --tgph-leading-8: 2.5rem;
  --tgph-leading-9: 3.5rem;
  --tgph-leading-code-0: 1rem;
  --tgph-leading-code-1: 1rem;
  --tgph-leading-code-2: 1.25rem;
  --tgph-leading-code-3: 1.5rem;
  --tgph-leading-code-4: 1.75rem;
  --tgph-leading-code-5: 1.75rem;
  --tgph-leading-code-6: 2rem;
  --tgph-leading-code-7: 2.25rem;
  --tgph-leading-code-8: 2.5rem;
  --tgph-leading-code-9: 3rem;
  --tgph-tracking-0: 0.25%;
  --tgph-tracking-1: 0.25%;
  --tgph-tracking-2: 0;
  --tgph-tracking-3: 0;
  --tgph-tracking-4: -0.25%;
  --tgph-tracking-5: -0.5%;
  --tgph-tracking-6: -0.625%;
  --tgph-tracking-7: -0.75%;
  --tgph-tracking-8: -1%;
  --tgph-tracking-9: -2.5%;
  --tgph-text-0: .6875rem;
  --tgph-text-1: .75rem;
  --tgph-text-2: .875rem;
  --tgph-text-3: 1rem;
  --tgph-text-4: 1.125rem;
  --tgph-text-5: 1.25rem;
  --tgph-text-6: 1.5rem;
  --tgph-text-7: 1.875rem;
  --tgph-text-8: 2.25rem;
  --tgph-text-9: 3rem;
  --tgph-text-code-0: .625rem;
  --tgph-text-code-1: .688rem;
  --tgph-text-code-2: .812rem;
  --tgph-text-code-4: 1.062rem;
  --tgph-text-code-5: 1.188rem;
  --tgph-text-code-6: 1.438rem;
  --tgph-text-code-7: 1.75rem;
  --tgph-text-code-8: 2.125rem;
  --tgph-text-code-9: 2.875rem;
  --tgph-weight-regular: 400;
  --tgph-weight-medium: 500;
  --tgph-weight-semi-bold: 600;
  --tgph-breakpoint-sm: 640px;
  --tgph-breakpoint-md: 768px;
  --tgph-breakpoint-lg: 1024px;
  --tgph-breakpoint-xl: 1280px;
  --tgph-breakpoint-2xl: 1536px;
  --tgph-zIndex-hidden: -1;
  --tgph-zIndex-base: 0;
  --tgph-zIndex-auto: auto;
  --tgph-zIndex-dropdown: 1000;
  --tgph-zIndex-sticky: 1100;
  --tgph-zIndex-banner: 1200;
  --tgph-zIndex-overlay: 1300;
  --tgph-zIndex-modal: 1400;
  --tgph-zIndex-popover: 1500;
  --tgph-zIndex-skipLink: 1600;
  --tgph-zIndex-toast: 1700;
  --tgph-zIndex-tooltip: 1800;
}

.tgph-text, .tgph-heading, .tgph-code {
  --color: var(--tgph-gray-12);
  --font-size: var(--tgph-text-2);
  --weight: var(--tgph-weight-regular);
  --leading: var(--tgph-leading-2);
  --tracking: var(--tgph-tracking-2);
  --text-align: left;
  --font-family: var(--tgph-family-sans);
  --text-overflow: clip;
  color: var(--color);
  font-size: var(--font-size);
  font-weight: var(--weight);
  line-height: var(--leading);
  letter-spacing: var(--tracking);
  text-align: var(--text-align);
  box-sizing: border-box;
  font-family: var(--font-family);
  text-overflow: var(--text-overflow);
}

.tgph-button {
  --tgph-button-hover-shadow: none;
  --tgph-button-focus-shadow: none;
  --tgph-button-active-shadow: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
  box-sizing: border-box;
  font-family: inherit;
  text-decoration: none;
  transition: background-color .2s ease-in-out, color .2s ease-in-out, box-shadow .2s ease-in-out;
}

.tgph-button:hover {
  box-shadow: var(--tgph-button-hover-shadow);
}

.tgph-button:focus {
  box-shadow: var(--tgph-button-focus-shadow);
}

.tgph-button[data-tgph-button-state="active"] {
  box-shadow: var(--tgph-button-active-shadow);
  background-color: var(--active_backgroundColor);
}

.tgph-button[data-tgph-button-state="disabled"][data-tgph-button-variant="solid"], .tgph-button[data-tgph-button-state="disabled"][data-tgph-button-variant="soft"], .tgph-button[data-tgph-button-state="disabled"][data-tgph-button-variant="outline"] {
  background-color: var(--tgph-gray-3);
}

.tgph-button[data-tgph-button-state="disabled"][data-tgph-button-variant="ghost"] {
  background-color: var(--tgph-transparent);
}

.tgph-button:disabled {
  cursor: not-allowed;
}

.tgph-button[data-tgph-button-state="disabled"]:hover {
  background-color: revert;
}

[data-tgph-button-loading-icon] {
  animation: 1s linear infinite button-loading-icon;
}

@keyframes button-loading-icon {
  0% {
    transform: rotate(0);
  }

  to {
    transform: rotate(360deg);
  }
}

.tgph-icon {
  display: inline-block;
}

.tgph-box {
  --background-color: none;
  --border-color: var(--tgph-gray-6);
  --box-shadow: none;
  --border-style: solid;
  --border-width: 0;
  --padding: 0;
  --margin: 0;
  --border-radius: 0;
  --width: auto;
  --max-width: auto;
  --min-width: auto;
  --height: auto;
  --max-height: auto;
  --min-height: auto;
  --z-index: auto;
  --position: static;
  --top: auto;
  --left: auto;
  --right: auto;
  --bottom: auto;
  --overflow: visible;
  background-color: var(--background-color);
  border-width: var(--border-width);
  border-style: var(--border-style);
  border-color: var(--border-color);
  box-shadow: var(--box-shadow);
  padding: var(--padding);
  margin: var(--margin);
  border-radius: var(--border-radius);
  width: var(--width);
  max-width: var(--max-width);
  min-width: var(--min-width);
  height: var(--height);
  max-height: var(--max-height);
  min-height: var(--min-height);
  z-index: var(--z-index);
  position: var(--position);
  top: var(--top);
  left: var(--left);
  right: var(--right);
  bottom: var(--bottom);
  overflow: var(--overflow);
}

.tgph-box--interactive {
  --hover_backgroundColor: none;
  --focus_backgroundColor: none;
  --active_backgroundColor: none;
}

.tgph-box--interactive:hover {
  background-color: var(--hover_backgroundColor);
}

.tgph-box--interactive:focus {
  background-color: var(--focus_backgroundColor);
}

.tgph-box--interactive:active {
  background-color: var(--active_backgroundColor);
}

.tgph-stack {
  --display: flex;
  --direction: row;
  --wrap: nowrap;
  --justify: flex-start;
  --align: stretch;
  --gap: 0;
  display: var(--display);
  flex-direction: var(--direction);
  flex-wrap: var(--wrap);
  justify-content: var(--justify);
  align-items: var(--align);
  gap: var(--gap);
}

.tgph .order-1 {
  order: 1;
}

.tgph .order-2 {
  order: 2;
}

.tgph .order-3 {
  order: 3;
}

.tgph .box-border {
  box-sizing: border-box;
}

.tgph .flex {
  display: flex;
}

.tgph .aspect-square {
  aspect-ratio: 1;
}

.tgph .h-10 {
  height: var(--tgph-spacing-10);
}

.tgph .h-6 {
  height: var(--tgph-spacing-6);
}

.tgph .h-8 {
  height: var(--tgph-spacing-8);
}

.tgph .h-full {
  height: var(--tgph-spacing-full);
}

.tgph .w-full {
  width: var(--tgph-spacing-full);
}

.tgph .cursor-not-allowed {
  cursor: not-allowed;
}

.tgph .appearance-none {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.tgph .items-center {
  align-items: center;
}

.tgph .justify-center {
  justify-content: center;
}

.tgph .rounded-2 {
  border-radius: var(--tgph-rounded-2);
}

.tgph .rounded-3 {
  border-radius: var(--tgph-rounded-3);
}

.tgph .border, .tgph .border-\[1px\] {
  border-width: 1px;
}

.tgph .border-solid {
  border-style: solid;
}

.tgph .border-none {
  border-style: none;
}

.tgph .border-gray-2 {
  border-color: var(--tgph-gray-2);
}

.tgph .border-gray-6 {
  border-color: var(--tgph-gray-6);
}

.tgph .border-red-6 {
  border-color: var(--tgph-red-6);
}

.tgph .border-transparent {
  border-color: var(--tgph-transparent);
}

.tgph .bg-gray-2 {
  background-color: var(--tgph-gray-2);
}

.tgph .bg-surface-1 {
  background-color: var(--tgph-surface-1);
}

.tgph .bg-transparent {
  background-color: var(--tgph-transparent);
}

.tgph .\!p-1 {
  padding: var(--tgph-spacing-1) !important;
}

.tgph .px-1 {
  padding-left: var(--tgph-spacing-1);
  padding-right: var(--tgph-spacing-1);
}

.tgph .px-2 {
  padding-left: var(--tgph-spacing-2);
  padding-right: var(--tgph-spacing-2);
}

.tgph .px-3 {
  padding-left: var(--tgph-spacing-3);
  padding-right: var(--tgph-spacing-3);
}

.tgph .pl-0 {
  padding-left: var(--tgph-spacing-0);
}

.tgph .pl-1 {
  padding-left: var(--tgph-spacing-1);
}

.tgph .pl-2 {
  padding-left: var(--tgph-spacing-2);
}

.tgph .pl-3 {
  padding-left: var(--tgph-spacing-3);
}

.tgph .pr-1 {
  padding-right: var(--tgph-spacing-1);
}

.tgph .pr-2 {
  padding-right: var(--tgph-spacing-2);
}

.tgph .pr-3 {
  padding-right: var(--tgph-spacing-3);
}

.tgph .text-gray-12 {
  color: var(--tgph-gray-12);
}

.tgph .shadow-0 {
  --tw-shadow: var(--tgph-shadow-0);
  --tw-shadow-colored: var(--tgph-shadow-0);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.tgph .outline {
  outline-style: solid;
}

.tgph .outline-0 {
  outline-width: 0;
}

.tgph .transition-all {
  transition-property: all;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.tgph .\[font-family\:inherit\] {
  font-family: inherit;
}

.tgph .placeholder\:text-gray-10::-moz-placeholder {
  color: var(--tgph-gray-10);
}

.tgph .placeholder\:text-gray-10::placeholder {
  color: var(--tgph-gray-10);
}

.tgph .placeholder\:text-gray-9::-moz-placeholder {
  color: var(--tgph-gray-9);
}

.tgph .placeholder\:text-gray-9::placeholder {
  color: var(--tgph-gray-9);
}

.tgph .focus-within\:\!border-blue-8:focus-within {
  border-color: var(--tgph-blue-8) !important;
}

.tgph .focus-within\:\!bg-gray-4:focus-within {
  background-color: var(--tgph-gray-4) !important;
}

.tgph .hover\:border-gray-7:hover {
  border-color: var(--tgph-gray-7);
}

.tgph .hover\:bg-gray-3:hover {
  background-color: var(--tgph-gray-3);
}

.tgph .\[\&\:has\(\[data-tgph-button-layout\=\'icon-only\'\]\)\]\:aspect-square:has([data-tgph-button-layout="icon-only"]) {
  aspect-ratio: 1;
}

.tgph .\[\&\:has\(\[data-tgph-button-layout\=\'icon-only\'\]\)\]\:\!p-1:has([data-tgph-button-layout="icon-only"]) {
  padding: var(--tgph-spacing-1) !important;
}

.tgph :is(.\[\&\>\[data-tgph-button-layout\=\'default\'\]\]\:px-2 > [data-tgph-button-layout="default"]) {
  padding-left: var(--tgph-spacing-2);
  padding-right: var(--tgph-spacing-2);
}

.tgph :is(.\[\&\>\[data-tgph-button-layout\=\'icon-only\'\]\]\:aspect-square > [data-tgph-button-layout="icon-only"]) {
  aspect-ratio: 1;
}

.tgph :is(.\[\&\>\[data-tgph-button-layout\=\'icon-only\'\]\]\:p-0 > [data-tgph-button-layout="icon-only"]) {
  padding: var(--tgph-spacing-0);
}

.tgph :is(.\[\&\>\[data-tgph-button\]\]\:h-auto > [data-tgph-button]) {
  height: var(--tgph-spacing-auto);
}

.tgph :is(.\[\&\>\[data-tgph-button\]\]\:w-full > [data-tgph-button]) {
  width: var(--tgph-spacing-full);
}

.tgph :is(.\[\&\>\[data-tgph-button\]\]\:rounded-1 > [data-tgph-button]) {
  border-radius: var(--tgph-rounded-1);
}

.tgph :is(.\[\&\>\[data-tgph-button\]\]\:rounded-2 > [data-tgph-button]) {
  border-radius: var(--tgph-rounded-2);
}

.tgph :is(.\[\&\>input\]\:text-gray-9 > input) {
  color: var(--tgph-gray-9);
}

.tgph :is(.\[\&_\[data-tgph-icon\]\]\:text-gray-8 [data-tgph-icon]) {
  color: var(--tgph-gray-8);
}

.tgph-motion {
  --motion-opacity: 1;
  --motion-scale: 1;
  --motion-x: 0;
  --motion-y: 0;
  --motion-transition-duration: .2s;
  --motion-transition-type: ease-in-out;
  --motion-rotate: 0deg;
  opacity: var(--motion-opacity);
  transform: scale(var(--motion-scale)) translate(var(--motion-x), var(--motion-y)) rotate(var(--motion-rotate));
  transition: all var(--motion-transition-duration) var(--motion-transition-type);
}

:root {
  --rtk-font-size-xs: .75rem;
  --rtk-font-size-sm: .875rem;
  --rtk-font-size-md: 1rem;
  --rtk-font-size-lg: 1.125rem;
  --rtk-font-size-xl: 1.266rem;
  --rtk-font-size-2xl: 1.5rem;
  --rtk-font-size-3xl: 1.75rem;
  --rtk-spacing-0: 0rem;
  --rtk-spacing-1: .25rem;
  --rtk-spacing-2: .5rem;
  --rtk-spacing-3: .75rem;
  --rtk-spacing-4: 1rem;
  --rtk-spacing-5: 1.25rem;
  --rtk-spacing-6: 1.5rem;
  --rtk-spacing-7: 2rem;
  --rtk-font-weight-normal: 400;
  --rtk-font-weight-medium: 500;
  --rtk-font-weight-semibold: 600;
  --rtk-font-weight-bold: 700;
  --rtk-font-family-sanserif: Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Ubuntu, "Helvetica Neue", sans-serif;
  --rtk-border-radius-sm: 2px;
  --rtk-border-radius-md: 4px;
  --rtk-border-radius-lg: 8px;
  --rtk-button-border-radius: 6px;
  --rtk-color-white: #fff;
  --rtk-color-white-a-75: #ffffffbf;
  --rtk-color-black: #000;
  --rtk-color-gray-900: #1a1f36;
  --rtk-color-gray-800: #3c4257;
  --rtk-color-gray-700: #3c4257;
  --rtk-color-gray-600: #515669;
  --rtk-color-gray-500: #697386;
  --rtk-color-gray-400: #9ea0aa;
  --rtk-color-gray-300: #a5acb8;
  --rtk-color-gray-200: #dddee1;
  --rtk-color-gray-100: #e4e8ee;
  --rtk-color-brand-500: #e95744;
  --rtk-color-brand-700: #e4321b;
  --rtk-color-brand-900: #891e10;
  --rtk-button-hover-color: #f7f7f8;
  --rtk-connected-color: #33a366;
  --rtk-disconnect-border-color: #e64733;
  --rtk-disconnect-background-color: #fff5f5;
  --rtk-error-red: #cd7b2e;
}

.rtk-connect__button {
  background-color: var(--rtk-color-white);
  border: 1px solid var(--rtk-color-gray-200);
  border-radius: var(--rtk-button-border-radius);
  box-sizing: border-box;
  color: var(--rtk-color-black);
  cursor: pointer;
  font-family: var(--rtk-font-family-sanserif);
  font-size: var(--rtk-font-size-sm);
  font-weight: var(--rtk-font-weight-normal);
  gap: var(--rtk-spacing-2);
  padding: var(--rtk-spacing-1) var(--rtk-spacing-2);
  text-overflow: ellipsis;
  text-wrap: nowrap;
  text-decoration: none;
  transition: background-color .3s, border-color .3s, color .3s;
  display: inline-flex;
}

.rtk-connect__button--connected {
  border-color: var(--rtk-connected-color);
  color: var(--rtk-connected-color);
  width: 120px;
}

.rtk-connect__button--error {
  border-color: var(--rtk-error-red);
  color: var(--rtk-error-red);
}

.rtk-connect__button--loading {
  border-color: var(--rtk-color-gray-100);
  color: var(--rtk-color-gray-400);
  pointer-events: none;
}

.rtk-connect__button--disconnected:hover {
  background-color: var(--rtk-button-hover-color);
}

.rtk-connect__button--connected:hover, .rtk-connect__button__text--connected:hover {
  background-color: var(--rtk-disconnect-background-color);
  border-color: var(--rtk-disconnect-border-color);
  color: var(--rtk-disconnect-border-color);
}

.rtk-connect__button--error:hover, .rtk-connect__button__text--error:hover {
  border-color: var(--rtk-color-black);
  color: var(--rtk-color-black);
}

.rtk-connect__button:active {
  transform: translate(1px, 1px);
}

.rtk-auth {
  background: var(--rtk-color-white);
  border: 1px solid var(--rtk-color-gray-100);
  border-radius: var(--rtk-border-radius-lg);
  font-family: var(--rtk-font-family-sanserif);
  font-size: var(--rtk-font-size-sm);
  font-weight: var(--rtk-font-weight-normal);
  padding: var(--rtk-spacing-5);
}

.rtk-auth__header {
  flex-direction: row;
  justify-content: space-between;
  display: flex;
}

.rtk-auth__title {
  color: #1a1f36;
  font-size: var(--rtk-font-size-md);
  line-height: var(--rtk-spacing-5);
  margin-top: var(--rtk-spacing-4);
}

.rtk-auth__description {
  color: #515669;
  font-size: var(--rtk-font-size-sm);
  line-height: var(--rtk-spacing-5);
}

.rtk-combobox__grid {
  width: 352px;
  font-size: var(--rtk-font-size-sm);
  font-family: var(--rtk-font-family-sanserif);
  grid-template-columns: min-content 1fr;
  align-items: center;
  display: grid;
}

.rtk-combobox__search {
  font-family: var(--rtk-font-family-sanserif) !important;
}

.rtk-combobox__error {
  background-color: var(--rtk-color-gray-100);
  border-radius: var(--rtk-border-radius-md);
  align-items: flex-start;
  gap: var(--rtk-spacing-1);
  padding: var(--rtk-spacing-2);
  grid-column: span 2;
  display: flex;
}

:root {
  --rsk-font-size-xs: .75rem;
  --rsk-font-size-sm: .875rem;
  --rsk-font-size-md: 1rem;
  --rsk-font-size-lg: 1.125rem;
  --rsk-font-size-xl: 1.266rem;
  --rsk-font-size-2xl: 1.5rem;
  --rsk-font-size-3xl: 1.75rem;
  --rsk-spacing-0: 0rem;
  --rsk-spacing-1: .25rem;
  --rsk-spacing-2: .5rem;
  --rsk-spacing-3: .75rem;
  --rsk-spacing-4: 1rem;
  --rsk-spacing-5: 1.25rem;
  --rsk-spacing-6: 1.5rem;
  --rsk-spacing-7: 2rem;
  --rsk-font-weight-normal: 400;
  --rsk-font-weight-medium: 500;
  --rsk-font-weight-semibold: 600;
  --rsk-font-weight-bold: 700;
  --rsk-font-family-sanserif: Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Ubuntu, "Helvetica Neue", sans-serif;
  --rsk-border-radius-sm: 2px;
  --rsk-border-radius-md: 4px;
  --rsk-border-radius-lg: 8px;
  --rsk-button-border-radius: 6px;
  --rsk-color-white: #fff;
  --rsk-color-white-a-75: #ffffffbf;
  --rsk-color-black: #000;
  --rsk-color-gray-900: #1a1f36;
  --rsk-color-gray-800: #3c4257;
  --rsk-color-gray-700: #3c4257;
  --rsk-color-gray-600: #515669;
  --rsk-color-gray-500: #697386;
  --rsk-color-gray-400: #9ea0aa;
  --rsk-color-gray-300: #a5acb8;
  --rsk-color-gray-200: #dddee1;
  --rsk-color-gray-100: #e4e8ee;
  --rsk-color-brand-500: #e95744;
  --rsk-color-brand-700: #e4321b;
  --rsk-color-brand-900: #891e10;
  --rsk-button-hover-color: #f7f7f8;
  --rsk-connected-color: #33a366;
  --rsk-disconnect-border-color: #e64733;
  --rsk-disconnect-background-color: #fff5f5;
  --rsk-error-red: #cd7b2e;
}

.rsk-connect__button {
  background-color: var(--rsk-color-white);
  border: 1px solid var(--rsk-color-gray-200);
  border-radius: var(--rsk-button-border-radius);
  box-sizing: border-box;
  color: var(--rsk-color-black);
  cursor: pointer;
  font-family: var(--rsk-font-family-sanserif);
  font-size: var(--rsk-font-size-sm);
  font-weight: var(--rsk-font-weight-normal);
  gap: var(--rsk-spacing-2);
  padding: var(--rsk-spacing-1) var(--rsk-spacing-2);
  text-overflow: ellipsis;
  text-wrap: nowrap;
  text-decoration: none;
  transition: background-color .3s, border-color .3s, color .3s;
  display: inline-flex;
}

.rsk-connect__button--connected {
  border-color: var(--rsk-connected-color);
  color: var(--rsk-connected-color);
  width: 120px;
}

.rsk-connect__button--error {
  border-color: var(--rsk-error-red);
  color: var(--rsk-error-red);
}

.rsk-connect__button--loading {
  border-color: var(--rsk-color-gray-100);
  color: var(--rsk-color-gray-400);
  pointer-events: none;
}

.rsk-connect__button--disconnected:hover {
  background-color: var(--rsk-button-hover-color);
}

.rsk-connect__button--connected:hover, .rsk-connect__button__text--connected:hover {
  background-color: var(--rsk-disconnect-background-color);
  border-color: var(--rsk-disconnect-border-color);
  color: var(--rsk-disconnect-border-color);
}

.rsk-connect__button--error:hover, .rsk-connect__button__text--error:hover {
  border-color: var(--rsk-color-black);
  color: var(--rsk-color-black);
}

.rsk-connect__button:active {
  transform: translate(1px, 1px);
}

.rsk-auth {
  background: var(--rsk-color-white);
  border: 1px solid var(--rsk-color-gray-100);
  border-radius: var(--rsk-border-radius-lg);
  font-family: var(--rsk-font-family-sanserif);
  font-size: var(--rsk-font-size-sm);
  font-weight: var(--rsk-font-weight-normal);
  padding: var(--rsk-spacing-5);
}

.rsk-auth__header {
  flex-direction: row;
  justify-content: space-between;
  display: flex;
}

.rsk-auth__title {
  color: #1a1f36;
  font-size: var(--rsk-font-size-md);
  line-height: var(--rsk-spacing-5);
  margin-top: var(--rsk-spacing-4);
}

.rsk-auth__description {
  color: #515669;
  font-size: var(--rsk-font-size-sm);
  line-height: var(--rsk-spacing-5);
}

.rsk-connect-channel {
  align-items: center;
  gap: var(--rsk-spacing-1);
  display: flex;
}

.rsk-connect-channel__input {
  border: 1px solid var(--rsk-color-gray-200);
  border-radius: var(--rsk-border-radius-md);
  height: 32px;
  padding-left: var(--rsk-spacing-2);
  width: 270px;
}

.rsk-connect-channel__button {
  background-color: var(--rsk-color-white);
  border-color: var(--rsk-color-gray-200);
  border-radius: var(--rsk-button-border-radius);
  color: var(--rsk-color-black);
  font-family: var(--rsk-font-family-sanserif);
  font-size: var(--rsk-font-size-sm);
  font-weight: var(--rsk-font-weight-normal);
  gap: var(--rsk-spacing-2);
  height: 24px;
  padding: var(--rsk-spacing-2);
  text-decoration: none;
  display: inline-flex;
}

.rsk-connect-channel__button:hover {
  background-color: var(--rsk-button-hover-color);
}

.rsk-combobox__grid {
  width: 352px;
  font-size: var(--rsk-font-size-sm);
  font-family: var(--rsk-font-family-sanserif);
  grid-template-columns: min-content 1fr;
  align-items: center;
  display: grid;
}

.rsk-combobox__label {
  align-self: start;
  align-items: center;
  display: flex;
}

.rsk-combobox__search {
  font-family: var(--rsk-font-family-sanserif) !important;
}

.rsk-combobox__error {
  background-color: var(--rsk-color-gray-100);
  border-radius: var(--rsk-border-radius-md);
  align-items: flex-start;
  gap: var(--rsk-spacing-1);
  padding: var(--rsk-spacing-2);
  grid-column: span 2;
  display: flex;
}


/* [project]/packages/notifications/styles.css [app-client] (css) */
:root {
  --rnf-notification-icon-button-size: 1rem;
}

.rnf-notification-icon-button svg {
  width: var(--rnf-notification-icon-button-size);
  height: var(--rnf-notification-icon-button-size);
}


/*# sourceMappingURL=_fa0e5f4f._.css.map*/