{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40arcjet%2Banalyze-wasm%401.0.0-beta.7/node_modules/%40arcjet/analyze-wasm/wasm/arcjet_analyze_js_req.component.js"], "sourcesContent": ["function instantiate(getCoreModule, imports, instantiateCore = WebAssembly.instantiate) {\n  \n  class ComponentError extends Error {\n    constructor (value) {\n      const enumerable = typeof value !== 'string';\n      super(enumerable ? `${String(value)} (see error.payload)` : value);\n      Object.defineProperty(this, 'payload', { value, enumerable });\n    }\n  }\n  \n  let dv = new DataView(new ArrayBuffer());\n  const dataView = mem => dv.buffer === mem.buffer ? dv : dv = new DataView(mem.buffer);\n  \n  function throwInvalidBool() {\n    throw new TypeError('invalid variant discriminant for bool');\n  }\n  \n  function toUint32(val) {\n    return val >>> 0;\n  }\n  \n  const utf8Decoder = new TextDecoder();\n  \n  const utf8Encoder = new TextEncoder();\n  \n  let utf8EncodedLen = 0;\n  function utf8Encode(s, realloc, memory) {\n    if (typeof s !== 'string') throw new TypeError('expected a string');\n    if (s.length === 0) {\n      utf8EncodedLen = 0;\n      return 1;\n    }\n    let buf = utf8Encoder.encode(s);\n    let ptr = realloc(0, 0, 1, buf.length);\n    new Uint8Array(memory.buffer).set(buf, ptr);\n    utf8EncodedLen = buf.length;\n    return ptr;\n  }\n  \n  \n  const module0 = getCoreModule('arcjet_analyze_js_req.component.core.wasm');\n  const module1 = getCoreModule('arcjet_analyze_js_req.component.core2.wasm');\n  const module2 = getCoreModule('arcjet_analyze_js_req.component.core3.wasm');\n  \n  const { detect } = imports['arcjet:js-req/bot-identifier'];\n  const { hasGravatar, hasMxRecords, isDisposableEmail, isFreeEmail } = imports['arcjet:js-req/email-validator-overrides'];\n  const { detect: detect$1 } = imports['arcjet:js-req/sensitive-information-identifier'];\n  const { verify } = imports['arcjet:js-req/verify-bot'];\n  let gen = (function* init () {\n    let exports0;\n    let exports1;\n    let memory0;\n    let realloc0;\n    \n    function trampoline0(arg0, arg1, arg2) {\n      var ptr0 = arg0;\n      var len0 = arg1;\n      var result0 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr0, len0));\n      const ret = detect(result0);\n      var vec2 = ret;\n      var len2 = vec2.length;\n      var result2 = realloc0(0, 0, 4, len2 * 8);\n      for (let i = 0; i < vec2.length; i++) {\n        const e = vec2[i];\n        const base = result2 + i * 8;var ptr1 = utf8Encode(e, realloc0, memory0);\n        var len1 = utf8EncodedLen;\n        dataView(memory0).setInt32(base + 4, len1, true);\n        dataView(memory0).setInt32(base + 0, ptr1, true);\n      }\n      dataView(memory0).setInt32(arg2 + 4, len2, true);\n      dataView(memory0).setInt32(arg2 + 0, result2, true);\n    }\n    \n    function trampoline1(arg0, arg1, arg2, arg3) {\n      var ptr0 = arg0;\n      var len0 = arg1;\n      var result0 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr0, len0));\n      var ptr1 = arg2;\n      var len1 = arg3;\n      var result1 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr1, len1));\n      const ret = verify(result0, result1);\n      var val2 = ret;\n      let enum2;\n      switch (val2) {\n        case 'verified': {\n          enum2 = 0;\n          break;\n        }\n        case 'spoofed': {\n          enum2 = 1;\n          break;\n        }\n        case 'unverifiable': {\n          enum2 = 2;\n          break;\n        }\n        default: {\n          if ((ret) instanceof Error) {\n            console.error(ret);\n          }\n          \n          throw new TypeError(`\"${val2}\" is not one of the cases of validator-response`);\n        }\n      }\n      return enum2;\n    }\n    \n    function trampoline2(arg0, arg1) {\n      var ptr0 = arg0;\n      var len0 = arg1;\n      var result0 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr0, len0));\n      const ret = isFreeEmail(result0);\n      var val1 = ret;\n      let enum1;\n      switch (val1) {\n        case 'yes': {\n          enum1 = 0;\n          break;\n        }\n        case 'no': {\n          enum1 = 1;\n          break;\n        }\n        case 'unknown': {\n          enum1 = 2;\n          break;\n        }\n        default: {\n          if ((ret) instanceof Error) {\n            console.error(ret);\n          }\n          \n          throw new TypeError(`\"${val1}\" is not one of the cases of validator-response`);\n        }\n      }\n      return enum1;\n    }\n    \n    function trampoline3(arg0, arg1) {\n      var ptr0 = arg0;\n      var len0 = arg1;\n      var result0 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr0, len0));\n      const ret = isDisposableEmail(result0);\n      var val1 = ret;\n      let enum1;\n      switch (val1) {\n        case 'yes': {\n          enum1 = 0;\n          break;\n        }\n        case 'no': {\n          enum1 = 1;\n          break;\n        }\n        case 'unknown': {\n          enum1 = 2;\n          break;\n        }\n        default: {\n          if ((ret) instanceof Error) {\n            console.error(ret);\n          }\n          \n          throw new TypeError(`\"${val1}\" is not one of the cases of validator-response`);\n        }\n      }\n      return enum1;\n    }\n    \n    function trampoline4(arg0, arg1) {\n      var ptr0 = arg0;\n      var len0 = arg1;\n      var result0 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr0, len0));\n      const ret = hasMxRecords(result0);\n      var val1 = ret;\n      let enum1;\n      switch (val1) {\n        case 'yes': {\n          enum1 = 0;\n          break;\n        }\n        case 'no': {\n          enum1 = 1;\n          break;\n        }\n        case 'unknown': {\n          enum1 = 2;\n          break;\n        }\n        default: {\n          if ((ret) instanceof Error) {\n            console.error(ret);\n          }\n          \n          throw new TypeError(`\"${val1}\" is not one of the cases of validator-response`);\n        }\n      }\n      return enum1;\n    }\n    \n    function trampoline5(arg0, arg1) {\n      var ptr0 = arg0;\n      var len0 = arg1;\n      var result0 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr0, len0));\n      const ret = hasGravatar(result0);\n      var val1 = ret;\n      let enum1;\n      switch (val1) {\n        case 'yes': {\n          enum1 = 0;\n          break;\n        }\n        case 'no': {\n          enum1 = 1;\n          break;\n        }\n        case 'unknown': {\n          enum1 = 2;\n          break;\n        }\n        default: {\n          if ((ret) instanceof Error) {\n            console.error(ret);\n          }\n          \n          throw new TypeError(`\"${val1}\" is not one of the cases of validator-response`);\n        }\n      }\n      return enum1;\n    }\n    \n    function trampoline6(arg0, arg1, arg2) {\n      var len1 = arg1;\n      var base1 = arg0;\n      var result1 = [];\n      for (let i = 0; i < len1; i++) {\n        const base = base1 + i * 8;\n        var ptr0 = dataView(memory0).getInt32(base + 0, true);\n        var len0 = dataView(memory0).getInt32(base + 4, true);\n        var result0 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr0, len0));\n        result1.push(result0);\n      }\n      const ret = detect$1(result1);\n      var vec5 = ret;\n      var len5 = vec5.length;\n      var result5 = realloc0(0, 0, 4, len5 * 16);\n      for (let i = 0; i < vec5.length; i++) {\n        const e = vec5[i];\n        const base = result5 + i * 16;var variant4 = e;\n        if (variant4 === null || variant4=== undefined) {\n          dataView(memory0).setInt8(base + 0, 0, true);\n        } else {\n          const e = variant4;\n          dataView(memory0).setInt8(base + 0, 1, true);\n          var variant3 = e;\n          switch (variant3.tag) {\n            case 'email': {\n              dataView(memory0).setInt8(base + 4, 0, true);\n              break;\n            }\n            case 'phone-number': {\n              dataView(memory0).setInt8(base + 4, 1, true);\n              break;\n            }\n            case 'ip-address': {\n              dataView(memory0).setInt8(base + 4, 2, true);\n              break;\n            }\n            case 'credit-card-number': {\n              dataView(memory0).setInt8(base + 4, 3, true);\n              break;\n            }\n            case 'custom': {\n              const e = variant3.val;\n              dataView(memory0).setInt8(base + 4, 4, true);\n              var ptr2 = utf8Encode(e, realloc0, memory0);\n              var len2 = utf8EncodedLen;\n              dataView(memory0).setInt32(base + 12, len2, true);\n              dataView(memory0).setInt32(base + 8, ptr2, true);\n              break;\n            }\n            default: {\n              throw new TypeError(`invalid variant tag value \\`${JSON.stringify(variant3.tag)}\\` (received \\`${variant3}\\`) specified for \\`SensitiveInfoEntity\\``);\n            }\n          }\n        }\n      }\n      dataView(memory0).setInt32(arg2 + 4, len5, true);\n      dataView(memory0).setInt32(arg2 + 0, result5, true);\n    }\n    let exports2;\n    let postReturn0;\n    let postReturn1;\n    let postReturn2;\n    let postReturn3;\n    let postReturn4;\n    Promise.all([module0, module1, module2]).catch(() => {});\n    ({ exports: exports0 } = yield instantiateCore(yield module1));\n    ({ exports: exports1 } = yield instantiateCore(yield module0, {\n      'arcjet:js-req/bot-identifier': {\n        detect: exports0['0'],\n      },\n      'arcjet:js-req/email-validator-overrides': {\n        'has-gravatar': exports0['5'],\n        'has-mx-records': exports0['4'],\n        'is-disposable-email': exports0['3'],\n        'is-free-email': exports0['2'],\n      },\n      'arcjet:js-req/sensitive-information-identifier': {\n        detect: exports0['6'],\n      },\n      'arcjet:js-req/verify-bot': {\n        verify: exports0['1'],\n      },\n    }));\n    memory0 = exports1.memory;\n    realloc0 = exports1.cabi_realloc;\n    ({ exports: exports2 } = yield instantiateCore(yield module2, {\n      '': {\n        $imports: exports0.$imports,\n        '0': trampoline0,\n        '1': trampoline1,\n        '2': trampoline2,\n        '3': trampoline3,\n        '4': trampoline4,\n        '5': trampoline5,\n        '6': trampoline6,\n      },\n    }));\n    postReturn0 = exports1['cabi_post_detect-bot'];\n    postReturn1 = exports1['cabi_post_generate-fingerprint'];\n    postReturn2 = exports1['cabi_post_validate-characteristics'];\n    postReturn3 = exports1['cabi_post_is-valid-email'];\n    postReturn4 = exports1['cabi_post_detect-sensitive-info'];\n    \n    function detectBot(arg0, arg1) {\n      var ptr0 = utf8Encode(arg0, realloc0, memory0);\n      var len0 = utf8EncodedLen;\n      var variant7 = arg1;\n      let variant7_0;\n      let variant7_1;\n      let variant7_2;\n      let variant7_3;\n      switch (variant7.tag) {\n        case 'allowed-bot-config': {\n          const e = variant7.val;\n          var {entities: v1_0, skipCustomDetect: v1_1 } = e;\n          var vec3 = v1_0;\n          var len3 = vec3.length;\n          var result3 = realloc0(0, 0, 4, len3 * 8);\n          for (let i = 0; i < vec3.length; i++) {\n            const e = vec3[i];\n            const base = result3 + i * 8;var ptr2 = utf8Encode(e, realloc0, memory0);\n            var len2 = utf8EncodedLen;\n            dataView(memory0).setInt32(base + 4, len2, true);\n            dataView(memory0).setInt32(base + 0, ptr2, true);\n          }\n          variant7_0 = 0;\n          variant7_1 = result3;\n          variant7_2 = len3;\n          variant7_3 = v1_1 ? 1 : 0;\n          break;\n        }\n        case 'denied-bot-config': {\n          const e = variant7.val;\n          var {entities: v4_0, skipCustomDetect: v4_1 } = e;\n          var vec6 = v4_0;\n          var len6 = vec6.length;\n          var result6 = realloc0(0, 0, 4, len6 * 8);\n          for (let i = 0; i < vec6.length; i++) {\n            const e = vec6[i];\n            const base = result6 + i * 8;var ptr5 = utf8Encode(e, realloc0, memory0);\n            var len5 = utf8EncodedLen;\n            dataView(memory0).setInt32(base + 4, len5, true);\n            dataView(memory0).setInt32(base + 0, ptr5, true);\n          }\n          variant7_0 = 1;\n          variant7_1 = result6;\n          variant7_2 = len6;\n          variant7_3 = v4_1 ? 1 : 0;\n          break;\n        }\n        default: {\n          throw new TypeError(`invalid variant tag value \\`${JSON.stringify(variant7.tag)}\\` (received \\`${variant7}\\`) specified for \\`BotConfig\\``);\n        }\n      }\n      const ret = exports1['detect-bot'](ptr0, len0, variant7_0, variant7_1, variant7_2, variant7_3);\n      let variant15;\n      switch (dataView(memory0).getUint8(ret + 0, true)) {\n        case 0: {\n          var len9 = dataView(memory0).getInt32(ret + 8, true);\n          var base9 = dataView(memory0).getInt32(ret + 4, true);\n          var result9 = [];\n          for (let i = 0; i < len9; i++) {\n            const base = base9 + i * 8;\n            var ptr8 = dataView(memory0).getInt32(base + 0, true);\n            var len8 = dataView(memory0).getInt32(base + 4, true);\n            var result8 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr8, len8));\n            result9.push(result8);\n          }\n          var len11 = dataView(memory0).getInt32(ret + 16, true);\n          var base11 = dataView(memory0).getInt32(ret + 12, true);\n          var result11 = [];\n          for (let i = 0; i < len11; i++) {\n            const base = base11 + i * 8;\n            var ptr10 = dataView(memory0).getInt32(base + 0, true);\n            var len10 = dataView(memory0).getInt32(base + 4, true);\n            var result10 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr10, len10));\n            result11.push(result10);\n          }\n          var bool12 = dataView(memory0).getUint8(ret + 20, true);\n          var bool13 = dataView(memory0).getUint8(ret + 21, true);\n          variant15= {\n            tag: 'ok',\n            val: {\n              allowed: result9,\n              denied: result11,\n              verified: bool12 == 0 ? false : (bool12 == 1 ? true : throwInvalidBool()),\n              spoofed: bool13 == 0 ? false : (bool13 == 1 ? true : throwInvalidBool()),\n            }\n          };\n          break;\n        }\n        case 1: {\n          var ptr14 = dataView(memory0).getInt32(ret + 4, true);\n          var len14 = dataView(memory0).getInt32(ret + 8, true);\n          var result14 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr14, len14));\n          variant15= {\n            tag: 'err',\n            val: result14\n          };\n          break;\n        }\n        default: {\n          throw new TypeError('invalid variant discriminant for expected');\n        }\n      }\n      const retVal = variant15;\n      postReturn0(ret);\n      if (typeof retVal === 'object' && retVal.tag === 'err') {\n        throw new ComponentError(retVal.val);\n      }\n      return retVal.val;\n    }\n    \n    function generateFingerprint(arg0, arg1) {\n      var ptr0 = utf8Encode(arg0, realloc0, memory0);\n      var len0 = utf8EncodedLen;\n      var vec2 = arg1;\n      var len2 = vec2.length;\n      var result2 = realloc0(0, 0, 4, len2 * 8);\n      for (let i = 0; i < vec2.length; i++) {\n        const e = vec2[i];\n        const base = result2 + i * 8;var ptr1 = utf8Encode(e, realloc0, memory0);\n        var len1 = utf8EncodedLen;\n        dataView(memory0).setInt32(base + 4, len1, true);\n        dataView(memory0).setInt32(base + 0, ptr1, true);\n      }\n      const ret = exports1['generate-fingerprint'](ptr0, len0, result2, len2);\n      let variant5;\n      switch (dataView(memory0).getUint8(ret + 0, true)) {\n        case 0: {\n          var ptr3 = dataView(memory0).getInt32(ret + 4, true);\n          var len3 = dataView(memory0).getInt32(ret + 8, true);\n          var result3 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr3, len3));\n          variant5= {\n            tag: 'ok',\n            val: result3\n          };\n          break;\n        }\n        case 1: {\n          var ptr4 = dataView(memory0).getInt32(ret + 4, true);\n          var len4 = dataView(memory0).getInt32(ret + 8, true);\n          var result4 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr4, len4));\n          variant5= {\n            tag: 'err',\n            val: result4\n          };\n          break;\n        }\n        default: {\n          throw new TypeError('invalid variant discriminant for expected');\n        }\n      }\n      const retVal = variant5;\n      postReturn1(ret);\n      if (typeof retVal === 'object' && retVal.tag === 'err') {\n        throw new ComponentError(retVal.val);\n      }\n      return retVal.val;\n    }\n    \n    function validateCharacteristics(arg0, arg1) {\n      var ptr0 = utf8Encode(arg0, realloc0, memory0);\n      var len0 = utf8EncodedLen;\n      var vec2 = arg1;\n      var len2 = vec2.length;\n      var result2 = realloc0(0, 0, 4, len2 * 8);\n      for (let i = 0; i < vec2.length; i++) {\n        const e = vec2[i];\n        const base = result2 + i * 8;var ptr1 = utf8Encode(e, realloc0, memory0);\n        var len1 = utf8EncodedLen;\n        dataView(memory0).setInt32(base + 4, len1, true);\n        dataView(memory0).setInt32(base + 0, ptr1, true);\n      }\n      const ret = exports1['validate-characteristics'](ptr0, len0, result2, len2);\n      let variant4;\n      switch (dataView(memory0).getUint8(ret + 0, true)) {\n        case 0: {\n          variant4= {\n            tag: 'ok',\n            val: undefined\n          };\n          break;\n        }\n        case 1: {\n          var ptr3 = dataView(memory0).getInt32(ret + 4, true);\n          var len3 = dataView(memory0).getInt32(ret + 8, true);\n          var result3 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr3, len3));\n          variant4= {\n            tag: 'err',\n            val: result3\n          };\n          break;\n        }\n        default: {\n          throw new TypeError('invalid variant discriminant for expected');\n        }\n      }\n      const retVal = variant4;\n      postReturn2(ret);\n      if (typeof retVal === 'object' && retVal.tag === 'err') {\n        throw new ComponentError(retVal.val);\n      }\n      return retVal.val;\n    }\n    \n    function isValidEmail(arg0, arg1) {\n      var ptr0 = utf8Encode(arg0, realloc0, memory0);\n      var len0 = utf8EncodedLen;\n      var variant7 = arg1;\n      let variant7_0;\n      let variant7_1;\n      let variant7_2;\n      let variant7_3;\n      let variant7_4;\n      switch (variant7.tag) {\n        case 'allow-email-validation-config': {\n          const e = variant7.val;\n          var {requireTopLevelDomain: v1_0, allowDomainLiteral: v1_1, allow: v1_2 } = e;\n          var vec3 = v1_2;\n          var len3 = vec3.length;\n          var result3 = realloc0(0, 0, 4, len3 * 8);\n          for (let i = 0; i < vec3.length; i++) {\n            const e = vec3[i];\n            const base = result3 + i * 8;var ptr2 = utf8Encode(e, realloc0, memory0);\n            var len2 = utf8EncodedLen;\n            dataView(memory0).setInt32(base + 4, len2, true);\n            dataView(memory0).setInt32(base + 0, ptr2, true);\n          }\n          variant7_0 = 0;\n          variant7_1 = v1_0 ? 1 : 0;\n          variant7_2 = v1_1 ? 1 : 0;\n          variant7_3 = result3;\n          variant7_4 = len3;\n          break;\n        }\n        case 'deny-email-validation-config': {\n          const e = variant7.val;\n          var {requireTopLevelDomain: v4_0, allowDomainLiteral: v4_1, deny: v4_2 } = e;\n          var vec6 = v4_2;\n          var len6 = vec6.length;\n          var result6 = realloc0(0, 0, 4, len6 * 8);\n          for (let i = 0; i < vec6.length; i++) {\n            const e = vec6[i];\n            const base = result6 + i * 8;var ptr5 = utf8Encode(e, realloc0, memory0);\n            var len5 = utf8EncodedLen;\n            dataView(memory0).setInt32(base + 4, len5, true);\n            dataView(memory0).setInt32(base + 0, ptr5, true);\n          }\n          variant7_0 = 1;\n          variant7_1 = v4_0 ? 1 : 0;\n          variant7_2 = v4_1 ? 1 : 0;\n          variant7_3 = result6;\n          variant7_4 = len6;\n          break;\n        }\n        default: {\n          throw new TypeError(`invalid variant tag value \\`${JSON.stringify(variant7.tag)}\\` (received \\`${variant7}\\`) specified for \\`EmailValidationConfig\\``);\n        }\n      }\n      const ret = exports1['is-valid-email'](ptr0, len0, variant7_0, variant7_1, variant7_2, variant7_3, variant7_4);\n      let variant12;\n      switch (dataView(memory0).getUint8(ret + 0, true)) {\n        case 0: {\n          let enum8;\n          switch (dataView(memory0).getUint8(ret + 4, true)) {\n            case 0: {\n              enum8 = 'valid';\n              break;\n            }\n            case 1: {\n              enum8 = 'invalid';\n              break;\n            }\n            default: {\n              throw new TypeError('invalid discriminant specified for EmailValidity');\n            }\n          }\n          var len10 = dataView(memory0).getInt32(ret + 12, true);\n          var base10 = dataView(memory0).getInt32(ret + 8, true);\n          var result10 = [];\n          for (let i = 0; i < len10; i++) {\n            const base = base10 + i * 8;\n            var ptr9 = dataView(memory0).getInt32(base + 0, true);\n            var len9 = dataView(memory0).getInt32(base + 4, true);\n            var result9 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr9, len9));\n            result10.push(result9);\n          }\n          variant12= {\n            tag: 'ok',\n            val: {\n              validity: enum8,\n              blocked: result10,\n            }\n          };\n          break;\n        }\n        case 1: {\n          var ptr11 = dataView(memory0).getInt32(ret + 4, true);\n          var len11 = dataView(memory0).getInt32(ret + 8, true);\n          var result11 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr11, len11));\n          variant12= {\n            tag: 'err',\n            val: result11\n          };\n          break;\n        }\n        default: {\n          throw new TypeError('invalid variant discriminant for expected');\n        }\n      }\n      const retVal = variant12;\n      postReturn3(ret);\n      if (typeof retVal === 'object' && retVal.tag === 'err') {\n        throw new ComponentError(retVal.val);\n      }\n      return retVal.val;\n    }\n    \n    function detectSensitiveInfo(arg0, arg1) {\n      var ptr0 = utf8Encode(arg0, realloc0, memory0);\n      var len0 = utf8EncodedLen;\n      var {entities: v1_0, contextWindowSize: v1_1, skipCustomDetect: v1_2 } = arg1;\n      var variant8 = v1_0;\n      let variant8_0;\n      let variant8_1;\n      let variant8_2;\n      switch (variant8.tag) {\n        case 'allow': {\n          const e = variant8.val;\n          var vec4 = e;\n          var len4 = vec4.length;\n          var result4 = realloc0(0, 0, 4, len4 * 12);\n          for (let i = 0; i < vec4.length; i++) {\n            const e = vec4[i];\n            const base = result4 + i * 12;var variant3 = e;\n            switch (variant3.tag) {\n              case 'email': {\n                dataView(memory0).setInt8(base + 0, 0, true);\n                break;\n              }\n              case 'phone-number': {\n                dataView(memory0).setInt8(base + 0, 1, true);\n                break;\n              }\n              case 'ip-address': {\n                dataView(memory0).setInt8(base + 0, 2, true);\n                break;\n              }\n              case 'credit-card-number': {\n                dataView(memory0).setInt8(base + 0, 3, true);\n                break;\n              }\n              case 'custom': {\n                const e = variant3.val;\n                dataView(memory0).setInt8(base + 0, 4, true);\n                var ptr2 = utf8Encode(e, realloc0, memory0);\n                var len2 = utf8EncodedLen;\n                dataView(memory0).setInt32(base + 8, len2, true);\n                dataView(memory0).setInt32(base + 4, ptr2, true);\n                break;\n              }\n              default: {\n                throw new TypeError(`invalid variant tag value \\`${JSON.stringify(variant3.tag)}\\` (received \\`${variant3}\\`) specified for \\`SensitiveInfoEntity\\``);\n              }\n            }\n          }\n          variant8_0 = 0;\n          variant8_1 = result4;\n          variant8_2 = len4;\n          break;\n        }\n        case 'deny': {\n          const e = variant8.val;\n          var vec7 = e;\n          var len7 = vec7.length;\n          var result7 = realloc0(0, 0, 4, len7 * 12);\n          for (let i = 0; i < vec7.length; i++) {\n            const e = vec7[i];\n            const base = result7 + i * 12;var variant6 = e;\n            switch (variant6.tag) {\n              case 'email': {\n                dataView(memory0).setInt8(base + 0, 0, true);\n                break;\n              }\n              case 'phone-number': {\n                dataView(memory0).setInt8(base + 0, 1, true);\n                break;\n              }\n              case 'ip-address': {\n                dataView(memory0).setInt8(base + 0, 2, true);\n                break;\n              }\n              case 'credit-card-number': {\n                dataView(memory0).setInt8(base + 0, 3, true);\n                break;\n              }\n              case 'custom': {\n                const e = variant6.val;\n                dataView(memory0).setInt8(base + 0, 4, true);\n                var ptr5 = utf8Encode(e, realloc0, memory0);\n                var len5 = utf8EncodedLen;\n                dataView(memory0).setInt32(base + 8, len5, true);\n                dataView(memory0).setInt32(base + 4, ptr5, true);\n                break;\n              }\n              default: {\n                throw new TypeError(`invalid variant tag value \\`${JSON.stringify(variant6.tag)}\\` (received \\`${variant6}\\`) specified for \\`SensitiveInfoEntity\\``);\n              }\n            }\n          }\n          variant8_0 = 1;\n          variant8_1 = result7;\n          variant8_2 = len7;\n          break;\n        }\n        default: {\n          throw new TypeError(`invalid variant tag value \\`${JSON.stringify(variant8.tag)}\\` (received \\`${variant8}\\`) specified for \\`SensitiveInfoEntities\\``);\n        }\n      }\n      var variant9 = v1_1;\n      let variant9_0;\n      let variant9_1;\n      if (variant9 === null || variant9=== undefined) {\n        variant9_0 = 0;\n        variant9_1 = 0;\n      } else {\n        const e = variant9;\n        variant9_0 = 1;\n        variant9_1 = toUint32(e);\n      }\n      const ret = exports1['detect-sensitive-info'](ptr0, len0, variant8_0, variant8_1, variant8_2, variant9_0, variant9_1, v1_2 ? 1 : 0);\n      var len12 = dataView(memory0).getInt32(ret + 4, true);\n      var base12 = dataView(memory0).getInt32(ret + 0, true);\n      var result12 = [];\n      for (let i = 0; i < len12; i++) {\n        const base = base12 + i * 20;\n        let variant11;\n        switch (dataView(memory0).getUint8(base + 8, true)) {\n          case 0: {\n            variant11= {\n              tag: 'email',\n            };\n            break;\n          }\n          case 1: {\n            variant11= {\n              tag: 'phone-number',\n            };\n            break;\n          }\n          case 2: {\n            variant11= {\n              tag: 'ip-address',\n            };\n            break;\n          }\n          case 3: {\n            variant11= {\n              tag: 'credit-card-number',\n            };\n            break;\n          }\n          case 4: {\n            var ptr10 = dataView(memory0).getInt32(base + 12, true);\n            var len10 = dataView(memory0).getInt32(base + 16, true);\n            var result10 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr10, len10));\n            variant11= {\n              tag: 'custom',\n              val: result10\n            };\n            break;\n          }\n          default: {\n            throw new TypeError('invalid variant discriminant for SensitiveInfoEntity');\n          }\n        }\n        result12.push({\n          start: dataView(memory0).getInt32(base + 0, true) >>> 0,\n          end: dataView(memory0).getInt32(base + 4, true) >>> 0,\n          identifiedType: variant11,\n        });\n      }\n      var len15 = dataView(memory0).getInt32(ret + 12, true);\n      var base15 = dataView(memory0).getInt32(ret + 8, true);\n      var result15 = [];\n      for (let i = 0; i < len15; i++) {\n        const base = base15 + i * 20;\n        let variant14;\n        switch (dataView(memory0).getUint8(base + 8, true)) {\n          case 0: {\n            variant14= {\n              tag: 'email',\n            };\n            break;\n          }\n          case 1: {\n            variant14= {\n              tag: 'phone-number',\n            };\n            break;\n          }\n          case 2: {\n            variant14= {\n              tag: 'ip-address',\n            };\n            break;\n          }\n          case 3: {\n            variant14= {\n              tag: 'credit-card-number',\n            };\n            break;\n          }\n          case 4: {\n            var ptr13 = dataView(memory0).getInt32(base + 12, true);\n            var len13 = dataView(memory0).getInt32(base + 16, true);\n            var result13 = utf8Decoder.decode(new Uint8Array(memory0.buffer, ptr13, len13));\n            variant14= {\n              tag: 'custom',\n              val: result13\n            };\n            break;\n          }\n          default: {\n            throw new TypeError('invalid variant discriminant for SensitiveInfoEntity');\n          }\n        }\n        result15.push({\n          start: dataView(memory0).getInt32(base + 0, true) >>> 0,\n          end: dataView(memory0).getInt32(base + 4, true) >>> 0,\n          identifiedType: variant14,\n        });\n      }\n      const retVal = {\n        allowed: result12,\n        denied: result15,\n      };\n      postReturn4(ret);\n      return retVal;\n    }\n    \n    return { detectBot, detectSensitiveInfo, generateFingerprint, isValidEmail, validateCharacteristics,  };\n  })();\n  let promise, resolve, reject;\n  function runNext (value) {\n    try {\n      let done;\n      do {\n        ({ value, done } = gen.next(value));\n      } while (!(value instanceof Promise) && !done);\n      if (done) {\n        if (resolve) resolve(value);\n        else return value;\n      }\n      if (!promise) promise = new Promise((_resolve, _reject) => (resolve = _resolve, reject = _reject));\n      value.then(nextVal => done ? resolve() : runNext(nextVal), reject);\n    }\n    catch (e) {\n      if (reject) reject(e);\n      else throw e;\n    }\n  }\n  const maybeSyncReturn = runNext(null);\n  return promise || maybeSyncReturn;\n}\n\nexport { instantiate };\n"], "names": [], "mappings": ";;;AAAA,SAAS,YAAY,aAAa,EAAE,OAAO,EAAE,kBAAkB,YAAY,WAAW;IAEpF,MAAM,uBAAuB;QAC3B,YAAa,KAAK,CAAE;YAClB,MAAM,aAAa,OAAO,UAAU;YACpC,KAAK,CAAC,aAAa,GAAG,OAAO,OAAO,oBAAoB,CAAC,GAAG;YAC5D,OAAO,cAAc,CAAC,IAAI,EAAE,WAAW;gBAAE;gBAAO;YAAW;QAC7D;IACF;IAEA,IAAI,KAAK,IAAI,SAAS,IAAI;IAC1B,MAAM,WAAW,CAAA,MAAO,GAAG,MAAM,KAAK,IAAI,MAAM,GAAG,KAAK,KAAK,IAAI,SAAS,IAAI,MAAM;IAEpF,SAAS;QACP,MAAM,IAAI,UAAU;IACtB;IAEA,SAAS,SAAS,GAAG;QACnB,OAAO,QAAQ;IACjB;IAEA,MAAM,cAAc,IAAI;IAExB,MAAM,cAAc,IAAI;IAExB,IAAI,iBAAiB;IACrB,SAAS,WAAW,CAAC,EAAE,OAAO,EAAE,MAAM;QACpC,IAAI,OAAO,MAAM,UAAU,MAAM,IAAI,UAAU;QAC/C,IAAI,EAAE,MAAM,KAAK,GAAG;YAClB,iBAAiB;YACjB,OAAO;QACT;QACA,IAAI,MAAM,YAAY,MAAM,CAAC;QAC7B,IAAI,MAAM,QAAQ,GAAG,GAAG,GAAG,IAAI,MAAM;QACrC,IAAI,WAAW,OAAO,MAAM,EAAE,GAAG,CAAC,KAAK;QACvC,iBAAiB,IAAI,MAAM;QAC3B,OAAO;IACT;IAGA,MAAM,UAAU,cAAc;IAC9B,MAAM,UAAU,cAAc;IAC9B,MAAM,UAAU,cAAc;IAE9B,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,+BAA+B;IAC1D,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC,0CAA0C;IACxH,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,OAAO,CAAC,iDAAiD;IACtF,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,2BAA2B;IACtD,IAAI,MAAM,AAAC,UAAU;QACnB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QAEJ,SAAS,YAAY,IAAI,EAAE,IAAI,EAAE,IAAI;YACnC,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;YACtE,MAAM,MAAM,OAAO;YACnB,IAAI,OAAO;YACX,IAAI,OAAO,KAAK,MAAM;YACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;YACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,MAAM,IAAI,IAAI,CAAC,EAAE;gBACjB,MAAM,OAAO,UAAU,IAAI;gBAAE,IAAI,OAAO,WAAW,GAAG,UAAU;gBAChE,IAAI,OAAO;gBACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;gBAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;YAC7C;YACA,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;YAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,SAAS;QAChD;QAEA,SAAS,YAAY,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;YACzC,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;YACtE,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;YACtE,MAAM,MAAM,OAAO,SAAS;YAC5B,IAAI,OAAO;YACX,IAAI;YACJ,OAAQ;gBACN,KAAK;oBAAY;wBACf,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAW;wBACd,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAgB;wBACnB,QAAQ;wBACR;oBACF;gBACA;oBAAS;wBACP,IAAI,AAAC,eAAgB,OAAO;4BAC1B,QAAQ,KAAK,CAAC;wBAChB;wBAEA,MAAM,IAAI,UAAU,CAAC,CAAC,EAAE,KAAK,+CAA+C,CAAC;oBAC/E;YACF;YACA,OAAO;QACT;QAEA,SAAS,YAAY,IAAI,EAAE,IAAI;YAC7B,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;YACtE,MAAM,MAAM,YAAY;YACxB,IAAI,OAAO;YACX,IAAI;YACJ,OAAQ;gBACN,KAAK;oBAAO;wBACV,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAM;wBACT,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAW;wBACd,QAAQ;wBACR;oBACF;gBACA;oBAAS;wBACP,IAAI,AAAC,eAAgB,OAAO;4BAC1B,QAAQ,KAAK,CAAC;wBAChB;wBAEA,MAAM,IAAI,UAAU,CAAC,CAAC,EAAE,KAAK,+CAA+C,CAAC;oBAC/E;YACF;YACA,OAAO;QACT;QAEA,SAAS,YAAY,IAAI,EAAE,IAAI;YAC7B,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;YACtE,MAAM,MAAM,kBAAkB;YAC9B,IAAI,OAAO;YACX,IAAI;YACJ,OAAQ;gBACN,KAAK;oBAAO;wBACV,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAM;wBACT,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAW;wBACd,QAAQ;wBACR;oBACF;gBACA;oBAAS;wBACP,IAAI,AAAC,eAAgB,OAAO;4BAC1B,QAAQ,KAAK,CAAC;wBAChB;wBAEA,MAAM,IAAI,UAAU,CAAC,CAAC,EAAE,KAAK,+CAA+C,CAAC;oBAC/E;YACF;YACA,OAAO;QACT;QAEA,SAAS,YAAY,IAAI,EAAE,IAAI;YAC7B,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;YACtE,MAAM,MAAM,aAAa;YACzB,IAAI,OAAO;YACX,IAAI;YACJ,OAAQ;gBACN,KAAK;oBAAO;wBACV,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAM;wBACT,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAW;wBACd,QAAQ;wBACR;oBACF;gBACA;oBAAS;wBACP,IAAI,AAAC,eAAgB,OAAO;4BAC1B,QAAQ,KAAK,CAAC;wBAChB;wBAEA,MAAM,IAAI,UAAU,CAAC,CAAC,EAAE,KAAK,+CAA+C,CAAC;oBAC/E;YACF;YACA,OAAO;QACT;QAEA,SAAS,YAAY,IAAI,EAAE,IAAI;YAC7B,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;YACtE,MAAM,MAAM,YAAY;YACxB,IAAI,OAAO;YACX,IAAI;YACJ,OAAQ;gBACN,KAAK;oBAAO;wBACV,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAM;wBACT,QAAQ;wBACR;oBACF;gBACA,KAAK;oBAAW;wBACd,QAAQ;wBACR;oBACF;gBACA;oBAAS;wBACP,IAAI,AAAC,eAAgB,OAAO;4BAC1B,QAAQ,KAAK,CAAC;wBAChB;wBAEA,MAAM,IAAI,UAAU,CAAC,CAAC,EAAE,KAAK,+CAA+C,CAAC;oBAC/E;YACF;YACA,OAAO;QACT;QAEA,SAAS,YAAY,IAAI,EAAE,IAAI,EAAE,IAAI;YACnC,IAAI,OAAO;YACX,IAAI,QAAQ;YACZ,IAAI,UAAU,EAAE;YAChB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;gBAC7B,MAAM,OAAO,QAAQ,IAAI;gBACzB,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;gBAChD,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;gBAChD,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;gBACtE,QAAQ,IAAI,CAAC;YACf;YACA,MAAM,MAAM,SAAS;YACrB,IAAI,OAAO;YACX,IAAI,OAAO,KAAK,MAAM;YACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;YACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,MAAM,IAAI,IAAI,CAAC,EAAE;gBACjB,MAAM,OAAO,UAAU,IAAI;gBAAG,IAAI,WAAW;gBAC7C,IAAI,aAAa,QAAQ,aAAY,WAAW;oBAC9C,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;gBACzC,OAAO;oBACL,MAAM,IAAI;oBACV,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;oBACvC,IAAI,WAAW;oBACf,OAAQ,SAAS,GAAG;wBAClB,KAAK;4BAAS;gCACZ,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;gCACvC;4BACF;wBACA,KAAK;4BAAgB;gCACnB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;gCACvC;4BACF;wBACA,KAAK;4BAAc;gCACjB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;gCACvC;4BACF;wBACA,KAAK;4BAAsB;gCACzB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;gCACvC;4BACF;wBACA,KAAK;4BAAU;gCACb,MAAM,IAAI,SAAS,GAAG;gCACtB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;gCACvC,IAAI,OAAO,WAAW,GAAG,UAAU;gCACnC,IAAI,OAAO;gCACX,SAAS,SAAS,QAAQ,CAAC,OAAO,IAAI,MAAM;gCAC5C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;gCAC3C;4BACF;wBACA;4BAAS;gCACP,MAAM,IAAI,UAAU,CAAC,4BAA4B,EAAE,KAAK,SAAS,CAAC,SAAS,GAAG,EAAE,eAAe,EAAE,SAAS,yCAAyC,CAAC;4BACtJ;oBACF;gBACF;YACF;YACA,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;YAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,SAAS;QAChD;QACA,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,QAAQ,GAAG,CAAC;YAAC;YAAS;YAAS;SAAQ,EAAE,KAAK,CAAC,KAAO;QACtD,CAAC,EAAE,SAAS,QAAQ,EAAE,GAAG,MAAM,gBAAgB,CAAA,MAAM,OAAM,EAAE;QAC7D,CAAC,EAAE,SAAS,QAAQ,EAAE,GAAG,MAAM,gBAAgB,CAAA,MAAM,OAAM,GAAG;YAC5D,gCAAgC;gBAC9B,QAAQ,QAAQ,CAAC,IAAI;YACvB;YACA,2CAA2C;gBACzC,gBAAgB,QAAQ,CAAC,IAAI;gBAC7B,kBAAkB,QAAQ,CAAC,IAAI;gBAC/B,uBAAuB,QAAQ,CAAC,IAAI;gBACpC,iBAAiB,QAAQ,CAAC,IAAI;YAChC;YACA,kDAAkD;gBAChD,QAAQ,QAAQ,CAAC,IAAI;YACvB;YACA,4BAA4B;gBAC1B,QAAQ,QAAQ,CAAC,IAAI;YACvB;QACF,EAAE;QACF,UAAU,SAAS,MAAM;QACzB,WAAW,SAAS,YAAY;QAChC,CAAC,EAAE,SAAS,QAAQ,EAAE,GAAG,MAAM,gBAAgB,CAAA,MAAM,OAAM,GAAG;YAC5D,IAAI;gBACF,UAAU,SAAS,QAAQ;gBAC3B,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;QACF,EAAE;QACF,cAAc,QAAQ,CAAC,uBAAuB;QAC9C,cAAc,QAAQ,CAAC,iCAAiC;QACxD,cAAc,QAAQ,CAAC,qCAAqC;QAC5D,cAAc,QAAQ,CAAC,2BAA2B;QAClD,cAAc,QAAQ,CAAC,kCAAkC;QAEzD,SAAS,UAAU,IAAI,EAAE,IAAI;YAC3B,IAAI,OAAO,WAAW,MAAM,UAAU;YACtC,IAAI,OAAO;YACX,IAAI,WAAW;YACf,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,OAAQ,SAAS,GAAG;gBAClB,KAAK;oBAAsB;wBACzB,MAAM,IAAI,SAAS,GAAG;wBACtB,IAAI,EAAC,UAAU,IAAI,EAAE,kBAAkB,IAAI,EAAE,GAAG;wBAChD,IAAI,OAAO;wBACX,IAAI,OAAO,KAAK,MAAM;wBACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;wBACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;4BACpC,MAAM,IAAI,IAAI,CAAC,EAAE;4BACjB,MAAM,OAAO,UAAU,IAAI;4BAAE,IAAI,OAAO,WAAW,GAAG,UAAU;4BAChE,IAAI,OAAO;4BACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;4BAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;wBAC7C;wBACA,aAAa;wBACb,aAAa;wBACb,aAAa;wBACb,aAAa,OAAO,IAAI;wBACxB;oBACF;gBACA,KAAK;oBAAqB;wBACxB,MAAM,IAAI,SAAS,GAAG;wBACtB,IAAI,EAAC,UAAU,IAAI,EAAE,kBAAkB,IAAI,EAAE,GAAG;wBAChD,IAAI,OAAO;wBACX,IAAI,OAAO,KAAK,MAAM;wBACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;wBACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;4BACpC,MAAM,IAAI,IAAI,CAAC,EAAE;4BACjB,MAAM,OAAO,UAAU,IAAI;4BAAE,IAAI,OAAO,WAAW,GAAG,UAAU;4BAChE,IAAI,OAAO;4BACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;4BAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;wBAC7C;wBACA,aAAa;wBACb,aAAa;wBACb,aAAa;wBACb,aAAa,OAAO,IAAI;wBACxB;oBACF;gBACA;oBAAS;wBACP,MAAM,IAAI,UAAU,CAAC,4BAA4B,EAAE,KAAK,SAAS,CAAC,SAAS,GAAG,EAAE,eAAe,EAAE,SAAS,+BAA+B,CAAC;oBAC5I;YACF;YACA,MAAM,MAAM,QAAQ,CAAC,aAAa,CAAC,MAAM,MAAM,YAAY,YAAY,YAAY;YACnF,IAAI;YACJ,OAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;gBAC1C,KAAK;oBAAG;wBACN,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAC/C,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAChD,IAAI,UAAU,EAAE;wBAChB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;4BAC7B,MAAM,OAAO,QAAQ,IAAI;4BACzB,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;4BAChD,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;4BAChD,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;4BACtE,QAAQ,IAAI,CAAC;wBACf;wBACA,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,IAAI;wBACjD,IAAI,SAAS,SAAS,SAAS,QAAQ,CAAC,MAAM,IAAI;wBAClD,IAAI,WAAW,EAAE;wBACjB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;4BAC9B,MAAM,OAAO,SAAS,IAAI;4BAC1B,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;4BACjD,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;4BACjD,IAAI,WAAW,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,OAAO;4BACxE,SAAS,IAAI,CAAC;wBAChB;wBACA,IAAI,SAAS,SAAS,SAAS,QAAQ,CAAC,MAAM,IAAI;wBAClD,IAAI,SAAS,SAAS,SAAS,QAAQ,CAAC,MAAM,IAAI;wBAClD,YAAW;4BACT,KAAK;4BACL,KAAK;gCACH,SAAS;gCACT,QAAQ;gCACR,UAAU,UAAU,IAAI,QAAS,UAAU,IAAI,OAAO;gCACtD,SAAS,UAAU,IAAI,QAAS,UAAU,IAAI,OAAO;4BACvD;wBACF;wBACA;oBACF;gBACA,KAAK;oBAAG;wBACN,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAChD,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAChD,IAAI,WAAW,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,OAAO;wBACxE,YAAW;4BACT,KAAK;4BACL,KAAK;wBACP;wBACA;oBACF;gBACA;oBAAS;wBACP,MAAM,IAAI,UAAU;oBACtB;YACF;YACA,MAAM,SAAS;YACf,YAAY;YACZ,IAAI,OAAO,WAAW,YAAY,OAAO,GAAG,KAAK,OAAO;gBACtD,MAAM,IAAI,eAAe,OAAO,GAAG;YACrC;YACA,OAAO,OAAO,GAAG;QACnB;QAEA,SAAS,oBAAoB,IAAI,EAAE,IAAI;YACrC,IAAI,OAAO,WAAW,MAAM,UAAU;YACtC,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,OAAO,KAAK,MAAM;YACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;YACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,MAAM,IAAI,IAAI,CAAC,EAAE;gBACjB,MAAM,OAAO,UAAU,IAAI;gBAAE,IAAI,OAAO,WAAW,GAAG,UAAU;gBAChE,IAAI,OAAO;gBACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;gBAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;YAC7C;YACA,MAAM,MAAM,QAAQ,CAAC,uBAAuB,CAAC,MAAM,MAAM,SAAS;YAClE,IAAI;YACJ,OAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;gBAC1C,KAAK;oBAAG;wBACN,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAC/C,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAC/C,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;wBACtE,WAAU;4BACR,KAAK;4BACL,KAAK;wBACP;wBACA;oBACF;gBACA,KAAK;oBAAG;wBACN,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAC/C,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAC/C,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;wBACtE,WAAU;4BACR,KAAK;4BACL,KAAK;wBACP;wBACA;oBACF;gBACA;oBAAS;wBACP,MAAM,IAAI,UAAU;oBACtB;YACF;YACA,MAAM,SAAS;YACf,YAAY;YACZ,IAAI,OAAO,WAAW,YAAY,OAAO,GAAG,KAAK,OAAO;gBACtD,MAAM,IAAI,eAAe,OAAO,GAAG;YACrC;YACA,OAAO,OAAO,GAAG;QACnB;QAEA,SAAS,wBAAwB,IAAI,EAAE,IAAI;YACzC,IAAI,OAAO,WAAW,MAAM,UAAU;YACtC,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,OAAO,KAAK,MAAM;YACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;YACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,MAAM,IAAI,IAAI,CAAC,EAAE;gBACjB,MAAM,OAAO,UAAU,IAAI;gBAAE,IAAI,OAAO,WAAW,GAAG,UAAU;gBAChE,IAAI,OAAO;gBACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;gBAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;YAC7C;YACA,MAAM,MAAM,QAAQ,CAAC,2BAA2B,CAAC,MAAM,MAAM,SAAS;YACtE,IAAI;YACJ,OAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;gBAC1C,KAAK;oBAAG;wBACN,WAAU;4BACR,KAAK;4BACL,KAAK;wBACP;wBACA;oBACF;gBACA,KAAK;oBAAG;wBACN,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAC/C,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAC/C,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;wBACtE,WAAU;4BACR,KAAK;4BACL,KAAK;wBACP;wBACA;oBACF;gBACA;oBAAS;wBACP,MAAM,IAAI,UAAU;oBACtB;YACF;YACA,MAAM,SAAS;YACf,YAAY;YACZ,IAAI,OAAO,WAAW,YAAY,OAAO,GAAG,KAAK,OAAO;gBACtD,MAAM,IAAI,eAAe,OAAO,GAAG;YACrC;YACA,OAAO,OAAO,GAAG;QACnB;QAEA,SAAS,aAAa,IAAI,EAAE,IAAI;YAC9B,IAAI,OAAO,WAAW,MAAM,UAAU;YACtC,IAAI,OAAO;YACX,IAAI,WAAW;YACf,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,OAAQ,SAAS,GAAG;gBAClB,KAAK;oBAAiC;wBACpC,MAAM,IAAI,SAAS,GAAG;wBACtB,IAAI,EAAC,uBAAuB,IAAI,EAAE,oBAAoB,IAAI,EAAE,OAAO,IAAI,EAAE,GAAG;wBAC5E,IAAI,OAAO;wBACX,IAAI,OAAO,KAAK,MAAM;wBACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;wBACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;4BACpC,MAAM,IAAI,IAAI,CAAC,EAAE;4BACjB,MAAM,OAAO,UAAU,IAAI;4BAAE,IAAI,OAAO,WAAW,GAAG,UAAU;4BAChE,IAAI,OAAO;4BACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;4BAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;wBAC7C;wBACA,aAAa;wBACb,aAAa,OAAO,IAAI;wBACxB,aAAa,OAAO,IAAI;wBACxB,aAAa;wBACb,aAAa;wBACb;oBACF;gBACA,KAAK;oBAAgC;wBACnC,MAAM,IAAI,SAAS,GAAG;wBACtB,IAAI,EAAC,uBAAuB,IAAI,EAAE,oBAAoB,IAAI,EAAE,MAAM,IAAI,EAAE,GAAG;wBAC3E,IAAI,OAAO;wBACX,IAAI,OAAO,KAAK,MAAM;wBACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;wBACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;4BACpC,MAAM,IAAI,IAAI,CAAC,EAAE;4BACjB,MAAM,OAAO,UAAU,IAAI;4BAAE,IAAI,OAAO,WAAW,GAAG,UAAU;4BAChE,IAAI,OAAO;4BACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;4BAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;wBAC7C;wBACA,aAAa;wBACb,aAAa,OAAO,IAAI;wBACxB,aAAa,OAAO,IAAI;wBACxB,aAAa;wBACb,aAAa;wBACb;oBACF;gBACA;oBAAS;wBACP,MAAM,IAAI,UAAU,CAAC,4BAA4B,EAAE,KAAK,SAAS,CAAC,SAAS,GAAG,EAAE,eAAe,EAAE,SAAS,2CAA2C,CAAC;oBACxJ;YACF;YACA,MAAM,MAAM,QAAQ,CAAC,iBAAiB,CAAC,MAAM,MAAM,YAAY,YAAY,YAAY,YAAY;YACnG,IAAI;YACJ,OAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;gBAC1C,KAAK;oBAAG;wBACN,IAAI;wBACJ,OAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;4BAC1C,KAAK;gCAAG;oCACN,QAAQ;oCACR;gCACF;4BACA,KAAK;gCAAG;oCACN,QAAQ;oCACR;gCACF;4BACA;gCAAS;oCACP,MAAM,IAAI,UAAU;gCACtB;wBACF;wBACA,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,IAAI;wBACjD,IAAI,SAAS,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBACjD,IAAI,WAAW,EAAE;wBACjB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;4BAC9B,MAAM,OAAO,SAAS,IAAI;4BAC1B,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;4BAChD,IAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;4BAChD,IAAI,UAAU,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,MAAM;4BACtE,SAAS,IAAI,CAAC;wBAChB;wBACA,YAAW;4BACT,KAAK;4BACL,KAAK;gCACH,UAAU;gCACV,SAAS;4BACX;wBACF;wBACA;oBACF;gBACA,KAAK;oBAAG;wBACN,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAChD,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;wBAChD,IAAI,WAAW,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,OAAO;wBACxE,YAAW;4BACT,KAAK;4BACL,KAAK;wBACP;wBACA;oBACF;gBACA;oBAAS;wBACP,MAAM,IAAI,UAAU;oBACtB;YACF;YACA,MAAM,SAAS;YACf,YAAY;YACZ,IAAI,OAAO,WAAW,YAAY,OAAO,GAAG,KAAK,OAAO;gBACtD,MAAM,IAAI,eAAe,OAAO,GAAG;YACrC;YACA,OAAO,OAAO,GAAG;QACnB;QAEA,SAAS,oBAAoB,IAAI,EAAE,IAAI;YACrC,IAAI,OAAO,WAAW,MAAM,UAAU;YACtC,IAAI,OAAO;YACX,IAAI,EAAC,UAAU,IAAI,EAAE,mBAAmB,IAAI,EAAE,kBAAkB,IAAI,EAAE,GAAG;YACzE,IAAI,WAAW;YACf,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,OAAQ,SAAS,GAAG;gBAClB,KAAK;oBAAS;wBACZ,MAAM,IAAI,SAAS,GAAG;wBACtB,IAAI,OAAO;wBACX,IAAI,OAAO,KAAK,MAAM;wBACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;wBACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;4BACpC,MAAM,IAAI,IAAI,CAAC,EAAE;4BACjB,MAAM,OAAO,UAAU,IAAI;4BAAG,IAAI,WAAW;4BAC7C,OAAQ,SAAS,GAAG;gCAClB,KAAK;oCAAS;wCACZ,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC;oCACF;gCACA,KAAK;oCAAgB;wCACnB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC;oCACF;gCACA,KAAK;oCAAc;wCACjB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC;oCACF;gCACA,KAAK;oCAAsB;wCACzB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC;oCACF;gCACA,KAAK;oCAAU;wCACb,MAAM,IAAI,SAAS,GAAG;wCACtB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC,IAAI,OAAO,WAAW,GAAG,UAAU;wCACnC,IAAI,OAAO;wCACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;wCAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;wCAC3C;oCACF;gCACA;oCAAS;wCACP,MAAM,IAAI,UAAU,CAAC,4BAA4B,EAAE,KAAK,SAAS,CAAC,SAAS,GAAG,EAAE,eAAe,EAAE,SAAS,yCAAyC,CAAC;oCACtJ;4BACF;wBACF;wBACA,aAAa;wBACb,aAAa;wBACb,aAAa;wBACb;oBACF;gBACA,KAAK;oBAAQ;wBACX,MAAM,IAAI,SAAS,GAAG;wBACtB,IAAI,OAAO;wBACX,IAAI,OAAO,KAAK,MAAM;wBACtB,IAAI,UAAU,SAAS,GAAG,GAAG,GAAG,OAAO;wBACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;4BACpC,MAAM,IAAI,IAAI,CAAC,EAAE;4BACjB,MAAM,OAAO,UAAU,IAAI;4BAAG,IAAI,WAAW;4BAC7C,OAAQ,SAAS,GAAG;gCAClB,KAAK;oCAAS;wCACZ,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC;oCACF;gCACA,KAAK;oCAAgB;wCACnB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC;oCACF;gCACA,KAAK;oCAAc;wCACjB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC;oCACF;gCACA,KAAK;oCAAsB;wCACzB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC;oCACF;gCACA,KAAK;oCAAU;wCACb,MAAM,IAAI,SAAS,GAAG;wCACtB,SAAS,SAAS,OAAO,CAAC,OAAO,GAAG,GAAG;wCACvC,IAAI,OAAO,WAAW,GAAG,UAAU;wCACnC,IAAI,OAAO;wCACX,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;wCAC3C,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,MAAM;wCAC3C;oCACF;gCACA;oCAAS;wCACP,MAAM,IAAI,UAAU,CAAC,4BAA4B,EAAE,KAAK,SAAS,CAAC,SAAS,GAAG,EAAE,eAAe,EAAE,SAAS,yCAAyC,CAAC;oCACtJ;4BACF;wBACF;wBACA,aAAa;wBACb,aAAa;wBACb,aAAa;wBACb;oBACF;gBACA;oBAAS;wBACP,MAAM,IAAI,UAAU,CAAC,4BAA4B,EAAE,KAAK,SAAS,CAAC,SAAS,GAAG,EAAE,eAAe,EAAE,SAAS,2CAA2C,CAAC;oBACxJ;YACF;YACA,IAAI,WAAW;YACf,IAAI;YACJ,IAAI;YACJ,IAAI,aAAa,QAAQ,aAAY,WAAW;gBAC9C,aAAa;gBACb,aAAa;YACf,OAAO;gBACL,MAAM,IAAI;gBACV,aAAa;gBACb,aAAa,SAAS;YACxB;YACA,MAAM,MAAM,QAAQ,CAAC,wBAAwB,CAAC,MAAM,MAAM,YAAY,YAAY,YAAY,YAAY,YAAY,OAAO,IAAI;YACjI,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;YAChD,IAAI,SAAS,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;YACjD,IAAI,WAAW,EAAE;YACjB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC9B,MAAM,OAAO,SAAS,IAAI;gBAC1B,IAAI;gBACJ,OAAQ,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;oBAC3C,KAAK;wBAAG;4BACN,YAAW;gCACT,KAAK;4BACP;4BACA;wBACF;oBACA,KAAK;wBAAG;4BACN,YAAW;gCACT,KAAK;4BACP;4BACA;wBACF;oBACA,KAAK;wBAAG;4BACN,YAAW;gCACT,KAAK;4BACP;4BACA;wBACF;oBACA,KAAK;wBAAG;4BACN,YAAW;gCACT,KAAK;4BACP;4BACA;wBACF;oBACA,KAAK;wBAAG;4BACN,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,OAAO,IAAI;4BAClD,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,OAAO,IAAI;4BAClD,IAAI,WAAW,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,OAAO;4BACxE,YAAW;gCACT,KAAK;gCACL,KAAK;4BACP;4BACA;wBACF;oBACA;wBAAS;4BACP,MAAM,IAAI,UAAU;wBACtB;gBACF;gBACA,SAAS,IAAI,CAAC;oBACZ,OAAO,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,UAAU;oBACtD,KAAK,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,UAAU;oBACpD,gBAAgB;gBAClB;YACF;YACA,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,MAAM,IAAI;YACjD,IAAI,SAAS,SAAS,SAAS,QAAQ,CAAC,MAAM,GAAG;YACjD,IAAI,WAAW,EAAE;YACjB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC9B,MAAM,OAAO,SAAS,IAAI;gBAC1B,IAAI;gBACJ,OAAQ,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG;oBAC3C,KAAK;wBAAG;4BACN,YAAW;gCACT,KAAK;4BACP;4BACA;wBACF;oBACA,KAAK;wBAAG;4BACN,YAAW;gCACT,KAAK;4BACP;4BACA;wBACF;oBACA,KAAK;wBAAG;4BACN,YAAW;gCACT,KAAK;4BACP;4BACA;wBACF;oBACA,KAAK;wBAAG;4BACN,YAAW;gCACT,KAAK;4BACP;4BACA;wBACF;oBACA,KAAK;wBAAG;4BACN,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,OAAO,IAAI;4BAClD,IAAI,QAAQ,SAAS,SAAS,QAAQ,CAAC,OAAO,IAAI;4BAClD,IAAI,WAAW,YAAY,MAAM,CAAC,IAAI,WAAW,QAAQ,MAAM,EAAE,OAAO;4BACxE,YAAW;gCACT,KAAK;gCACL,KAAK;4BACP;4BACA;wBACF;oBACA;wBAAS;4BACP,MAAM,IAAI,UAAU;wBACtB;gBACF;gBACA,SAAS,IAAI,CAAC;oBACZ,OAAO,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,UAAU;oBACtD,KAAK,SAAS,SAAS,QAAQ,CAAC,OAAO,GAAG,UAAU;oBACpD,gBAAgB;gBAClB;YACF;YACA,MAAM,SAAS;gBACb,SAAS;gBACT,QAAQ;YACV;YACA,YAAY;YACZ,OAAO;QACT;QAEA,OAAO;YAAE;YAAW;YAAqB;YAAqB;YAAc;QAA0B;IACxG;IACA,IAAI,SAAS,SAAS;IACtB,SAAS,QAAS,KAAK;QACrB,IAAI;YACF,IAAI;YACJ,GAAG;gBACD,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,IAAI,IAAI,CAAC,MAAM;YACpC,QAAS,CAAC,CAAC,iBAAiB,OAAO,KAAK,CAAC,KAAM;YAC/C,IAAI,MAAM;gBACR,IAAI,SAAS,QAAQ;qBAChB,OAAO;YACd;YACA,IAAI,CAAC,SAAS,UAAU,IAAI,QAAQ,CAAC,UAAU,UAAY,CAAC,UAAU,UAAU,SAAS,OAAO;YAChG,MAAM,IAAI,CAAC,CAAA,UAAW,OAAO,YAAY,QAAQ,UAAU;QAC7D,EACA,OAAO,GAAG;YACR,IAAI,QAAQ,OAAO;iBACd,MAAM;QACb;IACF;IACA,MAAM,kBAAkB,QAAQ;IAChC,OAAO,WAAW;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40arcjet%2Banalyze-wasm%401.0.0-beta.7/node_modules/%40arcjet/analyze-wasm/index.js"], "sourcesContent": ["import { instantiate } from './wasm/arcjet_analyze_js_req.component.js';\nimport { wasm } from './_virtual/arcjet_analyze_js_req.component.core.js';\nimport { wasm as wasm$1 } from './_virtual/arcjet_analyze_js_req.component.core2.js';\nimport { wasm as wasm$2 } from './_virtual/arcjet_analyze_js_req.component.core3.js';\n\nconst componentCoreWasmPromise = wasm();\nconst componentCore2WasmPromise = wasm$1();\nconst componentCore3WasmPromise = wasm$2();\nasync function moduleFromPath(path) {\n    if (path === \"arcjet_analyze_js_req.component.core.wasm\") {\n        return componentCoreWasmPromise;\n    }\n    if (path === \"arcjet_analyze_js_req.component.core2.wasm\") {\n        return componentCore2WasmPromise;\n    }\n    if (path === \"arcjet_analyze_js_req.component.core3.wasm\") {\n        return componentCore3WasmPromise;\n    }\n    throw new Error(`Unknown path: ${path}`);\n}\nasync function initializeWasm(coreImports) {\n    try {\n        // Await the instantiation to catch the failure\n        return instantiate(moduleFromPath, coreImports);\n    }\n    catch {\n        return undefined;\n    }\n}\n\nexport { initializeWasm };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,2BAA2B,CAAA,GAAA,0RAAA,CAAA,OAAI,AAAD;AACpC,MAAM,4BAA4B,CAAA,GAAA,2RAAA,CAAA,OAAM,AAAD;AACvC,MAAM,4BAA4B,CAAA,GAAA,2RAAA,CAAA,OAAM,AAAD;AACvC,eAAe,eAAe,IAAI;IAC9B,IAAI,SAAS,6CAA6C;QACtD,OAAO;IACX;IACA,IAAI,SAAS,8CAA8C;QACvD,OAAO;IACX;IACA,IAAI,SAAS,8CAA8C;QACvD,OAAO;IACX;IACA,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,MAAM;AAC3C;AACA,eAAe,eAAe,WAAW;IACrC,IAAI;QACA,+CAA+C;QAC/C,OAAO,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB;IACvC,EACA,OAAM;QACF,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}]}