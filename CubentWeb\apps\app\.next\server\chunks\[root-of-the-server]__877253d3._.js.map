{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/app/app/api/auth/clear-cross-domain-token/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { cookies } from 'next/headers';\n\nexport async function POST() {\n  try {\n    // Clear the cross-domain auth token\n    const cookieStore = await cookies();\n    cookieStore.set('cubent_auth_token', '', {\n      domain: '.cubent.dev',\n      httpOnly: false,\n      secure: true,\n      sameSite: 'lax',\n      maxAge: 0, // Expire immediately\n      path: '/'\n    });\n\n    return NextResponse.json({ success: true });\n  } catch (error) {\n    console.error('Error clearing cross-domain token:', error);\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,oCAAoC;QACpC,MAAM,cAAc,MAAM,CAAA,GAAA,gPAAA,CAAA,UAAO,AAAD;QAChC,YAAY,GAAG,CAAC,qBAAqB,IAAI;YACvC,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,MAAM;QACR;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}