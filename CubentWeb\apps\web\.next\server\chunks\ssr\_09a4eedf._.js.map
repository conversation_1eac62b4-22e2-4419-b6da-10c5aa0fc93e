{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/design-system/lib/fonts.ts"], "sourcesContent": ["import { cn } from '@repo/design-system/lib/utils';\nimport { GeistMono } from 'geist/font/mono';\nimport { GeistSans } from 'geist/font/sans';\n\nexport const fonts = cn(\n  GeistSans.variable,\n  GeistMono.variable,\n  'touch-manipulation font-sans antialiased'\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AAAA;;;;AAEO,MAAM,QAAQ,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACpB,0SAAA,CAAA,YAAS,CAAC,QAAQ,EAClB,0SAAA,CAAA,YAAS,CAAC,QAAQ,EAClB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/global-error.tsx"], "sourcesContent": ["'use client';\n\nimport { Button } from '@repo/design-system/components/ui/button';\nimport { fonts } from '@repo/design-system/lib/fonts';\nimport { captureException } from '@sentry/nextjs';\nimport type NextError from 'next/error';\nimport { useEffect } from 'react';\n\ntype GlobalErrorProperties = {\n  readonly error: NextError & { digest?: string };\n  readonly reset: () => void;\n};\n\nconst GlobalError = ({ error, reset }: GlobalErrorProperties) => {\n  useEffect(() => {\n    captureException(error);\n  }, [error]);\n\n  return (\n    <html lang=\"en\" className={fonts}>\n      <body>\n        <h1>Oops, something went wrong</h1>\n        <Button onClick={() => reset()}>Try again</Button>\n      </body>\n    </html>\n  );\n};\n\nexport default GlobalError;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAaA,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,KAAK,EAAyB;IAC1D,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,+QAAA,CAAA,mBAAgB,AAAD,EAAE;IACnB,GAAG;QAAC;KAAM;IAEV,qBACE,6VAAC;QAAK,MAAK;QAAK,WAAW,4IAAA,CAAA,QAAK;kBAC9B,cAAA,6VAAC;;8BACC,6VAAC;8BAAG;;;;;;8BACJ,6VAAC,2JAAA,CAAA,SAAM;oBAAC,SAAS,IAAM;8BAAS;;;;;;;;;;;;;;;;;AAIxC;uCAEe", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/geist@1.4.2_next@15.3.2_@op_71a80cc80e74b567b143c58fb994ca22/node_modules/geist/dist/geistmono_8e2790ea.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geistmono_8e2790ea-module__SaSh0q__className\",\n  \"variable\": \"geistmono_8e2790ea-module__SaSh0q__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/geist%401.4.2_next%4015.3.2_%40op_71a80cc80e74b567b143c58fb994ca22/node_modules/geist/dist/geistmono_8e2790ea.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/local/cssmodule.module.css?{%22path%22:%22mono.js%22,%22import%22:%22%22,%22arguments%22:[{%22src%22:%22./fonts/geist-mono/GeistMono-Variable.woff2%22,%22variable%22:%22--font-geist-mono%22,%22adjustFontFallback%22:false,%22fallback%22:[%22ui-monospace%22,%22SFMono-Regular%22,%22Roboto%20Mono%22,%22Menlo%22,%22Monaco%22,%22Liberation%20Mono%22,%22DejaVu%20Sans%20Mono%22,%22Courier%20New%22,%22monospace%22],%22weight%22:%22100%20900%22}],%22variableName%22:%22GeistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'GeistMono', ui-monospace, SFMono-Regular, Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,8QAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;IAEhB;AACJ;AAEA,IAAI,8QAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,8QAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/geist@1.4.2_next@15.3.2_@op_71a80cc80e74b567b143c58fb994ca22/node_modules/geist/dist/geistsans_81192321.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geistsans_81192321-module__qQlUNq__className\",\n  \"variable\": \"geistsans_81192321-module__qQlUNq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/geist%401.4.2_next%4015.3.2_%40op_71a80cc80e74b567b143c58fb994ca22/node_modules/geist/dist/geistsans_81192321.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/local/cssmodule.module.css?{%22path%22:%22sans.js%22,%22import%22:%22%22,%22arguments%22:[{%22src%22:%22./fonts/geist-sans/Geist-Variable.woff2%22,%22variable%22:%22--font-geist-sans%22,%22weight%22:%22100%20900%22}],%22variableName%22:%22GeistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'GeistSans', 'GeistSans Fallback'\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,8QAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;IAEhB;AACJ;AAEA,IAAI,8QAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,8QAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}