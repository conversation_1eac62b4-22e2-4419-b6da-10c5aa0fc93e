{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/app/app/not-found.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nexport default function NotFound() {\n  const router = useRouter();\n\n  useEffect(() => {\n    // Redirect to dashboard on 404\n    router.replace('/dashboard');\n  }, [router]);\n\n  // Show a brief loading message while redirecting\n  return (\n    <div className=\"flex items-center justify-center min-h-screen\">\n      <div className=\"text-center\">\n        <p className=\"text-muted-foreground\">Redirecting to dashboard...</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,oPAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;8BAAE;YACR,+BAA+B;YAC/B,OAAO,OAAO,CAAC;QACjB;6BAAG;QAAC;KAAO;IAEX,iDAAiD;IACjD,qBACE,4SAAC;QAAI,WAAU;kBACb,cAAA,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;;;;;;AAI7C;GAhBwB;;QACP,oPAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}