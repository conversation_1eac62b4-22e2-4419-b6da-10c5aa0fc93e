{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "Spinner.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/core/components/Spinner/Spinner.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\ntype Speed = \"fast\" | \"slow\" | \"medium\";\n\nfunction speedSwitch(speed: Speed) {\n  if (speed === \"fast\") return 600;\n  if (speed === \"slow\") return 900;\n  return 750;\n}\n\nexport interface SpinnerProps {\n  color?: string;\n  speed?: Speed;\n  gap?: number;\n  thickness?: number;\n  size?: string;\n}\n\nexport const Spinner: FunctionComponent<SpinnerProps> = ({\n  color = \"rgba(0,0,0,0.4)\",\n  speed = \"medium\",\n  gap = 4,\n  thickness = 4,\n  size = \"1em\",\n  ...props\n}) => (\n  <svg\n    height={size}\n    width={size}\n    {...props}\n    style={{ animationDuration: `${speedSwitch(speed)}ms` }}\n    className=\"__react-svg-spinner_circle\"\n    role=\"img\"\n    aria-labelledby=\"title desc\"\n    viewBox=\"0 0 32 32\"\n  >\n    <title id=\"title\">Circle loading spinner</title>\n    <desc id=\"desc\">Image of a partial circle indicating \"loading.\"</desc>\n    <style\n      dangerouslySetInnerHTML={{\n        __html: `\n      .__react-svg-spinner_circle{\n          transition-property: transform;\n          animation-name: __react-svg-spinner_infinite-spin;\n          animation-iteration-count: infinite;\n          animation-timing-function: linear;\n      }\n      @keyframes __react-svg-spinner_infinite-spin {\n          from {transform: rotate(0deg)}\n          to {transform: rotate(360deg)}\n      }\n    `,\n      }}\n    />\n    <circle\n      role=\"presentation\"\n      cx={16}\n      cy={16}\n      r={14 - thickness / 2}\n      stroke={color}\n      fill=\"none\"\n      strokeWidth={thickness}\n      strokeDasharray={Math.PI * 2 * (11 - gap)}\n      strokeLinecap=\"round\"\n    />\n  </svg>\n);\n"], "names": ["speedSwitch", "speed", "Spinner", "color", "gap", "thickness", "size", "props", "React", "animationDuration", "__html", "Math", "PI"], "mappings": ";;;;;AAIA,SAASA,EAAYC,CAAAA,EAAc;IAC7BA,OAAAA,MAAU,SAAe,MACzBA,MAAU,SAAe,MACtB;AACT;AAUO,MAAMC,IAA2CA,CAAC,EACvDC,OAAAA,IAAQ,iBAAA,EACRF,OAAAA,IAAQ,QAAA,EACRG,KAAAA,IAAM,CAAA,EACNC,WAAAA,IAAY,CAAA,EACZC,MAAAA,IAAO,KAAA,EACP,GAAGC,GACL,GACEC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QACC,QAAQF;QACR,OAAOA;QACP,GAAIC,CAAAA;QACJ,OAAO;YAAEE,mBAAmB,GAAGT,EAAYC,CAAK,CAAC,CAAA,EAAA,CAAA;QAAK;QACtD,WAAU;QACV,MAAK;QACL,mBAAgB;QAChB,SAAQ;IAER,GAAAO,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,SAAM;QAAA,IAAG;IAAA,GAAQ,wBAAsB,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CACvC,QAAK;QAAA,IAAG;IAAA,GAAO,iDAA+C,GAC9DA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,SAAA;QACC,yBAAyB;YACvBE,QAAQ,CAAA;;;;;;;;;;;IAAA,CAAA;QAYV;IAAE,CAAA,GAEHF,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,UAAA;QACC,MAAK;QACL,IAAI;QACJ,IAAI;QACJ,GAAG,KAAKH,IAAY;QACpB,QAAQF;QACR,MAAK;QACL,aAAaE;QACb,iBAAiBM,KAAKC,EAAAA,GAAK,IAAA,CAAK,KAAKR,CAAAA;QACrC,eAAc;IAAA,CAAO,CAEzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "file": "ButtonSpinner.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/core/components/Button/ButtonSpinner.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\nimport { Spinner } from \"../Spinner\";\n\nimport \"./styles.css\";\n\ntype ButtonSpinnerProps = {\n  hasLabel: boolean;\n};\n\nexport const ButtonSpinner: FunctionComponent<ButtonSpinnerProps> = ({\n  hasLabel,\n}) => (\n  <div\n    className={`rnf-button-spinner rnf-button-spinner--${\n      hasLabel ? \"with-label\" : \"without-label\"\n    }`}\n  >\n    <Spinner />\n  </div>\n);\n"], "names": ["<PERSON>ton<PERSON><PERSON>ner", "<PERSON><PERSON><PERSON><PERSON>", "React", "Spinner"], "mappings": ";;;;;;;2BAUO,MAAMA,IAAuDA,CAAC,EACnEC,UAAAA,CAAAA,EACF,GACEC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OACC;QAAA,WAAW,CAAA,uCAAA,EACTD,IAAW,eAAe,eAAe,EAAA;IAAA,GAG1CC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,uTAAAC,UAAAA,EAAA,IAAO,CACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "file": "Button.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/core/components/Button/Button.tsx"], "sourcesContent": ["import { useKnockFeed } from \"@knocklabs/react-core\";\nimport React, { PropsWithChildren } from \"react\";\nimport { FunctionComponent } from \"react\";\n\nimport { ButtonSpinner } from \"./ButtonSpinner\";\nimport \"./styles.css\";\n\nexport type ButtonProps = {\n  variant: \"primary\" | \"secondary\";\n  loadingText?: string;\n  isLoading?: boolean;\n  isDisabled?: boolean;\n  isFullWidth?: boolean;\n  onClick: (e: React.MouseEvent) => void;\n};\n\nexport const Button: FunctionComponent<PropsWithChildren<ButtonProps>> = ({\n  variant = \"primary\",\n  loadingText,\n  isLoading = false,\n  isDisabled = false,\n  isFullWidth = false,\n  onClick,\n  children,\n}) => {\n  const { colorMode } = useKnockFeed();\n\n  const classNames = [\n    \"rnf-button\",\n    `rnf-button--${variant}`,\n    isFullWidth ? \"rnf-button--full-width\" : \"\",\n    isLoading ? \"rnf-button--is-loading\" : \"\",\n    `rnf-button--${colorMode}`,\n  ].join(\" \");\n\n  // In this case when there's no loading text, we still want to display the original\n  // content of the button, but make it hidden. That allows us to keep the button width\n  // consistent and show the spinner in the middle, meaning no layout shift.\n  const textToShowWhileLoading = loadingText || (\n    <span className=\"rnf-button__button-text-hidden\">{children}</span>\n  );\n\n  return (\n    <button\n      onClick={onClick}\n      className={classNames}\n      disabled={isLoading || isDisabled}\n      type=\"button\"\n    >\n      {isLoading && <ButtonSpinner hasLabel={!!loadingText} />}\n      {isLoading ? textToShowWhileLoading : children}\n    </button>\n  );\n};\n"], "names": ["<PERSON><PERSON>", "variant", "loadingText", "isLoading", "isDisabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onClick", "children", "colorMode", "useKnockFeed", "classNames", "join", "textToShowWhileLoading", "React", "<PERSON>ton<PERSON><PERSON>ner"], "mappings": ";;;;;;;;;;2BAgBO,MAAMA,IAA4DA,CAAC,EACxEC,SAAAA,IAAU,SAAA,EACVC,aAAAA,CAAAA,EACAC,WAAAA,IAAY,CAAA,CAAA,EACZC,YAAAA,IAAa,CAAA,CAAA,EACbC,aAAAA,IAAc,CAAA,CAAA,EACdC,SAAAA,CAAAA,EACAC,UAAAA,CAAAA,EACF,KAAM;IACE,MAAA,EAAEC,WAAAA,CAAAA,EAAAA,gVAAcC,CAAa,IAE7BC,IAAa;QACjB;QACA,CAAA,YAAA,EAAeT,CAAO,EAAA;QACtBI,IAAc,2BAA2B;QACzCF,IAAY,2BAA2B;QACvC,CAAA,YAAA,EAAeK,CAAS,EAAE;KAAA,CAC1BG,IAAAA,CAAK,GAAG,GAKJC,IAAyBV,KAC7BW,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QAAK;QAAA,WAAU;IAAA,GAAkCN,CAAS;IAI3D,OAAAM,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,UAAA;QACC,SAAAP;QACA,WAAWI;QACX,UAAUP,KAAaC;QACvB,MAAK;IAAA,GAEJD,KAAcU,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,4TAAAC,gBAAAA,EAAA;QAAc,UAAU,CAAC,CAACZ;IAAAA,CAAe,GACvDC,IAAYS,IAAyBL,CACxC;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "file": "ButtonGroup.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/core/components/Button/ButtonGroup.tsx"], "sourcesContent": ["import { FunctionComponent, ReactNode } from \"react\";\n\nimport \"./styles.css\";\n\nexport const ButtonGroup: FunctionComponent<{\n  children?: ReactNode | undefined;\n}> = ({ children }) => <div className=\"rnf-button-group\">{children}</div>;\n"], "names": ["ButtonGroup", "children", "React"], "mappings": ";;;;;2BAIO,MAAMA,IAERA,CAAC,EAAEC,UAAAA,CAAAA,EAAS,GAAOC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QAAI,WAAU;IAAA,GAAoBD,CAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "file": "Bell.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/core/components/Icons/Bell.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\ntype BellIconProps = {\n  width?: number;\n  height?: number;\n  \"aria-hidden\"?: boolean;\n};\n\nconst BellIcon: FunctionComponent<BellIconProps> = ({\n  width = 24,\n  height = 24,\n  \"aria-hidden\": aria<PERSON><PERSON><PERSON>,\n}) => {\n  return (\n    <svg\n      width={width}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      height={height}\n      aria-hidden={ariaHidden}\n    >\n      <path\n        d=\"M20.0474 16.4728C18.8436 14.9996 17.9938 14.2496 17.9938 10.1879C17.9938 6.46832 16.0944 5.14317 14.5311 4.49957C14.3235 4.41426 14.128 4.21832 14.0647 4.00504C13.7905 3.07176 13.0217 2.24957 11.9999 2.24957C10.978 2.24957 10.2088 3.07223 9.93736 4.00598C9.87408 4.2216 9.67861 4.41426 9.47096 4.49957C7.9058 5.1441 6.0083 6.46457 6.0083 10.1879C6.00596 14.2496 5.15611 14.9996 3.95237 16.4728C3.45362 17.0832 3.89049 17.9996 4.76283 17.9996H19.2416C20.1092 17.9996 20.5433 17.0803 20.0474 16.4728Z\"\n        stroke=\"currentColor\"\n        strokeWidth=\"1.5\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      />\n      <path\n        d=\"M14.9999 17.9988V18.7488C14.9999 19.5445 14.6838 20.3075 14.1212 20.8701C13.5586 21.4327 12.7955 21.7488 11.9999 21.7488C11.2042 21.7488 10.4412 21.4327 9.87856 20.8701C9.31595 20.3075 8.99988 19.5445 8.99988 18.7488V17.9988\"\n        stroke=\"currentColor\"\n        strokeWidth=\"1.5\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      />\n    </svg>\n  );\n};\n\nexport { BellIcon };\n"], "names": ["BellIcon", "width", "height", "ariaHidden", "React"], "mappings": ";;;;;AAQA,MAAMA,IAA6CA,CAAC,EAClDC,OAAAA,IAAQ,EAAA,EACRC,QAAAA,IAAS,EAAA,EACT,eAAeC,CAAAA,EACjB,GAEKC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QACC,OAAAH;QACA,SAAQ;QACR,MAAK;QACL,QAAAC;QACA,eAAaC;IAAAA,GAEZC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,QAAA;QACC,GAAE;QACF,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;IAAO,CAAA,GAExBA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QAAA;QACC,GAAE;QACF,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;IAAA,CAAO,CAE1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "file": "CheckmarkCircle.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/core/components/Icons/CheckmarkCircle.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\ntype CheckmarkCircleProps = {\n  width?: number;\n  height?: number;\n  \"aria-hidden\"?: boolean;\n};\n\nconst CheckmarkCircle: FunctionComponent<CheckmarkCircleProps> = ({\n  width = 16,\n  height = 16,\n  \"aria-hidden\": aria<PERSON>id<PERSON>,\n}) => (\n  <svg\n    width={width}\n    height={height}\n    viewBox=\"0 0 16 16\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    aria-hidden={ariaHidden}\n  >\n    <path\n      d=\"M14 8.00012C14 4.68762 11.3125 2.00012 7.99997 2.00012C4.68747 2.00012 1.99997 4.68762 1.99997 8.00012C1.99997 11.3126 4.68747 14.0001 7.99997 14.0001C11.3125 14.0001 14 11.3126 14 8.00012Z\"\n      stroke=\"currentColor\"\n      strokeWidth=\"1.5\"\n      strokeMiterlimit=\"10\"\n    />\n    <path\n      d=\"M10.9999 5.5004L6.79994 10.5004L4.99994 8.5004\"\n      stroke=\"currentColor\"\n      strokeWidth=\"1.5\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n    />\n  </svg>\n);\n\nexport { CheckmarkCircle };\n"], "names": ["CheckmarkCircle", "width", "height", "ariaHidden", "React"], "mappings": ";;;;;AAQA,MAAMA,IAA2DA,CAAC,EAChEC,OAAAA,IAAQ,EAAA,EACRC,QAAAA,IAAS,EAAA,EACT,eAAeC,CAAAA,EACjB,GACGC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QACC,OAAAH;QACA,QAAAC;QACA,SAAQ;QACR,MAAK;QACL,OAAM;QACN,eAAaC;IAAAA,GAEZC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,QAAA;QACC,GAAE;QACF,QAAO;QACP,aAAY;QACZ,kBAAiB;IAAA,CAAI,GAEvBA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QAAA;QACC,GAAE;QACF,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;IAAA,CAAO,CAE1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "file": "ChevronDown.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/core/components/Icons/ChevronDown.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\ntype ChevronDownProps = {\n  width?: number;\n  height?: number;\n  \"aria-hidden\"?: boolean;\n};\n\nconst ChevronDown: FunctionComponent<ChevronDownProps> = ({\n  width = 8,\n  height = 6,\n  \"aria-hidden\": ariaHidden,\n}) => (\n  <svg\n    width={width}\n    height={height}\n    viewBox=\"0 0 8 6\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    aria-hidden={ariaHidden}\n  >\n    <path\n      d=\"M1.74994 1.87512L3.99994 4.12512L6.24994 1.87512\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n    />\n  </svg>\n);\n\nexport { ChevronDown };\n"], "names": ["ChevronDown", "width", "height", "ariaHidden", "React"], "mappings": ";;;;;AAQA,MAAMA,IAAmDA,CAAC,EACxDC,OAAAA,IAAQ,CAAA,EACRC,QAAAA,IAAS,CAAA,EACT,eAAeC,CAAAA,EACjB,GACEC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OACC;QAAA,OAAAH;QACA,QAAAC;QACA,SAAQ;QACR,MAAK;QACL,OAAM;QACN,eAAaC;IAAAA,GAAAA,aAAAA,GAAAA,oTAAAA,CAAAA,UAAAA,CAAAA,aAAAA,CAEZ,QACC;QAAA,GAAE;QACF,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;IAAA,CAAO,CAE1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "file": "CloseCircle.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/core/components/Icons/CloseCircle.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\ntype CloseCircleProps = {\n  width?: number;\n  height?: number;\n  \"aria-hidden\"?: boolean;\n};\n\nconst CloseCircle: FunctionComponent<CloseCircleProps> = ({\n  width = 14,\n  height = 14,\n  \"aria-hidden\": ariaHidden,\n}) => (\n  <svg\n    width={width}\n    height={height}\n    viewBox=\"0 0 14 14\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    aria-hidden={ariaHidden}\n  >\n    <path\n      d=\"M7.00012 0.499939C3.41606 0.499939 0.500122 3.41588 0.500122 6.99994C0.500122 10.584 3.41606 13.4999 7.00012 13.4999C10.5842 13.4999 13.5001 10.584 13.5001 6.99994C13.5001 3.41588 10.5842 0.499939 7.00012 0.499939ZM9.35356 8.6465C9.40194 8.69247 9.44063 8.74766 9.46735 8.80881C9.49407 8.86997 9.50828 8.93585 9.50913 9.00259C9.50999 9.06932 9.49747 9.13555 9.47233 9.19737C9.44718 9.25919 9.40992 9.31535 9.36273 9.36254C9.31553 9.40973 9.25937 9.447 9.19755 9.47214C9.13573 9.49729 9.0695 9.5098 9.00277 9.50895C8.93604 9.50809 8.87015 9.49389 8.809 9.46717C8.74784 9.44045 8.69265 9.40176 8.64668 9.35337L7.00012 7.70712L5.35356 9.35337C5.25903 9.44318 5.13315 9.49251 5.00277 9.49084C4.87239 9.48918 4.74782 9.43664 4.65562 9.34444C4.56342 9.25224 4.51088 9.12767 4.50921 8.99729C4.50755 8.86691 4.55687 8.74103 4.64668 8.6465L6.29293 6.99994L4.64668 5.35338C4.55687 5.25884 4.50755 5.13297 4.50921 5.00259C4.51088 4.87221 4.56342 4.74764 4.65562 4.65544C4.74782 4.56324 4.87239 4.5107 5.00277 4.50903C5.13315 4.50736 5.25903 4.55669 5.35356 4.6465L7.00012 6.29275L8.64668 4.6465C8.74121 4.55669 8.86709 4.50736 8.99747 4.50903C9.12785 4.5107 9.25242 4.56324 9.34462 4.65544C9.43682 4.74764 9.48936 4.87221 9.49103 5.00259C9.4927 5.13297 9.44337 5.25884 9.35356 5.35338L7.70731 6.99994L9.35356 8.6465Z\"\n      fill=\"currentColor\"\n    />\n  </svg>\n);\n\nexport { CloseCircle };\n"], "names": ["CloseCircle", "width", "height", "ariaHidden", "React"], "mappings": ";;;;;AAQA,MAAMA,IAAmDA,CAAC,EACxDC,OAAAA,IAAQ,EAAA,EACRC,QAAAA,IAAS,EAAA,EACT,eAAeC,CAAAA,EACjB,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CACG,OACC;QAAA,OAAAF;QACA,QAAAC;QACA,SAAQ;QACR,MAAK;QACL,OAAM;QACN,eAAaC;IAAAA,GAEZC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,QAAA;QACC,GAAE;QACF,MAAK;IAAA,CAAc,CAEvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "file": "useOnBottomScroll.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/core/hooks/useOnBottomScroll.ts"], "sourcesContent": ["import debounce from \"lodash.debounce\";\nimport { RefObject, useCallback, useEffect, useMemo } from \"react\";\n\ntype OnBottomScrollOptions = {\n  ref: RefObject<HTMLDivElement | undefined>;\n  callback: () => void;\n  offset?: number;\n};\n\nconst noop = () => {};\n\nfunction useOnBottomScroll(options: OnBottomScrollOptions) {\n  const callback = options.callback ?? noop;\n  const ref = options.ref;\n  const offset = options.offset ?? 0;\n\n  const debouncedCallback = useMemo(() => debounce(callback, 200), [callback]);\n\n  const handleOnScroll = useCallback(() => {\n    if (ref.current) {\n      const scrollNode = ref.current;\n      const scrollContainerBottomPosition = Math.round(\n        scrollNode.scrollTop + scrollNode.clientHeight,\n      );\n      const scrollPosition = Math.round(scrollNode.scrollHeight - offset);\n\n      if (scrollPosition <= scrollContainerBottomPosition) {\n        debouncedCallback();\n      }\n    }\n    // TODO: Check if we can remove this disable\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [debouncedCallback]);\n\n  useEffect(() => {\n    let element: HTMLElement | undefined;\n    if (ref.current) {\n      element = ref.current;\n      ref.current.addEventListener(\"scroll\", handleOnScroll);\n    }\n\n    return () => {\n      if (element) {\n        element.removeEventListener(\"scroll\", handleOnScroll);\n      }\n    };\n    // TODO: Check if we can remove this disable\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [handleOnScroll]);\n}\n\nexport default useOnBottomScroll;\n"], "names": ["noop", "useOnBottomScroll", "options", "callback", "ref", "offset", "deboun<PERSON><PERSON><PERSON><PERSON>", "useMemo", "debounce", "handleOnScroll", "useCallback", "current", "scrollNode", "scrollContainerBottomPosition", "Math", "round", "scrollTop", "clientHeight", "scrollHeight", "useEffect", "element", "addEventListener", "removeEventListener"], "mappings": ";;;;;;;AASA,MAAMA,IAAOA,KAAO,CAAD;AAEnB,SAASC,EAAkBC,CAAAA,EAAgC;IACnDC,MAAAA,IAAWD,EAAQC,QAAAA,IAAYH,GAC/BI,IAAMF,EAAQE,GAAAA,EACdC,IAASH,EAAQG,MAAAA,IAAU,GAE3BC,uUAAoBC,EAAQ,6NAAMC,EAASL,GAAU,GAAG,GAAG;QAACA,CAAQ;KAAC,GAErEM,2UAAiBC,EAAY,MAAM;QACvC,IAAIN,EAAIO,OAAAA,EAAS;YACf,MAAMC,IAAaR,EAAIO,OAAAA,EACjBE,IAAgCC,KAAKC,KAAAA,CACzCH,EAAWI,SAAAA,GAAYJ,EAAWK,YACpC;YACuBH,KAAKC,KAAAA,CAAMH,EAAWM,YAAAA,GAAeb,CAAM,KAE5CQ,KACFP,EAAA;QACpB;IACF,GAGC;QAACA,CAAiB;KAAC;yUAEtBa,EAAU,MAAM;QACVC,IAAAA;QACJ,OAAIhB,EAAIO,OAAAA,IAAAA,CACNS,IAAUhB,EAAIO,OAAAA,EACVA,EAAAA,OAAAA,CAAQU,gBAAAA,CAAiB,UAAUZ,CAAc,CAAA,GAGhD,MAAM;YACPW,KACME,EAAAA,mBAAAA,CAAoB,UAAUb,CAAc;QAExD;IAAA,GAGC;QAACA,CAAc;KAAC;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "file": "EmptyFeed.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/feed/components/EmptyFeed/EmptyFeed.tsx"], "sourcesContent": ["import { useKnockFeed, useTranslations } from \"@knocklabs/react-core\";\nimport { FunctionComponent } from \"react\";\n\nimport \"./styles.css\";\n\nexport const EmptyFeed: FunctionComponent = () => {\n  const { colorMode } = useKnockFeed();\n  const { t } = useTranslations();\n\n  return (\n    <div className={`rnf-empty-feed rnf-empty-feed--${colorMode}`}>\n      <div className=\"rnf-empty-feed__inner\">\n        <h2 className=\"rnf-empty-feed__header\">{t(\"emptyFeedTitle\")}</h2>\n        <p className=\"rnf-empty-feed__body\">{t(\"emptyFeedBody\")}</p>\n      </div>\n    </div>\n  );\n};\n"], "names": ["EmptyFeed", "colorMode", "useKnockFeed", "t", "useTranslations", "React"], "mappings": ";;;;;;;;;2BAKO,MAAMA,IAA+BA,MAAM;IAC1C,MAAA,EAAEC,WAAAA,CAAAA,EAAAA,gVAAcC,CAAa,IAC7B,EAAEC,CAAAA,EAAAA,GAAMC,4UAAAA,CAAgB;IAG5B,OAAAC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAI;QAAA,WAAW,CAAA,+BAAA,EAAkCJ,CAAS,EAAA;IACzD,GAAAI,aAAAA,GAAAA,+TAAAA,CAAA,aAAA,CAAC,OAAI;QAAA,WAAU;IACb,GAAAA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,MAAG;QAAA,WAAU;IAAA,GAA0BF,EAAE,gBAAgB,CAAE,GAC3DE,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,KAAA;QAAE,WAAU;IAAA,GAAwBF,EAAE,eAAe,CAAE,CAC1D,CACF;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "file": "ArchiveButton.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/feed/components/NotificationCell/ArchiveButton.tsx"], "sourcesContent": ["import { FeedItem } from \"@knocklabs/client\";\nimport { useKnockFeed, useTranslations } from \"@knocklabs/react-core\";\nimport { createPopper } from \"@popperjs/core\";\nimport { MouseEvent, useCallback, useEffect, useRef, useState } from \"react\";\n\nimport { CloseCircle } from \"../../../core/components/Icons\";\n\nexport interface ArchiveButtonProps {\n  item: FeedItem;\n}\n\nconst ArchiveButton: React.FC<ArchiveButtonProps> = ({ item }) => {\n  const { colorMode, feedClient } = useKnockFeed();\n  const { t } = useTranslations();\n  const [visible, setVisible] = useState(false);\n  const triggerRef = useRef<HTMLButtonElement>(null);\n  const tooltipRef = useRef<HTMLDivElement>(null);\n\n  const onClick = useCallback(\n    (e: MouseEvent<HTMLButtonElement>) => {\n      e.preventDefault();\n      e.stopPropagation();\n\n      feedClient.markAsArchived(item);\n    },\n    // TODO: Check if we can remove this disable\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [item],\n  );\n\n  useEffect(() => {\n    if (triggerRef.current && tooltipRef.current && visible) {\n      const popperInstance = createPopper(\n        triggerRef.current,\n        tooltipRef.current,\n        {\n          placement: \"top-end\",\n          modifiers: [\n            {\n              name: \"offset\",\n              options: {\n                offset: [0, 8],\n              },\n            },\n          ],\n        },\n      );\n\n      return () => {\n        popperInstance.destroy();\n      };\n    }\n  }, [visible]);\n\n  return (\n    <button\n      ref={triggerRef}\n      onClick={onClick}\n      onMouseEnter={() => setVisible(true)}\n      onMouseLeave={() => setVisible(false)}\n      type=\"button\"\n      aria-label={t(\"archiveNotification\")}\n      className={`rnf-archive-notification-btn rnf-archive-notification-btn--${colorMode}`}\n    >\n      <CloseCircle aria-hidden />\n\n      {visible && (\n        <div\n          ref={tooltipRef}\n          className={`rnf-tooltip rnf-tooltip--${colorMode}`}\n        >\n          {t(\"archiveNotification\")}\n        </div>\n      )}\n    </button>\n  );\n};\n\nexport { ArchiveButton };\n"], "names": ["ArchiveButton", "item", "colorMode", "feedClient", "useKnockFeed", "t", "useTranslations", "visible", "setVisible", "useState", "triggerRef", "useRef", "tooltipRef", "onClick", "useCallback", "e", "preventDefault", "stopPropagation", "markAsArchived", "useEffect", "current", "popperInstance", "createPopper", "placement", "modifiers", "name", "options", "offset", "destroy", "React", "CloseCircle"], "mappings": ";;;;;;;;;;;;;AAWA,MAAMA,IAA8CA,CAAC,EAAEC,MAAAA,CAAAA,EAAK,KAAM;IAC1D,MAAA,EAAEC,WAAAA,CAAAA,EAAWC,YAAAA,CAAAA,EAAAA,gUAAeC,gBAAAA,CAAa,IACzC,EAAEC,GAAAA,CAAAA,EAAAA,+UAAMC,CAAgB,IACxB,CAACC,GAASC,CAAU,CAAA,uUAAIC,EAAS,CAAA,CAAK,GACtCC,sUAAaC,EAA0B,IAAI,GAC3CC,sUAAaD,EAAuB,IAAI,GAExCE,IAAUC,uUAAAA,EACd,CAACC,MAAqC;QACpCA,EAAEC,cAAAA,CAAe,GACjBD,EAAEE,eAAAA,CAAgB,GAElBd,EAAWe,cAAAA,CAAejB,CAAI;IAChC,GAAA,4CAAA;IAAA,uDAAA;IAGA;QAACA,CAAI;KAAA;IAGPkB,OAAAA,qUAAAA,EAAU,MAAM;QACd,IAAIT,EAAWU,OAAAA,IAAWR,EAAWQ,OAAAA,IAAWb,GAAS;YACvD,MAAMc,2PAAiBC,EACrBZ,EAAWU,OAAAA,EACXR,EAAWQ,OAAAA,EACX;gBACEG,WAAW;gBACXC,WAAW;oBACT;wBACEC,MAAM;wBACNC,SAAS;4BACPC,QAAQ;gCAAC;gCAAG,CAAC;6BAAA;wBAAA;oBAEhB,CAAA;iBAAA;YAAA,CAGP;YAEA,OAAO,MAAM;gBACXN,EAAeO,OAAAA,CAAQ;YACzB;QAAA;IACF,GACC;QAACrB,CAAO;KAAC,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CAGT,UACC;QAAA,KAAKG;QACL,SAAAG;QACA,cAAc,IAAML,EAAW,CAAA,CAAI;QACnC,cAAc,IAAMA,EAAW,CAAA,CAAK;QACpC,MAAK;QACL,cAAYH,EAAE,qBAAqB;QACnC,WAAW,CAAA,2DAAA,EAA8DH,CAAS,EAAA;IAAA,GAElF2B,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,yTAACC,cAAAA,EAAAA;QAAY,eAAW,CAAA;IAAA,CAAA,GAEvBvB,KACCsB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QACC,KAAKjB;QACL,WAAW,CAAA,yBAAA,EAA4BV,CAAS,EAAA;IAAA,GAE/CG,EAAE,qBAAqB,CAC1B,CAEJ;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "file": "Avatar.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/feed/components/NotificationCell/Avatar.tsx"], "sourcesContent": ["import React from \"react\";\n\nimport \"./styles.css\";\n\nexport interface AvatarProps {\n  name: string;\n  src?: string | null;\n}\n\nexport const Avatar: React.FC<AvatarProps> = ({ name, src }) => {\n  function getInitials(name: string) {\n    const [firstName, lastName] = name.split(\" \");\n    return firstName && lastName\n      ? `${firstName.charAt(0)}${lastName.charAt(0)}`\n      : firstName\n        ? firstName.charAt(0)\n        : \"\";\n  }\n\n  return (\n    <div className=\"rnf-avatar\">\n      {src ? (\n        <img src={src} alt={name} className=\"rnf-avatar__image\" />\n      ) : (\n        <span className=\"rnf-avatar__initials\">{getInitials(name)}</span>\n      )}\n    </div>\n  );\n};\n"], "names": ["Avatar", "name", "src", "getInitials", "firstName", "lastName", "split", "char<PERSON>t", "React"], "mappings": ";;;;;2BASO,MAAMA,IAAgCA,CAAC,EAAEC,MAAAA,CAAAA,EAAMC,KAAAA,CAAAA,EAAI,KAAM;IAC9D,SAASC,EAAYF,CAAAA,EAAc;QACjC,MAAM,CAACG,GAAWC,CAAQ,CAAA,GAAIJ,EAAKK,KAAAA,CAAM,GAAG;QAC5C,OAAOF,KAAaC,IAChB,GAAGD,EAAUG,MAAAA,CAAO,CAAC,CAAC,GAAGF,EAASE,MAAAA,CAAO,CAAC,CAAC,EAAA,GAC3CH,IACEA,EAAUG,MAAAA,CAAO,CAAC,IAClB;IAAA;IAIN,OAAAC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,WAAU;IAAA,GACZN,IACEM,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QAAI,KAAAN;QAAU,KAAKD;QAAM,WAAU;IAAA,KAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CAEnC,QAAK;QAAA,WAAU;IAAA,GAAwBE,EAAYF,CAAI,CAAE,CAE9D;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "file": "NotificationCell.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/feed/components/NotificationCell/NotificationCell.tsx"], "sourcesContent": ["import {\n  <PERSON>Button,\n  ButtonSetContentBlock,\n  ContentBlock,\n  FeedItem,\n  MarkdownContentBlock,\n  TextContentBlock,\n} from \"@knocklabs/client\";\nimport {\n  formatTimestamp,\n  renderNodeOrFallback,\n  useKnockFeed,\n  useTranslations,\n} from \"@knocklabs/react-core\";\nimport React, { ReactNode, useMemo } from \"react\";\n\nimport { Button, ButtonGroup } from \"../../../core\";\n\nimport { ArchiveButton } from \"./ArchiveButton\";\nimport { Avatar } from \"./Avatar\";\nimport \"./styles.css\";\n\nexport interface NotificationCellProps {\n  item: FeedItem;\n  // Invoked when the outer container is clicked\n  onItemClick?: (item: FeedItem) => void;\n  // Invoked when a button in the notification cell is clicked\n  onButtonClick?: (item: FeedItem, action: ActionButton) => void;\n  avatar?: ReactNode;\n  children?: ReactNode;\n  archiveButton?: ReactNode;\n}\n\ntype BlockByName = {\n  [name: string]: ContentBlock;\n};\n\nfunction maybeNavigateToUrlWithDelay(url?: string) {\n  if (url && url !== \"\") {\n    setTimeout(() => window.location.assign(url), 200);\n  }\n}\n\nexport const NotificationCell = React.forwardRef<\n  HTMLDivElement,\n  NotificationCellProps\n>(\n  (\n    { item, onItemClick, onButtonClick, avatar, children, archiveButton },\n    ref,\n  ) => {\n    const { feedClient, colorMode } = useKnockFeed();\n    const { locale } = useTranslations();\n\n    const blocksByName: BlockByName = useMemo(() => {\n      return item.blocks.reduce((acc, block) => {\n        return { ...acc, [block.name]: block };\n      }, {});\n    }, [item]);\n\n    const actionUrl = (blocksByName.action_url as TextContentBlock)?.rendered;\n    const buttonSet = blocksByName.actions as ButtonSetContentBlock;\n\n    const onContainerClickHandler = React.useCallback(() => {\n      // Mark as interacted + read once we click the item\n      feedClient.markAsInteracted(item, {\n        type: \"cell_click\",\n        action: actionUrl,\n      });\n\n      if (onItemClick) return onItemClick(item);\n\n      return maybeNavigateToUrlWithDelay(actionUrl);\n    }, [item, actionUrl, onItemClick, feedClient]);\n\n    const onButtonClickHandler = React.useCallback(\n      (_e: React.MouseEvent, button: ActionButton) => {\n        // Record the interaction with the metadata for the button that was clicked\n        feedClient.markAsInteracted(item, {\n          type: \"button_click\",\n          name: button.name,\n          label: button.label,\n          action: button.action,\n        });\n\n        if (onButtonClick) return onButtonClick(item, button);\n\n        return maybeNavigateToUrlWithDelay(button.action);\n      },\n      [onButtonClick, feedClient, item],\n    );\n\n    const onKeyDown = React.useCallback(\n      (ev: React.KeyboardEvent<HTMLDivElement>) => {\n        switch (ev.key) {\n          case \"Enter\": {\n            ev.stopPropagation();\n            onContainerClickHandler();\n            break;\n          }\n          default:\n            break;\n        }\n      },\n      [onContainerClickHandler],\n    );\n\n    const actor = item.actors[0];\n\n    return (\n      // eslint-disable-next-line jsx-a11y/no-static-element-interactions\n      <div\n        ref={ref}\n        className={`rnf-notification-cell rnf-notification-cell--${colorMode}`}\n        onClick={onContainerClickHandler}\n        onKeyDown={onKeyDown}\n        // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex\n        tabIndex={0}\n      >\n        <div className=\"rnf-notification-cell__inner\">\n          {!item.read_at && (\n            <div className=\"rnf-notification-cell__unread-dot\" />\n          )}\n\n          {renderNodeOrFallback(\n            avatar,\n            actor && \"name\" in actor && actor.name && (\n              <Avatar name={actor.name} src={actor.avatar} />\n            ),\n          )}\n\n          <div className=\"rnf-notification-cell__content-outer\">\n            {blocksByName.body && (\n              <div\n                className=\"rnf-notification-cell__content\"\n                dangerouslySetInnerHTML={{\n                  __html: (blocksByName.body as MarkdownContentBlock).rendered,\n                }}\n              />\n            )}\n\n            {buttonSet && (\n              <div className=\"rnf-notification-cell__button-group\">\n                <ButtonGroup>\n                  {buttonSet.buttons.map((button, i) => (\n                    <Button\n                      variant={i === 0 ? \"primary\" : \"secondary\"}\n                      key={button.name}\n                      onClick={(e) => onButtonClickHandler(e, button)}\n                    >\n                      {button.label}\n                    </Button>\n                  ))}\n                </ButtonGroup>\n              </div>\n            )}\n\n            {children && (\n              <div className=\"rnf-notification-cell__child-content\">\n                {children}\n              </div>\n            )}\n\n            <span className=\"rnf-notification-cell__timestamp\">\n              {formatTimestamp(item.inserted_at, { locale })}\n            </span>\n          </div>\n\n          {renderNodeOrFallback(archiveButton, <ArchiveButton item={item} />)}\n        </div>\n      </div>\n    );\n  },\n);\n"], "names": ["maybeNavigateToUrlWithDelay", "url", "setTimeout", "window", "location", "assign", "NotificationCell", "React", "forwardRef", "item", "onItemClick", "onButtonClick", "avatar", "children", "archiveButton", "ref", "feedClient", "colorMode", "useKnockFeed", "locale", "useTranslations", "blocksByName", "useMemo", "blocks", "reduce", "acc", "block", "name", "actionUrl", "action_url", "rendered", "buttonSet", "actions", "onContainerClickHandler", "useCallback", "markAsInteracted", "type", "action", "onButtonClickHandler", "_e", "button", "label", "onKeyDown", "ev", "key", "stopPropagation", "actor", "actors", "read_at", "renderNodeOrFallback", "Avatar", "body", "__html", "ButtonGroup", "buttons", "map", "i", "<PERSON><PERSON>", "e", "formatTimestamp", "inserted_at", "ArchiveButton"], "mappings": ";;;;;;;;;;;;;;;;;;;;2BAqCA,SAASA,EAA4BC,CAAAA,EAAc;IAC7CA,KAAOA,MAAQ,MACjBC,WAAW,IAAMC,OAAOC,QAAAA,CAASC,MAAAA,CAAOJ,CAAG,GAAG,GAAG;AAErD;AAEaK,MAAAA,wTAAmBC,WAAAA,CAAMC,UAAAA,CAIpC,CACE,EAAEC,MAAAA,CAAAA,EAAMC,aAAAA,CAAAA,EAAaC,eAAAA,CAAAA,EAAeC,QAAAA,CAAAA,EAAQC,UAAAA,CAAAA,EAAUC,eAAAA,CAAAA,EAAc,EACpEC,MACG;;IACG,MAAA,EAAEC,YAAAA,CAAAA,EAAYC,WAAAA,CAAAA,EAAAA,gUAAcC,gBAAAA,CAAa,IACzC,EAAEC,QAAAA,CAAAA,EAAAA,+UAAWC,CAAgB,IAE7BC,QAA4BC,+TAAAA,EAAQ,IACjCb,EAAKc,MAAAA,CAAOC,MAAAA,CAAO,CAACC,GAAKC,IAAAA,CACvB;gBAAE,GAAGD,CAAAA;gBAAK,CAACC,EAAMC,IAAI,CAAA,EAAGD;YAAM,CAAA,GACpC,CAAA,CAAE,GACJ;QAACjB,CAAI;KAAC,GAEHmB,IAAAA,CAAaP,IAAAA,EAAaQ,UAAAA,KAAbR,OAAAA,KAAAA,IAAAA,EAA8CS,QAAAA,EAC3DC,IAAYV,EAAaW,OAAAA,EAEzBC,yTAA0B1B,UAAAA,CAAM2B,WAAAA,CAAY,IAAA,CAEhDlB,EAAWmB,gBAAAA,CAAiB1B,GAAM;YAChC2B,MAAM;YACNC,QAAQT;QAAAA,CACT,GAEGlB,IAAoBA,EAAYD,CAAI,IAEjCT,EAA4B4B,CAAS,CAAA,GAC3C;QAACnB;QAAMmB;QAAWlB;QAAaM,CAAU;KAAC,GAEvCsB,yTAAuB/B,UAAAA,CAAM2B,WAAAA,CACjC,CAACK,GAAsBC,IAAAA,CAErBxB,EAAWmB,gBAAAA,CAAiB1B,GAAM;YAChC2B,MAAM;YACNT,MAAMa,EAAOb,IAAAA;YACbc,OAAOD,EAAOC,KAAAA;YACdJ,QAAQG,EAAOH,MAAAA;QAAAA,CAChB,GAEG1B,IAAsBA,EAAcF,GAAM+B,CAAM,IAE7CxC,EAA4BwC,EAAOH,MAAM,CAAA,GAElD;QAAC1B;QAAeK;QAAYP,CAAI;KAClC,GAEMiC,yTAAYnC,UAAAA,CAAM2B,WAAAA,CACtB,CAACS,MAA4C;QAC3C,OAAQA,EAAGC,GAAAA,EAAG;YACZ,KAAK;gBAAS;oBACZD,EAAGE,eAAAA,CAAgB,GACKZ,EAAA;oBACxB;gBAAA;QAGA;IACJ,GAEF;QAACA,CAAuB;KAC1B,GAEMa,IAAQrC,EAAKsC,MAAAA,CAAO,CAAC,CAAA;IAE3B,OAAA,mEAAA;IAEExC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QACC,KAAAQ;QACA,WAAW,CAAA,6CAAA,EAAgDE,CAAS,EAAA;QACpE,SAASgB;QACT,WAAAS;QAEA,UAAU;IAAA,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CAET,OAAI;QAAA,WAAU;IAAA,GACZ,CAACjC,EAAKuC,OAAAA,IACLzC,aAAAA,GAAAA,+TAAAA,CAAA,aAAA,CAAC,OAAI;QAAA,WAAU;IAAA,CAChB,iUAEA0C,EACCrC,GACAkC,KAAS,UAAUA,KAASA,EAAMnB,IAAAA,IAC/BpB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA2C,uUAAAA,EAAA;QAAO,MAAMJ,EAAMnB,IAAAA;QAAM,KAAKmB,EAAMlC,MAAAA;IAAAA,EAEzC,GAEAL,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAI;QAAA,WAAU;IAAA,GACZc,EAAa8B,IAAAA,IAAAA,aAAAA,GAAAA,oTAAAA,CAAAA,UAAAA,CAAAA,aAAAA,CACX,OACC;QAAA,WAAU;QACV,yBAAyB;YACvBC,QAAS/B,EAAa8B,IAAAA,CAA8BrB,QAAAA;QACtD;IAAA,CAEH,GAEAC,KACCxB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,WAAU;IAAA,GACZA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,0TAAA8C,cAAAA,EAAA,MACEtB,EAAUuB,OAAAA,CAAQC,GAAAA,CAAI,CAACf,GAAQgB,IAC9BjD,aAAAA,GAAAA,+TAAAA,CAAA,aAAA,qTAACkD,SAAAA,EACC;YAAA,SAASD,MAAM,IAAI,YAAY;YAC/B,KAAKhB,EAAOb,IAAAA;YACZ,SAAU+B,CAAMpB,IAAAA,EAAqBoB,GAAGlB,CAAM;QAAA,GAE7CA,EAAOC,KACV,CACD,CACH,CACF,GAGD5B,KAAAA,aAAAA,GAAAA,oTAAAA,CAAAA,UAAAA,CAAAA,aAAAA,CACE,OAAI;QAAA,WAAU;IACZA,GAAAA,CACH,GAGFN,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QAAA;QAAK,WAAU;IAAA,4TACboD,EAAgBlD,EAAKmD,WAAAA,EAAa;QAAEzC,QAAAA;IAAAA,CAAQ,CAC/C,CACF,iUAEC8B,EAAqBnC,GAAgBP,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,sUAAAsD,gBAAAA,EAAA;QAAc,MAAApD;IAAAA,EAAc,CACpE;AAGN,CACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "file": "Dropdown.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/feed/components/NotificationFeed/Dropdown.tsx"], "sourcesContent": ["import { useKnockFeed } from \"@knocklabs/react-core\";\nimport React, { PropsWithChildren } from \"react\";\n\nimport { ChevronDown } from \"../../../core/components/Icons\";\n\nimport \"./styles.css\";\n\nexport type DropdownProps = {\n  value: string;\n  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;\n};\n\nexport const Dropdown: React.FC<PropsWithChildren<DropdownProps>> = ({\n  children,\n  value,\n  onChange,\n}) => {\n  const { colorMode } = useKnockFeed();\n\n  return (\n    <div className={`rnf-dropdown rnf-dropdown--${colorMode}`}>\n      <select\n        aria-label=\"Select notification filter\"\n        value={value}\n        onChange={onChange}\n      >\n        {children}\n      </select>\n      <ChevronDown aria-hidden />\n    </div>\n  );\n};\n"], "names": ["Dropdown", "children", "value", "onChange", "colorMode", "useKnockFeed", "ChevronDown"], "mappings": ";;;;;;;;;;2BAYO,MAAMA,IAAuDA,CAAC,EACnEC,UAAAA,CAAAA,EACAC,OAAAA,CAAAA,EACAC,UAAAA,CAAAA,EACF,KAAM;IACE,MAAA,EAAEC,WAAAA,CAAAA,EAAAA,GAAcC,6UAAAA,CAAa;IAEnC,OAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CACG,OAAI;QAAA,WAAW,CAAA,2BAAA,EAA8BD,CAAS,EAAA;IAAA,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CACpD,UACC;QAAA,cAAW;QACX,OAAAF;QACA,UAAAC;IAAAA,GAECF,CACH,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,yTACCK,cAAAA,EAAY;QAAA,eAAW,CAAA;IAAA,CAAA,CAC1B;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "file": "MarkAsRead.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/feed/components/NotificationFeed/MarkAsRead.tsx"], "sourcesContent": ["import { FeedItem } from \"@knocklabs/client\";\nimport { useKnockFeed, useTranslations } from \"@knocklabs/react-core\";\nimport * as React from \"react\";\n\nimport { CheckmarkCircle } from \"../../../core/components/Icons\";\n\nimport \"./styles.css\";\n\nexport type MarkAsReadProps = {\n  onClick?: (e: React.MouseEvent, unreadItems: FeedItem[]) => void;\n};\n\nexport const MarkAsRead: React.FC<MarkAsReadProps> = ({ onClick }) => {\n  const { useFeedStore, feedClient, colorMode } = useKnockFeed();\n  const { t } = useTranslations();\n\n  const unreadItems = useFeedStore((state) =>\n    state.items.filter((item) => !item.read_at),\n  );\n\n  const unreadCount = useFeedStore((state) => state.metadata.unread_count);\n\n  const onClickHandler = React.useCallback(\n    (e: React.MouseEvent) => {\n      feedClient.markAllAsRead();\n      if (onClick) onClick(e, unreadItems);\n    },\n    [feedClient, unreadItems, onClick],\n  );\n\n  return (\n    <button\n      className={`rnf-mark-all-as-read rnf-mark-all-as-read--${colorMode}`}\n      disabled={unreadCount === 0}\n      onClick={onClickHandler}\n      type=\"button\"\n    >\n      {t(\"markAllAsRead\")}\n      <CheckmarkCircle aria-hidden />\n    </button>\n  );\n};\n"], "names": ["MarkAsRead", "onClick", "useFeedStore", "feedClient", "colorMode", "useKnockFeed", "t", "useTranslations", "unreadItems", "state", "items", "filter", "item", "read_at", "unreadCount", "metadata", "unread_count", "onClickHandler", "React", "useCallback", "e", "markAllAsRead", "CheckmarkCircle"], "mappings": ";;;;;;;;;;;2BAYO,MAAMA,IAAwCA,CAAC,EAAEC,SAAAA,CAAAA,EAAQ,KAAM;IAC9D,MAAA,EAAEC,cAAAA,CAAAA,EAAcC,YAAAA,CAAAA,EAAYC,WAAAA,CAAAA,EAAAA,gVAAcC,CAAa,IACvD,EAAEC,GAAAA,CAAAA,EAAAA,IAAMC,2UAAAA,CAAgB,IAExBC,IAAcN,EAAcO,CAAAA,IAChCA,EAAMC,KAAAA,CAAMC,MAAAA,CAAQC,CAASA,IAAA,CAACA,EAAKC,OAAO,CAC5C,GAEMC,IAAcZ,EAAcO,CAAUA,IAAAA,EAAMM,QAAAA,CAASC,YAAY,GAEjEC,6TAAiBC,EAAMC,YAAAA,EAC3B,CAACC,MAAwB;QACvBjB,EAAWkB,aAAAA,CAAc,GACrBpB,KAAiBmB,EAAAA,GAAGZ,CAAW;IAErC,GAAA;QAACL;QAAYK;QAAaP,CAAO;KACnC;IAGE,OAAA,aAAA,GAAAiB,EAAA,uUAAA,EAAC,UAAA;QACC,WAAW,CAAA,2CAAA,EAA8Cd,CAAS,EAAA;QAClE,UAAUU,MAAgB;QAC1B,SAASG;QACT,MAAK;IAAA,GAEJX,EAAE,eAAe,GAAA,aAAA,GAAA,CAAA,GAAA,oTAAA,CAAA,gBAAA,8TACjBgB,kBAAAA,EAAgB;QAAA,eAAW,CAAA;IAAA,CAAA,CAC9B;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "file": "NotificationFeedHeader.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/feed/components/NotificationFeed/NotificationFeedHeader.tsx"], "sourcesContent": ["import { FeedItem } from \"@knocklabs/client\";\nimport { FilterStatus, useTranslations } from \"@knocklabs/react-core\";\nimport React, { SetStateAction } from \"react\";\n\nimport { Dropdown } from \"./Dropdown\";\nimport { MarkAsRead } from \"./MarkAsRead\";\n\nexport type NotificationFeedHeaderProps = {\n  filterStatus: FilterStatus;\n  setFilterStatus: React.Dispatch<SetStateAction<FilterStatus>>;\n  onMarkAllAsReadClick?: (e: React.MouseEvent, unreadItems: FeedItem[]) => void;\n};\n\nconst OrderedFilterStatuses = [\n  FilterStatus.All,\n  FilterStatus.Unread,\n  FilterStatus.Read,\n];\n\nexport const NotificationFeedHeader: React.FC<NotificationFeedHeaderProps> = ({\n  onMarkAllAsReadClick,\n  filterStatus,\n  setFilterStatus,\n}) => {\n  const { t } = useTranslations();\n\n  return (\n    <header className=\"rnf-notification-feed__header\">\n      <div className=\"rnf-notification-feed__selector\">\n        <span className=\"rnf-notification-feed__type\">\n          {t(\"notifications\")}\n        </span>\n        <Dropdown\n          value={filterStatus}\n          onChange={(e) => setFilterStatus(e.target.value as FilterStatus)}\n        >\n          {OrderedFilterStatuses.map((filterStatus) => (\n            <option key={filterStatus} value={filterStatus}>\n              {t(filterStatus)}\n            </option>\n          ))}\n        </Dropdown>\n      </div>\n      <MarkAsRead onClick={onMarkAllAsReadClick} />\n    </header>\n  );\n};\n"], "names": ["OrderedFilterStatuses", "FilterStatus", "All", "Unread", "Read", "NotificationFeedHeader", "onMarkAllAsReadClick", "filterStatus", "setFilterStatus", "t", "useTranslations", "React", "Dropdown", "e", "target", "value", "map", "MarkAsRead"], "mappings": ";;;;;;;;;;;;;AAaA,MAAMA,IAAwB;2SAC5BC,eAAAA,CAAaC,GAAAA;2SACbD,eAAAA,CAAaE,MAAAA;2SACbF,eAAAA,CAAaG,IAAI;CAAA,EAGNC,IAAgEA,CAAC,EAC5EC,sBAAAA,CAAAA,EACAC,cAAAA,CAAAA,EACAC,iBAAAA,CAAAA,EACF,KAAM;IACE,MAAA,EAAEC,GAAAA,CAAAA,EAAAA,IAAMC,2UAAAA,CAAgB;IAG5B,OAAAC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,UAAA;QAAO,WAAU;IAAA,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CACf,OAAI;QAAA,WAAU;IACb,GAAAA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QAAK;QAAA,WAAU;IAAA,GACbF,EAAE,eAAe,CACpB,GACAE,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,gUAACC,YAAAA,EAAAA;QACC,OAAOL;QACP,UAAWM,CAAAA,IAAML,EAAgBK,EAAEC,MAAAA,CAAOC,KAAqB;IAAA,GAE9Df,EAAsBgB,GAAAA,CAAKT,CAAAA,IACzBI,aAAAA,GAAAA,+TAAAA,CAAA,aAAA,CAAA,UAAA;YAAO,KAAKJ;YAAc,OAAOA;QAAAA,GAC/BE,EAAEF,CAAY,CACjB,CACD,CACH,CACF,GACCI,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,mUAAAM,aAAAA,EAAA;QAAW,SAASX;IAAqB,CAAA,CAC5C;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 621, "column": 0}, "map": {"version": 3, "file": "NotificationFeed.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/feed/components/NotificationFeed/NotificationFeed.tsx"], "sourcesContent": ["import { FeedItem, NetworkStatus, isRequestInFlight } from \"@knocklabs/client\";\nimport {\n  ColorMode,\n  FilterStatus,\n  useFeedSettings,\n  useKnockFeed,\n  useTranslations,\n} from \"@knocklabs/react-core\";\nimport { GenericData } from \"@knocklabs/types\";\nimport React, {\n  ReactNode,\n  useCallback,\n  useEffect,\n  useRef,\n  useState,\n} from \"react\";\n\nimport { Spinner } from \"../../../core/components/Spinner\";\nimport useOnBottomScroll from \"../../../core/hooks/useOnBottomScroll\";\nimport { EmptyFeed } from \"../EmptyFeed\";\nimport { NotificationCell, NotificationCellProps } from \"../NotificationCell\";\n\nimport {\n  NotificationFeedHeader,\n  NotificationFeedHeaderProps,\n} from \"./NotificationFeedHeader\";\nimport \"./styles.css\";\n\nexport type RenderItemProps<T = GenericData> = {\n  item: FeedItem<T>;\n  onItemClick?: NotificationCellProps[\"onItemClick\"];\n  onButtonClick?: NotificationCellProps[\"onButtonClick\"];\n};\n\nexport type RenderItem = (props: RenderItemProps) => ReactNode;\n\nexport interface NotificationFeedProps {\n  EmptyComponent?: ReactNode;\n  /**\n   * @deprecated Use `renderHeader` instead to accept `NotificationFeedHeaderProps`\n   */\n  header?: ReactNode;\n  renderItem?: RenderItem;\n  renderHeader?: (props: NotificationFeedHeaderProps) => ReactNode;\n  onNotificationClick?: NotificationCellProps[\"onItemClick\"];\n  onNotificationButtonClick?: NotificationCellProps[\"onButtonClick\"];\n  onMarkAllAsReadClick?: (e: React.MouseEvent, unreadItems: FeedItem[]) => void;\n  initialFilterStatus?: FilterStatus;\n}\n\nconst defaultRenderItem = (props: RenderItemProps) => (\n  <NotificationCell key={props.item.id} {...props} />\n);\n\nconst defaultRenderHeader = (props: NotificationFeedHeaderProps) => (\n  <NotificationFeedHeader {...props} />\n);\n\nconst LoadingSpinner = ({ colorMode }: { colorMode: ColorMode }) => (\n  <div className=\"rnf-notification-feed__spinner-container\">\n    <Spinner\n      thickness={3}\n      size=\"16px\"\n      color={colorMode === \"dark\" ? \"rgba(255, 255, 255, 0.65)\" : undefined}\n    />\n  </div>\n);\n\nconst poweredByKnockUrl =\n  \"https://knock.app?utm_source=powered-by-knock&utm_medium=referral&utm_campaign=knock-branding-feed\";\n\nexport const NotificationFeed: React.FC<NotificationFeedProps> = ({\n  EmptyComponent = <EmptyFeed />,\n  renderItem = defaultRenderItem,\n  onNotificationClick,\n  onNotificationButtonClick,\n  onMarkAllAsReadClick,\n  initialFilterStatus = FilterStatus.All,\n  header,\n  renderHeader = defaultRenderHeader,\n}) => {\n  const [status, setStatus] = useState(initialFilterStatus);\n  const { feedClient, useFeedStore, colorMode } = useKnockFeed();\n  const { settings } = useFeedSettings(feedClient);\n  const { t } = useTranslations();\n\n  const { pageInfo, items, networkStatus } = useFeedStore();\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    setStatus(initialFilterStatus);\n  }, [initialFilterStatus]);\n\n  useEffect(() => {\n    // When the feed client changes, or the status changes issue a re-fetch\n    feedClient.fetch({ status });\n  }, [feedClient, status]);\n\n  const noItems = items.length === 0;\n  const requestInFlight = isRequestInFlight(networkStatus);\n\n  // Handle fetching more once we reach the bottom of the list\n  const onBottomCallback = useCallback(() => {\n    if (!requestInFlight && pageInfo.after) {\n      feedClient.fetchNextPage();\n    }\n  }, [requestInFlight, pageInfo, feedClient]);\n\n  // Once we scroll to the bottom of the view we want to automatically fetch\n  // more items for the feed and bring them into the list\n  useOnBottomScroll({\n    ref: containerRef,\n    callback: onBottomCallback,\n    offset: 70,\n  });\n\n  return (\n    <div\n      className={`rnf-notification-feed rnf-notification-feed--${colorMode}`}\n    >\n      {header ||\n        renderHeader({\n          setFilterStatus: setStatus,\n          filterStatus: status,\n          onMarkAllAsReadClick,\n        })}\n\n      <div className=\"rnf-notification-feed__container\" ref={containerRef}>\n        {networkStatus === NetworkStatus.loading && (\n          <LoadingSpinner colorMode={colorMode} />\n        )}\n\n        <div className=\"rnf-notification-feed__feed-items-container\">\n          {networkStatus !== NetworkStatus.loading &&\n            items.map((item: FeedItem) =>\n              renderItem({\n                item,\n                onItemClick: onNotificationClick,\n                onButtonClick: onNotificationButtonClick,\n              }),\n            )}\n        </div>\n\n        {networkStatus === NetworkStatus.fetchMore && (\n          <LoadingSpinner colorMode={colorMode} />\n        )}\n\n        {!requestInFlight && noItems && EmptyComponent}\n      </div>\n\n      {settings?.features.branding_required && (\n        <div className=\"rnf-notification-feed__knock-branding\">\n          <a href={poweredByKnockUrl} target=\"_blank\">\n            {t(\"poweredBy\") || \"Powered by Knock\"}\n          </a>\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": ["defaultRenderItem", "props", "React", "NotificationCell", "item", "id", "defaultRenderHeader", "NotificationFeedHeader", "LoadingSpinner", "colorMode", "Spinner", "undefined", "poweredByKnockUrl", "NotificationFeed", "EmptyComponent", "EmptyFeed", "renderItem", "onNotificationClick", "onNotificationButtonClick", "onMarkAllAsReadClick", "initialFilterStatus", "FilterStatus", "All", "header", "renderHeader", "status", "setStatus", "useState", "feedClient", "useFeedStore", "useKnockFeed", "settings", "useFeedSettings", "t", "useTranslations", "pageInfo", "items", "networkStatus", "containerRef", "useRef", "useEffect", "fetch", "noItems", "length", "requestInFlight", "isRequestInFlight", "onBottomCallback", "useCallback", "after", "fetchNextPage", "useOnBottomScroll", "ref", "callback", "offset", "setFilterStatus", "filterStatus", "NetworkStatus", "loading", "map", "onItemClick", "onButtonClick", "fetchMore", "features", "branding_required"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;2BAkDA,MAAMA,IAAoBA,CAACC,IACxBC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,yUAAAC,mBAAAA,EAAA;QAAiB,KAAKF,EAAMG,IAAAA,CAAKC,EAAAA;QAAI,GAAIJ,CAAAA;IAC3C,CAAA,GAEKK,IAAsBA,CAACL,IAC1BC,aAAAA,uTAAAA,WAAAA,CAAA,aAAA,+UAAAK,yBAAAA,EAAA;QAA2BN,GAAAA,CAAAA;IAAAA,CAC7B,GAEKO,IAAiBA,CAAC,EAAEC,WAAAA,CAAAA,EAAoC,GAC3DP,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QAAI,WAAU;IAAA,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,uTACZQ,UAAAA,EACC;QAAA,WAAW;QACX,MAAK;QACL,OAAOD,MAAc,SAAS,8BAA8BE,KAAAA;IAAAA,CAAU,CAE1E,GAGIC,IACJ,sGAEWC,IAAoDA,CAAC,EAChEC,gBAAAA,IAAAA,aAAAA,GAAAA,oTAAAA,CAAAA,UAAAA,CAAAA,aAAAA,CAAkBC,sUAAAA,EAAY,IAAA,CAAA,EAC9BC,YAAAA,IAAahB,CAAAA,EACbiB,qBAAAA,CAAAA,EACAC,2BAAAA,CAAAA,EACAC,sBAAAA,CAAAA,EACAC,qBAAAA,2SAAsBC,eAAAA,CAAaC,GAAAA,EACnCC,QAAAA,CAAAA,EACAC,cAAAA,IAAelB,CAAAA,EACjB,KAAM;IACJ,MAAM,CAACmB,GAAQC,CAAS,CAAA,uUAAIC,EAASP,CAAmB,GAClD,EAAEQ,YAAAA,CAAAA,EAAYC,cAAAA,CAAAA,EAAcpB,WAAAA,CAAAA,EAAAA,GAAcqB,6UAAAA,CAAa,IACvD,EAAEC,UAAAA,CAAAA,EAAAA,6XAAaC,EAAgBJ,CAAU,GACzC,EAAEK,GAAAA,CAAAA,EAAAA,IAAMC,2UAAAA,CAAgB,IAExB,EAAEC,UAAAA,CAAAA,EAAUC,OAAAA,CAAAA,EAAOC,eAAAA,CAAAA,EAAAA,GAAkBR,EAAa,GAClDS,sUAAeC,EAAuB,IAAI;KAEhDC,oUAAAA,EAAU,MAAM;QACdd,EAAUN,CAAmB;IAAA,GAC5B;QAACA,CAAmB;KAAC,wUAExBoB,EAAU,MAAM;QAEdZ,EAAWa,KAAAA,CAAM;YAAEhB,QAAAA;QAAAA,CAAQ;IAAA,GAC1B;QAACG;QAAYH,CAAM;KAAC;IAEjBiB,MAAAA,IAAUN,EAAMO,MAAAA,KAAW,GAC3BC,+TAAkBC,EAAkBR,CAAa,GAGjDS,2UAAmBC,EAAY,MAAM;QACrC,CAACH,KAAmBT,EAASa,KAAAA,IAC/BpB,EAAWqB,aAAAA,CAAc;IAE1B,GAAA;QAACL;QAAiBT;QAAUP,CAAU;KAAC;IAIxB,qUAAAsB,EAAA;QAChBC,KAAKb;QACLc,UAAUN;QACVO,QAAQ;IAAA,CACT,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CAGE,OACC;QAAA,WAAW,CAAA,6CAAA,EAAgD5C,CAAS,EAAA;IAAA,GAEnEc,KACCC,EAAa;QACX8B,iBAAiB5B;QACjB6B,cAAc9B;QACdN,sBAAAA;IAAAA,CACD,GAEHjB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAI;QAAA,WAAU;QAAmC,KAAKoC;IACpDD,GAAAA,ySAAkBmB,gBAAAA,CAAcC,OAAAA,IAC9BvD,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAM,GAAA;QAAe,WAAAC;IACjB,CAAA,GAEDP,aAAAA,uTAAAA,WAAAA,CAAA,aAAA,CAAC,OAAI;QAAA,WAAU;IACZmC,GAAAA,ySAAkBmB,gBAAAA,CAAcC,OAAAA,IAC/BrB,EAAMsB,GAAAA,CAAI,CAACtD,IACTY,EAAW;YACTZ,MAAAA;YACAuD,aAAa1C;YACb2C,eAAe1C;QAAAA,CAChB,CACH,CACJ,GAECmB,ySAAkBmB,gBAAAA,CAAcK,SAAAA,IAC9B3D,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAM,GAAA;QAAe,WAAAC;IACjB,CAAA,GAEA,CAACmC,KAAmBF,KAAW5B,CAClC,GAAA,CAECiB,KAAAA,OAAAA,KAAAA,IAAAA,EAAU+B,QAAAA,CAASC,iBAAAA,KACjB7D,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QAAI,WAAU;IAAA,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CACZ,KAAE;QAAA,MAAMU;QAAmB,QAAO;IAAA,GAChCqB,EAAE,WAAW,KAAK,kBACrB,CACF,CAEJ;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 715, "column": 0}, "map": {"version": 3, "file": "NotificationFeedContainer.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/feed/components/NotificationFeedContainer/NotificationFeedContainer.tsx"], "sourcesContent": ["import { FunctionComponent, ReactNode } from \"react\";\n\nimport \"./styles.css\";\n\nexport const NotificationFeedContainer: FunctionComponent<{\n  children?: ReactNode | undefined;\n}> = ({ children }) => <div className=\"rnf-feed-provider\">{children}</div>;\n"], "names": ["NotificationFeedContainer", "children", "React"], "mappings": ";;;;;2BAIO,MAAMA,IAERA,CAAC,EAAEC,UAAAA,CAAAA,EAAS,GAAOC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QAAI,WAAU;IAAA,GAAqBD,CAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "file": "useComponentVisible.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/core/hooks/useComponentVisible.ts"], "sourcesContent": ["import { useEffect, useRef } from \"react\";\n\nfunction contains(parent: HTMLElement | null, child: HTMLElement) {\n  if (!parent) return false;\n  return parent === child || parent.contains(child);\n}\n\ntype Options = {\n  closeOnClickOutside: boolean;\n};\n\nexport default function useComponentVisible(\n  isVisible: boolean,\n  onClose: (event: Event) => void,\n  options: Options,\n) {\n  const ref = useRef<HTMLDivElement>(null);\n\n  const handleKeydown = (event: KeyboardEvent) => {\n    if (event.key === \"Escape\") {\n      onClose(event);\n    }\n  };\n\n  const handleClickOutside = (event: Event) => {\n    if (\n      options.closeOnClickOutside &&\n      !contains(ref.current, event.target as HTMLElement)\n    ) {\n      onClose(event);\n    }\n  };\n\n  useEffect(() => {\n    if (isVisible) {\n      document.addEventListener(\"keydown\", handleKeydown, true);\n      document.addEventListener(\"click\", handleClickOutside, true);\n    }\n\n    return () => {\n      document.removeEventListener(\"keydown\", handleKeydown, true);\n      document.removeEventListener(\"click\", handleClickOutside, true);\n    };\n    // TODO: Check if we can remove this disable\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isVisible]);\n\n  return { ref };\n}\n"], "names": ["contains", "parent", "child", "useComponentVisible", "isVisible", "onClose", "options", "ref", "useRef", "handleKeydown", "event", "key", "handleClickOutside", "closeOnClickOutside", "current", "target", "useEffect", "addEventListener", "removeEventListener"], "mappings": ";;;;;AAEA,SAASA,EAASC,CAAAA,EAA4BC,CAAAA,EAAoB;IAC5D,OAACD,IACEA,MAAWC,KAASD,EAAOD,QAAAA,CAASE,CAAK,IAD5B,CAAA;AAEtB;AAMwBC,SAAAA,EACtBC,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACA;IACMC,MAAAA,IAAMC,kUAAAA,EAAuB,IAAI,GAEjCC,IAAgBA,CAACC,MAAyB;QAC1CA,EAAMC,GAAAA,KAAQ,YAChBN,EAAQK,CAAK;IAEjB,GAEME,IAAqBA,CAACF,MAAiB;QAEzCJ,EAAQO,mBAAAA,IACR,CAACb,EAASO,EAAIO,OAAAA,EAASJ,EAAMK,MAAqB,KAElDV,EAAQK,CAAK;IAEjB;IAEAM,4UAAAA,EAAU,IAAA,CACJZ,KAAAA,CACOa,SAAAA,gBAAAA,CAAiB,WAAWR,GAAe,CAAA,CAAI,GAC/CQ,SAAAA,gBAAAA,CAAiB,SAASL,GAAoB,CAAA,CAAI,CAAA,GAGtD,MAAM;YACFM,SAAAA,mBAAAA,CAAoB,WAAWT,GAAe,CAAA,CAAI,GAClDS,SAAAA,mBAAAA,CAAoB,SAASN,GAAoB,CAAA,CAAI;QAChE,CAAA,GAGC;QAACR,CAAS;KAAC,GAEP;QAAEG,KAAAA;IAAI;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "file": "NotificationFeedPopover.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/feed/components/NotificationFeedPopover/NotificationFeedPopover.tsx"], "sourcesContent": ["import { Feed, FeedStoreState } from \"@knocklabs/client\";\nimport { useKnockFeed, useTranslations } from \"@knocklabs/react-core\";\nimport { Placement, createPopper } from \"@popperjs/core\";\nimport React, { RefObject, useEffect } from \"react\";\n\nimport useComponentVisible from \"../../../core/hooks/useComponentVisible\";\nimport { NotificationFeed, NotificationFeedProps } from \"../NotificationFeed\";\n\nimport \"./styles.css\";\n\ntype OnOpenOptions = {\n  store: FeedStoreState;\n  feedClient: Feed;\n};\n\nconst defaultOnOpen = ({ store, feedClient }: OnOpenOptions) => {\n  if (store.metadata.unseen_count > 0) {\n    feedClient.markAllAsSeen();\n  }\n};\n\nexport interface NotificationFeedPopoverProps extends NotificationFeedProps {\n  isVisible: boolean;\n  onOpen?: (arg: OnOpenOptions) => void;\n  onClose: (e: Event) => void;\n  buttonRef: RefObject<HTMLElement | null>;\n  closeOnClickOutside?: boolean;\n  placement?: Placement;\n}\n\nexport const NotificationFeedPopover: React.FC<\n  NotificationFeedPopoverProps\n> = ({\n  isVisible,\n  onOpen = defaultOnOpen,\n  onClose,\n  buttonRef,\n  closeOnClickOutside = true,\n  placement = \"bottom-end\",\n  ...feedProps\n}) => {\n  const { t } = useTranslations();\n  const { colorMode, feedClient, useFeedStore } = useKnockFeed();\n  const store = useFeedStore();\n\n  const { ref: popperRef } = useComponentVisible(isVisible, onClose, {\n    closeOnClickOutside,\n  });\n\n  useEffect(() => {\n    // Whenever the feed is opened, we want to invoke the `onOpen` callback\n    // function to handle any side effects.\n    if (isVisible && onOpen) {\n      onOpen({ store, feedClient });\n    }\n  }, [isVisible, onOpen, store, feedClient]);\n\n  useEffect(() => {\n    if (buttonRef.current && popperRef.current) {\n      const popperInstance = createPopper(\n        buttonRef.current,\n        popperRef.current,\n        {\n          strategy: \"fixed\",\n          placement,\n          modifiers: [\n            {\n              name: \"offset\",\n              options: {\n                offset: [0, 8],\n              },\n            },\n          ],\n        },\n      );\n\n      // Cleanup\n      return () => {\n        popperInstance.destroy();\n      };\n    }\n  }, [buttonRef, popperRef, placement]);\n\n  return (\n    <div\n      className={`rnf-notification-feed-popover rnf-notification-feed-popover--${colorMode}`}\n      style={{\n        visibility: isVisible ? \"visible\" : \"hidden\",\n        opacity: isVisible ? 1 : 0,\n      }}\n      ref={popperRef}\n      role=\"dialog\"\n      aria-label={t(\"notifications\")}\n      tabIndex={-1}\n    >\n      <div className=\"rnf-notification-feed-popover__inner\">\n        <NotificationFeed {...feedProps} />\n      </div>\n    </div>\n  );\n};\n"], "names": ["defaultOnOpen", "store", "feedClient", "metadata", "unseen_count", "markAllAsSeen", "NotificationFeedPopover", "isVisible", "onOpen", "onClose", "buttonRef", "closeOnClickOutside", "placement", "feedProps", "t", "useTranslations", "colorMode", "useFeedStore", "useKnockFeed", "ref", "popperRef", "useComponentVisible", "useEffect", "current", "popperInstance", "createPopper", "strategy", "modifiers", "name", "options", "offset", "destroy", "visibility", "opacity", "React", "NotificationFeed"], "mappings": ";;;;;;;;;;;;;;;;;wEAeA,MAAMA,IAAgBA,CAAC,EAAEC,OAAAA,CAAAA,EAAOC,YAAAA,CAAAA,EAA0B,KAAM;IAC1DD,EAAME,QAAAA,CAASC,YAAAA,GAAe,KAChCF,EAAWG,aAAAA,CAAc;AAE7B,GAWaC,IAETA,CAAC,EACHC,WAAAA,CAAAA,EACAC,QAAAA,IAASR,CAAAA,EACTS,SAAAA,CAAAA,EACAC,WAAAA,CAAAA,EACAC,qBAAAA,IAAsB,CAAA,CAAA,EACtBC,WAAAA,IAAY,YAAA,EACZ,GAAGC,GACL,KAAM;IACE,MAAA,EAAEC,GAAAA,CAAAA,EAAAA,GAAMC,4UAAAA,CAAgB,IACxB,EAAEC,WAAAA,CAAAA,EAAWd,YAAAA,CAAAA,EAAYe,cAAAA,CAAAA,EAAAA,IAAiBC,4UAAAA,CAAa,IACvDjB,IAAQgB,EAAa,GAErB,EAAEE,KAAKC,CAAAA,EAAAA,mUAAcC,EAAoBd,GAAWE,GAAS;QACjEE,qBAAAA;IAAAA,CACD;IAEDW,4UAAAA,EAAU,MAAM;QAGVf,KAAaC,KACRA,EAAA;YAAEP,OAAAA;YAAOC,YAAAA;QAAAA,CAAY;IAAA,GAE7B;QAACK;QAAWC;QAAQP;QAAOC,CAAU;KAAC,wUAEzCoB,EAAU,MAAM;QACVZ,IAAAA,EAAUa,OAAAA,IAAWH,EAAUG,OAAAA,EAAS;YAC1C,MAAMC,IAAiBC,uPAAAA,EACrBf,EAAUa,OAAAA,EACVH,EAAUG,OAAAA,EACV;gBACEG,UAAU;gBACVd,WAAAA;gBACAe,WAAW;oBACT;wBACEC,MAAM;wBACNC,SAAS;4BACPC,QAAQ;gCAAC;gCAAG,CAAC;6BAAA;wBAAA;oBAEhB,CAAA;iBAAA;YAAA,CAGP;YAGA,OAAO,MAAM;gBACXN,EAAeO,OAAAA,CAAQ;YACzB;QAAA;IAED,GAAA;QAACrB;QAAWU;QAAWR,CAAS;KAAC,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CAGjC,OACC;QAAA,WAAW,CAAA,6DAAA,EAAgEI,CAAS,EAAA;QACpF,OAAO;YACLgB,YAAYzB,IAAY,YAAY;YACpC0B,SAAS1B,IAAY,IAAI;QAC3B;QACA,KAAKa;QACL,MAAK;QACL,cAAYN,EAAE,eAAe;QAC7B,UAAU,CAAA;IAAA,GAEToB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QAAI,WAAU;IAAA,GACbA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,yUAACC,mBAAAA,EAAAA;QAAiB,GAAItB,CAAAA;IAAAA,CAAU,CAClC,CACF;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 843, "column": 0}, "map": {"version": 3, "file": "UnseenBadge.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/feed/components/UnseenBadge/UnseenBadge.tsx"], "sourcesContent": ["import { FeedMetadata } from \"@knocklabs/client\";\nimport { formatBadgeCount, useKnockFeed } from \"@knocklabs/react-core\";\nimport React from \"react\";\n\nimport \"./styles.css\";\n\nexport type BadgeCountType = \"unseen\" | \"unread\" | \"all\";\n\nexport type UnseenBadgeProps = {\n  badgeCountType?: BadgeCountType;\n};\n\nfunction selectBadgeCount(\n  badgeCountType: BadgeCountType,\n  metadata: FeedMetadata,\n) {\n  switch (badgeCountType) {\n    case \"all\":\n      return metadata.total_count;\n    case \"unread\":\n      return metadata.unread_count;\n    case \"unseen\":\n      return metadata.unseen_count;\n  }\n}\n\nexport const UnseenBadge: React.FC<UnseenBadgeProps> = ({\n  badgeCountType = \"unseen\",\n}) => {\n  const { useFeedStore } = useKnockFeed();\n  const badgeCountValue = useFeedStore((state) =>\n    selectBadgeCount(badgeCountType, state.metadata),\n  );\n\n  return badgeCountValue !== 0 ? (\n    <div className=\"rnf-unseen-badge\">\n      <span className=\"rnf-unseen-badge__count\">\n        {formatBadgeCount(badgeCountValue)}\n      </span>\n    </div>\n  ) : null;\n};\n"], "names": ["selectBadgeCount", "badgeCountType", "metadata", "total_count", "unread_count", "unseen_count", "UnseenBadge", "useFeedStore", "useKnockFeed", "badgeCountValue", "state", "React", "formatBadgeCount"], "mappings": ";;;;;;;;;2BAYA,SAASA,EACPC,CAAAA,EACAC,CAAAA,EACA;IACA,OAAQD,GAAc;QACpB,KAAK;YACH,OAAOC,EAASC,WAAAA;QAClB,KAAK;YACH,OAAOD,EAASE,YAAAA;QAClB,KAAK;YACH,OAAOF,EAASG,YAAAA;IAAAA;AAEtB;AAEO,MAAMC,IAA0CA,CAAC,EACtDL,gBAAAA,IAAiB,QAAA,EACnB,KAAM;IACE,MAAA,EAAEM,cAAAA,CAAAA,EAAAA,gVAAiBC,CAAa,IAChCC,IAAkBF,EAAcG,CAAAA,IACpCV,EAAiBC,GAAgBS,EAAMR,QAAQ,CACjD;IAEA,OAAOO,MAAoB,IACxBE,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QAAI,WAAU;IAAA,GACbA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QAAK;QAAA,WAAU;IACbC,6TAAAA,EAAiBH,CAAe,CACnC,CACF,IACE;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "file": "NotificationIconButton.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/feed/components/NotificationIconButton/NotificationIconButton.tsx"], "sourcesContent": ["import { useKnockFeed } from \"@knocklabs/react-core\";\nimport React, { SyntheticEvent } from \"react\";\n\nimport { BellIcon } from \"../../../core/components/Icons\";\nimport { BadgeCountType, UnseenBadge } from \"../UnseenBadge\";\n\nimport \"./styles.css\";\n\nexport interface NotificationIconButtonProps {\n  // What value should we use to drive the badge count?\n  badgeCountType?: BadgeCountType;\n  onClick: (e: SyntheticEvent) => void;\n}\n\nexport const NotificationIconButton = React.forwardRef<\n  HTMLButtonElement,\n  NotificationIconButtonProps\n>(({ onClick, badgeCountType }, ref) => {\n  const { colorMode } = useKnockFeed();\n\n  return (\n    <button\n      className={`rnf-notification-icon-button rnf-notification-icon-button--${colorMode}`}\n      aria-label=\"Open notification feed\"\n      ref={ref}\n      onClick={onClick}\n    >\n      <BellIcon aria-hidden />\n      <UnseenBadge badgeCountType={badgeCountType} />\n    </button>\n  );\n});\n"], "names": ["NotificationIconButton", "React", "forwardRef", "onClick", "badgeCountType", "ref", "colorMode", "useKnockFeed", "BellIcon", "UnseenBadge"], "mappings": ";;;;;;;;;;;;2BAcaA,MAAAA,yTAAyBC,UAAAA,CAAMC,UAAAA,CAG1C,CAAC,EAAEC,SAAAA,CAAAA,EAASC,gBAAAA,CAAAA,EAAe,EAAGC,MAAQ;IAChC,MAAA,EAAEC,WAAAA,CAAAA,EAAAA,gVAAcC,CAAa;IAEnC,OAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CACG,UACC;QAAA,WAAW,CAAA,2DAAA,EAA8DD,CAAS,EAAA;QAClF,cAAW;QACX,KAAAD;QACA,SAAAF;IAAAA,GAEAF,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,kTAACO,WAAAA,EAAAA;QAAS,eAAW,CAAA;IAAA,CAAA,GACpBP,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,+TAAAQ,cAAAA,EAAA;QAAY,gBAAAL;IAA+B,CAAA,CAC9C;AAEJ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "file": "helpers.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/guide/components/helpers.ts"], "sourcesContent": ["export const isValidHttpUrl = (input: string) => {\n  let url;\n\n  try {\n    url = new URL(input);\n  } catch (_) {\n    return false;\n  }\n\n  return url.protocol === \"http:\" || url.protocol === \"https:\";\n};\n\nexport const maybeNavigateToUrlWithDelay = (\n  url: string,\n  delay: number = 200,\n) => {\n  if (!window?.location) return;\n  if (!isValidHttpUrl(url)) return;\n\n  setTimeout(() => window.location.assign(url), delay);\n};\n"], "names": ["isValidHttpUrl", "input", "url", "URL", "protocol", "maybeNavigateToUrlWithDelay", "delay", "window", "location", "setTimeout", "assign"], "mappings": ";;;;AAAaA,MAAAA,IAAiBA,CAACC,MAAkB;IAC3CC,IAAAA;IAEA,IAAA;QACIA,IAAA,IAAIC,IAAIF,CAAK;IAAA,EAAA,OACT;QACH,OAAA,CAAA;IAAA;IAGT,OAAOC,EAAIE,QAAAA,KAAa,WAAWF,EAAIE,QAAAA,KAAa;AACtD,GAEaC,IAA8BA,CACzCH,GACAI,IAAgB,GAAA,KACb;IACEC,UAAAA,QAAAA,OAAQC,QAAAA,IACRR,EAAeE,CAAG,KAEvBO,WAAW,IAAMF,OAAOC,QAAAA,CAASE,MAAAA,CAAOR,CAAG,GAAGI,CAAK;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 934, "column": 0}, "map": {"version": 3, "file": "Banner.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/guide/components/Banner/Banner.tsx"], "sourcesContent": ["import { ColorMode, useGuide } from \"@knocklabs/react-core\";\nimport clsx from \"clsx\";\nimport React from \"react\";\n\nimport { maybeNavigateToUrlWithDelay } from \"../helpers\";\nimport { <PERSON>tonContent, TargetButton, TargetButtonWithGuide } from \"../types\";\n\nimport \"./styles.css\";\n\nconst MESSAGE_TYPE = \"banner\";\n\nconst Root: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-banner\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nRoot.displayName = \"BannerView.Root\";\n\nconst Content: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-banner__message\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nContent.displayName = \"BannerView.Content\";\n\nconst Title: React.FC<\n  { title: string } & React.ComponentPropsWithRef<\"div\">\n> = ({ title, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-banner__title\", className)} {...props}>\n      {title}\n    </div>\n  );\n};\nTitle.displayName = \"BannerView.Title\";\n\nconst Body: React.FC<{ body: string } & React.ComponentPropsWithRef<\"div\">> = ({\n  body,\n  className,\n  ...props\n}) => {\n  return (\n    <div\n      className={clsx(\"knock-guide-banner__body\", className)}\n      dangerouslySetInnerHTML={{ __html: body }}\n      {...props}\n    />\n  );\n};\nBody.displayName = \"BannerView.Body\";\n\nconst Actions: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-banner__actions\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nActions.displayName = \"BannerView.Actions\";\n\nconst PrimaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button\n      className={clsx(\"knock-guide-banner__action\", className)}\n      {...props}\n    >\n      {text}\n    </button>\n  );\n};\nPrimaryButton.displayName = \"BannerView.PrimaryButton\";\n\nconst SecondaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button\n      className={clsx(\n        \"knock-guide-banner__action knock-guide-banner__action--secondary\",\n        className,\n      )}\n      {...props}\n    >\n      {text}\n    </button>\n  );\n};\nSecondaryButton.displayName = \"BannerView.SecondaryButton\";\n\nconst DismissButton: React.FC<React.ComponentPropsWithRef<\"button\">> = ({\n  className,\n  ...props\n}) => {\n  return (\n    <button className={clsx(\"knock-guide-banner__close\", className)} {...props}>\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        width=\"18\"\n        height=\"18\"\n        fill=\"none\"\n      >\n        <g fill=\"#60646C\" fillRule=\"evenodd\" clipRule=\"evenodd\">\n          <path d=\"M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z\" />\n          <path d=\"M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z\" />\n        </g>\n      </svg>\n    </button>\n  );\n};\nDismissButton.displayName = \"BannerView.DismissButton\";\n\ntype BannerContent = {\n  title: string;\n  body: string;\n  primary_button?: ButtonContent;\n  secondary_button?: ButtonContent;\n  dismissible?: boolean;\n};\n\nconst DefaultView: React.FC<{\n  content: BannerContent;\n  colorMode?: ColorMode;\n  onDismiss?: () => void;\n  onButtonClick?: (e: React.MouseEvent, button: TargetButton) => void;\n}> = ({ content, colorMode = \"light\", onDismiss, onButtonClick }) => {\n  return (\n    <Root data-knock-color-mode={colorMode}>\n      <Content>\n        <Title title={content.title} />\n        <Body body={content.body} />\n      </Content>\n      <Actions>\n        {content.secondary_button && (\n          <SecondaryButton\n            text={content.secondary_button.text}\n            action={content.secondary_button.action}\n            onClick={(e) => {\n              if (onButtonClick) {\n                const { text, action } = content.secondary_button!;\n                onButtonClick(e, { name: \"secondary_button\", text, action });\n              }\n            }}\n          />\n        )}\n\n        {content.primary_button && (\n          <PrimaryButton\n            text={content.primary_button.text}\n            action={content.primary_button.action}\n            onClick={(e) => {\n              if (onButtonClick) {\n                const { text, action } = content.primary_button!;\n                onButtonClick(e, { name: \"primary_button\", text, action });\n              }\n            }}\n          />\n        )}\n\n        {content.dismissible && <DismissButton onClick={onDismiss} />}\n      </Actions>\n    </Root>\n  );\n};\nDefaultView.displayName = \"BannerView.Default\";\n\ntype BannerProps = {\n  guideKey?: string;\n  onButtonClick?: (e: React.MouseEvent, target: TargetButtonWithGuide) => void;\n};\n\nexport const Banner: React.FC<BannerProps> = ({ guideKey, onButtonClick }) => {\n  const { guide, step, colorMode } = useGuide({\n    key: guideKey,\n    type: MESSAGE_TYPE,\n  });\n\n  React.useEffect(() => {\n    if (step) step.markAsSeen();\n  }, [step]);\n\n  if (!guide || !step) return null;\n\n  return (\n    <DefaultView\n      content={step.content as BannerContent}\n      colorMode={colorMode}\n      onDismiss={() => step.markAsArchived()}\n      onButtonClick={(e, button) => {\n        const metadata = { ...button, type: \"button_click\" };\n        step.markAsInteracted({ metadata });\n\n        return onButtonClick\n          ? onButtonClick(e, { button, step, guide })\n          : maybeNavigateToUrlWithDelay(button.action);\n      }}\n    />\n  );\n};\nBanner.displayName = \"Banner\";\n\nexport const BannerView = {} as {\n  Default: typeof DefaultView;\n  Root: typeof Root;\n  Content: typeof Content;\n  Title: typeof Title;\n  Body: typeof Body;\n  Actions: typeof Actions;\n  PrimaryButton: typeof PrimaryButton;\n  SecondaryButton: typeof SecondaryButton;\n  DismissButton: typeof DismissButton;\n};\n\nObject.assign(BannerView, {\n  Default: DefaultView,\n  Root,\n  Content,\n  Title,\n  Body,\n  Actions,\n  PrimaryButton,\n  SecondaryButton,\n  DismissButton,\n});\n"], "names": ["MESSAGE_TYPE", "Root", "children", "className", "props", "React", "clsx", "displayName", "Content", "Title", "title", "Body", "body", "__html", "Actions", "PrimaryButton", "text", "action", "SecondaryButton", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "colorMode", "on<PERSON><PERSON><PERSON>", "onButtonClick", "secondary_button", "e", "name", "primary_button", "dismissible", "Banner", "<PERSON><PERSON><PERSON>", "guide", "step", "useGuide", "key", "type", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "markAsArchived", "button", "metadata", "markAsInteracted", "maybeNavigateToUrlWithDelay", "<PERSON><PERSON><PERSON><PERSON>", "Object", "assign", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;2BASA,MAAMA,IAAe,UAEfC,IAEFA,CAAC,EAAEC,UAAAA,CAAAA,EAAUC,WAAAA,CAAAA,EAAW,GAAGC,GAAM,GAEjCC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,gNAAWC,EAAK,sBAAsBH,CAAS;QAAG,GAAIC,CAAAA;IAAAA,GACxDF,CACH;AAGJD,EAAKM,WAAAA,GAAc;AAEnB,MAAMC,IAEFA,CAAC,EAAEN,UAAAA,CAAAA,EAAUC,WAAAA,CAAAA,EAAW,GAAGC,GAAM,GAEjCC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,qMAAWC,WAAAA,EAAK,+BAA+BH,CAAS;QAAG,GAAIC,CAAAA;IAAAA,GACjEF,CACH;AAGJM,EAAQD,WAAAA,GAAc;AAEtB,MAAME,IAEFA,CAAC,EAAEC,OAAAA,CAAAA,EAAOP,WAAAA,CAAAA,EAAW,GAAGC,GAAM,GAE9BC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,gNAAWC,EAAK,6BAA6BH,CAAS;QAAG,GAAIC,CAAAA;IAAAA,GAC/DM,CACH;AAGJD,EAAMF,WAAAA,GAAc;AAEpB,MAAMI,IAAwEA,CAAC,EAC7EC,MAAAA,CAAAA,EACAT,WAAAA,CAAAA,EACA,GAAGC,GACL,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CAEK,OACC;QAAA,gNAAWE,EAAK,4BAA4BH,CAAS;QACrD,yBAAyB;YAAEU,QAAQD;QAAAA;QAC/BR,GAAAA,CAAAA;IACJ,CAAA;AAGNO,EAAKJ,WAAAA,GAAc;AAEnB,MAAMO,IAEFA,CAAC,EAAEZ,UAAAA,CAAAA,EAAUC,WAAAA,CAAAA,EAAW,GAAGC,GAAM,GAEjCC,aAAAA,GAAAA,+TAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,gNAAWC,EAAK,+BAA+BH,CAAS;QAAG,GAAIC,CAAAA;IAAAA,GACjEF,CACH;AAGJY,EAAQP,WAAAA,GAAc;AAEtB,MAAMQ,IAEFA,CAAC,EAAEC,MAAAA,CAAAA,EAAMC,QAAAA,CAAAA,EAAQd,WAAAA,CAAAA,EAAW,GAAGC,GAAM,GAErCC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,UAAA;QACC,gNAAWC,EAAK,8BAA8BH,CAAS;QACvD,GAAIC,CAAAA;IAAAA,GAEHY,CACH;AAGJD,EAAcR,WAAAA,GAAc;AAE5B,MAAMW,IAEFA,CAAC,EAAEF,MAAAA,CAAAA,EAAMC,QAAAA,CAAAA,EAAQd,WAAAA,CAAAA,EAAW,GAAGC,GAAM,GAErCC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,UAAA;QACC,gNAAWC,EACT,oEACAH,CACF;QACA,GAAIC,CAAAA;IAAAA,GAEHY,CACH;AAGJE,EAAgBX,WAAAA,GAAc;AAE9B,MAAMY,IAAiEA,CAAC,EACtEhB,WAAAA,CAAAA,EACA,GAAGC,GACL,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CAEK,UAAO;QAAA,gNAAWE,EAAK,6BAA6BH,CAAS;QAAG,GAAIC,CAAAA;IAAAA,GACnEC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,MAAK;IAAA,GAEJA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,KAAA;QAAE,MAAK;QAAU,UAAS;QAAU,UAAS;IAAA,GAC3CA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,QAAA;QAAK,GAAE;IAAA,CAAsF,GAC7FA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,QAAA;QAAK,GAAE;IAAA,CAAqF,CAC/F,CACF,CACF;AAGJc,EAAcZ,WAAAA,GAAc;AAU5B,MAAMa,IAKDA,CAAC,EAAEC,SAAAA,CAAAA,EAASC,WAAAA,IAAY,OAAA,EAASC,WAAAA,CAAAA,EAAWC,eAAAA,CAAAA,EAAc,GAE1DnB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAJ,GAAA;QAAK,yBAAuBqB;IAAAA,GAAAA,aAAAA,GAAAA,oTAAAA,CAAAA,UAAAA,CAAAA,aAAAA,CAC1Bd,GACC,MAAAH,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAACI,GAAM;QAAA,OAAOY,EAAQX,KAAAA;IAAAA,CAAM,GAC3BL,aAAAA,GAAAA,+TAAAA,CAAA,aAAA,CAAAM,GAAA;QAAK,MAAMU,EAAQT,IAAAA;IAAAA,CAAK,CAC3B,GACCP,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAS,GAAA,MACEO,EAAQI,gBAAAA,IAAAA,aAAAA,GAAAA,oTAAAA,CAAAA,UAAAA,CAAAA,aAAAA,CACNP,GACC;QAAA,MAAMG,EAAQI,gBAAAA,CAAiBT,IAAAA;QAC/B,QAAQK,EAAQI,gBAAAA,CAAiBR,MAAAA;QACjC,SAAUS,CAAMA,MAAA;YACd,IAAIF,GAAe;gBACX,MAAA,EAAER,MAAAA,CAAAA,EAAMC,QAAAA,CAAAA,EAAAA,GAAWI,EAAQI,gBAAAA;gBACjCD,EAAcE,GAAG;oBAAEC,MAAM;oBAAoBX,MAAAA;oBAAMC,QAAAA;gBAAAA,CAAQ;YAAA;QAC7D;IAGL,CAAA,GAEAI,EAAQO,cAAAA,IAAAA,aAAAA,GAAAA,oTAAAA,CAAAA,UAAAA,CAAAA,aAAAA,CACNb,GACC;QAAA,MAAMM,EAAQO,cAAAA,CAAeZ,IAAAA;QAC7B,QAAQK,EAAQO,cAAAA,CAAeX,MAAAA;QAC/B,SAAUS,CAAMA,MAAA;YACd,IAAIF,GAAe;gBACX,MAAA,EAAER,MAAAA,CAAAA,EAAMC,QAAAA,CAAAA,EAAAA,GAAWI,EAAQO,cAAAA;gBACjCJ,EAAcE,GAAG;oBAAEC,MAAM;oBAAkBX,MAAAA;oBAAMC,QAAAA;gBAAAA,CAAQ;YAAA;QAE7D;IAAA,CAEH,GAEAI,EAAQQ,WAAAA,IAAAA,aAAAA,GAAAA,oTAAAA,CAAAA,UAAAA,CAAAA,aAAAA,CAAgBV,GAAc;QAAA,SAASI;IAAa,CAAA,CAC/D,CACF;AAGJH,EAAYb,WAAAA,GAAc;AAOnB,MAAMuB,IAAgCA,CAAC,EAAEC,UAAAA,CAAAA,EAAUP,eAAAA,CAAAA,EAAc,KAAM;IACtE,MAAA,EAAEQ,OAAAA,CAAAA,EAAOC,MAAAA,CAAAA,EAAMX,WAAAA,CAAAA,EAAAA,OAAcY,2TAAAA,EAAS;QAC1CC,KAAKJ;QACLK,MAAMpC;IAAAA,CACP;IAMD,4TAJAK,UAAAA,CAAMgC,SAAAA,CAAU,MAAM;QAChBJ,KAAAA,EAAWK,UAAAA,CAAW;IAAA,GACzB;QAACL,CAAI;KAAC,GAEL,CAACD,KAAS,CAACC,IAAa,OAGzB5B,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAe,GAAA;QACC,SAASa,EAAKZ,OAAAA;QACd,WAAAC;QACA,WAAW,IAAMW,EAAKM,cAAAA,CAAe;QACrC,eAAe,CAACb,GAAGc,MAAW;YAC5B,MAAMC,IAAW;gBAAE,GAAGD,CAAAA;gBAAQJ,MAAM;YAAe;YACnDH,OAAAA,EAAKS,gBAAAA,CAAiB;gBAAED,UAAAA;YAAAA,CAAU,GAE3BjB,IACHA,EAAcE,GAAG;gBAAEc,QAAAA;gBAAQP,MAAAA;gBAAMD,OAAAA;YAAAA,CAAO,kVACxCW,EAA4BH,EAAOvB,MAAM;QAAA;IAAA,CAE/C;AAEN;AACAa,EAAOvB,WAAAA,GAAc;AAEd,MAAMqC,IAAa,CAAA;AAY1BC,OAAOC,MAAAA,CAAOF,GAAY;IACxBG,SAAS3B;IACTnB,MAAAA;IACAO,SAAAA;IACAC,OAAAA;IACAE,MAAAA;IACAG,SAAAA;IACAC,eAAAA;IACAG,iBAAAA;IACAC,eAAAA;AACF,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1088, "column": 0}, "map": {"version": 3, "file": "Card.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/guide/components/Card/Card.tsx"], "sourcesContent": ["import { ColorMode, useGuide } from \"@knocklabs/react-core\";\nimport clsx from \"clsx\";\nimport React from \"react\";\n\nimport { isValidHttpUrl, maybeNavigateToUrlWithDelay } from \"../helpers\";\nimport {\n  ButtonContent,\n  ImageContent,\n  TargetButton,\n  TargetButtonWithGuide,\n  TargetImage,\n  TargetImageWithGuide,\n} from \"../types\";\n\nimport \"./styles.css\";\n\nconst MESSAGE_TYPE = \"card\";\n\nconst Root: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nRoot.displayName = \"CardView.Root\";\n\nconst Content: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card__message\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nContent.displayName = \"CardView.Content\";\n\nconst Header: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card__header\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nHeader.displayName = \"CardView.Header\";\n\nconst Headline: React.FC<\n  { headline: string } & React.ComponentPropsWithRef<\"div\">\n> = ({ headline, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card__headline\", className)} {...props}>\n      {headline}\n    </div>\n  );\n};\nHeadline.displayName = \"CardView.Headline\";\n\nconst Title: React.FC<\n  { title: string } & React.ComponentPropsWithRef<\"div\">\n> = ({ title, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card__title\", className)} {...props}>\n      {title}\n    </div>\n  );\n};\nTitle.displayName = \"CardView.Title\";\n\nconst Body: React.FC<{ body: string } & React.ComponentPropsWithRef<\"div\">> = ({\n  body,\n  className,\n  ...props\n}) => {\n  return (\n    <div\n      className={clsx(\"knock-guide-card__body\", className)}\n      dangerouslySetInnerHTML={{ __html: body }}\n      {...props}\n    />\n  );\n};\nBody.displayName = \"CardView.Body\";\n\nconst Img: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"img\">>\n> = ({ children, className, alt, ...props }) => {\n  return (\n    <img\n      className={clsx(\"knock-guide-card__img\", className)}\n      alt={alt || \"\"}\n      {...props}\n    >\n      {children}\n    </img>\n  );\n};\nImg.displayName = \"CardView.Img\";\n\nconst Actions: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card__actions\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nActions.displayName = \"CardView.Actions\";\n\nconst PrimaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button className={clsx(\"knock-guide-card__action\", className)} {...props}>\n      {text}\n    </button>\n  );\n};\nPrimaryButton.displayName = \"CardView.PrimaryButton\";\n\nconst SecondaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button\n      className={clsx(\n        \"knock-guide-card__action knock-guide-card__action--secondary\",\n        className,\n      )}\n      {...props}\n    >\n      {text}\n    </button>\n  );\n};\nSecondaryButton.displayName = \"CardView.SecondaryButton\";\n\nconst DismissButton: React.FC<React.ComponentPropsWithRef<\"button\">> = ({\n  className,\n  ...props\n}) => {\n  return (\n    <button className={clsx(\"knock-guide-card__close\", className)} {...props}>\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        width=\"18\"\n        height=\"18\"\n        fill=\"none\"\n      >\n        <g fill=\"#60646C\" fillRule=\"evenodd\" clipRule=\"evenodd\">\n          <path d=\"M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z\" />\n          <path d=\"M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z\" />\n        </g>\n      </svg>\n    </button>\n  );\n};\nDismissButton.displayName = \"CardView.DismissButton\";\n\ntype CardContent = {\n  headline: string;\n  title: string;\n  body: string;\n  image?: ImageContent;\n  primary_button?: ButtonContent;\n  secondary_button?: ButtonContent;\n  dismissible?: boolean;\n};\n\nconst DefaultView: React.FC<{\n  content: CardContent;\n  colorMode?: ColorMode;\n  onDismiss?: () => void;\n  onButtonClick?: (e: React.MouseEvent, button: TargetButton) => void;\n  onImageClick?: (e: React.MouseEvent, image: TargetImage) => void;\n}> = ({\n  content,\n  colorMode = \"light\",\n  onDismiss,\n  onButtonClick,\n  onImageClick,\n}) => {\n  return (\n    <Root data-knock-color-mode={colorMode}>\n      <Content>\n        <Header>\n          <Headline headline={content.headline} />\n          {content.dismissible && <DismissButton onClick={onDismiss} />}\n        </Header>\n\n        <Title title={content.title} />\n        <Body body={content.body} />\n      </Content>\n      {content.image && (\n        <a\n          href={\n            isValidHttpUrl(content.image.action)\n              ? content.image.action\n              : undefined\n          }\n          target=\"_blank\"\n        >\n          <Img\n            src={content.image.url}\n            alt={content.image.alt}\n            onClick={(e) => {\n              if (onImageClick) {\n                onImageClick(e, content.image!);\n              }\n            }}\n          />\n        </a>\n      )}\n      {(content.primary_button || content.secondary_button) && (\n        <Actions>\n          {content.primary_button && (\n            <PrimaryButton\n              text={content.primary_button.text}\n              action={content.primary_button.action}\n              onClick={(e) => {\n                if (onButtonClick) {\n                  const { text, action } = content.primary_button!;\n                  onButtonClick(e, { name: \"primary_button\", text, action });\n                }\n              }}\n            />\n          )}\n          {content.secondary_button && (\n            <SecondaryButton\n              text={content.secondary_button.text}\n              action={content.secondary_button.action}\n              onClick={(e) => {\n                if (onButtonClick) {\n                  const { text, action } = content.secondary_button!;\n                  onButtonClick(e, { name: \"secondary_button\", text, action });\n                }\n              }}\n            />\n          )}\n        </Actions>\n      )}\n    </Root>\n  );\n};\nDefaultView.displayName = \"CardView.Default\";\n\ntype CardProps = {\n  guideKey?: string;\n  onButtonClick?: (e: React.MouseEvent, target: TargetButtonWithGuide) => void;\n  onImageClick?: (e: React.MouseEvent, target: TargetImageWithGuide) => void;\n};\n\nexport const Card: React.FC<CardProps> = ({\n  guideKey,\n  onButtonClick,\n  onImageClick,\n}) => {\n  const { guide, step, colorMode } = useGuide({\n    key: guideKey,\n    type: MESSAGE_TYPE,\n  });\n\n  React.useEffect(() => {\n    if (step) step.markAsSeen();\n  }, [step]);\n\n  if (!guide || !step) return null;\n\n  return (\n    <DefaultView\n      content={step.content as CardContent}\n      colorMode={colorMode}\n      onDismiss={() => step.markAsArchived()}\n      onButtonClick={(e, button) => {\n        const metadata = { ...button, type: \"button_click\" };\n        step.markAsInteracted({ metadata });\n\n        return onButtonClick\n          ? onButtonClick(e, { button, step, guide })\n          : maybeNavigateToUrlWithDelay(button.action);\n      }}\n      onImageClick={(e, image) => {\n        const metadata = { ...image, type: \"image_click\" };\n        step.markAsInteracted({ metadata });\n\n        if (onImageClick) {\n          return onImageClick(e, { image, step, guide });\n        }\n      }}\n    />\n  );\n};\nCard.displayName = \"Card\";\n\nexport const CardView = {} as {\n  Default: typeof DefaultView;\n  Root: typeof Root;\n  Content: typeof Content;\n  Headline: typeof Headline;\n  Title: typeof Title;\n  Body: typeof Body;\n  Img: typeof Img;\n  Actions: typeof Actions;\n  PrimaryButton: typeof PrimaryButton;\n  SecondaryButton: typeof SecondaryButton;\n  DismissButton: typeof DismissButton;\n};\n\nObject.assign(CardView, {\n  Default: DefaultView,\n  Root,\n  Content,\n  Title,\n  Body,\n  Img,\n  Actions,\n  PrimaryButton,\n  SecondaryButton,\n  DismissButton,\n});\n"], "names": ["MESSAGE_TYPE", "Root", "children", "className", "props", "React", "clsx", "displayName", "Content", "Header", "Headline", "headline", "Title", "title", "Body", "body", "__html", "Img", "alt", "Actions", "PrimaryButton", "text", "action", "SecondaryButton", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "colorMode", "on<PERSON><PERSON><PERSON>", "onButtonClick", "onImageClick", "dismissible", "image", "isValidHttpUrl", "undefined", "url", "e", "primary_button", "secondary_button", "name", "Card", "<PERSON><PERSON><PERSON>", "guide", "step", "useGuide", "key", "type", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "markAsArchived", "button", "metadata", "markAsInteracted", "maybeNavigateToUrlWithDelay", "Card<PERSON>iew", "Object", "assign", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;2BAgBA,MAAMA,IAAe,QAEfC,IAEFA,CAAC,EAAEC,UAAAA,CAAAA,EAAUC,WAAAA,CAAAA,EAAW,GAAGC,GAAM,GAEjCC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,sMAAWC,UAAAA,EAAK,oBAAoBH,CAAS;QAAG,GAAIC,CAAAA;IAAAA,GACtDF,CACH;AAGJD,EAAKM,WAAAA,GAAc;AAEnB,MAAMC,IAEFA,CAAC,EAAEN,UAAAA,CAAAA,EAAUC,WAAAA,CAAAA,EAAW,GAAGC,GAAM,GAEjCC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,sMAAWC,UAAAA,EAAK,6BAA6BH,CAAS;QAAG,GAAIC,CAAAA;IAAAA,GAC/DF,CACH;AAGJM,EAAQD,WAAAA,GAAc;AAEtB,MAAME,IAEFA,CAAC,EAAEP,UAAAA,CAAAA,EAAUC,WAAAA,CAAAA,EAAW,GAAGC,GAAM,GAEjCC,aAAAA,GAAAA,+TAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,gNAAWC,EAAK,4BAA4BH,CAAS;QAAG,GAAIC,CAAAA;IAAAA,GAC9DF,CACH;AAGJO,EAAOF,WAAAA,GAAc;AAErB,MAAMG,IAEFA,CAAC,EAAEC,UAAAA,CAAAA,EAAUR,WAAAA,CAAAA,EAAW,GAAGC,GAAM,GAEjCC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,gNAAWC,EAAK,8BAA8BH,CAAS;QAAG,GAAIC,CAAAA;IAAAA,GAChEO,CACH;AAGJD,EAASH,WAAAA,GAAc;AAEvB,MAAMK,IAEFA,CAAC,EAAEC,OAAAA,CAAAA,EAAOV,WAAAA,CAAAA,EAAW,GAAGC,GAAM,GAE9BC,aAAAA,GAAAA,+TAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,gNAAWC,EAAK,2BAA2BH,CAAS;QAAG,GAAIC,CAAAA;IAAAA,GAC7DS,CACH;AAGJD,EAAML,WAAAA,GAAc;AAEpB,MAAMO,IAAwEA,CAAC,EAC7EC,MAAAA,CAAAA,EACAZ,WAAAA,CAAAA,EACA,GAAGC,GACL,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CAEK,OACC;QAAA,gNAAWE,EAAK,0BAA0BH,CAAS;QACnD,yBAAyB;YAAEa,QAAQD;QAAAA;QAC/BX,GAAAA,CAAAA;IACJ,CAAA;AAGNU,EAAKP,WAAAA,GAAc;AAEnB,MAAMU,IAEFA,CAAC,EAAEf,UAAAA,CAAAA,EAAUC,WAAAA,CAAAA,EAAWe,KAAAA,CAAAA,EAAK,GAAGd,GAAM,GAErCC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QACC,gNAAWC,EAAK,yBAAyBH,CAAS;QAClD,KAAKe,KAAO;QACRd,GAAAA,CAAAA;IAAAA,GAEHF,CACH;AAGJe,EAAIV,WAAAA,GAAc;AAElB,MAAMY,IAEFA,CAAC,EAAEjB,UAAAA,CAAAA,EAAUC,WAAAA,CAAAA,EAAW,GAAGC,GAAM,GAEjCC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,YAAWC,oMAAAA,EAAK,6BAA6BH,CAAS;QAAG,GAAIC,CAAAA;IAAAA,GAC/DF,CACH;AAGJiB,EAAQZ,WAAAA,GAAc;AAEtB,MAAMa,IAEFA,CAAC,EAAEC,MAAAA,CAAAA,EAAMC,QAAAA,CAAAA,EAAQnB,WAAAA,CAAAA,EAAW,GAAGC,GAAM,GAErCC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,UAAA;QAAO,eAAWC,iMAAAA,EAAK,4BAA4BH,CAAS;QAAG,GAAIC,CAAAA;IAAAA,GACjEiB,CACH;AAGJD,EAAcb,WAAAA,GAAc;AAE5B,MAAMgB,IAEFA,CAAC,EAAEF,MAAAA,CAAAA,EAAMC,QAAAA,CAAAA,EAAQnB,WAAAA,CAAAA,EAAW,GAAGC,GAAM,GAErCC,aAAAA,GAAAA,+TAAAA,CAAA,aAAA,CAAC,UAAA;QACC,gNAAWC,EACT,gEACAH,CACF;QACA,GAAIC,CAAAA;IAAAA,GAEHiB,CACH;AAGJE,EAAgBhB,WAAAA,GAAc;AAE9B,MAAMiB,IAAiEA,CAAC,EACtErB,WAAAA,CAAAA,EACA,GAAGC,GACL,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CAEK,UAAO;QAAA,gNAAWE,EAAK,2BAA2BH,CAAS;QAAG,GAAIC,CAAAA;IAAAA,GACjEC,aAAAA,uTAAAA,WAAAA,CAAA,aAAA,CAAC,OAAA;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,MAAK;IAAA,GAEJA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,KAAA;QAAE,MAAK;QAAU,UAAS;QAAU,UAAS;IAAA,GAC3CA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,QAAA;QAAK,GAAE;IAAA,CAAsF,GAC7FA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,QAAA;QAAK,GAAE;IAAA,CAAqF,CAC/F,CACF,CACF;AAGJmB,EAAcjB,WAAAA,GAAc;AAY5B,MAAMkB,IAMDA,CAAC,EACJC,SAAAA,CAAAA,EACAC,WAAAA,IAAY,OAAA,EACZC,WAAAA,CAAAA,EACAC,eAAAA,CAAAA,EACAC,cAAAA,CAAAA,EACF,GAEKzB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAJ,GAAA;QAAK,yBAAuB0B;IAAAA,GAC1BtB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAG,GAAA,MACEH,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAI,GAAA,MACEJ,aAAAA,GAAAA,+TAAAA,CAAA,aAAA,CAAAK,GAAA;QAAS,UAAUgB,EAAQf,QAAAA;IAAS,CAAA,GACpCe,EAAQK,WAAAA,IAAgB1B,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAmB,GAAA;QAAc,SAASI;IAAAA,CAAa,CAC/D,GAECvB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAO,GAAA;QAAM,OAAOc,EAAQb,KAAAA;IAAAA,CAAM,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CAC3BC,GAAK;QAAA,MAAMY,EAAQX,IAAAA;IAAAA,CAAK,CAC3B,GACCW,EAAQM,KAAAA,IACN3B,aAAAA,GAAAA,+TAAAA,CAAA,aAAA,CAAA,KAAA;QACC,uUACE4B,EAAeP,EAAQM,KAAAA,CAAMV,MAAM,IAC/BI,EAAQM,KAAAA,CAAMV,MAAAA,GACdY,KAAAA;QAEN,QAAO;IAAA,GAEN7B,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAY,GAAA;QACC,KAAKS,EAAQM,KAAAA,CAAMG,GAAAA;QACnB,KAAKT,EAAQM,KAAAA,CAAMd,GAAAA;QACnB,SAAUkB,CAAMA,MAAA;YACVN,KACWM,EAAAA,GAAGV,EAAQM,KAAM;QAElC;IAAA,CAAE,CAEN,GAAA,CAEAN,EAAQW,cAAAA,IAAkBX,EAAQY,gBAAAA,KACjCjC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAc,GAAA,MACEO,EAAQW,cAAAA,IAAAA,aAAAA,GAAAA,oTAAAA,CAAAA,UAAAA,CAAAA,aAAAA,CACNjB,GACC;QAAA,MAAMM,EAAQW,cAAAA,CAAehB,IAAAA;QAC7B,QAAQK,EAAQW,cAAAA,CAAef,MAAAA;QAC/B,SAAUc,CAAMA,MAAA;YACd,IAAIP,GAAe;gBACX,MAAA,EAAER,MAAAA,CAAAA,EAAMC,QAAAA,CAAAA,EAAAA,GAAWI,EAAQW,cAAAA;gBACjCR,EAAcO,GAAG;oBAAEG,MAAM;oBAAkBlB,MAAAA;oBAAMC,QAAAA;gBAAAA,CAAQ;YAAA;QAC3D;IAGL,CAAA,GACAI,EAAQY,gBAAAA,IAAAA,aAAAA,GAAAA,oTAAAA,CAAAA,UAAAA,CAAAA,aAAAA,CACNf,GACC;QAAA,MAAMG,EAAQY,gBAAAA,CAAiBjB,IAAAA;QAC/B,QAAQK,EAAQY,gBAAAA,CAAiBhB,MAAAA;QACjC,SAAUc,CAAMA,MAAA;YACd,IAAIP,GAAe;gBACX,MAAA,EAAER,MAAAA,CAAAA,EAAMC,QAAAA,CAAAA,EAAAA,GAAWI,EAAQY,gBAAAA;gBACjCT,EAAcO,GAAG;oBAAEG,MAAM;oBAAoBlB,MAAAA;oBAAMC,QAAAA;gBAAAA,CAAQ;YAAA;QAE/D;IAAA,CAEH,CACH,CAEJ;AAGJG,EAAYlB,WAAAA,GAAc;AAQnB,MAAMiC,IAA4BA,CAAC,EACxCC,UAAAA,CAAAA,EACAZ,eAAAA,CAAAA,EACAC,cAAAA,CAAAA,EACF,KAAM;IACE,MAAA,EAAEY,OAAAA,CAAAA,EAAOC,MAAAA,CAAAA,EAAMhB,WAAAA,CAAAA,EAAAA,OAAciB,2TAAAA,EAAS;QAC1CC,KAAKJ;QACLK,MAAM9C;IAAAA,CACP;IAMD,4TAJAK,UAAAA,CAAM0C,SAAAA,CAAU,MAAM;QAChBJ,KAAAA,EAAWK,UAAAA,CAAW;IAAA,GACzB;QAACL,CAAI;KAAC,GAEL,CAACD,KAAS,CAACC,IAAa,OAGzBtC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAoB,GAAA;QACC,SAASkB,EAAKjB,OAAAA;QACd,WAAAC;QACA,WAAW,IAAMgB,EAAKM,cAAAA,CAAe;QACrC,eAAe,CAACb,GAAGc,MAAW;YAC5B,MAAMC,IAAW;gBAAE,GAAGD,CAAAA;gBAAQJ,MAAM;YAAe;YACnDH,OAAAA,EAAKS,gBAAAA,CAAiB;gBAAED,UAAAA;YAAAA,CAAU,GAE3BtB,IACHA,EAAcO,GAAG;gBAAEc,QAAAA;gBAAQP,MAAAA;gBAAMD,OAAAA;YAAAA,CAAO,kVACxCW,EAA4BH,EAAO5B,MAAM;QAAA;QAE/C,cAAc,CAACc,GAAGJ,MAAU;YAC1B,MAAMmB,IAAW;gBAAE,GAAGnB,CAAAA;gBAAOc,MAAM;YAAc;YAGjD,IAFAH,EAAKS,gBAAAA,CAAiB;gBAAED,UAAAA;YAAAA,CAAU,GAE9BrB,GACF,OAAOA,EAAaM,GAAG;gBAAEJ,OAAAA;gBAAOW,MAAAA;gBAAMD,OAAAA;YAAAA,CAAO;QAC/C;IAAA,CAEF;AAEN;AACAF,EAAKjC,WAAAA,GAAc;AAEZ,MAAM+C,IAAW,CAAA;AAcxBC,OAAOC,MAAAA,CAAOF,GAAU;IACtBG,SAAShC;IACTxB,MAAAA;IACAO,SAAAA;IACAI,OAAAA;IACAE,MAAAA;IACAG,KAAAA;IACAE,SAAAA;IACAC,eAAAA;IACAG,iBAAAA;IACAC,eAAAA;AACF,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1283, "column": 0}, "map": {"version": 3, "file": "Modal.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/guide/components/Modal/Modal.tsx"], "sourcesContent": ["import { ColorMode, useGuide } from \"@knocklabs/react-core\";\nimport * as Dialog from \"@radix-ui/react-dialog\";\nimport clsx from \"clsx\";\nimport React from \"react\";\n\nimport { isValidHttpUrl, maybeNavigateToUrlWithDelay } from \"../helpers\";\nimport {\n  ButtonContent,\n  ImageContent,\n  TargetButton,\n  TargetButtonWithGuide,\n  TargetImage,\n  TargetImageWithGuide,\n} from \"../types\";\n\nimport \"./styles.css\";\n\nconst MESSAGE_TYPE = \"modal\";\n\ntype RootProps = Omit<\n  React.ComponentPropsWithoutRef<typeof Dialog.Root>,\n  \"modal\"\n> &\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>;\n\nconst Root = ({ children, onOpenChange, ...props }: RootProps) => {\n  return (\n    <Dialog.Root defaultOpen onOpenChange={onOpenChange} {...props}>\n      <Dialog.Portal>{children}</Dialog.Portal>\n    </Dialog.Root>\n  );\n};\nRoot.displayName = \"ModalView.Root\";\n\ntype OverlayProps = React.ComponentPropsWithoutRef<typeof Dialog.Overlay> &\n  React.ComponentPropsWithRef<\"div\">;\ntype OverlayRef = React.ElementRef<\"div\">;\n\nconst Overlay = React.forwardRef<OverlayRef, OverlayProps>(\n  ({ className, ...props }, forwardedRef) => {\n    return (\n      <Dialog.Overlay\n        className={clsx(\"knock-guide-modal__overlay\", className)}\n        ref={forwardedRef}\n        {...props}\n      />\n    );\n  },\n);\nOverlay.displayName = \"ModalView.Overlay\";\n\ntype ContentProps = React.ComponentPropsWithoutRef<typeof Dialog.Content> &\n  React.ComponentPropsWithRef<\"div\">;\ntype ContentRef = React.ElementRef<\"div\">;\n\nconst Content = React.forwardRef<ContentRef, ContentProps>(\n  ({ children, className, ...props }, forwardedRef) => {\n    return (\n      <Dialog.Content\n        className={clsx(\"knock-guide-modal\", className)}\n        ref={forwardedRef}\n        {...props}\n      >\n        {children}\n      </Dialog.Content>\n    );\n  },\n);\nContent.displayName = \"ModalView.Content\";\n\nconst Header: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-modal__header\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nHeader.displayName = \"ModalView.Header\";\n\ntype TitleProps = React.ComponentPropsWithoutRef<typeof Dialog.Title> &\n  React.ComponentPropsWithRef<\"div\"> & {\n    title: string;\n  };\n\nconst Title = ({ title, className, ...props }: TitleProps) => {\n  return (\n    <Dialog.Title\n      className={clsx(\"knock-guide-modal__title\", className)}\n      {...props}\n    >\n      {title}\n    </Dialog.Title>\n  );\n};\nTitle.displayName = \"ModalView.Title\";\n\nconst Body: React.FC<{ body: string } & React.ComponentPropsWithRef<\"div\">> = ({\n  body,\n  className,\n  ...props\n}) => {\n  return (\n    <Dialog.Description\n      className={clsx(\"knock-guide-modal__body\", className)}\n      dangerouslySetInnerHTML={{ __html: body }}\n      {...props}\n    />\n  );\n};\nBody.displayName = \"ModalView.Body\";\n\nconst Img: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"img\">>\n> = ({ children, className, alt, ...props }) => {\n  return (\n    <img\n      className={clsx(\"knock-guide-modal__img\", className)}\n      alt={alt || \"\"}\n      {...props}\n    >\n      {children}\n    </img>\n  );\n};\nImg.displayName = \"ModalView.Img\";\n\nconst Actions: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-modal__actions\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nActions.displayName = \"ModalView.Actions\";\n\nconst PrimaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button className={clsx(\"knock-guide-modal__action\", className)} {...props}>\n      {text}\n    </button>\n  );\n};\nPrimaryButton.displayName = \"ModalView.PrimaryButton\";\n\nconst SecondaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button\n      className={clsx(\n        \"knock-guide-modal__action knock-guide-modal__action--secondary\",\n        className,\n      )}\n      {...props}\n    >\n      {text}\n    </button>\n  );\n};\nSecondaryButton.displayName = \"ModalView.SecondaryButton\";\n\ntype CloseProps = React.ComponentPropsWithoutRef<typeof Dialog.Close> &\n  React.ComponentPropsWithRef<\"button\">;\n\nconst Close = ({ className, ...props }: CloseProps) => {\n  return (\n    <Dialog.Close\n      className={clsx(\"knock-guide-modal__close\", className)}\n      {...props}\n    >\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        width=\"18\"\n        height=\"18\"\n        fill=\"none\"\n      >\n        <g fill=\"#60646C\" fillRule=\"evenodd\" clipRule=\"evenodd\">\n          <path d=\"M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z\" />\n          <path d=\"M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z\" />\n        </g>\n      </svg>\n    </Dialog.Close>\n  );\n};\nClose.displayName = \"ModalView.Close\";\n\ntype ModalContent = {\n  title: string;\n  body: string;\n  image?: ImageContent;\n  primary_button?: ButtonContent;\n  secondary_button?: ButtonContent;\n  dismissible?: boolean;\n};\n\nconst DefaultView: React.FC<{\n  content: ModalContent;\n  colorMode?: ColorMode;\n  onOpenChange?: (open: boolean) => void;\n  onDismiss?: () => void;\n  onButtonClick?: (e: React.MouseEvent, button: TargetButton) => void;\n  onImageClick?: (e: React.MouseEvent, image: TargetImage) => void;\n}> = ({\n  content,\n  colorMode = \"light\",\n  onOpenChange,\n  onDismiss,\n  onButtonClick,\n  onImageClick,\n}) => {\n  return (\n    <Root onOpenChange={onOpenChange}>\n      <Overlay />\n      {/* Must pass color mode to content for css variables to be set properly */}\n      <Content\n        data-knock-color-mode={colorMode}\n        onPointerDownOutside={onDismiss}\n      >\n        <Header>\n          <Title title={content.title} />\n          {content.dismissible && <Close onClick={onDismiss} />}\n        </Header>\n\n        <Body body={content.body} />\n\n        {content.image && (\n          <a\n            href={\n              isValidHttpUrl(content.image.action)\n                ? content.image.action\n                : undefined\n            }\n            target=\"_blank\"\n          >\n            <Img\n              src={content.image.url}\n              alt={content.image.alt}\n              onClick={(e) => {\n                if (onImageClick) {\n                  onImageClick(e, content.image!);\n                }\n              }}\n            />\n          </a>\n        )}\n\n        {(content.primary_button || content.secondary_button) && (\n          <Actions>\n            {content.secondary_button && (\n              <SecondaryButton\n                text={content.secondary_button.text}\n                action={content.secondary_button.action}\n                onClick={(e) => {\n                  if (onButtonClick) {\n                    const { text, action } = content.secondary_button!;\n                    onButtonClick(e, {\n                      name: \"secondary_button\",\n                      text,\n                      action,\n                    });\n                  }\n                }}\n              />\n            )}\n            {content.primary_button && (\n              <PrimaryButton\n                text={content.primary_button.text}\n                action={content.primary_button.action}\n                onClick={(e) => {\n                  if (onButtonClick) {\n                    const { text, action } = content.primary_button!;\n                    onButtonClick(e, { name: \"primary_button\", text, action });\n                  }\n                }}\n              />\n            )}\n          </Actions>\n        )}\n      </Content>\n    </Root>\n  );\n};\nDefaultView.displayName = \"ModalView.Default\";\n\ntype ModalProps = {\n  guideKey?: string;\n  onButtonClick?: (e: React.MouseEvent, target: TargetButtonWithGuide) => void;\n  onImageClick?: (e: React.MouseEvent, target: TargetImageWithGuide) => void;\n};\n\nexport const Modal: React.FC<ModalProps> = ({\n  guideKey,\n  onButtonClick,\n  onImageClick,\n}) => {\n  const { guide, step, colorMode } = useGuide({\n    key: guideKey,\n    type: MESSAGE_TYPE,\n  });\n\n  React.useEffect(() => {\n    if (step) step.markAsSeen();\n  }, [step]);\n\n  if (!guide || !step) return null;\n\n  return (\n    <DefaultView\n      content={step.content as ModalContent}\n      colorMode={colorMode}\n      onDismiss={() => step.markAsArchived()}\n      onButtonClick={(e, button) => {\n        const metadata = { ...button, type: \"button_click\" };\n        step.markAsInteracted({ metadata });\n\n        return onButtonClick\n          ? onButtonClick(e, { button, step, guide })\n          : maybeNavigateToUrlWithDelay(button.action);\n      }}\n      onImageClick={(e, image) => {\n        const metadata = { ...image, type: \"image_click\" };\n        step.markAsInteracted({ metadata });\n\n        if (onImageClick) {\n          return onImageClick(e, { image, step, guide });\n        }\n      }}\n    />\n  );\n};\nModal.displayName = \"Modal\";\n\nexport const ModalView = {} as {\n  Default: typeof DefaultView;\n  Root: typeof Root;\n  Overlay: typeof Overlay;\n  Content: typeof Content;\n  Title: typeof Title;\n  Body: typeof Body;\n  Img: typeof Img;\n  Actions: typeof Actions;\n  PrimaryButton: typeof PrimaryButton;\n  SecondaryButton: typeof SecondaryButton;\n  Close: typeof Close;\n};\n\nObject.assign(ModalView, {\n  Default: DefaultView,\n  Root,\n  Overlay,\n  Content,\n  Title,\n  Body,\n  Img,\n  Actions,\n  PrimaryButton,\n  SecondaryButton,\n  Close,\n});\n"], "names": ["MESSAGE_TYPE", "Root", "children", "onOpenChange", "props", "React", "Dialog", "displayName", "Overlay", "forwardRef", "className", "forwardedRef", "clsx", "Content", "Header", "Title", "title", "Body", "body", "__html", "Img", "alt", "Actions", "PrimaryButton", "text", "action", "SecondaryButton", "Close", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "colorMode", "on<PERSON><PERSON><PERSON>", "onButtonClick", "onImageClick", "dismissible", "image", "isValidHttpUrl", "undefined", "url", "e", "primary_button", "secondary_button", "name", "Modal", "<PERSON><PERSON><PERSON>", "guide", "step", "useGuide", "key", "type", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "markAsArchived", "button", "metadata", "markAsInteracted", "maybeNavigateToUrlWithDelay", "<PERSON><PERSON><PERSON>iew", "Object", "assign", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;2BAiBA,MAAMA,IAAe,SAQfC,IAAOA,CAAC,EAAEC,UAAAA,CAAAA,EAAUC,cAAAA,CAAAA,EAAc,GAAGC,GAAiB,GAEvDC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAC,EAAO,qRAAA,EAAP;QAAY,aAAW,CAAA;QAAC,cAAAH;QAA4B,GAAIC,CAAAA;IAAAA,GACtDC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,iRAAAC,EAAO,OAAA,EAAP,MAAeJ,CAAS,CAC3B;AAGJD,EAAKM,WAAAA,GAAc;AAMnB,MAAMC,IAAUH,+TAAAA,CAAMI,UAAAA,CACpB,CAAC,EAAEC,WAAAA,CAAAA,EAAW,GAAGN,GAAM,EAAGO,IAErBN,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,iRAAAC,EAAO,QAAA,EAAP;QACC,gNAAWM,EAAK,8BAA8BF,CAAS;QACvD,KAAKC;QACL,GAAIP,CAAAA;IACJ,CAAA,CAGR;AACAI,EAAQD,WAAAA,GAAc;AAMtB,MAAMM,IAAUR,+TAAAA,CAAMI,UAAAA,CACpB,CAAC,EAAEP,UAAAA,CAAAA,EAAUQ,WAAAA,CAAAA,EAAW,GAAGN,GAAM,EAAGO,IAE/BN,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,iRAAAC,EAAO,QAAA,EAAP;QACC,gNAAWM,EAAK,qBAAqBF,CAAS;QAC9C,KAAKC;QACDP,GAAAA,CAAAA;IAAAA,GAEHF,CACH,CAGN;AACAW,EAAQN,WAAAA,GAAc;AAEtB,MAAMO,IAEFA,CAAC,EAAEZ,UAAAA,CAAAA,EAAUQ,WAAAA,CAAAA,EAAW,GAAGN,GAAM,GAEjCC,aAAAA,GAAAA,+TAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,gNAAWO,EAAK,6BAA6BF,CAAS;QAAG,GAAIN,CAAAA;IAAAA,GAC/DF,CACH;AAGJY,EAAOP,WAAAA,GAAc;AAOrB,MAAMQ,IAAQA,CAAC,EAAEC,OAAAA,CAAAA,EAAON,WAAAA,CAAAA,EAAW,GAAGN,GAAkB,GAEpDC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,iRAACC,EAAO,MAAA,EAAP;QACC,gNAAWM,EAAK,4BAA4BF,CAAS;QACrD,GAAIN,CAAAA;IAAAA,GAEHY,CACH;AAGJD,EAAMR,WAAAA,GAAc;AAEpB,MAAMU,IAAwEA,CAAC,EAC7EC,MAAAA,CAAAA,EACAR,WAAAA,CAAAA,EACA,GAAGN,GACL,GAEIC,aAAAA,GAAAA,+TAAAA,CAAA,aAAA,iRAACC,EAAO,YAAA,EAAP;QACC,eAAWM,iMAAAA,EAAK,2BAA2BF,CAAS;QACpD,yBAAyB;YAAES,QAAQD;QAAAA;QAC/Bd,GAAAA,CAAAA;IACJ,CAAA;AAGNa,EAAKV,WAAAA,GAAc;AAEnB,MAAMa,IAEFA,CAAC,EAAElB,UAAAA,CAAAA,EAAUQ,WAAAA,CAAAA,EAAWW,KAAAA,CAAAA,EAAK,GAAGjB,GAAM,GAErCC,aAAAA,uTAAAA,WAAAA,CAAA,aAAA,CAAA,OAAA;QACC,gNAAWO,EAAK,0BAA0BF,CAAS;QACnD,KAAKW,KAAO;QACRjB,GAAAA,CAAAA;IAAAA,GAEHF,CACH;AAGJkB,EAAIb,WAAAA,GAAc;AAElB,MAAMe,IAEFA,CAAC,EAAEpB,UAAAA,CAAAA,EAAUQ,WAAAA,CAAAA,EAAW,GAAGN,GAAM,GAEjCC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,sMAAWO,UAAAA,EAAK,8BAA8BF,CAAS;QAAG,GAAIN,CAAAA;IAAAA,GAChEF,CACH;AAGJoB,EAAQf,WAAAA,GAAc;AAEtB,MAAMgB,IAEFA,CAAC,EAAEC,MAAAA,CAAAA,EAAMC,QAAAA,CAAAA,EAAQf,WAAAA,CAAAA,EAAW,GAAGN,GAAM,GAErCC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,UAAA;QAAO,qMAAWO,WAAAA,EAAK,6BAA6BF,CAAS;QAAG,GAAIN,CAAAA;IAAAA,GAClEoB,CACH;AAGJD,EAAchB,WAAAA,GAAc;AAE5B,MAAMmB,IAEFA,CAAC,EAAEF,MAAAA,CAAAA,EAAMC,QAAAA,CAAAA,EAAQf,WAAAA,CAAAA,EAAW,GAAGN,GAAM,GAErCC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,UAAA;QACC,gNAAWO,EACT,kEACAF,CACF;QACA,GAAIN,CAAAA;IAAAA,GAEHoB,CACH;AAGJE,EAAgBnB,WAAAA,GAAc;AAK9B,MAAMoB,IAAQA,CAAC,EAAEjB,WAAAA,CAAAA,EAAW,GAAGN,GAAkB,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,iRAE5CE,EAAO,MAAA,EAAP;QACC,YAAWM,oMAAAA,EAAK,4BAA4BF,CAAS;QACrD,GAAIN,CAAAA;IAAAA,GAAAA,aAAAA,GAAAA,oTAAAA,CAAAA,UAAAA,CAAAA,aAAAA,CAEH,OACC;QAAA,OAAM;QACN,OAAM;QACN,QAAO;QACP,MAAK;IAEL,GAAAC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,KAAA;QAAE,MAAK;QAAU,UAAS;QAAU,UAAS;IAAA,GAC3CA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,QAAA;QAAK,GAAE;IAAA,CAAsF,GAC7FA,aAAAA,uTAAAA,WAAAA,CAAA,aAAA,CAAA,QAAA;QAAK,GAAE;IAAqF,CAAA,CAC/F,CACF,CACF;AAGJsB,EAAMpB,WAAAA,GAAc;AAWpB,MAAMqB,IAODA,CAAC,EACJC,SAAAA,CAAAA,EACAC,WAAAA,IAAY,OAAA,EACZ3B,cAAAA,CAAAA,EACA4B,WAAAA,CAAAA,EACAC,eAAAA,CAAAA,EACAC,cAAAA,CAAAA,EACF,GAEK5B,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAJ,GAAA;QAAK,cAAAE;IAAAA,GACJE,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAACG,GAAO,IAAA,GAEPH,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAQ,GAAA;QACC,yBAAuBiB;QACvB,sBAAsBC;IAAAA,GAErB1B,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAS,GAAA,MACET,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAU,GAAA;QAAM,OAAOc,EAAQb,KAAAA;IAAM,CAAA,GAC3Ba,EAAQK,WAAAA,IAAgB7B,aAAAA,uTAAAA,WAAAA,CAAA,aAAA,CAAAsB,GAAA;QAAM,SAASI;IAAAA,CAAa,CACvD,GAEC1B,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAY,GAAA;QAAK,MAAMY,EAAQX,IAAAA;IAAK,CAAA,GAExBW,EAAQM,KAAAA,IACN9B,aAAAA,GAAAA,+TAAAA,CAAA,aAAA,CAAA,KAAA;QACC,uUACE+B,EAAeP,EAAQM,KAAAA,CAAMV,MAAM,IAC/BI,EAAQM,KAAAA,CAAMV,MAAAA,GACdY,KAAAA;QAEN,QAAO;IAAA,GAENhC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAe,GAAA;QACC,KAAKS,EAAQM,KAAAA,CAAMG,GAAAA;QACnB,KAAKT,EAAQM,KAAAA,CAAMd,GAAAA;QACnB,SAAUkB,CAAMA,MAAA;YACVN,KACWM,EAAAA,GAAGV,EAAQM,KAAM;QAElC;IAAA,CAAE,CAEN,GAAA,CAGAN,EAAQW,cAAAA,IAAkBX,EAAQY,gBAAAA,KACjCpC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAiB,GAAA,MACEO,EAAQY,gBAAAA,IAAAA,aAAAA,GAAAA,oTAAAA,CAAAA,UAAAA,CAAAA,aAAAA,CACNf,GACC;QAAA,MAAMG,EAAQY,gBAAAA,CAAiBjB,IAAAA;QAC/B,QAAQK,EAAQY,gBAAAA,CAAiBhB,MAAAA;QACjC,SAAUc,CAAMA,MAAA;YACd,IAAIP,GAAe;gBACX,MAAA,EAAER,MAAAA,CAAAA,EAAMC,QAAAA,CAAAA,EAAAA,GAAWI,EAAQY,gBAAAA;gBACjCT,EAAcO,GAAG;oBACfG,MAAM;oBACNlB,MAAAA;oBACAC,QAAAA;gBAAAA,CACD;YAAA;QACH;IAGL,CAAA,GACAI,EAAQW,cAAAA,IAAAA,aAAAA,GAAAA,oTAAAA,CAAAA,UAAAA,CAAAA,aAAAA,CACNjB,GACC;QAAA,MAAMM,EAAQW,cAAAA,CAAehB,IAAAA;QAC7B,QAAQK,EAAQW,cAAAA,CAAef,MAAAA;QAC/B,SAAUc,CAAMA,MAAA;YACd,IAAIP,GAAe;gBACX,MAAA,EAAER,MAAAA,CAAAA,EAAMC,QAAAA,CAAAA,EAAAA,GAAWI,EAAQW,cAAAA;gBACjCR,EAAcO,GAAG;oBAAEG,MAAM;oBAAkBlB,MAAAA;oBAAMC,QAAAA;gBAAAA,CAAQ;YAAA;QAC3D;IAGL,CAAA,CACH,CAEJ,CACF;AAGJG,EAAYrB,WAAAA,GAAc;AAQnB,MAAMoC,IAA8BA,CAAC,EAC1CC,UAAAA,CAAAA,EACAZ,eAAAA,CAAAA,EACAC,cAAAA,CAAAA,EACF,KAAM;IACE,MAAA,EAAEY,OAAAA,CAAAA,EAAOC,MAAAA,CAAAA,EAAMhB,WAAAA,CAAAA,EAAAA,kUAAciB,EAAS;QAC1CC,KAAKJ;QACLK,MAAMjD;IAAAA,CACP;IAMD,4TAJAK,UAAAA,CAAM6C,SAAAA,CAAU,MAAM;QAChBJ,KAAAA,EAAWK,UAAAA,CAAW;IAAA,GACzB;QAACL,CAAI;KAAC,GAEL,CAACD,KAAS,CAACC,IAAa,OAGzBzC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAuB,GAAA;QACC,SAASkB,EAAKjB,OAAAA;QACd,WAAAC;QACA,WAAW,IAAMgB,EAAKM,cAAAA,CAAe;QACrC,eAAe,CAACb,GAAGc,MAAW;YAC5B,MAAMC,IAAW;gBAAE,GAAGD,CAAAA;gBAAQJ,MAAM;YAAe;YACnDH,OAAAA,EAAKS,gBAAAA,CAAiB;gBAAED,UAAAA;YAAAA,CAAU,GAE3BtB,IACHA,EAAcO,GAAG;gBAAEc,QAAAA;gBAAQP,MAAAA;gBAAMD,OAAAA;YAAAA,CAAO,kVACxCW,EAA4BH,EAAO5B,MAAM;QAAA;QAE/C,cAAc,CAACc,GAAGJ,MAAU;YAC1B,MAAMmB,IAAW;gBAAE,GAAGnB,CAAAA;gBAAOc,MAAM;YAAc;YAGjD,IAFAH,EAAKS,gBAAAA,CAAiB;gBAAED,UAAAA;YAAAA,CAAU,GAE9BrB,GACF,OAAOA,EAAaM,GAAG;gBAAEJ,OAAAA;gBAAOW,MAAAA;gBAAMD,OAAAA;YAAAA,CAAO;QAC/C;IAAA,CAEF;AAEN;AACAF,EAAMpC,WAAAA,GAAc;AAEb,MAAMkD,IAAY,CAAA;AAczBC,OAAOC,MAAAA,CAAOF,GAAW;IACvBG,SAAShC;IACT3B,MAAAA;IACAO,SAAAA;IACAK,SAAAA;IACAE,OAAAA;IACAE,MAAAA;IACAG,KAAAA;IACAE,SAAAA;IACAC,eAAAA;IACAG,iBAAAA;IACAC,OAAAA;AACF,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1485, "column": 0}, "map": {"version": 3, "file": "utils.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/core/utils.ts"], "sourcesContent": ["export const openPopupWindow = (url: string) => {\n  const width = 600;\n  const height = 800;\n  // Calculate the position to center the window\n  const screenLeft = window.screenLeft ?? window.screenX;\n  const screenTop = window.screenTop ?? window.screenY;\n\n  const innerWidth =\n    window.innerWidth ?? document.documentElement.clientWidth ?? screen.width;\n  const innerHeight =\n    window.innerHeight ??\n    document.documentElement.clientHeight ??\n    screen.height;\n\n  const left = innerWidth / 2 - width / 2 + screenLeft;\n  const top = innerHeight / 2 - height / 2 + screenTop;\n\n  // Window features\n  const features = `width=${width},height=${height},top=${top},left=${left}`;\n\n  window.open(url, \"_blank\", features);\n};\n"], "names": ["openPopupWindow", "url", "screenLeft", "window", "screenX", "screenTop", "screenY", "innerWidth", "document", "documentElement", "clientWidth", "screen", "width", "innerHeight", "clientHeight", "height", "left", "features", "open"], "mappings": ";;;AAAaA,MAAAA,IAAkBA,CAACC,MAAgB;IAIxCC,MAAAA,IAAaC,OAAOD,UAAAA,IAAcC,OAAOC,OAAAA,EACzCC,IAAYF,OAAOE,SAAAA,IAAaF,OAAOG,OAAAA,EAEvCC,IACJJ,OAAOI,UAAAA,IAAcC,SAASC,eAAAA,CAAgBC,WAAAA,IAAeC,OAAOC,KAAAA,EAChEC,IACJV,OAAOU,WAAAA,IACPL,SAASC,eAAAA,CAAgBK,YAAAA,IACzBH,OAAOI,MAAAA,EAEHC,IAAOT,IAAa,IAAIK,MAAQ,IAAIV,GAIpCe,IAAW,CAAA,yBAAA,EAHLJ,IAAc,IAAIE,MAAS,IAAIV,CAGgB,CAAA,MAAA,EAASW,CAAI,EAAA;IAEjEE,OAAAA,IAAAA,CAAKjB,GAAK,UAAUgB,CAAQ;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1500, "column": 0}, "map": {"version": 3, "file": "MsTeamsIcon.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/ms-teams/components/MsTeamsIcon/MsTeamsIcon.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\nexport interface MsTeamsIconProps {\n  height: string;\n  width: string;\n}\n\nexport const MsTeamsIcon: FunctionComponent<MsTeamsIconProps> = ({\n  height,\n  width,\n}) => {\n  return (\n    // Source: https://commons.wikimedia.org/wiki/File:Microsoft_Office_Teams_(2018%E2%80%93present).svg\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      style={{ height, width }}\n      viewBox=\"0 0 2228.833 2073.333\"\n    >\n      <path\n        fill=\"#5059C9\"\n        d=\"M1554.637,777.5h575.713c54.391,0,98.483,44.092,98.483,98.483c0,0,0,0,0,0v524.398 c0,199.901-162.051,361.952-361.952,361.952h0h-1.711c-199.901,0.028-361.975-162-362.004-361.901c0-0.017,0-0.034,0-0.052V828.971 C1503.167,800.544,1526.211,777.5,1554.637,777.5L1554.637,777.5z\"\n      />\n      <circle fill=\"#5059C9\" cx=\"1943.75\" cy=\"440.583\" r=\"233.25\" />\n      <circle fill=\"#7B83EB\" cx=\"1218.083\" cy=\"336.917\" r=\"336.917\" />\n      <path\n        fill=\"#7B83EB\"\n        d=\"M1667.323,777.5H717.01c-53.743,1.33-96.257,45.931-95.01,99.676v598.105 c-7.505,322.519,247.657,590.16,570.167,598.053c322.51-7.893,577.671-275.534,570.167-598.053V877.176 C1763.579,823.431,1721.066,778.83,1667.323,777.5z\"\n      />\n      <path\n        opacity=\".1\"\n        d=\"M1244,777.5v838.145c-0.258,38.435-23.549,72.964-59.09,87.598 c-11.316,4.787-23.478,7.254-35.765,7.257H667.613c-6.738-17.105-12.958-34.21-18.142-51.833 c-18.144-59.477-27.402-121.307-27.472-183.49V877.02c-1.246-53.659,41.198-98.19,94.855-99.52H1244z\"\n      />\n      <path\n        opacity=\".2\"\n        d=\"M1192.167,777.5v889.978c-0.002,12.287-2.47,24.449-7.257,35.765 c-14.634,35.541-49.163,58.833-87.598,59.09H691.975c-8.812-17.105-17.105-34.21-24.362-51.833 c-7.257-17.623-12.958-34.21-18.142-51.833c-18.144-59.476-27.402-121.307-27.472-183.49V877.02 c-1.246-53.659,41.198-98.19,94.855-99.52H1192.167z\"\n      />\n      <path\n        opacity=\".2\"\n        d=\"M1192.167,777.5v786.312c-0.395,52.223-42.632,94.46-94.855,94.855h-447.84 c-18.144-59.476-27.402-121.307-27.472-183.49V877.02c-1.246-53.659,41.198-98.19,94.855-99.52H1192.167z\"\n      />\n      <path\n        opacity=\".2\"\n        d=\"M1140.333,777.5v786.312c-0.395,52.223-42.632,94.46-94.855,94.855H649.472 c-18.144-59.476-27.402-121.307-27.472-183.49V877.02c-1.246-53.659,41.198-98.19,94.855-99.52H1140.333z\"\n      />\n      <path\n        opacity=\".1\"\n        d=\"M1244,509.522v163.275c-8.812,0.518-17.105,1.037-25.917,1.037 c-8.812,0-17.105-0.518-25.917-1.037c-17.496-1.161-34.848-3.937-51.833-8.293c-104.963-24.857-191.679-98.469-233.25-198.003 c-7.153-16.715-12.706-34.071-16.587-51.833h258.648C1201.449,414.866,1243.801,457.217,1244,509.522z\"\n      />\n      <path\n        opacity=\".2\"\n        d=\"M1192.167,561.355v111.442c-17.496-1.161-34.848-3.937-51.833-8.293 c-104.963-24.857-191.679-98.469-233.25-198.003h190.228C1149.616,466.699,1191.968,509.051,1192.167,561.355z\"\n      />\n      <path\n        opacity=\".2\"\n        d=\"M1192.167,561.355v111.442c-17.496-1.161-34.848-3.937-51.833-8.293 c-104.963-24.857-191.679-98.469-233.25-198.003h190.228C1149.616,466.699,1191.968,509.051,1192.167,561.355z\"\n      />\n      <path\n        opacity=\".2\"\n        d=\"M1140.333,561.355v103.148c-104.963-24.857-191.679-98.469-233.25-198.003 h138.395C1097.783,466.699,1140.134,509.051,1140.333,561.355z\"\n      />\n      <linearGradient\n        id=\"a\"\n        gradientUnits=\"userSpaceOnUse\"\n        x1=\"198.099\"\n        y1=\"1683.0726\"\n        x2=\"942.2344\"\n        y2=\"394.2607\"\n        gradientTransform=\"matrix(1 0 0 -1 0 2075.3333)\"\n      >\n        <stop offset=\"0\" stopColor=\"#5a62c3\" />\n        <stop offset=\".5\" stopColor=\"#4d55bd\" />\n        <stop offset=\"1\" stopColor=\"#3940ab\" />\n      </linearGradient>\n      <path\n        fill=\"url(#a)\"\n        d=\"M95.01,466.5h950.312c52.473,0,95.01,42.538,95.01,95.01v950.312c0,52.473-42.538,95.01-95.01,95.01 H95.01c-52.473,0-95.01-42.538-95.01-95.01V561.51C0,509.038,42.538,466.5,95.01,466.5z\"\n      />\n      <path\n        fill=\"#FFF\"\n        d=\"M820.211,828.193H630.241v517.297H509.211V828.193H320.123V727.844h500.088V828.193z\"\n      />\n    </svg>\n  );\n};\n"], "names": ["MsTeamsIcon", "height", "width", "React"], "mappings": ";;;;;AAOO,MAAMA,IAAmDA,CAAC,EAC/DC,QAAAA,CAAAA,EACAC,OAAAA,CAAAA,EACF,GAAA,oGAAA;IAGKC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QACC,OAAM;QACN,OAAO;YAAEF,QAAAA;YAAQC,OAAAA;QAAAA;QACjB,SAAQ;IAER,GAAAC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QAAA;QACC,MAAK;QACL,GAAE;IAAA,CAAiR,GAErRA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,UAAO;QAAA,MAAK;QAAU,IAAG;QAAU,IAAG;QAAU,GAAE;IAAA,CAAQ,GAC3DA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,UAAA;QAAO,MAAK;QAAU,IAAG;QAAW,IAAG;QAAU,GAAE;IAAS,CAAA,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CAC5D,QACC;QAAA,MAAK;QACL,GAAE;IAAA,CAA8N,GAEjOA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,QAAA;QACC,SAAQ;QACR,GAAE;IAAA,CAA0P,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CAE7P,QACC;QAAA,SAAQ;QACR,GAAE;IAA4S,CAAA,GAEhTA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QACC;QAAA,SAAQ;QACR,GAAE;IAAgL,CAAA,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CAEnL,QACC;QAAA,SAAQ;QACR,GAAE;IAAA,CAAgL,GAEpLA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QACC;QAAA,SAAQ;QACR,GAAE;IAAA,CAA2R,GAE/RA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QACC;QAAA,SAAQ;QACR,GAAE;IAA8K,CAAA,GAElLA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QACC;QAAA,SAAQ;QACR,GAAE;IAA8K,CAAA,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CAEjL,QACC;QAAA,SAAQ;QACR,GAAE;IAAsI,CAAA,GAEzIA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,kBAAA;QACC,IAAG;QACH,eAAc;QACd,IAAG;QACH,IAAG;QACH,IAAG;QACH,IAAG;QACH,mBAAkB;IAElB,GAAAA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QAAK;QAAA,QAAO;QAAI,WAAU;IAAA,CAAS,GACpCA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QAAK;QAAA,QAAO;QAAK,WAAU;IAAS,CAAA,GACrCA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QAAA;QAAK,QAAO;QAAI,WAAU;IAAS,CAAA,CACtC,GACCA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,QAAA;QACC,MAAK;QACL,GAAE;IAAA,CAAuL,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CAE1L,QACC;QAAA,MAAK;QACL,GAAE;IAAA,CAAmF,CAEzF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1585, "column": 0}, "map": {"version": 3, "file": "MsTeamsAuthButton.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/ms-teams/components/MsTeamsAuthButton/MsTeamsAuthButton.tsx"], "sourcesContent": ["import {\n  useKnockClient,\n  useKnockMsTeamsClient,\n  useMsTeamsAuth,\n  useTranslations,\n} from \"@knocklabs/react-core\";\nimport { FunctionComponent, useEffect } from \"react\";\n\nimport { openPopupWindow } from \"../../../core/utils\";\nimport \"../../theme.css\";\nimport { MsTeamsIcon } from \"../MsTeamsIcon\";\n\nimport \"./styles.css\";\n\nexport interface MsTeamsAuthButtonProps {\n  msTeamsBotId: string;\n  redirectUrl?: string;\n  onAuthenticationComplete?: (authenticationResp: string) => void;\n}\n\nexport const MsTeamsAuthButton: FunctionComponent<MsTeamsAuthButtonProps> = ({\n  msTeamsBotId,\n  redirectUrl,\n  onAuthenticationComplete,\n}) => {\n  const { t } = useTranslations();\n  const knock = useKnockClient();\n\n  const {\n    setConnectionStatus,\n    connectionStatus,\n    setActionLabel,\n    actionLabel,\n    errorLabel,\n  } = useKnockMsTeamsClient();\n\n  const { buildMsTeamsAuthUrl, disconnectFromMsTeams } = useMsTeamsAuth(\n    msTeamsBotId,\n    redirectUrl,\n  );\n\n  useEffect(() => {\n    const receiveMessage = (event: MessageEvent) => {\n      if (event.origin !== knock.host) {\n        return;\n      }\n\n      try {\n        if (event.data === \"authComplete\") {\n          setConnectionStatus(\"connected\");\n        }\n\n        if (event.data === \"authFailed\") {\n          setConnectionStatus(\"error\");\n        }\n\n        onAuthenticationComplete?.(event.data);\n      } catch (_error) {\n        setConnectionStatus(\"error\");\n      }\n    };\n\n    window.addEventListener(\"message\", receiveMessage, false);\n\n    // Cleanup the event listener when the component unmounts\n    return () => {\n      window.removeEventListener(\"message\", receiveMessage);\n    };\n  }, [knock.host, onAuthenticationComplete, setConnectionStatus]);\n\n  const disconnectLabel = t(\"msTeamsDisconnect\") || null;\n  const reconnectLabel = t(\"msTeamsReconnect\") || null;\n\n  // Loading states\n  if (\n    connectionStatus === \"connecting\" ||\n    connectionStatus === \"disconnecting\"\n  ) {\n    return (\n      <div className=\"rtk-connect__button rtk-connect__button--loading\">\n        <MsTeamsIcon height=\"16px\" width=\"16px\" />\n        <span>\n          {connectionStatus === \"connecting\"\n            ? t(\"msTeamsConnecting\")\n            : t(\"msTeamsDisconnecting\")}\n        </span>\n      </div>\n    );\n  }\n\n  // Error state\n  if (connectionStatus === \"error\") {\n    return (\n      <button\n        onClick={() => openPopupWindow(buildMsTeamsAuthUrl())}\n        className=\"rtk-connect__button rtk-connect__button--error\"\n        onMouseEnter={() => setActionLabel(reconnectLabel)}\n        onMouseLeave={() => setActionLabel(null)}\n      >\n        <MsTeamsIcon height=\"16px\" width=\"16px\" />\n        <span className=\"rtk-connect__button__text--error\">\n          {actionLabel || errorLabel || t(\"msTeamsError\")}\n        </span>\n      </button>\n    );\n  }\n\n  // Disconnected state\n  if (connectionStatus === \"disconnected\") {\n    return (\n      <button\n        onClick={() => openPopupWindow(buildMsTeamsAuthUrl())}\n        className=\"rtk-connect__button rtk-connect__button--disconnected\"\n      >\n        <MsTeamsIcon height=\"16px\" width=\"16px\" />\n        <span>{t(\"msTeamsConnect\")}</span>\n      </button>\n    );\n  }\n\n  // Connected state\n  return (\n    <button\n      onClick={disconnectFromMsTeams}\n      className=\"rtk-connect__button rtk-connect__button--connected\"\n      onMouseEnter={() => setActionLabel(disconnectLabel)}\n      onMouseLeave={() => setActionLabel(null)}\n    >\n      <MsTeamsIcon height=\"16px\" width=\"16px\" />\n      <span className=\"rtk-connect__button__text--connected\">\n        {actionLabel || t(\"msTeamsConnected\")}\n      </span>\n    </button>\n  );\n};\n"], "names": ["MsTeamsAuthButton", "msTeamsBotId", "redirectUrl", "onAuthenticationComplete", "t", "useTranslations", "knock", "useKnockClient", "setConnectionStatus", "connectionStatus", "setActionLabel", "actionLabel", "error<PERSON><PERSON><PERSON>", "useKnockMsTeamsClient", "buildMsTeamsAuthUrl", "disconnectFromMsTeams", "useMsTeamsAuth", "useEffect", "receiveMessage", "event", "origin", "host", "data", "addEventListener", "removeEventListener", "disconnectLabel", "reconnectLabel", "React", "MsTeamsIcon", "openPopupWindow"], "mappings": ";;;;;;;;;;;;;;;2BAoBO,MAAMA,IAA+DA,CAAC,EAC3EC,cAAAA,CAAAA,EACAC,aAAAA,CAAAA,EACAC,0BAAAA,CAAAA,EACF,KAAM;IACE,MAAA,EAAEC,CAAAA,EAAAA,4TAAMC,mBAAAA,CAAgB,IACxBC,+UAAQC,CAAe,IAEvB,EACJC,qBAAAA,CAAAA,EACAC,kBAAAA,CAAAA,EACAC,gBAAAA,CAAAA,EACAC,aAAAA,CAAAA,EACAC,YAAAA,CAAAA,EAAAA,OACEC,4VAAAA,CAAsB,IAEpB,EAAEC,qBAAAA,CAAAA,EAAqBC,uBAAAA,CAAAA,EAAAA,iYAA0BC,EACrDf,GACAC,CACF;yUAEAe,EAAU,MAAM;QACRC,MAAAA,IAAiBA,CAACC,MAAwB;YAC1CA,IAAAA,EAAMC,MAAAA,KAAWd,EAAMe,IAAAA,EAIvB,IAAA;gBACEF,EAAMG,IAAAA,KAAS,kBACjBd,EAAoB,WAAW,GAG7BW,EAAMG,IAAAA,KAAS,gBACjBd,EAAoB,OAAO,GAG7BL,KAAAA,QAAAA,EAA2BgB,EAAMG,IAAAA;YAAAA,EAAAA,OAClB;gBACfd,EAAoB,OAAO;YAAA;QAE/B;QAEOe,OAAAA,OAAAA,gBAAAA,CAAiB,WAAWL,GAAgB,CAAA,CAAK,GAGjD,MAAM;YACJM,OAAAA,mBAAAA,CAAoB,WAAWN,CAAc;QACtD;IAAA,GACC;QAACZ,EAAMe,IAAAA;QAAMlB;QAA0BK,CAAmB;KAAC;IAExDiB,MAAAA,IAAkBrB,EAAE,mBAAmB,KAAK,MAC5CsB,IAAiBtB,EAAE,kBAAkB,KAAK;IAI9CK,OAAAA,MAAqB,gBACrBA,MAAqB,kBAGnBkB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,WAAU;IAAA,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,sUACZC,cAAAA,EAAY;QAAA,QAAO;QAAO,OAAM;IAAM,CAAA,GACtCD,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,QAAA,MAEKvB,EADHK,MAAqB,eAChB,sBACA,sBADmB,CAE3B,CACF,IAKAA,MAAqB,UAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CAEpB,UACC;QAAA,SAAS,qTAAMoB,EAAgBf,GAAqB;QACpD,WAAU;QACV,cAAc,IAAMJ,EAAegB,CAAc;QACjD,cAAc,IAAMhB,EAAe,IAAI;IAAA,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,sUAEtCkB,cAAAA,EAAY;QAAA,QAAO;QAAO,OAAM;IAAA,CAAM,GACvCD,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QAAK;QAAA,WAAU;IAAA,GACbhB,KAAeC,KAAcR,EAAE,cAAc,CAChD,CACF,IAKAK,MAAqB,iBAErBkB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,UAAA;QACC,SAAS,qTAAME,EAAgBf,EAAoB,CAAC;QACpD,WAAU;IAEV,GAAAa,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,qUAACC,eAAAA,EAAAA;QAAY,QAAO;QAAO,OAAM;IAAM,CAAA,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CACtC,QAAMxB,MAAAA,EAAE,gBAAgB,CAAE,CAC7B,IAMDuB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,UAAA;QACC,SAASZ;QACT,WAAU;QACV,cAAc,IAAML,EAAee,CAAe;QAClD,cAAc,IAAMf,EAAe,IAAI;IAAA,GAEvCiB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,sUAACC,cAAAA,EAAY;QAAA,QAAO;QAAO,OAAM;IAAM,CAAA,GACvCD,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QAAK;QAAA,WAAU;IACbhB,GAAAA,KAAeP,EAAE,kBAAkB,CACtC,CACF;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1660, "column": 0}, "map": {"version": 3, "file": "MsTeamsAuthContainer.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/ms-teams/components/MsTeamsAuthContainer/MsTeamsAuthContainer.tsx"], "sourcesContent": ["import { useTranslations } from \"@knocklabs/react-core\";\nimport { FunctionComponent } from \"react\";\n\nimport \"../../theme.css\";\nimport { MsTeamsIcon } from \"../MsTeamsIcon\";\n\nimport \"./styles.css\";\n\nexport interface MsTeamsAuthContainerProps {\n  actionButton: React.ReactElement;\n}\n\nexport const MsTeamsAuthContainer: FunctionComponent<\n  MsTeamsAuthContainerProps\n> = ({ actionButton }) => {\n  const { t } = useTranslations();\n\n  return (\n    <div className=\"rtk-auth\">\n      <div className=\"rtk-auth__header\">\n        <MsTeamsIcon height=\"32px\" width=\"32px\" />\n        <div>{actionButton}</div>\n      </div>\n      <div className=\"rtk-auth__title\">Microsoft Teams</div>\n      <div className=\"rtk-auth__description\">\n        {t(\"msTeamsConnectContainerDescription\")}\n      </div>\n    </div>\n  );\n};\n"], "names": ["MsTeamsAuthContainer", "actionButton", "t", "useTranslations", "React", "MsTeamsIcon"], "mappings": ";;;;;;;;;;2BAYO,MAAMA,IAETA,CAAC,EAAEC,cAAAA,CAAAA,EAAa,KAAM;IAClB,MAAA,EAAEC,GAAAA,CAAAA,EAAAA,+UAAMC,CAAgB;IAE9B,OACGC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QAAI,WAAU;IAAA,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CACZ,OAAI;QAAA,WAAU;IACb,GAAAA,aAAAA,uTAAAA,WAAAA,CAAA,aAAA,sUAACC,cAAAA,EAAY;QAAA,QAAO;QAAO,OAAM;IAAA,CAAM,GACvCD,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAKH,MAAAA,CAAa,CACrB,GACCG,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QAAI,WAAU;IAAkB,GAAA,iBAAe,GAChDA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,WAAU;IAAA,GACZF,EAAE,oCAAoC,CACzC,CACF;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1693, "column": 0}, "map": {"version": 3, "file": "utils.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/ms-teams/utils.ts"], "sourcesContent": ["export const sortByDisplayName = <T extends { displayName: string }>(\n  items: T[],\n) =>\n  items.sort((a, b) =>\n    a.displayName.toLowerCase().localeCompare(b.displayName.toLowerCase()),\n  );\n"], "names": ["sortByDisplayName", "items", "sort", "a", "b", "displayName", "toLowerCase", "localeCompare"], "mappings": ";;;AAAO,MAAMA,IAAoB,CAC/BC,IAEAA,EAAMC,IAAAA,CAAK,CAACC,GAAGC,IACbD,EAAEE,WAAAA,CAAYC,WAAAA,GAAcC,aAAAA,CAAcH,EAAEC,WAAAA,CAAYC,WAAAA,EAAa,CACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1705, "column": 0}, "map": {"version": 3, "file": "MsTeamsErrorMessage.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsErrorMessage.tsx"], "sourcesContent": ["import { Icon, Lucide } from \"@telegraph/icon\";\nimport { Text } from \"@telegraph/typography\";\nimport { FunctionComponent } from \"react\";\n\ninterface Props {\n  message?: string;\n}\n\nconst MsTeamsErrorMessage: FunctionComponent<Props> = ({ message }) => {\n  return (\n    <div className=\"rtk-combobox__error\">\n      <span>\n        <Icon icon={Lucide.Info} color=\"black\" size=\"1\" aria-hidden />\n      </span>\n      <Text as=\"div\" color=\"black\" size=\"1\">\n        {message}\n      </Text>\n    </div>\n  );\n};\n\nexport default MsTeamsErrorMessage;\n"], "names": ["MsTeamsErrorMessage", "message", "React", "Icon", "Lucide", "Info", "Text"], "mappings": ";;;;;;;;;;;AAQA,MAAMA,IAAgDA,CAAC,EAAEC,SAAAA,CAAAA,EAAQ,GAE5DC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QAAI,WAAU;IAAA,GACZA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,QAAA,MACEA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,qRAAAC,OAAAA,EAAA;QAAK,8RAAMC,SAAAA,CAAOC,IAAAA;QAAM,OAAM;QAAQ,MAAK;QAAI,eAAW,CAAA;IAAA,CAAA,CAC7D,GACAH,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,2QAACI,OAAAA,EAAK;QAAA,IAAG;QAAM,OAAM;QAAQ,MAAK;IAC/BL,GAAAA,CACH,CACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1736, "column": 0}, "map": {"version": 3, "file": "MsTeamsChannelInTeamCombobox.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsChannelInTeamCombobox.tsx"], "sourcesContent": ["import { MsTeamsChannelConnection } from \"@knocklabs/client\";\nimport {\n  MsTeamsChannelQueryOptions,\n  RecipientObject,\n  useConnectedMsTeamsChannels,\n  useKnockMsTeamsClient,\n  useMsTeamsChannels,\n} from \"@knocklabs/react-core\";\nimport { Combobox } from \"@telegraph/combobox\";\nimport { Box } from \"@telegraph/layout\";\nimport { FunctionComponent, useCallback, useMemo } from \"react\";\n\nimport { sortByDisplayName } from \"../../utils\";\n\nimport MsTeamsErrorMessage from \"./MsTeamsErrorMessage\";\n\ninterface MsTeamsChannelInTeamComboboxProps {\n  teamId?: string;\n  msTeamsChannelsRecipientObject: RecipientObject;\n  queryOptions?: MsTeamsChannelQueryOptions;\n}\n\nexport const MsTeamsChannelInTeamCombobox: FunctionComponent<\n  MsTeamsChannelInTeamComboboxProps\n> = ({ teamId, msTeamsChannelsRecipientObject, queryOptions }) => {\n  const { connectionStatus } = useKnockMsTeamsClient();\n\n  const { data: availableChannels = [] } = useMsTeamsChannels({\n    teamId,\n    queryOptions,\n  });\n\n  const sortedChannels = useMemo(\n    () => sortByDisplayName(availableChannels),\n    [availableChannels],\n  );\n\n  const {\n    data: currentConnections,\n    updateConnectedChannels,\n    error: connectedChannelsError,\n  } = useConnectedMsTeamsChannels({ msTeamsChannelsRecipientObject });\n\n  const inErrorState = useMemo(\n    () =>\n      connectionStatus === \"disconnected\" ||\n      connectionStatus === \"error\" ||\n      !!connectedChannelsError,\n    [connectionStatus, connectedChannelsError],\n  );\n\n  const inLoadingState = useMemo(\n    () =>\n      connectionStatus === \"connecting\" || connectionStatus === \"disconnecting\",\n    [connectionStatus],\n  );\n\n  const isChannelInThisTeam = useCallback(\n    (channelId: string) =>\n      availableChannels.some((channel) => channel.id === channelId),\n    [availableChannels],\n  );\n\n  const comboboxValue = useMemo(\n    () =>\n      currentConnections\n        ?.filter(\n          (connection) =>\n            connection.ms_teams_channel_id &&\n            isChannelInThisTeam(connection.ms_teams_channel_id),\n        )\n        .map((connection) => connection.ms_teams_channel_id),\n    [currentConnections, isChannelInThisTeam],\n  );\n\n  return (\n    <>\n      <Box w=\"full\" minW=\"0\">\n        <Combobox.Root\n          value={comboboxValue}\n          onValueChange={(channelIds) => {\n            const connectedChannelsInThisTeam =\n              channelIds.map<MsTeamsChannelConnection>((channelId) => ({\n                ms_teams_team_id: teamId,\n                ms_teams_channel_id: channelId,\n              }));\n            const connectedChannelsInOtherTeams =\n              currentConnections?.filter(\n                (connection) =>\n                  !connection.ms_teams_channel_id ||\n                  !isChannelInThisTeam(connection.ms_teams_channel_id),\n              ) ?? [];\n\n            const updatedConnections = [\n              ...connectedChannelsInOtherTeams,\n              ...connectedChannelsInThisTeam,\n            ];\n\n            updateConnectedChannels(updatedConnections).catch(console.error);\n          }}\n          placeholder=\"Select channels\"\n          disabled={\n            teamId === undefined ||\n            inErrorState ||\n            inLoadingState ||\n            availableChannels.length === 0\n          }\n          closeOnSelect={false}\n          layout=\"wrap\"\n          modal={\n            // Modal comboboxes cause page layout to shift when body has padding. See KNO-7854.\n            false\n          }\n        >\n          <Combobox.Trigger />\n          <Combobox.Content>\n            <Combobox.Search className=\"rtk-combobox__search\" />\n            <Combobox.Options maxHeight=\"36\">\n              {sortedChannels.map((channel) => (\n                <Combobox.Option key={channel.id} value={channel.id}>\n                  {channel.displayName}\n                </Combobox.Option>\n              ))}\n            </Combobox.Options>\n            <Combobox.Empty />\n          </Combobox.Content>\n        </Combobox.Root>\n      </Box>\n      {!!connectedChannelsError && (\n        <MsTeamsErrorMessage message={connectedChannelsError} />\n      )}\n    </>\n  );\n};\n"], "names": ["MsTeamsChannelInTeamCombobox", "teamId", "msTeamsChannelsRecipientObject", "queryOptions", "connectionStatus", "useKnockMsTeamsClient", "data", "availableChannels", "useMsTeamsChannels", "sortedChannels", "useMemo", "sortByDisplayName", "currentConnections", "updateConnectedChannels", "error", "connectedChannelsError", "useConnectedMsTeamsChannels", "inErrorState", "inLoadingState", "isChannelInThisTeam", "useCallback", "channelId", "some", "channel", "id", "comboboxValue", "filter", "connection", "ms_teams_channel_id", "map", "React", "Box", "Combobox", "channelIds", "connectedChannelsInThisTeam", "ms_teams_team_id", "updatedConnections", "catch", "console", "undefined", "length", "displayName", "MsTeamsErrorMessage"], "mappings": ";;;;;;;;;;;;;;;;;;AAsBO,MAAMA,IAETA,CAAC,EAAEC,QAAAA,CAAAA,EAAQC,gCAAAA,CAAAA,EAAgCC,cAAAA,CAAAA,EAAa,KAAM;IAC1D,MAAA,EAAEC,kBAAAA,CAAAA,EAAAA,mWAAqBC,CAAsB,IAE7C,EAAEC,MAAMC,IAAoB,CAAA,CAAA,EAAA,uXAAOC,sBAAAA,EAAmB;QAC1DP,QAAAA;QACAE,cAAAA;IAAAA,CACD,GAEKM,uUAAiBC,EACrB,8TAAMC,EAAkBJ,CAAiB,GACzC;QAACA,CAAiB;KACpB,GAEM,EACJD,MAAMM,CAAAA,EACNC,yBAAAA,CAAAA,EACAC,OAAOC,CAAAA,EAAAA,waACLC,EAA4B;QAAEd,gCAAAA;IAAAA,CAAgC,GAE5De,uUAAeP,EACnB,IACEN,MAAqB,kBACrBA,MAAqB,WACrB,CAAC,CAACW,GACJ;QAACX;QAAkBW,CAAsB;KAC3C,GAEMG,uUAAiBR,EACrB,IACEN,MAAqB,gBAAgBA,MAAqB,iBAC5D;QAACA,CAAgB;KACnB,GAEMe,KAAsBC,sUAAAA,EAC1B,CAACC,IACCd,EAAkBe,IAAAA,CAAMC,CAAYA,IAAAA,EAAQC,EAAAA,KAAOH,CAAS,GAC9D;QAACd,CAAiB;KACpB,GAEMkB,uUAAgBf,EACpB,IACEE,KAAAA,OAAAA,KAAAA,IAAAA,EACIc,MAAAA,CACCC,CACCA,IAAAA,EAAWC,mBAAAA,IACXT,EAAoBQ,EAAWC,mBAAmB,GAErDC,GAAAA,CAAKF,CAAeA,IAAAA,EAAWC,mBAAAA,GACpC;QAAChB;QAAoBO,CAAmB;KAC1C;IAEA,OAEIW,aAAAA,uTAAAA,WAAAA,CAAA,aAAA,sTAAAA,UAAAA,CAAA,QAAA,EAAA,MAAAA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,uQAACC,MAAAA,EAAI;QAAA,GAAE;QAAO,MAAK;IAAA,GAChBD,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,4QAAAE,WAAAA,CAAS,IAAA,EAAT;QACC,OAAOP;QACP,eAAgBQ,CAAeA,MAAA;YACvBC,MAAAA,IACJD,EAAWJ,GAAAA,CAA+BR,CAAeA,IAAAA,CAAA;oBACvDc,kBAAkBlC;oBAClB2B,qBAAqBP;gBAAAA,CAAAA,CACrB,GAQEe,IAAqB,CACzB;mBAAA,CAPAxB,KAAAA,OAAAA,KAAAA,IAAAA,EAAoBc,MAAAA,CACjBC,CAAAA,IACC,CAACA,EAAWC,mBAAAA,IACZ,CAACT,EAAoBQ,EAAWC,mBAAmB,EAAA,KAClD,CAAE,CAAA,EAIP;mBAAGM,CAA2B;aAAA;YAGhCrB,EAAwBuB,CAAkB,EAAEC,KAAAA,CAAMC,QAAQxB,KAAK;QAAA;QAEjE,aAAY;QACZ,UACEb,MAAWsC,KAAAA,KACXtB,KACAC,KACAX,EAAkBiC,MAAAA,KAAW;QAE/B,eAAe,CAAA;QACf,QAAO;QACP,OAAA,mFAAA;QAEE,CAAA;IAGF,GAAAV,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,4QAACE,WAAAA,CAAS,OAAA,EAAT,IAAgB,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,4QAChBA,WAAAA,CAAS,OAAA,EAAT,MACCF,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,4QAACE,WAAAA,CAAS,MAAA,EAAT;QAAgB,WAAU;IAAsB,CAAA,GACjDF,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,4QAACE,WAAAA,CAAS,OAAA,EAAT;QAAiB,WAAU;IACzBvB,GAAAA,EAAeoB,GAAAA,CAAKN,CAAAA,IAClBO,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,4QAAAE,WAAAA,CAAS,MAAA,EAAT;YAAgB,KAAKT,EAAQC,EAAAA;YAAI,OAAOD,EAAQC,EAAAA;QAC9CD,GAAAA,EAAQkB,WACX,CACD,CACH,GACCX,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,4QAAAE,WAAAA,CAAS,KAAA,EAAT,IAAc,CACjB,CACF,CACF,GACC,CAAC,CAACjB,KACAe,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,yVAAAY,UAAAA,EAAA;QAAoB,SAAS3B;IAC/B,CAAA,CACH;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1813, "column": 0}, "map": {"version": 3, "file": "MsTeamsConnectionError.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsConnectionError.tsx"], "sourcesContent": ["import { useKnockMsTeamsClient, useTranslations } from \"@knocklabs/react-core\";\nimport { FunctionComponent } from \"react\";\n\nimport MsTeamsErrorMessage from \"./MsTeamsErrorMessage\";\n\nconst MsTeamsConnectionError: FunctionComponent = () => {\n  const { t } = useTranslations();\n  const { connectionStatus } = useKnockMsTeamsClient();\n\n  if (connectionStatus === \"disconnected\" || connectionStatus === \"error\") {\n    return (\n      <MsTeamsErrorMessage\n        message={\n          connectionStatus === \"disconnected\"\n            ? t(\"msTeamsConnectionErrorOccurred\")\n            : t(\"msTeamsConnectionErrorExists\")\n        }\n      />\n    );\n  }\n\n  return null;\n};\n\nexport default MsTeamsConnectionError;\n"], "names": ["MsTeamsConnectionError", "t", "useTranslations", "connectionStatus", "useKnockMsTeamsClient", "React", "MsTeamsErrorMessage"], "mappings": ";;;;;;;;;;;AAKA,MAAMA,IAA4CA,MAAM;IAChD,MAAA,EAAEC,GAAAA,CAAAA,EAAAA,+UAAMC,CAAgB,IACxB,EAAEC,kBAAAA,CAAAA,EAAAA,IAAqBC,+VAAAA,CAAsB;IAE/CD,OAAAA,MAAqB,kBAAkBA,MAAqB,UAE5DE,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,yVAACC,UAAAA,EACC;QAAA,SAEML,EADJE,MAAqB,iBACf,mCACA,8BADgC;IAGxC,CAAA,IAIC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1838, "column": 0}, "map": {"version": 3, "file": "MsTeamsTeamCombobox.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsTeamCombobox.tsx"], "sourcesContent": ["import { MsTeamsTeam } from \"@knocklabs/client\";\nimport {\n  MsTeamsTeamQueryOptions,\n  useKnockMsTeamsClient,\n  useMsTeamsTeams,\n} from \"@knocklabs/react-core\";\nimport { Combobox } from \"@telegraph/combobox\";\nimport { Box } from \"@telegraph/layout\";\nimport { FunctionComponent, useMemo } from \"react\";\n\nimport { sortByDisplayName } from \"../../utils\";\n\ninterface MsTeamsTeamComboboxProps {\n  team: MsTeamsTeam | null;\n  onTeamChange: (team: MsTeamsTeam) => void;\n  getChannelCount: (teamId: string) => number;\n  queryOptions?: MsTeamsTeamQueryOptions;\n}\n\nexport const MsTeamsTeamCombobox: FunctionComponent<\n  MsTeamsTeamComboboxProps\n> = ({ team, onTeamChange, getChannelCount, queryOptions }) => {\n  const { connectionStatus } = useKnockMsTeamsClient();\n\n  const { data: teams, isLoading: isLoadingTeams } = useMsTeamsTeams({\n    queryOptions,\n  });\n\n  const sortedTeams = useMemo(() => sortByDisplayName(teams), [teams]);\n\n  const inErrorState = useMemo(\n    () => connectionStatus === \"disconnected\" || connectionStatus === \"error\",\n    [connectionStatus],\n  );\n\n  const inLoadingState = useMemo(\n    () =>\n      connectionStatus === \"connecting\" ||\n      connectionStatus === \"disconnecting\" ||\n      isLoadingTeams,\n    [connectionStatus, isLoadingTeams],\n  );\n\n  return (\n    <Box w=\"full\" minW=\"0\">\n      <Combobox.Root\n        value={team?.id}\n        onValueChange={(teamId) => {\n          const selectedTeam = sortedTeams.find((team) => team.id === teamId);\n          if (selectedTeam) {\n            onTeamChange(selectedTeam);\n          }\n        }}\n        placeholder=\"Select team\"\n        disabled={inErrorState || inLoadingState || sortedTeams.length === 0}\n        modal={\n          // Modal comboboxes cause page layout to shift when body has padding. See KNO-7854.\n          false\n        }\n      >\n        <Combobox.Trigger />\n        <Combobox.Content>\n          <Combobox.Search className=\"rtk-combobox__search\" />\n          <Combobox.Options maxHeight=\"36\">\n            {sortedTeams.map((team) => {\n              const channelCount = getChannelCount(team.id);\n              return (\n                <Combobox.Option key={team.id} value={team.id}>\n                  {channelCount > 0\n                    ? `${team.displayName} (${channelCount})`\n                    : team.displayName}\n                </Combobox.Option>\n              );\n            })}\n          </Combobox.Options>\n          <Combobox.Empty />\n        </Combobox.Content>\n      </Combobox.Root>\n    </Box>\n  );\n};\n"], "names": ["MsTeamsTeamCombobox", "team", "onTeamChange", "getChannelCount", "queryOptions", "connectionStatus", "useKnockMsTeamsClient", "data", "teams", "isLoading", "isLoadingTeams", "useMsTeamsTeams", "sortedTeams", "useMemo", "sortByDisplayName", "inErrorState", "inLoadingState", "React", "Box", "Combobox", "id", "teamId", "selectedTeam", "find", "length", "map", "channelCount", "displayName"], "mappings": ";;;;;;;;;;;;;;;AAmBO,MAAMA,IAETA,CAAC,EAAEC,MAAAA,CAAAA,EAAMC,cAAAA,CAAAA,EAAcC,iBAAAA,CAAAA,EAAiBC,cAAAA,CAAAA,EAAa,KAAM;IACvD,MAAA,EAAEC,kBAAAA,CAAAA,EAAAA,GAAqBC,gWAAAA,CAAsB,IAE7C,EAAEC,MAAMC,CAAAA,EAAOC,WAAWC,CAAAA,EAAAA,oYAAmBC,EAAgB;QACjEP,cAAAA;IAAAA,CACD,GAEKQ,uUAAcC,EAAQ,8TAAMC,EAAkBN,CAAK,GAAG;QAACA,CAAK;KAAC,GAE7DO,uUAAeF,EACnB,IAAMR,MAAqB,kBAAkBA,MAAqB,SAClE;QAACA,CAAgB;KACnB,GAEMW,KAAiBH,kUAAAA,EACrB,IACER,MAAqB,gBACrBA,MAAqB,mBACrBK,GACF;QAACL;QAAkBK,CAAc;KACnC;IAEA,OACGO,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,uQAAAC,MAAAA,EAAA;QAAI,GAAE;QAAO,MAAK;IAAA,GACjBD,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,2QAACE,YAAAA,CAAS,IAAA,EAAT;QACC,OAAOlB,KAAAA,OAAAA,KAAAA,IAAAA,EAAMmB,EAAAA;QACb,eAAgBC,CAAWA,MAAA;YACzB,MAAMC,IAAeV,EAAYW,IAAAA,CAAMtB,CAAAA,IAASA,EAAKmB,EAAAA,KAAOC,CAAM;YAC9DC,KACFpB,EAAaoB,CAAY;QAC3B;QAEF,aAAY;QACZ,UAAUP,KAAgBC,KAAkBJ,EAAYY,MAAAA,KAAW;QACnE,OAAA,mFAAA;QAEE,CAAA;IAGF,GAAAP,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,4QAACE,WAAAA,CAAS,OAAA,EAAT,IAAgB,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,4QAChBA,WAAAA,CAAS,OAAA,EAAT,MACCF,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAACE,sRAAAA,CAAS,MAAA,EAAT;QAAgB,WAAU;IAAA,CAAsB,GACjDF,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAACE,sRAAAA,CAAS,OAAA,EAAT;QAAiB,WAAU;IACzBP,GAAAA,EAAYa,GAAAA,CAAKxB,CAAAA,MAAS;QACnByB,MAAAA,IAAevB,EAAgBF,EAAKmB,EAAE;QAC5C,OAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,4QACGD,WAAAA,CAAS,MAAA,EAAT;YAAgB,KAAKlB,EAAKmB,EAAAA;YAAI,OAAOnB,EAAKmB,EAAAA;QAAAA,GACxCM,IAAe,IACZ,GAAGzB,EAAK0B,WAAW,CAAA,EAAA,EAAKD,CAAY,CAAA,CAAA,CAAA,GACpCzB,EAAK0B,WACX;IAAA,CAEH,CACH,GACAV,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,4QAACE,WAAAA,CAAS,KAAA,EAAT,IAAc,CACjB,CACF,CACF;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1897, "column": 0}, "map": {"version": 3, "file": "MsTeamsChannelCombobox.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsChannelCombobox.tsx"], "sourcesContent": ["import { MsTea<PERSON>Team } from \"@knocklabs/client\";\nimport {\n  MsTeamsChannelQueryOptions,\n  MsTeamsTeamQueryOptions,\n  RecipientObject,\n  useConnectedMsTeamsChannels,\n} from \"@knocklabs/react-core\";\nimport { Icon, Lucide } from \"@telegraph/icon\";\nimport { Stack } from \"@telegraph/layout\";\nimport { Text } from \"@telegraph/typography\";\nimport { FunctionComponent, useCallback, useState } from \"react\";\n\nimport \"../../theme.css\";\n\nimport { MsTeamsChannelInTeamCombobox } from \"./MsTeamsChannelInTeamCombobox\";\nimport MsTeamsConnectionError from \"./MsTeamsConnectionError\";\nimport { MsTeamsTeamCombobox } from \"./MsTeamsTeamCombobox\";\nimport \"./styles.css\";\n\ninterface Props {\n  msTeamsChannelsRecipientObject: RecipientObject;\n  teamQueryOptions?: MsTeamsTeamQueryOptions;\n  channelQueryOptions?: MsTeamsChannelQueryOptions;\n}\n\nconst MsTeamsChannelCombobox: FunctionComponent<Props> = ({\n  msTeamsChannelsRecipientObject,\n  teamQueryOptions,\n  channelQueryOptions,\n}) => {\n  const [selectedTeam, setSelectedTeam] = useState<MsTeamsTeam | null>(null);\n\n  const { data: currentConnections } = useConnectedMsTeamsChannels({\n    msTeamsChannelsRecipientObject,\n  });\n\n  const getChannelCount = useCallback(\n    (teamId: string) =>\n      currentConnections?.filter(\n        (connection) =>\n          connection.ms_teams_team_id === teamId &&\n          !!connection.ms_teams_channel_id,\n      ).length ?? 0,\n    [currentConnections],\n  );\n\n  return (\n    <Stack className=\"tgph rtk-combobox__grid\" gap=\"3\">\n      <Text color=\"gray\" size=\"2\" as=\"div\">\n        Team\n      </Text>\n      <MsTeamsTeamCombobox\n        team={selectedTeam}\n        onTeamChange={setSelectedTeam}\n        getChannelCount={getChannelCount}\n        queryOptions={teamQueryOptions}\n      />\n      <Stack\n        alignItems=\"center\"\n        gap=\"3\"\n        minHeight=\"8\"\n        style={{ alignSelf: \"start\" }}\n      >\n        <Icon color=\"gray\" size=\"1\" icon={Lucide.CornerDownRight} aria-hidden />\n        <Text color=\"gray\" size=\"2\" as=\"div\">\n          Channels\n        </Text>\n      </Stack>\n      <MsTeamsChannelInTeamCombobox\n        teamId={selectedTeam?.id}\n        msTeamsChannelsRecipientObject={msTeamsChannelsRecipientObject}\n        queryOptions={channelQueryOptions}\n      />\n      <MsTeamsConnectionError />\n    </Stack>\n  );\n};\n\nexport default MsTeamsChannelCombobox;\n"], "names": ["MsTeamsChannelCombobox", "msTeamsChannelsRecipientObject", "teamQueryOptions", "channelQueryOptions", "selectedTeam", "setSelectedTeam", "useState", "data", "currentConnections", "useConnectedMsTeamsChannels", "getChannelCount", "useCallback", "teamId", "filter", "connection", "ms_teams_team_id", "ms_teams_channel_id", "length", "React", "<PERSON><PERSON>", "Text", "MsTeamsTeamCombobox", "alignSelf", "Icon", "Lucide", "CornerDownRight", "MsTeamsChannelInTeamCombobox", "id", "MsTeamsConnectionError"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;2BAyBA,MAAMA,IAAmDA,CAAC,EACxDC,gCAAAA,CAAAA,EACAC,kBAAAA,CAAAA,EACAC,qBAAAA,CAAAA,EACF,KAAM;IACJ,MAAM,CAACC,GAAcC,CAAe,CAAA,uUAAIC,EAA6B,IAAI,GAEnE,EAAEC,MAAMC,CAAAA,EAAAA,IAAuBC,oaAAAA,EAA4B;QAC/DR,gCAAAA;IAAAA,CACD,GAEKS,QAAkBC,mUAAAA,EACtB,CAACC,IAAAA,CACCJ,KAAAA,OAAAA,KAAAA,IAAAA,EAAoBK,MAAAA,CACjBC,CAAAA,IACCA,EAAWC,gBAAAA,KAAqBH,KAChC,CAAC,CAACE,EAAWE,mBAAAA,EACfC,MAAAA,KAAU,GACd;QAACT,CAAkB;KACrB;IAEA,OACGU,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAAC,8QAAAA,EAAA;QAAM,WAAU;QAA0B,KAAI;IAC7C,GAAAD,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAACE,iRAAAA,EAAK;QAAA,OAAM;QAAO,MAAK;QAAI,IAAG;IAAA,GAAK,MAEpC,GACAF,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,yVAACG,sBAAAA,EACC;QAAA,MAAMjB;QACN,cAAcC;QACd,iBAAAK;QACA,cAAcR;IAAAA,CAAiB,GAEhCgB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,uQAAAC,QAAAA,EAAA;QACC,YAAW;QACX,KAAI;QACJ,WAAU;QACV,OAAO;YAAEG,WAAW;QAAA;IAAA,GAEpBJ,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,qRAACK,OAAAA,EAAK;QAAA,OAAM;QAAO,MAAK;QAAI,8RAAMC,SAAAA,CAAOC,eAAAA;QAAiB,eAAW,CAAA;IAAA,CAAA,GACrEP,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,2QAACE,OAAAA,EAAAA;QAAK,OAAM;QAAO,MAAK;QAAI,IAAG;IAAK,GAAA,UAEpC,CACF,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,kWACCM,+BAAAA,EACC;QAAA,QAAQtB,KAAAA,OAAAA,KAAAA,IAAAA,EAAcuB,EAAAA;QACtB,gCAAA1B;QACA,cAAcE;IAAoB,CAAA,GAEpCe,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,4VAACU,UAAAA,EAAAA,IAAsB,CACzB;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1967, "column": 0}, "map": {"version": 3, "file": "SlackIcon.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/slack/components/SlackIcon/SlackIcon.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\nexport interface SlackIconProps {\n  height: string;\n  width: string;\n}\n\nexport const SlackIcon: FunctionComponent<SlackIconProps> = ({\n  height,\n  width,\n}) => {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      style={{ height, width }}\n      viewBox=\"0 0 122.8 122.8\"\n    >\n      <path\n        d=\"M25.8 77.6c0 7.1-5.8 12.9-12.9 12.9S0 84.7 0 77.6s5.8-12.9 12.9-12.9h12.9v12.9zm6.5 0c0-7.1 5.8-12.9 12.9-12.9s12.9 5.8 12.9 12.9v32.3c0 7.1-5.8 12.9-12.9 12.9s-12.9-5.8-12.9-12.9V77.6z\"\n        fill=\"#e01e5a\"\n      ></path>\n      <path\n        d=\"M45.2 25.8c-7.1 0-12.9-5.8-12.9-12.9S38.1 0 45.2 0s12.9 5.8 12.9 12.9v12.9H45.2zm0 6.5c7.1 0 12.9 5.8 12.9 12.9s-5.8 12.9-12.9 12.9H12.9C5.8 58.1 0 52.3 0 45.2s5.8-12.9 12.9-12.9h32.3z\"\n        fill=\"#36c5f0\"\n      ></path>\n      <path\n        d=\"M97 45.2c0-7.1 5.8-12.9 12.9-12.9s12.9 5.8 12.9 12.9-5.8 12.9-12.9 12.9H97V45.2zm-6.5 0c0 7.1-5.8 12.9-12.9 12.9s-12.9-5.8-12.9-12.9V12.9C64.7 5.8 70.5 0 77.6 0s12.9 5.8 12.9 12.9v32.3z\"\n        fill=\"#2eb67d\"\n      ></path>\n      <path\n        d=\"M77.6 97c7.1 0 12.9 5.8 12.9 12.9s-5.8 12.9-12.9 12.9-12.9-5.8-12.9-12.9V97h12.9zm0-6.5c-7.1 0-12.9-5.8-12.9-12.9s5.8-12.9 12.9-12.9h32.3c7.1 0 12.9 5.8 12.9 12.9s-5.8 12.9-12.9 12.9H77.6z\"\n        fill=\"#ecb22e\"\n      ></path>\n    </svg>\n  );\n};\n"], "names": ["SlackIcon", "height", "width", "React"], "mappings": ";;;;;AAOO,MAAMA,IAA+CA,CAAC,EAC3DC,QAAAA,CAAAA,EACAC,OAAAA,CAAAA,EACF,GAEKC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QACC,OAAM;QACN,OAAO;YAAEF,QAAAA;YAAQC,OAAAA;QAAAA;QACjB,SAAQ;IAAA,GAERC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QACC;QAAA,GAAE;QACF,MAAK;IACN,CAAA,GACAA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,QAAA;QACC,GAAE;QACF,MAAK;IACN,CAAA,GACAA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,QAAA;QACC,GAAE;QACF,MAAK;IAAA,CACN,GACDA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QACC;QAAA,GAAE;QACF,MAAK;IAAA,CACN,CACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2000, "column": 0}, "map": {"version": 3, "file": "SlackAuthButton.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/slack/components/SlackAuthButton/SlackAuthButton.tsx"], "sourcesContent": ["import {\n  useKnockClient,\n  useKnockSlackClient,\n  useSlackAuth,\n  useTranslations,\n} from \"@knocklabs/react-core\";\nimport { FunctionComponent, useMemo } from \"react\";\nimport { useEffect } from \"react\";\n\nimport { openPopupWindow } from \"../../../core/utils\";\nimport \"../../theme.css\";\nimport { SlackIcon } from \"../SlackIcon\";\n\nimport \"./styles.css\";\n\nexport interface SlackAuthButtonProps {\n  slackClientId: string;\n  redirectUrl?: string;\n  onAuthenticationComplete?: (authenticationResp: string) => void;\n  // When provided, the default scopes will be overridden with the provided scopes\n  scopes?: string[];\n  // Additional scopes to add to the default scopes\n  additionalScopes?: string[];\n}\n\nexport const SlackAuthButton: FunctionComponent<SlackAuthButtonProps> = ({\n  slackClientId,\n  redirectUrl,\n  onAuthenticationComplete,\n  scopes,\n  additionalScopes,\n}) => {\n  const { t } = useTranslations();\n  const knock = useKnockClient();\n\n  const {\n    setConnectionStatus,\n    connectionStatus,\n    setActionLabel,\n    actionLabel,\n    errorLabel,\n  } = useKnockSlackClient();\n\n  const useSlackAuthOptions = useMemo(\n    () => ({\n      scopes,\n      additionalScopes,\n    }),\n    [scopes, additionalScopes],\n  );\n\n  const { buildSlackAuthUrl, disconnectFromSlack } = useSlackAuth(\n    slackClientId,\n    redirectUrl,\n    useSlackAuthOptions,\n  );\n\n  useEffect(() => {\n    const receiveMessage = (event: MessageEvent) => {\n      if (event.origin !== knock.host) {\n        return;\n      }\n\n      try {\n        if (event.data === \"authComplete\") {\n          setConnectionStatus(\"connected\");\n        }\n\n        if (event.data === \"authFailed\") {\n          setConnectionStatus(\"error\");\n        }\n\n        if (onAuthenticationComplete) {\n          onAuthenticationComplete(event.data);\n        }\n      } catch (_error) {\n        setConnectionStatus(\"error\");\n      }\n    };\n\n    window.addEventListener(\"message\", receiveMessage, false);\n\n    // Cleanup the event listener when the component unmounts\n    return () => {\n      window.removeEventListener(\"message\", receiveMessage);\n    };\n  }, [knock.host, onAuthenticationComplete, setConnectionStatus]);\n\n  const disconnectLabel = t(\"slackDisconnect\") || null;\n  const reconnectLabel = t(\"slackReconnect\") || null;\n\n  // Loading states\n  if (\n    connectionStatus === \"connecting\" ||\n    connectionStatus === \"disconnecting\"\n  ) {\n    return (\n      <div className=\"rsk-connect__button rsk-connect__button--loading\">\n        <SlackIcon height=\"16px\" width=\"16px\" />\n        <span>\n          {connectionStatus === \"connecting\"\n            ? t(\"slackConnecting\")\n            : t(\"slackDisconnecting\")}\n        </span>\n      </div>\n    );\n  }\n\n  // Error state\n  if (connectionStatus === \"error\") {\n    return (\n      <button\n        onClick={() => openPopupWindow(buildSlackAuthUrl())}\n        className=\"rsk-connect__button rsk-connect__button--error\"\n        onMouseEnter={() => setActionLabel(reconnectLabel)}\n        onMouseLeave={() => setActionLabel(null)}\n      >\n        <SlackIcon height=\"16px\" width=\"16px\" />\n        <span className=\"rsk-connect__button__text--error\">\n          {actionLabel || errorLabel || t(\"slackError\")}\n        </span>\n      </button>\n    );\n  }\n\n  // Disconnected state\n  if (connectionStatus === \"disconnected\") {\n    return (\n      <button\n        onClick={() => openPopupWindow(buildSlackAuthUrl())}\n        className=\"rsk-connect__button rsk-connect__button--disconnected\"\n      >\n        <SlackIcon height=\"16px\" width=\"16px\" />\n        <span>{t(\"slackConnect\")}</span>\n      </button>\n    );\n  }\n\n  // Connected state\n  return (\n    <button\n      onClick={disconnectFromSlack}\n      className=\"rsk-connect__button rsk-connect__button--connected\"\n      onMouseEnter={() => setActionLabel(disconnectLabel)}\n      onMouseLeave={() => setActionLabel(null)}\n    >\n      <SlackIcon height=\"16px\" width=\"16px\" />\n      <span className=\"rsk-connect__button__text--connected\">\n        {actionLabel || t(\"slackConnected\")}\n      </span>\n    </button>\n  );\n};\n"], "names": ["SlackAuthButton", "slackClientId", "redirectUrl", "onAuthenticationComplete", "scopes", "additionalScopes", "t", "useTranslations", "knock", "useKnockClient", "setConnectionStatus", "connectionStatus", "setActionLabel", "actionLabel", "error<PERSON><PERSON><PERSON>", "useKnockSlackClient", "useSlackAuthOptions", "useMemo", "buildSlackAuthUrl", "disconnectFromSlack", "useSlackAuth", "useEffect", "receiveMessage", "event", "origin", "host", "data", "addEventListener", "removeEventListener", "disconnectLabel", "reconnectLabel", "React", "SlackIcon", "openPopupWindow"], "mappings": ";;;;;;;;;;;;;;;2BAyBO,MAAMA,IAA2DA,CAAC,EACvEC,eAAAA,CAAAA,EACAC,aAAAA,CAAAA,EACAC,0BAAAA,CAAAA,EACAC,QAAAA,CAAAA,EACAC,kBAAAA,CAAAA,EACF,KAAM;IACE,MAAA,EAAEC,CAAAA,EAAAA,4TAAMC,mBAAAA,CAAgB,IACxBC,+UAAQC,CAAe,IAEvB,EACJC,qBAAAA,CAAAA,EACAC,kBAAAA,CAAAA,EACAC,gBAAAA,CAAAA,EACAC,aAAAA,CAAAA,EACAC,YAAAA,CAAAA,EAAAA,yVACEC,CAAoB,IAElBC,IAAsBC,mUAAAA,EAC1B,IAAA,CAAO;YACLb,QAAAA;YACAC,kBAAAA;QAAAA,CAAAA,GAEF;QAACD;QAAQC,CAAgB;KAC3B,GAEM,EAAEa,mBAAAA,CAAAA,EAAmBC,qBAAAA,CAAAA,EAAwBC,qXAAAA,EACjDnB,GACAC,GACAc,CACF;yUAEAK,EAAU,MAAM;QACRC,MAAAA,IAAiBA,CAACC,MAAwB;YAC1CA,IAAAA,EAAMC,MAAAA,KAAWhB,EAAMiB,IAAAA,EAIvB,IAAA;gBACEF,EAAMG,IAAAA,KAAS,kBACjBhB,EAAoB,WAAW,GAG7Ba,EAAMG,IAAAA,KAAS,gBACjBhB,EAAoB,OAAO,GAGzBP,KACFA,EAAyBoB,EAAMG,IAAI;YAAA,EAAA,OAEtB;gBACfhB,EAAoB,OAAO;YAAA;QAE/B;QAEOiB,OAAAA,OAAAA,gBAAAA,CAAiB,WAAWL,GAAgB,CAAA,CAAK,GAGjD,MAAM;YACJM,OAAAA,mBAAAA,CAAoB,WAAWN,CAAc;QACtD;IAAA,GACC;QAACd,EAAMiB,IAAAA;QAAMtB;QAA0BO,CAAmB;KAAC;IAExDmB,MAAAA,IAAkBvB,EAAE,iBAAiB,KAAK,MAC1CwB,IAAiBxB,EAAE,gBAAgB,KAAK;IAI5CK,OAAAA,MAAqB,gBACrBA,MAAqB,kBAGnBoB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,WAAU;IAAA,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,4TACZC,YAAAA,EAAU;QAAA,QAAO;QAAO,OAAM;IAAM,CAAA,GACpCD,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,QAAA,MAEKzB,EADHK,MAAqB,eAChB,oBACA,oBADiB,CAEzB,CACF,IAKAA,MAAqB,UAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CAEpB,UACC;QAAA,SAAS,QAAMsB,6SAAAA,EAAgBf,GAAmB;QAClD,WAAU;QACV,cAAc,IAAMN,EAAekB,CAAc;QACjD,cAAc,IAAMlB,EAAe,IAAI;IAAA,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,4TAEtCoB,YAAAA,EAAU;QAAA,QAAO;QAAO,OAAM;IAAA,CAAM,GACrCD,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QAAK;QAAA,WAAU;IAAA,GACblB,KAAeC,KAAcR,EAAE,YAAY,CAC9C,CACF,IAKAK,MAAqB,iBAErBoB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,UAAA;QACC,SAAS,qTAAME,EAAgBf,EAAkB,CAAC;QAClD,WAAU;IAEV,GAAAa,aAAAA,GAAAA,+TAAAA,CAAA,aAAA,4TAACC,YAAAA,EAAAA;QAAU,QAAO;QAAO,OAAM;IAAM,CAAA,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CACpC,QAAM1B,MAAAA,EAAE,cAAc,CAAE,CAC3B,IAMDyB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,UAAA;QACC,SAASZ;QACT,WAAU;QACV,cAAc,IAAMP,EAAeiB,CAAe;QAClD,cAAc,IAAMjB,EAAe,IAAI;IAAA,GAEvCmB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,4TAACC,YAAAA,EAAU;QAAA,QAAO;QAAO,OAAM;IAAM,CAAA,GACrCD,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,QAAK;QAAA,WAAU;IACblB,GAAAA,KAAeP,EAAE,gBAAgB,CACpC,CACF;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2081, "column": 0}, "map": {"version": 3, "file": "SlackAuthContainer.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/slack/components/SlackAuthContainer/SlackAuthContainer.tsx"], "sourcesContent": ["import { useTranslations } from \"@knocklabs/react-core\";\nimport { FunctionComponent } from \"react\";\n\nimport \"../../theme.css\";\nimport { SlackIcon } from \"../SlackIcon\";\n\nimport \"./styles.css\";\n\nexport interface SlackAuthContainerProps {\n  actionButton: React.ReactElement;\n}\n\nexport const SlackAuthContainer: FunctionComponent<SlackAuthContainerProps> = ({\n  actionButton,\n}) => {\n  const { t } = useTranslations();\n\n  return (\n    <div className=\"rsk-auth\">\n      <div className=\"rsk-auth__header\">\n        <SlackIcon height=\"32px\" width=\"32px\" />\n        <div>{actionButton}</div>\n      </div>\n      <div className=\"rsk-auth__title\">Slack</div>\n      <div className=\"rsk-auth__description\">\n        {t(\"slackConnectContainerDescription\")}\n      </div>\n    </div>\n  );\n};\n"], "names": ["SlackAuthContainer", "actionButton", "t", "useTranslations", "React", "SlackIcon"], "mappings": ";;;;;;;;;;2BAYO,MAAMA,IAAiEA,CAAC,EAC7EC,cAAAA,CAAAA,EACF,KAAM;IACE,MAAA,EAAEC,GAAAA,CAAAA,EAAAA,+UAAMC,CAAgB;IAE9B,OACGC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QAAI,WAAU;IAAA,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CACZ,OAAI;QAAA,WAAU;IACb,GAAAA,aAAAA,uTAAAA,WAAAA,CAAA,aAAA,4TAACC,YAAAA,EAAU;QAAA,QAAO;QAAO,OAAM;IAAA,CAAM,GACrCD,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAKH,MAAAA,CAAa,CACrB,GACCG,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QAAI,WAAU;IAAkB,GAAA,OAAK,GACtCA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,WAAU;IAAA,GACZF,EAAE,kCAAkC,CACvC,CACF;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2114, "column": 0}, "map": {"version": 3, "file": "utils.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/slack/utils.ts"], "sourcesContent": ["import { SlackChannel } from \"@knocklabs/client\";\n\nexport const sortSlackChannelsAlphabetically = (\n  channels: readonly SlackChannel[],\n) =>\n  [...channels].sort((channel1, channel2) =>\n    channel1.name.toLowerCase().localeCompare(channel2.name.toLowerCase()),\n  );\n"], "names": ["sortSlackChannelsAlphabetically", "channels", "sort", "channel1", "channel2", "name", "toLowerCase", "localeCompare"], "mappings": ";;;AAEaA,MAAAA,IAAkCA,CAC7CC,IAEA,CAAC;WAAGA,CAAQ;KAAA,CAAEC,IAAAA,CAAK,CAACC,GAAUC,IAC5BD,EAASE,IAAAA,CAAKC,WAAAA,GAAcC,aAAAA,CAAcH,EAASC,IAAAA,CAAKC,WAAAA,EAAa,CACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2128, "column": 0}, "map": {"version": 3, "file": "SlackErrorMessage.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/slack/components/SlackChannelCombobox/SlackErrorMessage.tsx"], "sourcesContent": ["import { Icon, Lucide } from \"@telegraph/icon\";\nimport { Text } from \"@telegraph/typography\";\nimport { FunctionComponent } from \"react\";\n\ninterface Props {\n  message?: string;\n}\n\nconst SlackErrorMessage: FunctionComponent<Props> = ({ message }) => {\n  return (\n    <div className=\"rsk-combobox__error\">\n      <span>\n        <Icon icon={Lucide.Info} color=\"black\" size=\"1\" aria-hidden />\n      </span>\n      <Text as=\"div\" color=\"black\" size=\"1\">\n        {message}\n      </Text>\n    </div>\n  );\n};\n\nexport default SlackErrorMessage;\n"], "names": ["SlackErrorMessage", "message", "React", "Icon", "Lucide", "Info", "Text"], "mappings": ";;;;;;;;;;;AAQA,MAAMA,IAA8CA,CAAC,EAAEC,SAAAA,CAAAA,EAAQ,GAE1DC,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QAAI,WAAU;IAAA,GACZA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,QAAA,MACEA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,qRAAAC,OAAAA,EAAA;QAAK,8RAAMC,SAAAA,CAAOC,IAAAA;QAAM,OAAM;QAAQ,MAAK;QAAI,eAAW,CAAA;IAAA,CAAA,CAC7D,GACAH,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,2QAACI,OAAAA,EAAK;QAAA,IAAG;QAAM,OAAM;QAAQ,MAAK;IAC/BL,GAAAA,CACH,CACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2159, "column": 0}, "map": {"version": 3, "file": "SlackConnectionError.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/slack/components/SlackChannelCombobox/SlackConnectionError.tsx"], "sourcesContent": ["import { useKnockSlackClient, useTranslations } from \"@knocklabs/react-core\";\nimport { FunctionComponent } from \"react\";\n\nimport SlackErrorMessage from \"./SlackErrorMessage\";\n\nconst SlackConnectionError: FunctionComponent = () => {\n  const { t } = useTranslations();\n  const { connectionStatus } = useKnockSlackClient();\n\n  if (connectionStatus === \"disconnected\" || connectionStatus === \"error\") {\n    return (\n      <SlackErrorMessage\n        message={\n          connectionStatus === \"disconnected\"\n            ? t(\"slackConnectionErrorOccurred\")\n            : t(\"slackConnectionErrorExists\")\n        }\n      />\n    );\n  }\n\n  return null;\n};\n\nexport default SlackConnectionError;\n"], "names": ["SlackConnectionError", "t", "useTranslations", "connectionStatus", "useKnockSlackClient", "React", "SlackErrorMessage"], "mappings": ";;;;;;;;;;;AAKA,MAAMA,IAA0CA,MAAM;IAC9C,MAAA,EAAEC,GAAAA,CAAAA,EAAAA,+UAAMC,CAAgB,IACxB,EAAEC,kBAAAA,CAAAA,EAAAA,IAAqBC,qVAAAA,CAAoB;IAE7CD,OAAAA,MAAqB,kBAAkBA,MAAqB,UAE5DE,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,+UAACC,UAAAA,EACC;QAAA,SAEML,EADJE,MAAqB,iBACf,iCACA,4BAD8B;IAGtC,CAAA,IAIC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2184, "column": 0}, "map": {"version": 3, "file": "SlackAddChannelInput.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/slack/components/SlackAddChannelInput/SlackAddChannelInput.tsx"], "sourcesContent": ["import { SlackChannelConnection } from \"@knocklabs/client\";\nimport { useTranslations } from \"@knocklabs/react-core\";\nimport { useState } from \"react\";\nimport { FunctionComponent } from \"react\";\n\nimport { Spinner } from \"../../../core\";\nimport \"../../theme.css\";\nimport ConnectionErrorInfoBoxes from \"../SlackChannelCombobox/SlackConnectionError\";\nimport { SlackIcon } from \"../SlackIcon\";\n\nimport \"./styles.css\";\n\nexport interface SlackAddChannelInputProps {\n  inErrorState: boolean;\n  connectedChannels: SlackChannelConnection[];\n  updateConnectedChannels: (channels: SlackChannelConnection[]) => void;\n  connectedChannelsError: string | null;\n  connectedChannelsUpdating: boolean;\n}\n\nconst SlackAddChannelInput: FunctionComponent<SlackAddChannelInputProps> = ({\n  inErrorState,\n  connectedChannels = [],\n  updateConnectedChannels,\n  connectedChannelsError,\n  connectedChannelsUpdating,\n}) => {\n  const { t } = useTranslations();\n  const [value, setValue] = useState<string | null>(null);\n  const [localError, setLocalError] = useState<string | null>(null);\n\n  const submitChannel = () => {\n    if (!value) {\n      return;\n    }\n\n    if (localError) {\n      setLocalError(null);\n    }\n\n    const alreadyConnected = connectedChannels.find(\n      (channel) => channel.channel_id === value,\n    );\n\n    if (alreadyConnected) {\n      setValue(\"\");\n      return setLocalError(t(\"slackChannelAlreadyConnected\") || \"\");\n    }\n\n    const channelsToSendToKnock = [...connectedChannels, { channel_id: value }];\n    updateConnectedChannels(channelsToSendToKnock);\n    setValue(\"\");\n  };\n\n  return (\n    <div className=\"rsk-connect-channel\">\n      <input\n        className={`rsk-connect-channel__input ${(inErrorState || !!localError) && !value && \"rsk-connect-channel__input--error\"}`}\n        tabIndex={-1}\n        id=\"slack-channel-search\"\n        type=\"text\"\n        placeholder={\n          localError || connectedChannelsError || t(\"slackChannelId\")\n        }\n        onChange={(e) => setValue(e.target.value)}\n        value={value || \"\"}\n      />\n      <button className=\"rsk-connect-channel__button\" onClick={submitChannel}>\n        {connectedChannelsUpdating ? (\n          <Spinner size=\"15px\" thickness={3} />\n        ) : (\n          <SlackIcon height=\"16px\" width=\"16px\" />\n        )}\n        {t(\"slackConnectChannel\")}\n      </button>\n      <ConnectionErrorInfoBoxes />\n    </div>\n  );\n};\n\nexport default SlackAddChannelInput;\n"], "names": ["SlackAddChannelInput", "inErrorState", "connectedChannels", "updateConnectedChannels", "connectedChannelsError", "connectedChannelsUpdating", "t", "useTranslations", "value", "setValue", "useState", "localError", "setLocalError", "submitChannel", "find", "channel", "channel_id", "channelsToSendToKnock", "React", "e", "target", "Spinner", "SlackIcon", "ConnectionErrorInfoBoxes"], "mappings": ";;;;;;;;;;;;;;;;2BAoBA,MAAMA,IAAqEA,CAAC,EAC1EC,cAAAA,CAAAA,EACAC,mBAAAA,IAAoB,CAAE,CAAA,EACtBC,yBAAAA,CAAAA,EACAC,wBAAAA,CAAAA,EACAC,2BAAAA,CAAAA,EACF,KAAM;IACE,MAAA,EAAEC,CAAAA,EAAAA,OAAMC,wUAAAA,CAAgB,IACxB,CAACC,GAAOC,CAAQ,CAAA,uUAAIC,EAAwB,IAAI,GAChD,CAACC,GAAYC,CAAa,CAAA,uUAAIF,EAAwB,IAAI,GAE1DG,IAAgBA,MAAM;QAC1B,IAAI,CAACL,GACH;QAWF,IARIG,KACFC,EAAc,IAAI,GAGKV,EAAkBY,IAAAA,CACxCC,CAAYA,IAAAA,EAAQC,UAAAA,KAAeR,CACtC,GAGEC,OAAAA,EAAS,EAAE,GACJG,EAAcN,EAAE,8BAA8B,KAAK,EAAE;QAGxDW,MAAAA,IAAwB,CAAC;eAAGf;YAAmB;gBAAEc,YAAYR;YAAAA,CAAO;SAAA;QAC1EL,EAAwBc,CAAqB,GAC7CR,EAAS,EAAE;IACb;IAEA,OACGS,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,OAAA;QAAI,WAAU;IAAA,GACZA,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,SAAA;QACC,WAAW,CAAA,2BAAA,EAAA,CAA+BjB,KAAgB,CAAC,CAACU,CAAAA,KAAe,CAACH,KAAS,mCAAmC,EAAA;QACxH,UAAU,CAAA;QACV,IAAG;QACH,MAAK;QACL,aACEG,KAAcP,KAA0BE,EAAE,gBAAgB;QAE5D,UAAWa,CAAAA,IAAMV,EAASU,EAAEC,MAAAA,CAAOZ,KAAK;QACxC,OAAOA,KAAS;IAAA,CAAG,GAEpBU,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,CAAA,UAAA;QAAO,WAAU;QAA8B,SAASL;IAAAA,GACtDR,IACCa,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,uTAACG,UAAAA,EAAAA;QAAQ,MAAK;QAAO,WAAW;IAAA,CAAK,IAErCH,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,4TAACI,YAAAA,EAAU;QAAA,QAAO;QAAO,OAAM;IAAA,CAChC,GACAhB,EAAE,qBAAqB,CAC1B,GACAY,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,kVAACK,UAAAA,EAAAA,IAAwB,CAC3B;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2241, "column": 0}, "map": {"version": 3, "file": "SlackChannelCombobox.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40knocklabs%2Breact%400.7.11_%40ty_6fc524c218170b7480adccae4612663b/node_modules/%40knocklabs/react/src/modules/slack/components/SlackChannelCombobox/SlackChannelCombobox.tsx"], "sourcesContent": ["import { SlackChannelConnection } from \"@knocklabs/client\";\nimport {\n  RecipientObject,\n  SlackChannelQueryOptions,\n  useConnectedSlackChannels,\n  useKnockSlackClient,\n  useSlackChannels,\n  useTranslations,\n} from \"@knocklabs/react-core\";\nimport { Combobox } from \"@telegraph/combobox\";\nimport { Icon, Lucide } from \"@telegraph/icon\";\nimport { Stack } from \"@telegraph/layout\";\nimport { Text } from \"@telegraph/typography\";\nimport { useMemo } from \"react\";\nimport { FunctionComponent } from \"react\";\n\nimport \"../../theme.css\";\nimport { sortSlackChannelsAlphabetically } from \"../../utils\";\nimport SlackAddChannelInput from \"../SlackAddChannelInput/SlackAddChannelInput\";\n\nimport SlackConnectionError from \"./SlackConnectionError\";\nimport SlackErrorMessage from \"./SlackErrorMessage\";\nimport \"./styles.css\";\n\nconst MAX_ALLOWED_CHANNELS = 1000;\n\nexport type SlackChannelComboboxInputMessages = {\n  disconnected: string;\n  error: string;\n  noChannelsConnected: string;\n  noSlackChannelsFound: string;\n};\n\nexport interface SlackChannelComboboxProps {\n  slackChannelsRecipientObject: RecipientObject;\n  queryOptions?: SlackChannelQueryOptions;\n  inputMessages?: SlackChannelComboboxInputMessages;\n}\n\nexport const SlackChannelCombobox: FunctionComponent<\n  SlackChannelComboboxProps\n> = ({ slackChannelsRecipientObject, queryOptions, inputMessages }) => {\n  const { t } = useTranslations();\n\n  // Gather API data\n  const { connectionStatus, errorLabel: connectionErrorLabel } =\n    useKnockSlackClient();\n\n  const { data: unsortedSlackChannels, isLoading: slackChannelsLoading } =\n    useSlackChannels({ queryOptions });\n\n  const slackChannels = useMemo(\n    () => sortSlackChannelsAlphabetically(unsortedSlackChannels),\n    [unsortedSlackChannels],\n  );\n\n  const {\n    data: connectedChannels,\n    updateConnectedChannels,\n    error: connectedChannelsError,\n    updating: connectedChannelsUpdating,\n  } = useConnectedSlackChannels({ slackChannelsRecipientObject });\n\n  const currentConnectedChannels = useMemo<SlackChannelConnection[]>(() => {\n    // Used to make sure we're only showing currently available channels to select from.\n    // There are cases where a channel is \"connected\" in Knock, but it wouldn't be\n    // posting to it if the channel is private and the Slackbot doesn't belong to it,\n    // so the channel won't show up here and it won't be posted to.\n    const slackChannelsMap = new Map(\n      slackChannels.map((channel) => [channel.id, channel]),\n    );\n\n    return (\n      connectedChannels?.filter((connectedChannel) => {\n        return slackChannelsMap.has(connectedChannel.channel_id || \"\");\n      }) || []\n    );\n  }, [connectedChannels, slackChannels]);\n\n  const inErrorState = useMemo(\n    () =>\n      connectionStatus === \"disconnected\" ||\n      connectionStatus === \"error\" ||\n      !!connectedChannelsError,\n    [connectedChannelsError, connectionStatus],\n  );\n\n  const inLoadingState = useMemo(\n    () =>\n      connectionStatus === \"connecting\" ||\n      connectionStatus === \"disconnecting\" ||\n      slackChannelsLoading,\n\n    [connectionStatus, slackChannelsLoading],\n  );\n\n  // Construct placeholder text\n  const searchPlaceholder = useMemo(() => {\n    const DEFAULT_INPUT_MESSAGES = {\n      disconnected: t(\"slackSearchbarDisconnected\"),\n      noChannelsConnected: t(\"slackSearchbarNoChannelsConnected\"),\n      noSlackChannelsFound: t(\"slackSearchbarNoChannelsFound\"),\n      channelsError: t(\"slackSearchbarChannelsError\"),\n    };\n\n    // Connection status message\n    if (connectionStatus === \"disconnected\") {\n      return inputMessages?.disconnected || DEFAULT_INPUT_MESSAGES.disconnected;\n    }\n\n    if (connectionStatus === \"error\") {\n      return inputMessages?.error || connectionErrorLabel;\n    }\n\n    // Channels status messages\n    if (!inLoadingState && slackChannels.length === 0) {\n      return (\n        inputMessages?.noSlackChannelsFound ||\n        DEFAULT_INPUT_MESSAGES.noSlackChannelsFound\n      );\n    }\n\n    const numberConnectedChannels = currentConnectedChannels?.length || 0;\n\n    if (currentConnectedChannels && numberConnectedChannels === 0) {\n      return (\n        inputMessages?.noChannelsConnected ||\n        DEFAULT_INPUT_MESSAGES.noChannelsConnected\n      );\n    }\n\n    return \"\";\n  }, [\n    connectionStatus,\n    inLoadingState,\n    slackChannels,\n    currentConnectedChannels,\n    inputMessages,\n    connectionErrorLabel,\n    t,\n  ]);\n\n  const comboboxValue = useMemo(\n    () => currentConnectedChannels.map((connection) => connection.channel_id),\n    [currentConnectedChannels],\n  );\n\n  if (slackChannels.length > MAX_ALLOWED_CHANNELS) {\n    return (\n      <SlackAddChannelInput\n        inErrorState={inErrorState}\n        connectedChannels={currentConnectedChannels || []}\n        updateConnectedChannels={updateConnectedChannels}\n        connectedChannelsError={connectedChannelsError}\n        connectedChannelsUpdating={connectedChannelsUpdating}\n      />\n    );\n  }\n\n  return (\n    <Stack className=\"tgph rsk-combobox__grid\" gap=\"3\">\n      <Text\n        color=\"gray\"\n        size=\"2\"\n        as=\"div\"\n        minHeight=\"8\"\n        className=\"rsk-combobox__label\"\n      >\n        Channels\n      </Text>\n      <Combobox.Root\n        value={comboboxValue}\n        onValueChange={(channelIds) => {\n          const updatedConnections = channelIds.map<SlackChannelConnection>(\n            (channelId) => ({\n              channel_id: channelId,\n            }),\n          );\n\n          updateConnectedChannels(updatedConnections).catch(console.error);\n        }}\n        placeholder={searchPlaceholder ?? \"\"}\n        disabled={inErrorState || slackChannels.length === 0}\n        errored={inErrorState}\n        closeOnSelect={false}\n        layout=\"wrap\"\n        modal={\n          // Modal comboboxes cause page layout to shift when body has padding. See KNO-7854.\n          false\n        }\n      >\n        <Combobox.Trigger />\n        <Combobox.Content>\n          <Combobox.Search\n            label={t(\"slackSearchChannels\")}\n            className=\"rsk-combobox__search\"\n          />\n          <Combobox.Options maxHeight=\"36\">\n            {slackChannels.map((channel) => (\n              <Combobox.Option key={channel.id} value={channel.id}>\n                <Stack align=\"center\" gap=\"1\">\n                  <Icon\n                    icon={channel.is_private ? Lucide.Lock : Lucide.Hash}\n                    size=\"0\"\n                    aria-hidden\n                  />\n                  {channel.name}\n                </Stack>\n              </Combobox.Option>\n            ))}\n          </Combobox.Options>\n          <Combobox.Empty />\n        </Combobox.Content>\n      </Combobox.Root>\n      <SlackConnectionError />\n      {!!connectedChannelsError && (\n        <SlackErrorMessage message={connectedChannelsError} />\n      )}\n    </Stack>\n  );\n};\n"], "names": ["MAX_ALLOWED_CHANNELS", "SlackChannelCombobox", "slackChannelsRecipientObject", "queryOptions", "inputMessages", "t", "useTranslations", "connectionStatus", "error<PERSON><PERSON><PERSON>", "connectionErrorLabel", "useKnockSlackClient", "data", "unsortedSlackChannels", "isLoading", "slackChannelsLoading", "useSlackChannels", "slackChannels", "useMemo", "sortSlackChannelsAlphabetically", "connectedChannels", "updateConnectedChannels", "error", "connectedChannelsError", "updating", "connectedChannelsUpdating", "useConnectedSlackChannels", "currentConnectedChannels", "slackChannelsMap", "Map", "map", "channel", "id", "filter", "connectedChannel", "has", "channel_id", "inErrorState", "inLoadingState", "searchPlaceholder", "DEFAULT_INPUT_MESSAGES", "disconnected", "noChannelsConnected", "noSlackChannelsFound", "channelsError", "length", "numberConnectedChannels", "comboboxValue", "connection", "React", "SlackAddChannelInput", "<PERSON><PERSON>", "Text", "Combobox", "channelIds", "updatedConnections", "channelId", "catch", "console", "Icon", "is_private", "Lucide", "Lock", "Hash", "name", "SlackConnectionError", "SlackErrorMessage"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAwBA,MAAMA,IAAuB,KAehBC,IAETA,CAAC,EAAEC,8BAAAA,CAAAA,EAA8BC,cAAAA,CAAAA,EAAcC,eAAAA,CAAAA,EAAc,KAAM;IAC/D,MAAA,EAAEC,GAAAA,CAAAA,EAAAA,OAAMC,wUAAAA,CAAgB,IAGxB,EAAEC,kBAAAA,CAAAA,EAAkBC,YAAYC,CAAAA,EAAAA,yVACpCC,CAAoB,IAEhB,EAAEC,MAAMC,CAAAA,EAAuBC,WAAWC,CAAAA,EAAAA,iYAC9CC,EAAiB;QAAEZ,cAAAA;IAAAA,CAAc,GAE7Ba,uUAAgBC,EACpB,IAAMC,kUAAAA,EAAgCN,CAAqB,GAC3D;QAACA,CAAqB;KACxB,GAEM,EACJD,MAAMQ,CAAAA,EACNC,yBAAAA,CAAAA,EACAC,OAAOC,CAAAA,EACPC,UAAUC,CAAAA,EAAAA,4ZACRC,EAA0B;QAAEvB,8BAAAA;IAAAA,CAA8B,GAExDwB,uUAA2BT,EAAkC,MAAM;QAKjEU,MAAAA,IAAmB,IAAIC,IAC3BZ,EAAca,GAAAA,CAAKC,CAAYA,IAAA;gBAACA,EAAQC,EAAAA;gBAAID,CAAO;aAAC,CACtD;QAGEX,OAAAA,CAAAA,KAAAA,OAAAA,KAAAA,IAAAA,EAAmBa,MAAAA,CAAQC,CAAqBA,IACvCN,EAAiBO,GAAAA,CAAID,EAAiBE,UAAAA,IAAc,EAAE,EAAA,KACzD,CAAE,CAAA;IAAA,GAET;QAAChB;QAAmBH,CAAa;KAAC,GAE/BoB,uUAAenB,EACnB,IACEV,MAAqB,kBACrBA,MAAqB,WACrB,CAAC,CAACe,GACJ;QAACA;QAAwBf,CAAgB;KAC3C,GAEM8B,IAAiBpB,mUAAAA,EACrB,IACEV,MAAqB,gBACrBA,MAAqB,mBACrBO,GAEF;QAACP;QAAkBO,CAAoB;KACzC,GAGMwB,KAAoBrB,kUAAAA,EAAQ,MAAM;QACtC,MAAMsB,IAAyB;YAC7BC,cAAcnC,EAAE,4BAA4B;YAC5CoC,qBAAqBpC,EAAE,mCAAmC;YAC1DqC,sBAAsBrC,EAAE,+BAA+B;YACvDsC,eAAetC,EAAE,6BAA6B;QAChD;QAGA,IAAIE,MAAqB,gBAChBH,OAAAA,CAAAA,KAAAA,OAAAA,KAAAA,IAAAA,EAAeoC,YAAAA,KAAgBD,EAAuBC,YAAAA;QAG/D,IAAIjC,MAAqB,SACvB,OAAA,CAAOH,KAAAA,OAAAA,KAAAA,IAAAA,EAAeiB,KAAAA,KAASZ;QAIjC,IAAI,CAAC4B,KAAkBrB,EAAc4B,MAAAA,KAAW,GAE5CxC,OAAAA,CAAAA,KAAAA,OAAAA,KAAAA,IAAAA,EAAesC,oBAAAA,KACfH,EAAuBG,oBAAAA;QAIrBG,MAAAA,IAAAA,CAA0BnB,KAAAA,OAAAA,KAAAA,IAAAA,EAA0BkB,MAAAA,KAAU;QAEhElB,OAAAA,KAA4BmB,MAA4B,IAAA,CAExDzC,KAAAA,OAAAA,KAAAA,IAAAA,EAAeqC,mBAAAA,KACfF,EAAuBE,mBAAAA,GAIpB;IAAA,GACN;QACDlC;QACA8B;QACArB;QACAU;QACAtB;QACAK;QACAJ,CAAC;KACF,GAEKyC,QAAgB7B,+TAAAA,EACpB,IAAMS,EAAyBG,GAAAA,CAAKkB,CAAeA,IAAAA,EAAWZ,UAAU,GACxE;QAACT,CAAwB;KAC3B;IAEIV,OAAAA,EAAc4B,MAAAA,GAAS5C,IAEvBgD,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,kVAACC,UAAAA,EAAAA;QACC,cAAAb;QACA,mBAAmBV,KAA4B,EAAA;QAC/C,yBAAAN;QACA,wBAAAE;QACA,2BAAAE;IACA,CAAA,IAKJwB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,sQAACE,SAAAA,EAAM;QAAA,WAAU;QAA0B,KAAI;IAC7C,GAAAF,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,2QAACG,OAAAA,EACC;QAAA,OAAM;QACN,MAAK;QACL,IAAG;QACH,WAAU;QACV,WAAU;IAAqB,GAAA,UAGjC,GACAH,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,4QAACI,WAAAA,CAAS,IAAA,EAAT;QACC,OAAON;QACP,eAAgBO,CAAeA,MAAA;YACvBC,MAAAA,IAAqBD,EAAWxB,GAAAA,CACnC0B,CAAeA,IAAAA,CAAA;oBACdpB,YAAYoB;gBAAAA,CAAAA,CAEhB;YAEAnC,EAAwBkC,CAAkB,EAAEE,KAAAA,CAAMC,QAAQpC,KAAK;QAAA;QAEjE,aAAaiB,KAAqB;QAClC,UAAUF,KAAgBpB,EAAc4B,MAAAA,KAAW;QACnD,SAASR;QACT,eAAe,CAAA;QACf,QAAO;QACP,OAAA,mFAAA;QAEE,CAAA;IAAA,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,4QAGDgB,WAAAA,CAAS,OAAA,EAAT,IAAgB,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,4QAChBA,WAAAA,CAAS,OAAA,EAAT,MACCJ,aAAAA,uTAAAA,WAAAA,CAAA,aAAA,4QAACI,WAAAA,CAAS,MAAA,EAAT;QACC,OAAO/C,EAAE,qBAAqB;QAC9B,WAAU;IAAsB,CAAA,GAElC2C,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,4QAACI,WAAAA,CAAS,OAAA,EAAT;QAAiB,WAAU;IACzBpC,GAAAA,EAAca,GAAAA,CAAKC,CAClBA,IAAAkB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,4QAACI,WAAAA,CAAS,MAAA,EAAT;YAAgB,KAAKtB,EAAQC,EAAAA;YAAI,OAAOD,EAAQC,EAAAA;QAAAA,GAC9CiB,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,uQAAAE,QAAAA,EAAA;YAAM,OAAM;YAAS,KAAI;QACxB,GAAAF,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,qRAACU,OAAAA,EACC;YAAA,MAAM5B,EAAQ6B,UAAAA,2RAAaC,SAAAA,CAAOC,IAAAA,2RAAOD,SAAAA,CAAOE,IAAAA;YAChD,MAAK;YACL,eAAW,CAAA;QAAA,CAAA,GAEZhC,EAAQiC,IACX,CACF,CACD,CACH,GACCf,aAAAA,wTAAAA,UAAAA,CAAA,aAAA,4QAAAI,WAAAA,CAAS,KAAA,EAAT,IAAc,CACjB,CACF,GACAJ,aAAAA,GAAAA,+TAAAA,CAAA,aAAA,kVAACgB,UAAAA,EAAoB,IAAA,GACpB,CAAC,CAAC1C,KAAAA,aAAAA,GAAAA,oTAAAA,CAAAA,UAAAA,CAAAA,aAAAA,+UACA2C,UAAAA,EAAkB;QAAA,SAAS3C;IAAAA,CAC7B,CACH;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2372, "column": 0}, "map": {"version": 3, "file": "index.mjs", "sources": [], "names": [], "mappings": "", "debugId": null}}]}