{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/runtime/node/safe-node-apis.js"], "sourcesContent": ["/**\n * This file is used for conditional imports to mitigate bundling issues with Next.js server actions on version prior to 14.1.0.\n */\n// eslint-disable-next-line @typescript-eslint/no-require-imports\nconst { existsSync, writeFileSync, readFileSync, appendFileSync, mkdirSync, rmSync } = require('node:fs');\n// eslint-disable-next-line @typescript-eslint/no-require-imports\nconst path = require('node:path');\nconst fs = {\n  existsSync,\n  writeFileSync,\n  readFileSync,\n  appendFileSync,\n  mkdirSync,\n  rmSync,\n};\n\nconst cwd = () => process.cwd();\n\nmodule.exports = { fs, path, cwd };\n"], "names": [], "mappings": ";;;;;AAAA,IAAA,yBAAA,CAAA,GAAA,+QAAA,CAAA,aAAA,EAAA;IAAA,sCAAA,OAAA,EAAA,MAAA;QAIA,MAAM,EAAE,UAAA,EAAY,aAAA,EAAe,YAAA,EAAc,cAAA,EAAgB,SAAA,EAAW,MAAA,CAAO,CAAA,GAAI,QAAQ,SAAS;QAExG,MAAM,OAAO,QAAQ,WAAW;QAChC,MAAM,KAAK;YACT;YACA;YACA;YACA;YACA;YACA;QACF;QAEA,MAAM,MAAM,IAAM,QAAQ,GAAA,CAAI;QAE9B,OAAO,OAAA,GAAU;YAAE;YAAI;YAAM;QAAI;IAAA;AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/fs/utils.ts"], "sourcesContent": ["/**\n * Attention: Only import this module when the node runtime is used.\n * We are using conditional imports to mitigate bundling issues with Next.js server actions on version prior to 14.1.0.\n */\n// @ts-ignore\nimport nodeRuntime from '#safe-node-apis';\n\nconst throwMissingFsModule = (module: string) => {\n  throw new Error(`Clerk: ${module} is missing. This is an internal error. Please contact Clerk's support.`);\n};\n\nconst nodeFsOrThrow = () => {\n  if (!nodeRuntime.fs) {\n    throwMissingFsModule('fs');\n  }\n  return nodeRuntime.fs;\n};\n\nconst nodePathOrThrow = () => {\n  if (!nodeRuntime.path) {\n    throwMissingFsModule('path');\n  }\n  return nodeRuntime.path;\n};\n\nconst nodeCwdOrThrow = () => {\n  if (!nodeRuntime.cwd) {\n    throwMissingFsModule('cwd');\n  }\n  return nodeRuntime.cwd;\n};\n\nexport { nodeCwdOrThrow, nodeFsOrThrow, nodePathOrThrow };\n"], "names": [], "mappings": ";;;;;AAKA,OAAO,iBAAiB;;;AAExB,MAAM,uBAAuB,CAAC,WAAmB;IAC/C,MAAM,IAAI,MAAM,CAAA,OAAA,EAAU,MAAM,CAAA,uEAAA,CAAyE;AAC3G;AAEA,MAAM,gBAAgB,MAAM;IAC1B,IAAI,uSAAC,UAAA,CAAY,EAAA,EAAI;QACnB,qBAAqB,IAAI;IAC3B;IACA,6SAAO,UAAA,CAAY,EAAA;AACrB;AAEA,MAAM,kBAAkB,MAAM;IAC5B,IAAI,uSAAC,UAAA,CAAY,IAAA,EAAM;QACrB,qBAAqB,MAAM;IAC7B;IACA,6SAAO,UAAA,CAAY,IAAA;AACrB;AAEA,MAAM,iBAAiB,MAAM;IAC3B,IAAI,uSAAC,UAAA,CAAY,GAAA,EAAK;QACpB,qBAAqB,KAAK;IAC5B;IACA,6SAAO,UAAA,CAAY,GAAA;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/keyless-node.ts"], "sourcesContent": ["import type { AccountlessApplication } from '@clerk/backend';\n\nimport { createClerkClientWithOptions } from './createClerkClient';\nimport { nodeCwdOrThrow, nodeFsOrThrow, nodePathOrThrow } from './fs/utils';\n\n/**\n * The Clerk-specific directory name.\n */\nconst CLERK_HIDDEN = '.clerk';\n\n/**\n * The Clerk-specific lock file that is used to mitigate multiple key creation.\n * This is automatically cleaned up.\n */\nconst CLERK_LOCK = 'clerk.lock';\n\n/**\n * The `.clerk/` directory is NOT safe to be committed as it may include sensitive information about a Clerk instance.\n * It may include an instance's secret key and the secret token for claiming that instance.\n */\nfunction updateGitignore() {\n  const { existsSync, writeFileSync, readFileSync, appendFileSync } = nodeFsOrThrow();\n\n  const path = nodePathOrThrow();\n  const cwd = nodeCwdOrThrow();\n  const gitignorePath = path.join(cwd(), '.gitignore');\n  if (!existsSync(gitignorePath)) {\n    writeFileSync(gitignorePath, '');\n  }\n\n  // Check if `.clerk/` entry exists in .gitignore\n  const gitignoreContent = readFileSync(gitignorePath, 'utf-8');\n  const COMMENT = `# clerk configuration (can include secrets)`;\n  if (!gitignoreContent.includes(CLERK_HIDDEN + '/')) {\n    appendFileSync(gitignorePath, `\\n${COMMENT}\\n/${CLERK_HIDDEN}/\\n`);\n  }\n}\n\nconst generatePath = (...slugs: string[]) => {\n  const path = nodePathOrThrow();\n  const cwd = nodeCwdOrThrow();\n  return path.join(cwd(), CLERK_HIDDEN, ...slugs);\n};\n\nconst _TEMP_DIR_NAME = '.tmp';\nconst getKeylessConfigurationPath = () => generatePath(_TEMP_DIR_NAME, 'keyless.json');\nconst getKeylessReadMePath = () => generatePath(_TEMP_DIR_NAME, 'README.md');\n\nlet isCreatingFile = false;\n\nexport function safeParseClerkFile(): AccountlessApplication | undefined {\n  const { readFileSync } = nodeFsOrThrow();\n  try {\n    const CONFIG_PATH = getKeylessConfigurationPath();\n    let fileAsString;\n    try {\n      fileAsString = readFileSync(CONFIG_PATH, { encoding: 'utf-8' }) || '{}';\n    } catch {\n      fileAsString = '{}';\n    }\n    return JSON.parse(fileAsString) as AccountlessApplication;\n  } catch {\n    return undefined;\n  }\n}\n\n/**\n * Using both an in-memory and file system lock seems to be the most effective solution.\n */\nconst lockFileWriting = () => {\n  const { writeFileSync } = nodeFsOrThrow();\n\n  isCreatingFile = true;\n\n  writeFileSync(\n    CLERK_LOCK,\n    // In the rare case, the file persists give the developer enough context.\n    'This file can be deleted. Please delete this file and refresh your application',\n    {\n      encoding: 'utf8',\n      mode: '0777',\n      flag: 'w',\n    },\n  );\n};\n\nconst unlockFileWriting = () => {\n  const { rmSync } = nodeFsOrThrow();\n\n  try {\n    rmSync(CLERK_LOCK, { force: true, recursive: true });\n  } catch {\n    // Simply ignore if the removal of the directory/file fails\n  }\n\n  isCreatingFile = false;\n};\n\nconst isFileWritingLocked = () => {\n  const { existsSync } = nodeFsOrThrow();\n  return isCreatingFile || existsSync(CLERK_LOCK);\n};\n\nasync function createOrReadKeyless(): Promise<AccountlessApplication | null> {\n  const { writeFileSync, mkdirSync } = nodeFsOrThrow();\n\n  /**\n   * If another request is already in the process of acquiring keys return early.\n   * Using both an in-memory and file system lock seems to be the most effective solution.\n   */\n  if (isFileWritingLocked()) {\n    return null;\n  }\n\n  lockFileWriting();\n\n  const CONFIG_PATH = getKeylessConfigurationPath();\n  const README_PATH = getKeylessReadMePath();\n\n  mkdirSync(generatePath(_TEMP_DIR_NAME), { recursive: true });\n  updateGitignore();\n\n  /**\n   * When the configuration file exists, always read the keys from the file\n   */\n  const envVarsMap = safeParseClerkFile();\n  if (envVarsMap?.publishableKey && envVarsMap?.secretKey) {\n    unlockFileWriting();\n\n    return envVarsMap;\n  }\n\n  /**\n   * At this step, it is safe to create new keys and store them.\n   */\n  const client = createClerkClientWithOptions({});\n  const accountlessApplication = await client.__experimental_accountlessApplications\n    .createAccountlessApplication()\n    .catch(() => null);\n\n  if (accountlessApplication) {\n    writeFileSync(CONFIG_PATH, JSON.stringify(accountlessApplication), {\n      encoding: 'utf8',\n      mode: '0777',\n      flag: 'w',\n    });\n\n    // TODO-KEYLESS: Add link to official documentation.\n    const README_NOTIFICATION = `\n## DO NOT COMMIT\nThis directory is auto-generated from \\`@clerk/nextjs\\` because you are running in Keyless mode. Avoid committing the \\`.clerk/\\` directory as it includes the secret key of the unclaimed instance.\n  `;\n\n    writeFileSync(README_PATH, README_NOTIFICATION, {\n      encoding: 'utf8',\n      mode: '0777',\n      flag: 'w',\n    });\n  }\n  /**\n   * Clean up locks.\n   */\n  unlockFileWriting();\n\n  return accountlessApplication;\n}\n\nfunction removeKeyless() {\n  const { rmSync } = nodeFsOrThrow();\n\n  /**\n   * If another request is already in the process of acquiring keys return early.\n   * Using both an in-memory and file system lock seems to be the most effective solution.\n   */\n  if (isFileWritingLocked()) {\n    return undefined;\n  }\n\n  lockFileWriting();\n\n  try {\n    rmSync(generatePath(), { force: true, recursive: true });\n  } catch {\n    // Simply ignore if the removal of the directory/file fails\n  }\n\n  /**\n   * Clean up locks.\n   */\n  unlockFileWriting();\n}\n\nexport { createOrReadKeyless, removeKeyless };\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,oCAAoC;AAC7C,SAAS,gBAAgB,eAAe,uBAAuB;;;;AAK/D,MAAM,eAAe;AAMrB,MAAM,aAAa;AAMnB,SAAS,kBAAkB;IACzB,MAAM,EAAE,UAAA,EAAY,aAAA,EAAe,YAAA,EAAc,cAAA,CAAe,CAAA,0RAAI,iBAAA,CAAc;IAElF,MAAM,+RAAO,kBAAA,CAAgB;IAC7B,MAAM,6RAAM,kBAAA,CAAe;IAC3B,MAAM,gBAAgB,KAAK,IAAA,CAAK,IAAI,GAAG,YAAY;IACnD,IAAI,CAAC,WAAW,aAAa,GAAG;QAC9B,cAAc,eAAe,EAAE;IACjC;IAGA,MAAM,mBAAmB,aAAa,eAAe,OAAO;IAC5D,MAAM,UAAU,CAAA,2CAAA,CAAA;IAChB,IAAI,CAAC,iBAAiB,QAAA,CAAS,eAAe,GAAG,GAAG;QAClD,eAAe,eAAe,CAAA;AAAA,EAAK,OAAO,CAAA;CAAA,EAAM,YAAY,CAAA;AAAA,CAAK;IACnE;AACF;AAEA,MAAM,eAAe,CAAA,GAAI,UAAoB;IAC3C,MAAM,WAAO,sSAAA,CAAgB;IAC7B,MAAM,8RAAM,iBAAA,CAAe;IAC3B,OAAO,KAAK,IAAA,CAAK,IAAI,GAAG,cAAc,GAAG,KAAK;AAChD;AAEA,MAAM,iBAAiB;AACvB,MAAM,8BAA8B,IAAM,aAAa,gBAAgB,cAAc;AACrF,MAAM,uBAAuB,IAAM,aAAa,gBAAgB,WAAW;AAE3E,IAAI,iBAAiB;AAEd,SAAS,qBAAyD;IACvE,MAAM,EAAE,YAAA,CAAa,CAAA,2RAAI,gBAAA,CAAc;IACvC,IAAI;QACF,MAAM,cAAc,4BAA4B;QAChD,IAAI;QACJ,IAAI;YACF,eAAe,aAAa,aAAa;gBAAE,UAAU;YAAQ,CAAC,KAAK;QACrE,EAAA,OAAQ;YACN,eAAe;QACjB;QACA,OAAO,KAAK,KAAA,CAAM,YAAY;IAChC,EAAA,OAAQ;QACN,OAAO,KAAA;IACT;AACF;AAKA,MAAM,kBAAkB,MAAM;IAC5B,MAAM,EAAE,aAAA,CAAc,CAAA,2RAAI,gBAAA,CAAc;IAExC,iBAAiB;IAEjB,cACE,YAAA,yEAAA;IAEA,kFACA;QACE,UAAU;QACV,MAAM;QACN,MAAM;IACR;AAEJ;AAEA,MAAM,oBAAoB,MAAM;IAC9B,MAAM,EAAE,MAAA,CAAO,CAAA,2RAAI,gBAAA,CAAc;IAEjC,IAAI;QACF,OAAO,YAAY;YAAE,OAAO;YAAM,WAAW;QAAK,CAAC;IACrD,EAAA,OAAQ,CAER;IAEA,iBAAiB;AACnB;AAEA,MAAM,sBAAsB,MAAM;IAChC,MAAM,EAAE,UAAA,CAAW,CAAA,2RAAI,gBAAA,CAAc;IACrC,OAAO,kBAAkB,WAAW,UAAU;AAChD;AAEA,eAAe,sBAA8D;IAC3E,MAAM,EAAE,aAAA,EAAe,SAAA,CAAU,CAAA,2RAAI,gBAAA,CAAc;IAMnD,IAAI,oBAAoB,GAAG;QACzB,OAAO;IACT;IAEA,gBAAgB;IAEhB,MAAM,cAAc,4BAA4B;IAChD,MAAM,cAAc,qBAAqB;IAEzC,UAAU,aAAa,cAAc,GAAG;QAAE,WAAW;IAAK,CAAC;IAC3D,gBAAgB;IAKhB,MAAM,aAAa,mBAAmB;IACtC,IAAA,CAAI,cAAA,OAAA,KAAA,IAAA,WAAY,cAAA,KAAA,CAAkB,cAAA,OAAA,KAAA,IAAA,WAAY,SAAA,GAAW;QACvD,kBAAkB;QAElB,OAAO;IACT;IAKA,MAAM,uSAAS,+BAAA,EAA6B,CAAC,CAAC;IAC9C,MAAM,yBAAyB,MAAM,OAAO,sCAAA,CACzC,4BAAA,CAA6B,EAC7B,KAAA,CAAM,IAAM,IAAI;IAEnB,IAAI,wBAAwB;QAC1B,cAAc,aAAa,KAAK,SAAA,CAAU,sBAAsB,GAAG;YACjE,UAAU;YACV,MAAM;YACN,MAAM;QACR,CAAC;QAGD,MAAM,sBAAsB,CAAA;;;EAAA,CAAA;QAK5B,cAAc,aAAa,qBAAqB;YAC9C,UAAU;YACV,MAAM;YACN,MAAM;QACR,CAAC;IACH;IAIA,kBAAkB;IAElB,OAAO;AACT;AAEA,SAAS,gBAAgB;IACvB,MAAM,EAAE,MAAA,CAAO,CAAA,2RAAI,gBAAA,CAAc;IAMjC,IAAI,oBAAoB,GAAG;QACzB,OAAO,KAAA;IACT;IAEA,gBAAgB;IAEhB,IAAI;QACF,OAAO,aAAa,GAAG;YAAE,OAAO;YAAM,WAAW;QAAK,CAAC;IACzD,EAAA,OAAQ,CAER;IAKA,kBAAkB;AACpB", "ignoreList": [0], "debugId": null}}]}