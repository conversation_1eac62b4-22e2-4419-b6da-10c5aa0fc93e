{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Tracer } from './Tracer';\nexport {\n  BasicTracerProvider,\n  EXPORTER_FACTORY,\n  ForceFlushState,\n  PROPAGATOR_FACTORY,\n} from './BasicTracerProvider';\nexport { BatchSpanProcessor, RandomIdGenerator } from './platform';\nexport { ConsoleSpanExporter } from './export/ConsoleSpanExporter';\nexport { InMemorySpanExporter } from './export/InMemorySpanExporter';\nexport { ReadableSpan } from './export/ReadableSpan';\nexport { SimpleSpanProcessor } from './export/SimpleSpanProcessor';\nexport { SpanExporter } from './export/SpanExporter';\nexport { NoopSpanProcessor } from './export/NoopSpanProcessor';\nexport { AlwaysOffSampler } from './sampler/AlwaysOffSampler';\nexport { AlwaysOnSampler } from './sampler/AlwaysOnSampler';\nexport { ParentBasedSampler } from './sampler/ParentBasedSampler';\nexport { TraceIdRatioBasedSampler } from './sampler/TraceIdRatioBasedSampler';\nexport { Sampler, SamplingDecision, SamplingResult } from './Sampler';\nexport { Span } from './Span';\nexport { SpanProcessor } from './SpanProcessor';\nexport { TimedEvent } from './TimedEvent';\nexport {\n  BatchSpanProcessorBrowserConfig,\n  BufferConfig,\n  GeneralLimits,\n  SDKRegistrationConfig,\n  SpanLimits,\n  TracerConfig,\n} from './types';\nexport { IdGenerator } from './IdGenerator';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "file": "enums.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/enums.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// Event name definitions\nexport const ExceptionEventName = 'exception';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,yBAAyB;;;;AAClB,IAAM,kBAAkB,GAAG,WAAW,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "file": "Span.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/Span.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  diag,\n  Exception,\n  HrTime,\n  Link,\n  Span as APISpan,\n  SpanAttributes,\n  SpanAttributeValue,\n  SpanContext,\n  SpanKind,\n  SpanStatus,\n  SpanStatusCode,\n  TimeInput,\n} from '@opentelemetry/api';\nimport {\n  addHrTimes,\n  millisToHrTime,\n  getTimeOrigin,\n  hrTime,\n  hrTimeDuration,\n  InstrumentationLibrary,\n  isAttributeValue,\n  isTimeInput,\n  isTimeInputHrTime,\n  otperformance,\n  sanitizeAttributes,\n} from '@opentelemetry/core';\nimport { IResource } from '@opentelemetry/resources';\nimport {\n  SEMATTRS_EXCEPTION_MESSAGE,\n  SEMATTRS_EXCEPTION_STACKTRACE,\n  SEMATTRS_EXCEPTION_TYPE,\n} from '@opentelemetry/semantic-conventions';\nimport { ExceptionEventName } from './enums';\nimport { ReadableSpan } from './export/ReadableSpan';\nimport { SpanProcessor } from './SpanProcessor';\nimport { TimedEvent } from './TimedEvent';\nimport { Tracer } from './Tracer';\nimport { SpanLimits } from './types';\n\n/**\n * This class represents a span.\n */\nexport class Span implements APISpan, ReadableSpan {\n  // Below properties are included to implement ReadableSpan for export\n  // purposes but are not intended to be written-to directly.\n  private readonly _spanContext: SpanContext;\n  readonly kind: SpanKind;\n  readonly parentSpanId?: string;\n  readonly attributes: SpanAttributes = {};\n  readonly links: Link[] = [];\n  readonly events: TimedEvent[] = [];\n  readonly startTime: HrTime;\n  readonly resource: IResource;\n  readonly instrumentationLibrary: InstrumentationLibrary;\n\n  private _droppedAttributesCount = 0;\n  private _droppedEventsCount: number = 0;\n  private _droppedLinksCount: number = 0;\n\n  name: string;\n  status: SpanStatus = {\n    code: SpanStatusCode.UNSET,\n  };\n  endTime: HrTime = [0, 0];\n  private _ended = false;\n  private _duration: HrTime = [-1, -1];\n  private readonly _spanProcessor: SpanProcessor;\n  private readonly _spanLimits: SpanLimits;\n  private readonly _attributeValueLengthLimit: number;\n\n  private readonly _performanceStartTime: number;\n  private readonly _performanceOffset: number;\n  private readonly _startTimeProvided: boolean;\n\n  /**\n   * Constructs a new Span instance.\n   *\n   * @deprecated calling Span constructor directly is not supported. Please use tracer.startSpan.\n   * */\n  constructor(\n    parentTracer: Tracer,\n    context: Context,\n    spanName: string,\n    spanContext: SpanContext,\n    kind: SpanKind,\n    parentSpanId?: string,\n    links: Link[] = [],\n    startTime?: TimeInput,\n    _deprecatedClock?: unknown, // keeping this argument even though it is unused to ensure backwards compatibility\n    attributes?: SpanAttributes\n  ) {\n    this.name = spanName;\n    this._spanContext = spanContext;\n    this.parentSpanId = parentSpanId;\n    this.kind = kind;\n    this.links = links;\n\n    const now = Date.now();\n    this._performanceStartTime = otperformance.now();\n    this._performanceOffset =\n      now - (this._performanceStartTime + getTimeOrigin());\n    this._startTimeProvided = startTime != null;\n\n    this.startTime = this._getTime(startTime ?? now);\n\n    this.resource = parentTracer.resource;\n    this.instrumentationLibrary = parentTracer.instrumentationLibrary;\n    this._spanLimits = parentTracer.getSpanLimits();\n    this._attributeValueLengthLimit =\n      this._spanLimits.attributeValueLengthLimit || 0;\n\n    if (attributes != null) {\n      this.setAttributes(attributes);\n    }\n\n    this._spanProcessor = parentTracer.getActiveSpanProcessor();\n    this._spanProcessor.onStart(this, context);\n  }\n\n  spanContext(): SpanContext {\n    return this._spanContext;\n  }\n\n  setAttribute(key: string, value?: SpanAttributeValue): this;\n  setAttribute(key: string, value: unknown): this {\n    if (value == null || this._isSpanEnded()) return this;\n    if (key.length === 0) {\n      diag.warn(`Invalid attribute key: ${key}`);\n      return this;\n    }\n    if (!isAttributeValue(value)) {\n      diag.warn(`Invalid attribute value set for key: ${key}`);\n      return this;\n    }\n\n    if (\n      Object.keys(this.attributes).length >=\n        this._spanLimits.attributeCountLimit! &&\n      !Object.prototype.hasOwnProperty.call(this.attributes, key)\n    ) {\n      this._droppedAttributesCount++;\n      return this;\n    }\n    this.attributes[key] = this._truncateToSize(value);\n    return this;\n  }\n\n  setAttributes(attributes: SpanAttributes): this {\n    for (const [k, v] of Object.entries(attributes)) {\n      this.setAttribute(k, v);\n    }\n    return this;\n  }\n\n  /**\n   *\n   * @param name Span Name\n   * @param [attributesOrStartTime] Span attributes or start time\n   *     if type is {@type TimeInput} and 3rd param is undefined\n   * @param [timeStamp] Specified time stamp for the event\n   */\n  addEvent(\n    name: string,\n    attributesOrStartTime?: SpanAttributes | TimeInput,\n    timeStamp?: TimeInput\n  ): this {\n    if (this._isSpanEnded()) return this;\n    if (this._spanLimits.eventCountLimit === 0) {\n      diag.warn('No events allowed.');\n      this._droppedEventsCount++;\n      return this;\n    }\n    if (this.events.length >= this._spanLimits.eventCountLimit!) {\n      if (this._droppedEventsCount === 0) {\n        diag.debug('Dropping extra events.');\n      }\n      this.events.shift();\n      this._droppedEventsCount++;\n    }\n\n    if (isTimeInput(attributesOrStartTime)) {\n      if (!isTimeInput(timeStamp)) {\n        timeStamp = attributesOrStartTime;\n      }\n      attributesOrStartTime = undefined;\n    }\n\n    const attributes = sanitizeAttributes(attributesOrStartTime);\n\n    this.events.push({\n      name,\n      attributes,\n      time: this._getTime(timeStamp),\n      droppedAttributesCount: 0,\n    });\n    return this;\n  }\n\n  addLink(link: Link): this {\n    this.links.push(link);\n    return this;\n  }\n\n  addLinks(links: Link[]): this {\n    this.links.push(...links);\n    return this;\n  }\n\n  setStatus(status: SpanStatus): this {\n    if (this._isSpanEnded()) return this;\n    this.status = { ...status };\n\n    // When using try-catch, the caught \"error\" is of type `any`. When then assigning `any` to `status.message`,\n    // TypeScript will not error. While this can happen during use of any API, it is more common on Span#setStatus()\n    // as it's likely used in a catch-block. Therefore, we validate if `status.message` is actually a string, null, or\n    // undefined to avoid an incorrect type causing issues downstream.\n    if (this.status.message != null && typeof status.message !== 'string') {\n      diag.warn(\n        `Dropping invalid status.message of type '${typeof status.message}', expected 'string'`\n      );\n      delete this.status.message;\n    }\n\n    return this;\n  }\n\n  updateName(name: string): this {\n    if (this._isSpanEnded()) return this;\n    this.name = name;\n    return this;\n  }\n\n  end(endTime?: TimeInput): void {\n    if (this._isSpanEnded()) {\n      diag.error(\n        `${this.name} ${this._spanContext.traceId}-${this._spanContext.spanId} - You can only call end() on a span once.`\n      );\n      return;\n    }\n    this._ended = true;\n\n    this.endTime = this._getTime(endTime);\n    this._duration = hrTimeDuration(this.startTime, this.endTime);\n\n    if (this._duration[0] < 0) {\n      diag.warn(\n        'Inconsistent start and end time, startTime > endTime. Setting span duration to 0ms.',\n        this.startTime,\n        this.endTime\n      );\n      this.endTime = this.startTime.slice() as HrTime;\n      this._duration = [0, 0];\n    }\n\n    if (this._droppedEventsCount > 0) {\n      diag.warn(\n        `Dropped ${this._droppedEventsCount} events because eventCountLimit reached`\n      );\n    }\n\n    this._spanProcessor.onEnd(this);\n  }\n\n  private _getTime(inp?: TimeInput): HrTime {\n    if (typeof inp === 'number' && inp <= otperformance.now()) {\n      // must be a performance timestamp\n      // apply correction and convert to hrtime\n      return hrTime(inp + this._performanceOffset);\n    }\n\n    if (typeof inp === 'number') {\n      return millisToHrTime(inp);\n    }\n\n    if (inp instanceof Date) {\n      return millisToHrTime(inp.getTime());\n    }\n\n    if (isTimeInputHrTime(inp)) {\n      return inp;\n    }\n\n    if (this._startTimeProvided) {\n      // if user provided a time for the start manually\n      // we can't use duration to calculate event/end times\n      return millisToHrTime(Date.now());\n    }\n\n    const msDuration = otperformance.now() - this._performanceStartTime;\n    return addHrTimes(this.startTime, millisToHrTime(msDuration));\n  }\n\n  isRecording(): boolean {\n    return this._ended === false;\n  }\n\n  recordException(exception: Exception, time?: TimeInput): void {\n    const attributes: SpanAttributes = {};\n    if (typeof exception === 'string') {\n      attributes[SEMATTRS_EXCEPTION_MESSAGE] = exception;\n    } else if (exception) {\n      if (exception.code) {\n        attributes[SEMATTRS_EXCEPTION_TYPE] = exception.code.toString();\n      } else if (exception.name) {\n        attributes[SEMATTRS_EXCEPTION_TYPE] = exception.name;\n      }\n      if (exception.message) {\n        attributes[SEMATTRS_EXCEPTION_MESSAGE] = exception.message;\n      }\n      if (exception.stack) {\n        attributes[SEMATTRS_EXCEPTION_STACKTRACE] = exception.stack;\n      }\n    }\n\n    // these are minimum requirements from spec\n    if (\n      attributes[SEMATTRS_EXCEPTION_TYPE] ||\n      attributes[SEMATTRS_EXCEPTION_MESSAGE]\n    ) {\n      this.addEvent(ExceptionEventName, attributes, time);\n    } else {\n      diag.warn(`Failed to record an exception ${exception}`);\n    }\n  }\n\n  get duration(): HrTime {\n    return this._duration;\n  }\n\n  get ended(): boolean {\n    return this._ended;\n  }\n\n  get droppedAttributesCount(): number {\n    return this._droppedAttributesCount;\n  }\n\n  get droppedEventsCount(): number {\n    return this._droppedEventsCount;\n  }\n\n  get droppedLinksCount(): number {\n    return this._droppedLinksCount;\n  }\n\n  private _isSpanEnded(): boolean {\n    if (this._ended) {\n      diag.warn(\n        `Can not execute the operation on ended Span {traceId: ${this._spanContext.traceId}, spanId: ${this._spanContext.spanId}}`\n      );\n    }\n    return this._ended;\n  }\n\n  // Utility function to truncate given value within size\n  // for value type of string, will truncate to given limit\n  // for type of non-string, will return same value\n  private _truncateToLimitUtil(value: string, limit: number): string {\n    if (value.length <= limit) {\n      return value;\n    }\n    return value.substring(0, limit);\n  }\n\n  /**\n   * If the given attribute value is of type string and has more characters than given {@code attributeValueLengthLimit} then\n   * return string with truncated to {@code attributeValueLengthLimit} characters\n   *\n   * If the given attribute value is array of strings then\n   * return new array of strings with each element truncated to {@code attributeValueLengthLimit} characters\n   *\n   * Otherwise return same Attribute {@code value}\n   *\n   * @param value Attribute value\n   * @returns truncated attribute value if required, otherwise same value\n   */\n  private _truncateToSize(value: SpanAttributeValue): SpanAttributeValue {\n    const limit = this._attributeValueLengthLimit;\n    // Check limit\n    if (limit <= 0) {\n      // Negative values are invalid, so do not truncate\n      diag.warn(`Attribute value limit must be positive, got ${limit}`);\n      return value;\n    }\n\n    // String\n    if (typeof value === 'string') {\n      return this._truncateToLimitUtil(value, limit);\n    }\n\n    // Array of strings\n    if (Array.isArray(value)) {\n      return (value as []).map(val =>\n        typeof val === 'string' ? this._truncateToLimitUtil(val, limit) : val\n      );\n    }\n\n    // Other types, no need to apply value length limit\n    return value;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAEL,IAAI,EAUJ,cAAc,GAEf,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EACL,UAAU,EACV,cAAc,EACd,aAAa,EACb,MAAM,EACN,cAAc,EAEd,gBAAgB,EAChB,WAAW,EACX,iBAAiB,EACjB,aAAa,EACb,kBAAkB,GACnB,MAAM,qBAAqB,CAAC;;;AAE7B,OAAO,EACL,0BAA0B,EAC1B,6BAA6B,EAC7B,uBAAuB,GACxB,MAAM,qCAAqC,CAAC;AAC7C,OAAO,EAAE,kBAAkB,EAAE,MAAM,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C;;GAEG,CACH,IAAA,OAAA;IAgCE;;;;SAIK,CACL,SAAA,KACE,YAAoB,EACpB,OAAgB,EAChB,QAAgB,EAChB,WAAwB,EACxB,IAAc,EACd,YAAqB,EACrB,KAAkB,EAClB,SAAqB,EACrB,gBAA0B,EAAE,AAC5B,UAA2B,yEADoF;QAF/G,IAAA,UAAA,KAAA,GAAA;YAAA,QAAA,EAAkB;QAAA;QAtCX,IAAA,CAAA,UAAU,GAAmB,CAAA,CAAE,CAAC;QAChC,IAAA,CAAA,KAAK,GAAW,EAAE,CAAC;QACnB,IAAA,CAAA,MAAM,GAAiB,EAAE,CAAC;QAK3B,IAAA,CAAA,uBAAuB,GAAG,CAAC,CAAC;QAC5B,IAAA,CAAA,mBAAmB,GAAW,CAAC,CAAC;QAChC,IAAA,CAAA,kBAAkB,GAAW,CAAC,CAAC;QAGvC,IAAA,CAAA,MAAM,GAAe;YACnB,IAAI,gMAAE,iBAAc,CAAC,KAAK;SAC3B,CAAC;QACF,IAAA,CAAA,OAAO,GAAW;YAAC,CAAC;YAAE,CAAC;SAAC,CAAC;QACjB,IAAA,CAAA,MAAM,GAAG,KAAK,CAAC;QACf,IAAA,CAAA,SAAS,GAAW;YAAC,CAAC,CAAC;YAAE,CAAC,CAAC;SAAC,CAAC;QA0BnC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;QACrB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,qBAAqB,gTAAG,gBAAa,CAAC,GAAG,EAAE,CAAC;QACjD,IAAI,CAAC,kBAAkB,GACrB,GAAG,GAAG,CAAC,IAAI,CAAC,qBAAqB,mSAAG,gBAAA,AAAa,GAAE,CAAC,CAAC;QACvD,IAAI,CAAC,kBAAkB,GAAG,SAAS,IAAI,IAAI,CAAC;QAE5C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,KAAA,QAAT,SAAS,KAAA,KAAA,IAAT,SAAS,GAAI,GAAG,CAAC,CAAC;QAEjD,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;QACtC,IAAI,CAAC,sBAAsB,GAAG,YAAY,CAAC,sBAAsB,CAAC;QAClE,IAAI,CAAC,WAAW,GAAG,YAAY,CAAC,aAAa,EAAE,CAAC;QAChD,IAAI,CAAC,0BAA0B,GAC7B,IAAI,CAAC,WAAW,CAAC,yBAAyB,IAAI,CAAC,CAAC;QAElD,IAAI,UAAU,IAAI,IAAI,EAAE;YACtB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;SAChC;QAED,IAAI,CAAC,cAAc,GAAG,YAAY,CAAC,sBAAsB,EAAE,CAAC;QAC5D,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED,KAAA,SAAA,CAAA,WAAW,GAAX;QACE,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAGD,KAAA,SAAA,CAAA,YAAY,GAAZ,SAAa,GAAW,EAAE,KAAc;QACtC,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE,OAAO,IAAI,CAAC;QACtD,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;0MACpB,OAAI,CAAC,IAAI,CAAC,4BAA0B,GAAK,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC;SACb;QACD,IAAI,uSAAC,mBAAA,AAAgB,EAAC,KAAK,CAAC,EAAE;0MAC5B,OAAI,CAAC,IAAI,CAAC,0CAAwC,GAAK,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC;SACb;QAED,IACE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,IACjC,IAAI,CAAC,WAAW,CAAC,mBAAoB,IACvC,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,EAC3D;YACA,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAA,SAAA,CAAA,aAAa,GAAb,SAAc,UAA0B;;;YACtC,IAAqB,IAAA,KAAA,SAAA,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;gBAAtC,IAAA,KAAA,OAAA,GAAA,KAAA,EAAA,EAAM,EAAL,CAAC,GAAA,EAAA,CAAA,EAAA,EAAE,CAAC,GAAA,EAAA,CAAA,EAAA;gBACd,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACzB;;;;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG,CACH,KAAA,SAAA,CAAA,QAAQ,GAAR,SACE,IAAY,EACZ,qBAAkD,EAClD,SAAqB;QAErB,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE,OAAO,IAAI,CAAC;QACrC,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,KAAK,CAAC,EAAE;0MAC1C,OAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAChC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC;SACb;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,eAAgB,EAAE;YAC3D,IAAI,IAAI,CAAC,mBAAmB,KAAK,CAAC,EAAE;8MAClC,OAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;aACtC;YACD,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACpB,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC5B;QAED,oSAAI,cAAA,AAAW,EAAC,qBAAqB,CAAC,EAAE;YACtC,IAAI,CAAC,8SAAA,AAAW,EAAC,SAAS,CAAC,EAAE;gBAC3B,SAAS,GAAG,qBAAqB,CAAC;aACnC;YACD,qBAAqB,GAAG,SAAS,CAAC;SACnC;QAED,IAAM,UAAU,ySAAG,qBAAA,AAAkB,EAAC,qBAAqB,CAAC,CAAC;QAE7D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf,IAAI,EAAA,IAAA;YACJ,UAAU,EAAA,UAAA;YACV,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC9B,sBAAsB,EAAE,CAAC;SAC1B,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,IAAU;QAChB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAA,SAAA,CAAA,QAAQ,GAAR,SAAS,KAAa;;QACpB,CAAA,KAAA,IAAI,CAAC,KAAK,CAAA,CAAC,IAAI,CAAA,KAAA,CAAA,IAAA,cAAA,EAAA,EAAA,OAAI,KAAK,GAAA,QAAE;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAA,SAAA,CAAA,SAAS,GAAT,SAAU,MAAkB;QAC1B,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE,OAAO,IAAI,CAAC;QACrC,IAAI,CAAC,MAAM,GAAA,SAAA,CAAA,GAAQ,MAAM,CAAE,CAAC;QAE5B,4GAA4G;QAC5G,gHAAgH;QAChH,kHAAkH;QAClH,kEAAkE;QAClE,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,IAAI,OAAO,MAAM,CAAC,OAAO,KAAK,QAAQ,EAAE;0MACrE,OAAI,CAAC,IAAI,CACP,8CAA4C,OAAO,MAAM,CAAC,OAAO,GAAA,sBAAsB,CACxF,CAAC;YACF,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;SAC5B;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAA,SAAA,CAAA,UAAU,GAAV,SAAW,IAAY;QACrB,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE,OAAO,IAAI,CAAC;QACrC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAA,SAAA,CAAA,GAAG,GAAH,SAAI,OAAmB;QACrB,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;0MACvB,OAAI,CAAC,KAAK,CACL,IAAI,CAAC,IAAI,GAAA,MAAI,IAAI,CAAC,YAAY,CAAC,OAAO,GAAA,MAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAA,4CAA4C,CAClH,CAAC;YACF,OAAO;SACR;QACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAEnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACtC,IAAI,CAAC,SAAS,mSAAG,iBAAA,AAAc,EAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;0MACzB,OAAI,CAAC,IAAI,CACP,qFAAqF,EACrF,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,OAAO,CACb,CAAC;YACF,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAY,CAAC;YAChD,IAAI,CAAC,SAAS,GAAG;gBAAC,CAAC;gBAAE,CAAC;aAAC,CAAC;SACzB;QAED,IAAI,IAAI,CAAC,mBAAmB,GAAG,CAAC,EAAE;0MAChC,OAAI,CAAC,IAAI,CACP,aAAW,IAAI,CAAC,mBAAmB,GAAA,yCAAyC,CAC7E,CAAC;SACH;QAED,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAEO,KAAA,SAAA,CAAA,QAAQ,GAAhB,SAAiB,GAAe;QAC9B,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,iTAAI,gBAAa,CAAC,GAAG,EAAE,EAAE;YACzD,kCAAkC;YAClC,yCAAyC;YACzC,uSAAO,SAAA,AAAM,EAAC,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC9C;QAED,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,uSAAO,iBAAA,AAAc,EAAC,GAAG,CAAC,CAAC;SAC5B;QAED,IAAI,GAAG,YAAY,IAAI,EAAE;YACvB,uSAAO,iBAAA,AAAc,EAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;SACtC;QAED,oSAAI,oBAAA,AAAiB,EAAC,GAAG,CAAC,EAAE;YAC1B,OAAO,GAAG,CAAC;SACZ;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,iDAAiD;YACjD,qDAAqD;YACrD,uSAAO,iBAAA,AAAc,EAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;SACnC;QAED,IAAM,UAAU,+SAAG,iBAAa,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACpE,uSAAO,aAAA,AAAU,EAAC,IAAI,CAAC,SAAS,EAAE,iTAAc,AAAd,EAAe,UAAU,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,KAAA,SAAA,CAAA,WAAW,GAAX;QACE,OAAO,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC;IAC/B,CAAC;IAED,KAAA,SAAA,CAAA,eAAe,GAAf,SAAgB,SAAoB,EAAE,IAAgB;QACpD,IAAM,UAAU,GAAmB,CAAA,CAAE,CAAC;QACtC,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YACjC,UAAU,wSAAC,6BAA0B,CAAC,GAAG,SAAS,CAAC;SACpD,MAAM,IAAI,SAAS,EAAE;YACpB,IAAI,SAAS,CAAC,IAAI,EAAE;gBAClB,UAAU,wSAAC,0BAAuB,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;aACjE,MAAM,IAAI,SAAS,CAAC,IAAI,EAAE;gBACzB,UAAU,wSAAC,0BAAuB,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC;aACtD;YACD,IAAI,SAAS,CAAC,OAAO,EAAE;gBACrB,UAAU,wSAAC,6BAA0B,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC;aAC5D;YACD,IAAI,SAAS,CAAC,KAAK,EAAE;gBACnB,UAAU,wSAAC,gCAA6B,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;aAC7D;SACF;QAED,2CAA2C;QAC3C,IACE,UAAU,wSAAC,0BAAuB,CAAC,IACnC,UAAU,wSAAC,6BAA0B,CAAC,EACtC;YACA,IAAI,CAAC,QAAQ,0RAAC,sBAAkB,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;SACrD,MAAM;0MACL,OAAI,CAAC,IAAI,CAAC,mCAAiC,SAAW,CAAC,CAAC;SACzD;IACH,CAAC;IAED,OAAA,cAAA,CAAI,KAAA,SAAA,EAAA,UAAQ,EAAA;aAAZ;YACE,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;;;OAAA;IAED,OAAA,cAAA,CAAI,KAAA,SAAA,EAAA,OAAK,EAAA;aAAT;YACE,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;;;OAAA;IAED,OAAA,cAAA,CAAI,KAAA,SAAA,EAAA,wBAAsB,EAAA;aAA1B;YACE,OAAO,IAAI,CAAC,uBAAuB,CAAC;QACtC,CAAC;;;OAAA;IAED,OAAA,cAAA,CAAI,KAAA,SAAA,EAAA,oBAAkB,EAAA;aAAtB;YACE,OAAO,IAAI,CAAC,mBAAmB,CAAC;QAClC,CAAC;;;OAAA;IAED,OAAA,cAAA,CAAI,KAAA,SAAA,EAAA,mBAAiB,EAAA;aAArB;YACE,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACjC,CAAC;;;OAAA;IAEO,KAAA,SAAA,CAAA,YAAY,GAApB;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,qMAAI,CAAC,IAAI,CACP,2DAAyD,IAAI,CAAC,YAAY,CAAC,OAAO,GAAA,eAAa,IAAI,CAAC,YAAY,CAAC,MAAM,GAAA,GAAG,CAC3H,CAAC;SACH;QACD,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,uDAAuD;IACvD,yDAAyD;IACzD,iDAAiD;IACzC,KAAA,SAAA,CAAA,oBAAoB,GAA5B,SAA6B,KAAa,EAAE,KAAa;QACvD,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,EAAE;YACzB,OAAO,KAAK,CAAC;SACd;QACD,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACnC,CAAC;IAED;;;;;;;;;;;OAWG,CACK,KAAA,SAAA,CAAA,eAAe,GAAvB,SAAwB,KAAyB;QAAjD,IAAA,QAAA,IAAA,CAuBC;QAtBC,IAAM,KAAK,GAAG,IAAI,CAAC,0BAA0B,CAAC;QAC9C,cAAc;QACd,IAAI,KAAK,IAAI,CAAC,EAAE;YACd,kDAAkD;0MAClD,OAAI,CAAC,IAAI,CAAC,iDAA+C,KAAO,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC;SACd;QAED,SAAS;QACT,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SAChD;QAED,mBAAmB;QACnB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,OAAQ,KAAY,CAAC,GAAG,CAAC,SAAA,GAAG;gBAC1B,OAAA,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG;YAArE,CAAqE,CACtE,CAAC;SACH;QAED,mDAAmD;QACnD,OAAO,KAAK,CAAC;IACf,CAAC;IACH,OAAA,IAAC;AAAD,CAAC,AAtWD,IAsWC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "file": "Sampler.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/Sampler.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  Link,\n  SpanAttributes,\n  SpanKind,\n  TraceState,\n} from '@opentelemetry/api';\n\n/**\n * A sampling decision that determines how a {@link Span} will be recorded\n * and collected.\n */\nexport enum SamplingDecision {\n  /**\n   * `Span.isRecording() === false`, span will not be recorded and all events\n   * and attributes will be dropped.\n   */\n  NOT_RECORD,\n  /**\n   * `Span.isRecording() === true`, but `Sampled` flag in {@link TraceFlags}\n   * MUST NOT be set.\n   */\n  RECORD,\n  /**\n   * `Span.isRecording() === true` AND `Sampled` flag in {@link TraceFlags}\n   * MUST be set.\n   */\n  RECORD_AND_SAMPLED,\n}\n\n/**\n * A sampling result contains a decision for a {@link Span} and additional\n * attributes the sampler would like to added to the Span.\n */\nexport interface SamplingResult {\n  /**\n   * A sampling decision, refer to {@link SamplingDecision} for details.\n   */\n  decision: SamplingDecision;\n  /**\n   * The list of attributes returned by SamplingResult MUST be immutable.\n   * Caller may call {@link Sampler}.shouldSample any number of times and\n   * can safely cache the returned value.\n   */\n  attributes?: Readonly<SpanAttributes>;\n  /**\n   * A {@link TraceState} that will be associated with the {@link Span} through\n   * the new {@link SpanContext}. Samplers SHOULD return the TraceState from\n   * the passed-in {@link Context} if they do not intend to change it. Leaving\n   * the value undefined will also leave the TraceState unchanged.\n   */\n  traceState?: TraceState;\n}\n\n/**\n * This interface represent a sampler. Sampling is a mechanism to control the\n * noise and overhead introduced by OpenTelemetry by reducing the number of\n * samples of traces collected and sent to the backend.\n */\nexport interface Sampler {\n  /**\n   * Checks whether span needs to be created and tracked.\n   *\n   * @param context Parent Context which may contain a span.\n   * @param traceId of the span to be created. It can be different from the\n   *     traceId in the {@link SpanContext}. Typically in situations when the\n   *     span to be created starts a new trace.\n   * @param spanName of the span to be created.\n   * @param spanKind of the span to be created.\n   * @param attributes Initial set of SpanAttributes for the Span being constructed.\n   * @param links Collection of links that will be associated with the Span to\n   *     be created. Typically useful for batch operations.\n   * @returns a {@link SamplingResult}.\n   */\n  shouldSample(\n    context: Context,\n    traceId: string,\n    spanName: string,\n    spanKind: SpanKind,\n    attributes: SpanAttributes,\n    links: Link[]\n  ): SamplingResult;\n\n  /** Returns the sampler name or short description with the configuration. */\n  toString(): string;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAUH;;;GAGG;;;AACH,IAAY,gBAgBX;AAhBD,CAAA,SAAY,gBAAgB;IAC1B;;;OAGG,CACH,gBAAA,CAAA,gBAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAU,CAAA;IACV;;;OAGG,CACH,gBAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAM,CAAA;IACN;;;OAGG,CACH,gBAAA,CAAA,gBAAA,CAAA,qBAAA,GAAA,EAAA,GAAA,oBAAkB,CAAA;AACpB,CAAC,EAhBW,gBAAgB,IAAA,CAAhB,gBAAgB,GAAA,CAAA,CAAA,GAgB3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "file": "AlwaysOffSampler.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/sampler/AlwaysOffSampler.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Sampler, SamplingDecision, SamplingResult } from '../Sampler';\n\n/** Sampler that samples no traces. */\nexport class AlwaysOffSampler implements Sampler {\n  shouldSample(): SamplingResult {\n    return {\n      decision: SamplingDecision.NOT_RECORD,\n    };\n  }\n\n  toString(): string {\n    return 'AlwaysOffSampler';\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAW,gBAAgB,EAAkB,MAAM,YAAY,CAAC;;AAEvE,oCAAA,EAAsC,CACtC,IAAA,mBAAA;IAAA,SAAA,oBAUA,CAAC;IATC,iBAAA,SAAA,CAAA,YAAY,GAAZ;QACE,OAAO;YACL,QAAQ,8RAAE,mBAAgB,CAAC,UAAU;SACtC,CAAC;IACJ,CAAC;IAED,iBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IACH,OAAA,gBAAC;AAAD,CAAC,AAVD,IAUC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "file": "AlwaysOnSampler.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/sampler/AlwaysOnSampler.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Sampler, SamplingDecision, SamplingResult } from '../Sampler';\n\n/** Sampler that samples all traces. */\nexport class AlwaysOnSampler implements Sampler {\n  shouldSample(): SamplingResult {\n    return {\n      decision: SamplingDecision.RECORD_AND_SAMPLED,\n    };\n  }\n\n  toString(): string {\n    return 'AlwaysOnSampler';\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAW,gBAAgB,EAAkB,MAAM,YAAY,CAAC;;AAEvE,qCAAA,EAAuC,CACvC,IAAA,kBAAA;IAAA,SAAA,mBAUA,CAAC;IATC,gBAAA,SAAA,CAAA,YAAY,GAAZ;QACE,OAAO;YACL,QAAQ,8RAAE,mBAAgB,CAAC,kBAAkB;SAC9C,CAAC;IACJ,CAAC;IAED,gBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IACH,OAAA,eAAC;AAAD,CAAC,AAVD,IAUC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "file": "ParentBasedSampler.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/sampler/ParentBasedSampler.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  isSpanContextValid,\n  Link,\n  SpanAttributes,\n  SpanKind,\n  TraceFlags,\n  trace,\n} from '@opentelemetry/api';\nimport { globalErrorHandler } from '@opentelemetry/core';\nimport { AlwaysOffSampler } from './AlwaysOffSampler';\nimport { AlwaysOnSampler } from './AlwaysOnSampler';\nimport { Sampler, SamplingResult } from '../Sampler';\n\n/**\n * A composite sampler that either respects the parent span's sampling decision\n * or delegates to `delegateSampler` for root spans.\n */\nexport class ParentBasedSampler implements Sampler {\n  private _root: Sampler;\n  private _remoteParentSampled: Sampler;\n  private _remoteParentNotSampled: Sampler;\n  private _localParentSampled: Sampler;\n  private _localParentNotSampled: Sampler;\n\n  constructor(config: ParentBasedSamplerConfig) {\n    this._root = config.root;\n\n    if (!this._root) {\n      globalErrorHandler(\n        new Error('ParentBasedSampler must have a root sampler configured')\n      );\n      this._root = new AlwaysOnSampler();\n    }\n\n    this._remoteParentSampled =\n      config.remoteParentSampled ?? new AlwaysOnSampler();\n    this._remoteParentNotSampled =\n      config.remoteParentNotSampled ?? new AlwaysOffSampler();\n    this._localParentSampled =\n      config.localParentSampled ?? new AlwaysOnSampler();\n    this._localParentNotSampled =\n      config.localParentNotSampled ?? new AlwaysOffSampler();\n  }\n\n  shouldSample(\n    context: Context,\n    traceId: string,\n    spanName: string,\n    spanKind: SpanKind,\n    attributes: SpanAttributes,\n    links: Link[]\n  ): SamplingResult {\n    const parentContext = trace.getSpanContext(context);\n\n    if (!parentContext || !isSpanContextValid(parentContext)) {\n      return this._root.shouldSample(\n        context,\n        traceId,\n        spanName,\n        spanKind,\n        attributes,\n        links\n      );\n    }\n\n    if (parentContext.isRemote) {\n      if (parentContext.traceFlags & TraceFlags.SAMPLED) {\n        return this._remoteParentSampled.shouldSample(\n          context,\n          traceId,\n          spanName,\n          spanKind,\n          attributes,\n          links\n        );\n      }\n      return this._remoteParentNotSampled.shouldSample(\n        context,\n        traceId,\n        spanName,\n        spanKind,\n        attributes,\n        links\n      );\n    }\n\n    if (parentContext.traceFlags & TraceFlags.SAMPLED) {\n      return this._localParentSampled.shouldSample(\n        context,\n        traceId,\n        spanName,\n        spanKind,\n        attributes,\n        links\n      );\n    }\n\n    return this._localParentNotSampled.shouldSample(\n      context,\n      traceId,\n      spanName,\n      spanKind,\n      attributes,\n      links\n    );\n  }\n\n  toString(): string {\n    return `ParentBased{root=${this._root.toString()}, remoteParentSampled=${this._remoteParentSampled.toString()}, remoteParentNotSampled=${this._remoteParentNotSampled.toString()}, localParentSampled=${this._localParentSampled.toString()}, localParentNotSampled=${this._localParentNotSampled.toString()}}`;\n  }\n}\n\ninterface ParentBasedSamplerConfig {\n  /** Sampler called for spans with no parent */\n  root: Sampler;\n  /** Sampler called for spans with a remote parent which was sampled. Default AlwaysOn */\n  remoteParentSampled?: Sampler;\n  /** Sampler called for spans with a remote parent which was not sampled. Default AlwaysOff */\n  remoteParentNotSampled?: Sampler;\n  /** Sampler called for spans with a local parent which was sampled. Default AlwaysOn */\n  localParentSampled?: Sampler;\n  /** Sampler called for spans with a local parent which was not sampled. Default AlwaysOff */\n  localParentNotSampled?: Sampler;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAEL,kBAAkB,EAIlB,UAAU,EACV,KAAK,GACN,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;AACzD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;;;;;AAGpD;;;GAGG,CACH,IAAA,qBAAA;IAOE,SAAA,mBAAY,MAAgC;;QAC1C,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;QAEzB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;kUACf,qBAAA,AAAkB,EAChB,IAAI,KAAK,CAAC,wDAAwD,CAAC,CACpE,CAAC;YACF,IAAI,CAAC,KAAK,GAAG,mTAAI,kBAAe,EAAE,CAAC;SACpC;QAED,IAAI,CAAC,oBAAoB,GACvB,CAAA,KAAA,MAAM,CAAC,mBAAmB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,mTAAI,kBAAe,EAAE,CAAC;QACtD,IAAI,CAAC,uBAAuB,GAC1B,CAAA,KAAA,MAAM,CAAC,sBAAsB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,oTAAI,mBAAgB,EAAE,CAAC;QAC1D,IAAI,CAAC,mBAAmB,GACtB,CAAA,KAAA,MAAM,CAAC,kBAAkB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,mTAAI,kBAAe,EAAE,CAAC;QACrD,IAAI,CAAC,sBAAsB,GACzB,CAAA,KAAA,MAAM,CAAC,qBAAqB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,oTAAI,mBAAgB,EAAE,CAAC;IAC3D,CAAC;IAED,mBAAA,SAAA,CAAA,YAAY,GAAZ,SACE,OAAgB,EAChB,OAAe,EACf,QAAgB,EAChB,QAAkB,EAClB,UAA0B,EAC1B,KAAa;QAEb,IAAM,aAAa,gMAAG,SAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAEpD,IAAI,CAAC,aAAa,IAAI,mMAAC,qBAAA,AAAkB,EAAC,aAAa,CAAC,EAAE;YACxD,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAC5B,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,KAAK,CACN,CAAC;SACH;QAED,IAAI,aAAa,CAAC,QAAQ,EAAE;YAC1B,IAAI,aAAa,CAAC,UAAU,iMAAG,aAAU,CAAC,OAAO,EAAE;gBACjD,OAAO,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAC3C,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,KAAK,CACN,CAAC;aACH;YACD,OAAO,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAC9C,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,KAAK,CACN,CAAC;SACH;QAED,IAAI,aAAa,CAAC,UAAU,iMAAG,aAAU,CAAC,OAAO,EAAE;YACjD,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAC1C,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,KAAK,CACN,CAAC;SACH;QAED,OAAO,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAC7C,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,KAAK,CACN,CAAC;IACJ,CAAC;IAED,mBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,sBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAA,2BAAyB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,GAAA,8BAA4B,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,GAAA,0BAAwB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,GAAA,6BAA2B,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,GAAA,GAAG,CAAC;IAClT,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AA7FD,IA6FC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 658, "column": 0}, "map": {"version": 3, "file": "TraceIdRatioBasedSampler.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/sampler/TraceIdRatioBasedSampler.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { isValidTraceId } from '@opentelemetry/api';\nimport { Sampler, SamplingDecision, SamplingResult } from '../Sampler';\n\n/** Sampler that samples a given fraction of traces based of trace id deterministically. */\nexport class TraceIdRatioBasedSampler implements Sampler {\n  private _upperBound: number;\n\n  constructor(private readonly _ratio: number = 0) {\n    this._ratio = this._normalize(_ratio);\n    this._upperBound = Math.floor(this._ratio * 0xffffffff);\n  }\n\n  shouldSample(context: unknown, traceId: string): SamplingResult {\n    return {\n      decision:\n        isValidTraceId(traceId) && this._accumulate(traceId) < this._upperBound\n          ? SamplingDecision.RECORD_AND_SAMPLED\n          : SamplingDecision.NOT_RECORD,\n    };\n  }\n\n  toString(): string {\n    return `TraceIdRatioBased{${this._ratio}}`;\n  }\n\n  private _normalize(ratio: number): number {\n    if (typeof ratio !== 'number' || isNaN(ratio)) return 0;\n    return ratio >= 1 ? 1 : ratio <= 0 ? 0 : ratio;\n  }\n\n  private _accumulate(traceId: string): number {\n    let accumulation = 0;\n    for (let i = 0; i < traceId.length / 8; i++) {\n      const pos = i * 8;\n      const part = parseInt(traceId.slice(pos, pos + 8), 16);\n      accumulation = (accumulation ^ part) >>> 0;\n    }\n    return accumulation;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,cAAc,EAAE,MAAM,oBAAoB,CAAC;AACpD,OAAO,EAAW,gBAAgB,EAAkB,MAAM,YAAY,CAAC;;;AAEvE,yFAAA,EAA2F,CAC3F,IAAA,2BAAA;IAGE,SAAA,yBAA6B,MAAkB;QAAlB,IAAA,WAAA,KAAA,GAAA;YAAA,SAAA,CAAkB;QAAA;QAAlB,IAAA,CAAA,MAAM,GAAN,MAAM,CAAY;QAC7C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED,yBAAA,SAAA,CAAA,YAAY,GAAZ,SAAa,OAAgB,EAAE,OAAe;QAC5C,OAAO;YACL,QAAQ,oMACN,iBAAA,AAAc,EAAC,OAAO,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,WAAW,+RACnE,mBAAgB,CAAC,kBAAkB,+RACnC,mBAAgB,CAAC,UAAU;SAClC,CAAC;IACJ,CAAC;IAED,yBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,uBAAqB,IAAI,CAAC,MAAM,GAAA,GAAG,CAAC;IAC7C,CAAC;IAEO,yBAAA,SAAA,CAAA,UAAU,GAAlB,SAAmB,KAAa;QAC9B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;QACxD,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACjD,CAAC;IAEO,yBAAA,SAAA,CAAA,WAAW,GAAnB,SAAoB,OAAe;QACjC,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;YAC3C,IAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YAClB,IAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACvD,YAAY,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;SAC5C;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;IACH,OAAA,wBAAC;AAAD,CAAC,AAnCD,IAmCC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/config.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { getEnv, TracesSamplerValues, ENVIRONMENT } from '@opentelemetry/core';\nimport { Sampler } from './Sampler';\nimport { AlwaysOffSampler } from './sampler/AlwaysOffSampler';\nimport { AlwaysOnSampler } from './sampler/AlwaysOnSampler';\nimport { ParentBasedSampler } from './sampler/ParentBasedSampler';\nimport { TraceIdRatioBasedSampler } from './sampler/TraceIdRatioBasedSampler';\n\nconst FALLBACK_OTEL_TRACES_SAMPLER = TracesSamplerValues.AlwaysOn;\nconst DEFAULT_RATIO = 1;\n\n/**\n * Load default configuration. For fields with primitive values, any user-provided\n * value will override the corresponding default value. For fields with\n * non-primitive values (like `spanLimits`), the user-provided value will be\n * used to extend the default value.\n */\n\n// object needs to be wrapped in this function and called when needed otherwise\n// envs are parsed before tests are ran - causes tests using these envs to fail\nexport function loadDefaultConfig() {\n  const env = getEnv();\n\n  return {\n    sampler: buildSamplerFromEnv(env),\n    forceFlushTimeoutMillis: 30000,\n    generalLimits: {\n      attributeValueLengthLimit: env.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n      attributeCountLimit: env.OTEL_ATTRIBUTE_COUNT_LIMIT,\n    },\n    spanLimits: {\n      attributeValueLengthLimit: env.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n      attributeCountLimit: env.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT,\n      linkCountLimit: env.OTEL_SPAN_LINK_COUNT_LIMIT,\n      eventCountLimit: env.OTEL_SPAN_EVENT_COUNT_LIMIT,\n      attributePerEventCountLimit:\n        env.OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,\n      attributePerLinkCountLimit: env.OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT,\n    },\n    mergeResourceWithDefaults: true,\n  };\n}\n\n/**\n * Based on environment, builds a sampler, complies with specification.\n * @param environment optional, by default uses getEnv(), but allows passing a value to reuse parsed environment\n */\nexport function buildSamplerFromEnv(\n  environment: Required<ENVIRONMENT> = getEnv()\n): Sampler {\n  switch (environment.OTEL_TRACES_SAMPLER) {\n    case TracesSamplerValues.AlwaysOn:\n      return new AlwaysOnSampler();\n    case TracesSamplerValues.AlwaysOff:\n      return new AlwaysOffSampler();\n    case TracesSamplerValues.ParentBasedAlwaysOn:\n      return new ParentBasedSampler({\n        root: new AlwaysOnSampler(),\n      });\n    case TracesSamplerValues.ParentBasedAlwaysOff:\n      return new ParentBasedSampler({\n        root: new AlwaysOffSampler(),\n      });\n    case TracesSamplerValues.TraceIdRatio:\n      return new TraceIdRatioBasedSampler(\n        getSamplerProbabilityFromEnv(environment)\n      );\n    case TracesSamplerValues.ParentBasedTraceIdRatio:\n      return new ParentBasedSampler({\n        root: new TraceIdRatioBasedSampler(\n          getSamplerProbabilityFromEnv(environment)\n        ),\n      });\n    default:\n      diag.error(\n        `OTEL_TRACES_SAMPLER value \"${environment.OTEL_TRACES_SAMPLER} invalid, defaulting to ${FALLBACK_OTEL_TRACES_SAMPLER}\".`\n      );\n      return new AlwaysOnSampler();\n  }\n}\n\nfunction getSamplerProbabilityFromEnv(\n  environment: Required<ENVIRONMENT>\n): number | undefined {\n  if (\n    environment.OTEL_TRACES_SAMPLER_ARG === undefined ||\n    environment.OTEL_TRACES_SAMPLER_ARG === ''\n  ) {\n    diag.error(\n      `OTEL_TRACES_SAMPLER_ARG is blank, defaulting to ${DEFAULT_RATIO}.`\n    );\n    return DEFAULT_RATIO;\n  }\n\n  const probability = Number(environment.OTEL_TRACES_SAMPLER_ARG);\n\n  if (isNaN(probability)) {\n    diag.error(\n      `OTEL_TRACES_SAMPLER_ARG=${environment.OTEL_TRACES_SAMPLER_ARG} was given, but it is invalid, defaulting to ${DEFAULT_RATIO}.`\n    );\n    return DEFAULT_RATIO;\n  }\n\n  if (probability < 0 || probability > 1) {\n    diag.error(\n      `OTEL_TRACES_SAMPLER_ARG=${environment.OTEL_TRACES_SAMPLER_ARG} was given, but it is out of range ([0..1]), defaulting to ${DEFAULT_RATIO}.`\n    );\n    return DEFAULT_RATIO;\n  }\n\n  return probability;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,MAAM,EAAE,mBAAmB,EAAe,MAAM,qBAAqB,CAAC;;AAE/E,OAAO,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAC9D,OAAO,EAAE,eAAe,EAAE,MAAM,2BAA2B,CAAC;AAC5D,OAAO,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAClE,OAAO,EAAE,wBAAwB,EAAE,MAAM,oCAAoC,CAAC;;;;;;;AAE9E,IAAM,4BAA4B,kSAAG,sBAAmB,CAAC,QAAQ,CAAC;AAClE,IAAM,aAAa,GAAG,CAAC,CAAC;AAWlB,SAAU,iBAAiB;IAC/B,IAAM,GAAG,GAAG,0TAAA,AAAM,EAAE,CAAC;IAErB,OAAO;QACL,OAAO,EAAE,mBAAmB,CAAC,GAAG,CAAC;QACjC,uBAAuB,EAAE,KAAK;QAC9B,aAAa,EAAE;YACb,yBAAyB,EAAE,GAAG,CAAC,iCAAiC;YAChE,mBAAmB,EAAE,GAAG,CAAC,0BAA0B;SACpD;QACD,UAAU,EAAE;YACV,yBAAyB,EAAE,GAAG,CAAC,sCAAsC;YACrE,mBAAmB,EAAE,GAAG,CAAC,+BAA+B;YACxD,cAAc,EAAE,GAAG,CAAC,0BAA0B;YAC9C,eAAe,EAAE,GAAG,CAAC,2BAA2B;YAChD,2BAA2B,EACzB,GAAG,CAAC,yCAAyC;YAC/C,0BAA0B,EAAE,GAAG,CAAC,wCAAwC;SACzE;QACD,yBAAyB,EAAE,IAAI;KAChC,CAAC;AACJ,CAAC;AAMK,SAAU,mBAAmB,CACjC,WAA6C;IAA7C,IAAA,gBAAA,KAAA,GAAA;QAAA,eAAqC,yTAAA,AAAM,EAAE;IAAA;IAE7C,OAAQ,WAAW,CAAC,mBAAmB,EAAE;QACvC,oSAAK,sBAAmB,CAAC,QAAQ;YAC/B,OAAO,IAAI,iUAAe,EAAE,CAAC;QAC/B,oSAAK,sBAAmB,CAAC,SAAS;YAChC,OAAO,oTAAI,mBAAgB,EAAE,CAAC;QAChC,oSAAK,sBAAmB,CAAC,mBAAmB;YAC1C,OAAO,sTAAI,qBAAkB,CAAC;gBAC5B,IAAI,EAAE,mTAAI,kBAAe,EAAE;aAC5B,CAAC,CAAC;QACL,mSAAK,uBAAmB,CAAC,oBAAoB;YAC3C,OAAO,sTAAI,qBAAkB,CAAC;gBAC5B,IAAI,EAAE,oTAAI,mBAAgB,EAAE;aAC7B,CAAC,CAAC;QACL,KAAK,qTAAmB,CAAC,YAAY;YACnC,OAAO,4TAAI,2BAAwB,CACjC,4BAA4B,CAAC,WAAW,CAAC,CAC1C,CAAC;QACJ,oSAAK,sBAAmB,CAAC,uBAAuB;YAC9C,OAAO,sTAAI,qBAAkB,CAAC;gBAC5B,IAAI,EAAE,4TAAI,2BAAwB,CAChC,4BAA4B,CAAC,WAAW,CAAC,CAC1C;aACF,CAAC,CAAC;QACL;YACE,qMAAI,CAAC,KAAK,CACR,iCAA8B,WAAW,CAAC,mBAAmB,GAAA,6BAA2B,4BAA4B,GAAA,KAAI,CACzH,CAAC;YACF,OAAO,mTAAI,kBAAe,EAAE,CAAC;KAChC;AACH,CAAC;AAED,SAAS,4BAA4B,CACnC,WAAkC;IAElC,IACE,WAAW,CAAC,uBAAuB,KAAK,SAAS,IACjD,WAAW,CAAC,uBAAuB,KAAK,EAAE,EAC1C;QACA,qMAAI,CAAC,KAAK,CACR,qDAAmD,aAAa,GAAA,GAAG,CACpE,CAAC;QACF,OAAO,aAAa,CAAC;KACtB;IAED,IAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC;IAEhE,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE;sMACtB,OAAI,CAAC,KAAK,CACR,6BAA2B,WAAW,CAAC,uBAAuB,GAAA,kDAAgD,aAAa,GAAA,GAAG,CAC/H,CAAC;QACF,OAAO,aAAa,CAAC;KACtB;IAED,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,CAAC,EAAE;sMACtC,OAAI,CAAC,KAAK,CACR,6BAA2B,WAAW,CAAC,uBAAuB,GAAA,gEAA8D,aAAa,GAAA,GAAG,CAC7I,CAAC;QACF,OAAO,aAAa,CAAC;KACtB;IAED,OAAO,WAAW,CAAC;AACrB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 822, "column": 0}, "map": {"version": 3, "file": "utility.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/utility.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { buildSamplerFromEnv, loadDefaultConfig } from './config';\nimport { Sampler } from './Sampler';\nimport { SpanLimits, TracerConfig, GeneralLimits } from './types';\nimport {\n  DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n  DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n  getEnvWithoutDefaults,\n} from '@opentelemetry/core';\n\n/**\n * Function to merge Default configuration (as specified in './config') with\n * user provided configurations.\n */\nexport function mergeConfig(userConfig: TracerConfig): TracerConfig & {\n  sampler: Sampler;\n  spanLimits: SpanLimits;\n  generalLimits: GeneralLimits;\n} {\n  const perInstanceDefaults: Partial<TracerConfig> = {\n    sampler: buildSamplerFromEnv(),\n  };\n\n  const DEFAULT_CONFIG = loadDefaultConfig();\n\n  const target = Object.assign(\n    {},\n    DEFAULT_CONFIG,\n    perInstanceDefaults,\n    userConfig\n  );\n\n  target.generalLimits = Object.assign(\n    {},\n    DEFAULT_CONFIG.generalLimits,\n    userConfig.generalLimits || {}\n  );\n\n  target.spanLimits = Object.assign(\n    {},\n    DEFAULT_CONFIG.spanLimits,\n    userConfig.spanLimits || {}\n  );\n\n  return target;\n}\n\n/**\n * When general limits are provided and model specific limits are not,\n * configures the model specific limits by using the values from the general ones.\n * @param userConfig User provided tracer configuration\n */\nexport function reconfigureLimits(userConfig: TracerConfig): TracerConfig {\n  const spanLimits = Object.assign({}, userConfig.spanLimits);\n\n  const parsedEnvConfig = getEnvWithoutDefaults();\n\n  /**\n   * Reassign span attribute count limit to use first non null value defined by user or use default value\n   */\n  spanLimits.attributeCountLimit =\n    userConfig.spanLimits?.attributeCountLimit ??\n    userConfig.generalLimits?.attributeCountLimit ??\n    parsedEnvConfig.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT ??\n    parsedEnvConfig.OTEL_ATTRIBUTE_COUNT_LIMIT ??\n    DEFAULT_ATTRIBUTE_COUNT_LIMIT;\n\n  /**\n   * Reassign span attribute value length limit to use first non null value defined by user or use default value\n   */\n  spanLimits.attributeValueLengthLimit =\n    userConfig.spanLimits?.attributeValueLengthLimit ??\n    userConfig.generalLimits?.attributeValueLengthLimit ??\n    parsedEnvConfig.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT ??\n    parsedEnvConfig.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT ??\n    DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT;\n\n  return Object.assign({}, userConfig, { spanLimits });\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,OAAO,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,MAAM,UAAU,CAAC;AAGlE,OAAO,EACL,6BAA6B,EAC7B,oCAAoC,EACpC,qBAAqB,GACtB,MAAM,qBAAqB,CAAC;;;;AAMvB,SAAU,WAAW,CAAC,UAAwB;IAKlD,IAAM,mBAAmB,GAA0B;QACjD,OAAO,iSAAE,sBAAA,AAAmB,EAAE;KAC/B,CAAC;IAEF,IAAM,cAAc,iSAAG,qBAAA,AAAiB,EAAE,CAAC;IAE3C,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAC1B,CAAA,CAAE,EACF,cAAc,EACd,mBAAmB,EACnB,UAAU,CACX,CAAC;IAEF,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAClC,CAAA,CAAE,EACF,cAAc,CAAC,aAAa,EAC5B,UAAU,CAAC,aAAa,IAAI,CAAA,CAAE,CAC/B,CAAC;IAEF,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,CAC/B,CAAA,CAAE,EACF,cAAc,CAAC,UAAU,EACzB,UAAU,CAAC,UAAU,IAAI,CAAA,CAAE,CAC5B,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC;AAOK,SAAU,iBAAiB,CAAC,UAAwB;;IACxD,IAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;IAE5D,IAAM,eAAe,oTAAG,wBAAA,AAAqB,EAAE,CAAC;IAEhD;;OAEG,CACH,UAAU,CAAC,mBAAmB,GAC5B,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,UAAU,CAAC,UAAU,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,mBAAmB,MAAA,QAAA,OAAA,KAAA,IAAA,KAC1C,CAAA,KAAA,UAAU,CAAC,aAAa,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,mBAAmB,MAAA,QAAA,OAAA,KAAA,IAAA,KAC7C,eAAe,CAAC,+BAA+B,MAAA,QAAA,OAAA,KAAA,IAAA,KAC/C,eAAe,CAAC,0BAA0B,MAAA,QAAA,OAAA,KAAA,IAAA,sSAC1C,iCAA6B,CAAC;IAEhC;;OAEG,CACH,UAAU,CAAC,yBAAyB,GAClC,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,UAAU,CAAC,UAAU,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,yBAAyB,MAAA,QAAA,OAAA,KAAA,IAAA,KAChD,CAAA,KAAA,UAAU,CAAC,aAAa,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,yBAAyB,MAAA,QAAA,OAAA,KAAA,IAAA,KACnD,eAAe,CAAC,sCAAsC,MAAA,QAAA,OAAA,KAAA,IAAA,KACtD,eAAe,CAAC,iCAAiC,MAAA,QAAA,OAAA,KAAA,IAAA,uSACjD,uCAAoC,CAAC;IAEvC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,UAAU,EAAE;QAAE,UAAU,EAAA,UAAA;IAAA,CAAE,CAAC,CAAC;AACvD,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 875, "column": 0}, "map": {"version": 3, "file": "RandomIdGenerator.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/platform/node/RandomIdGenerator.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { IdGenerator } from '../../IdGenerator';\n\nconst SPAN_ID_BYTES = 8;\nconst TRACE_ID_BYTES = 16;\n\nexport class RandomIdGenerator implements IdGenerator {\n  /**\n   * Returns a random 16-byte trace ID formatted/encoded as a 32 lowercase hex\n   * characters corresponding to 128 bits.\n   */\n  generateTraceId = getIdGenerator(TRACE_ID_BYTES);\n\n  /**\n   * Returns a random 8-byte span ID formatted/encoded as a 16 lowercase hex\n   * characters corresponding to 64 bits.\n   */\n  generateSpanId = getIdGenerator(SPAN_ID_BYTES);\n}\n\nconst SHARED_BUFFER = Buffer.allocUnsafe(TRACE_ID_BYTES);\nfunction getIdGenerator(bytes: number): () => string {\n  return function generateId() {\n    for (let i = 0; i < bytes / 4; i++) {\n      // unsigned right shift drops decimal part of the number\n      // it is required because if a number between 2**32 and 2**32 - 1 is generated, an out of range error is thrown by writeUInt32BE\n      SHARED_BUFFER.writeUInt32BE((Math.random() * 2 ** 32) >>> 0, i * 4);\n    }\n\n    // If buffer is all 0, set the last byte to 1 to guarantee a valid w3c id is generated\n    for (let i = 0; i < bytes; i++) {\n      if (SHARED_BUFFER[i] > 0) {\n        break;\n      } else if (i === bytes - 1) {\n        SHARED_BUFFER[bytes - 1] = 1;\n      }\n    }\n\n    return SHARED_BUFFER.toString('hex', 0, bytes);\n  };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,IAAM,aAAa,GAAG,CAAC,CAAC;AACxB,IAAM,cAAc,GAAG,EAAE,CAAC;AAE1B,IAAA,oBAAA;IAAA,SAAA;QACE;;;WAGG,CACH,IAAA,CAAA,eAAe,GAAG,cAAc,CAAC,cAAc,CAAC,CAAC;QAEjD;;;WAGG,CACH,IAAA,CAAA,cAAc,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;IAAD,OAAA,iBAAC;AAAD,CAAC,AAZD,IAYC;;AAED,IAAM,aAAa,GAAG,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;AACzD,SAAS,cAAc,CAAC,KAAa;IACnC,OAAO,SAAS,UAAU;QACxB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;YAClC,wDAAwD;YACxD,gIAAgI;YAChI,aAAa,CAAC,aAAa,CAAC,AAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAA,GAAA,CAAA,CAAC,EAAI,EAAE,CAAA,CAAC,IAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;SACrE;QAED,sFAAsF;QACtF,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAE;YAC9B,IAAI,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;gBACxB,MAAM;aACP,MAAM,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC,EAAE;gBAC1B,aAAa,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;aAC9B;SACF;QAED,OAAO,aAAa,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC,CAAC;AACJ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 933, "column": 0}, "map": {"version": 3, "file": "Tracer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/Tracer.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport {\n  InstrumentationLibrary,\n  sanitizeAttributes,\n  isTracingSuppressed,\n} from '@opentelemetry/core';\nimport { IResource } from '@opentelemetry/resources';\nimport { BasicTracerProvider } from './BasicTracerProvider';\nimport { Span } from './Span';\nimport { GeneralLimits, SpanLimits, TracerConfig } from './types';\nimport { mergeConfig } from './utility';\nimport { SpanProcessor } from './SpanProcessor';\nimport { Sampler } from './Sampler';\nimport { IdGenerator } from './IdGenerator';\nimport { RandomIdGenerator } from './platform';\n\n/**\n * This class represents a basic tracer.\n */\nexport class Tracer implements api.Tracer {\n  private readonly _sampler: Sampler;\n  private readonly _generalLimits: GeneralLimits;\n  private readonly _spanLimits: SpanLimits;\n  private readonly _idGenerator: IdGenerator;\n  readonly resource: IResource;\n  readonly instrumentationLibrary: InstrumentationLibrary;\n\n  /**\n   * Constructs a new Tracer instance.\n   */\n  constructor(\n    instrumentationLibrary: InstrumentationLibrary,\n    config: TracerConfig,\n    private _tracerProvider: BasicTracerProvider\n  ) {\n    const localConfig = mergeConfig(config);\n    this._sampler = localConfig.sampler;\n    this._generalLimits = localConfig.generalLimits;\n    this._spanLimits = localConfig.spanLimits;\n    this._idGenerator = config.idGenerator || new RandomIdGenerator();\n    this.resource = _tracerProvider.resource;\n    this.instrumentationLibrary = instrumentationLibrary;\n  }\n\n  /**\n   * Starts a new Span or returns the default NoopSpan based on the sampling\n   * decision.\n   */\n  startSpan(\n    name: string,\n    options: api.SpanOptions = {},\n    context = api.context.active()\n  ): api.Span {\n    // remove span from context in case a root span is requested via options\n    if (options.root) {\n      context = api.trace.deleteSpan(context);\n    }\n    const parentSpan = api.trace.getSpan(context);\n\n    if (isTracingSuppressed(context)) {\n      api.diag.debug('Instrumentation suppressed, returning Noop Span');\n      const nonRecordingSpan = api.trace.wrapSpanContext(\n        api.INVALID_SPAN_CONTEXT\n      );\n      return nonRecordingSpan;\n    }\n\n    const parentSpanContext = parentSpan?.spanContext();\n    const spanId = this._idGenerator.generateSpanId();\n    let traceId;\n    let traceState;\n    let parentSpanId;\n    if (\n      !parentSpanContext ||\n      !api.trace.isSpanContextValid(parentSpanContext)\n    ) {\n      // New root span.\n      traceId = this._idGenerator.generateTraceId();\n    } else {\n      // New child span.\n      traceId = parentSpanContext.traceId;\n      traceState = parentSpanContext.traceState;\n      parentSpanId = parentSpanContext.spanId;\n    }\n\n    const spanKind = options.kind ?? api.SpanKind.INTERNAL;\n    const links = (options.links ?? []).map(link => {\n      return {\n        context: link.context,\n        attributes: sanitizeAttributes(link.attributes),\n      };\n    });\n    const attributes = sanitizeAttributes(options.attributes);\n    // make sampling decision\n    const samplingResult = this._sampler.shouldSample(\n      context,\n      traceId,\n      name,\n      spanKind,\n      attributes,\n      links\n    );\n\n    traceState = samplingResult.traceState ?? traceState;\n\n    const traceFlags =\n      samplingResult.decision === api.SamplingDecision.RECORD_AND_SAMPLED\n        ? api.TraceFlags.SAMPLED\n        : api.TraceFlags.NONE;\n    const spanContext = { traceId, spanId, traceFlags, traceState };\n    if (samplingResult.decision === api.SamplingDecision.NOT_RECORD) {\n      api.diag.debug(\n        'Recording is off, propagating context in a non-recording span'\n      );\n      const nonRecordingSpan = api.trace.wrapSpanContext(spanContext);\n      return nonRecordingSpan;\n    }\n\n    // Set initial span attributes. The attributes object may have been mutated\n    // by the sampler, so we sanitize the merged attributes before setting them.\n    const initAttributes = sanitizeAttributes(\n      Object.assign(attributes, samplingResult.attributes)\n    );\n\n    const span = new Span(\n      this,\n      context,\n      name,\n      spanContext,\n      spanKind,\n      parentSpanId,\n      links,\n      options.startTime,\n      undefined,\n      initAttributes\n    );\n    return span;\n  }\n\n  /**\n   * Starts a new {@link Span} and calls the given function passing it the\n   * created span as first argument.\n   * Additionally the new span gets set in context and this context is activated\n   * for the duration of the function call.\n   *\n   * @param name The name of the span\n   * @param [options] SpanOptions used for span creation\n   * @param [context] Context to use to extract parent\n   * @param fn function called in the context of the span and receives the newly created span as an argument\n   * @returns return value of fn\n   * @example\n   *   const something = tracer.startActiveSpan('op', span => {\n   *     try {\n   *       do some work\n   *       span.setStatus({code: SpanStatusCode.OK});\n   *       return something;\n   *     } catch (err) {\n   *       span.setStatus({\n   *         code: SpanStatusCode.ERROR,\n   *         message: err.message,\n   *       });\n   *       throw err;\n   *     } finally {\n   *       span.end();\n   *     }\n   *   });\n   * @example\n   *   const span = tracer.startActiveSpan('op', span => {\n   *     try {\n   *       do some work\n   *       return span;\n   *     } catch (err) {\n   *       span.setStatus({\n   *         code: SpanStatusCode.ERROR,\n   *         message: err.message,\n   *       });\n   *       throw err;\n   *     }\n   *   });\n   *   do some more work\n   *   span.end();\n   */\n  startActiveSpan<F extends (span: api.Span) => ReturnType<F>>(\n    name: string,\n    fn: F\n  ): ReturnType<F>;\n  startActiveSpan<F extends (span: api.Span) => ReturnType<F>>(\n    name: string,\n    opts: api.SpanOptions,\n    fn: F\n  ): ReturnType<F>;\n  startActiveSpan<F extends (span: api.Span) => ReturnType<F>>(\n    name: string,\n    opts: api.SpanOptions,\n    ctx: api.Context,\n    fn: F\n  ): ReturnType<F>;\n  startActiveSpan<F extends (span: api.Span) => ReturnType<F>>(\n    name: string,\n    arg2?: F | api.SpanOptions,\n    arg3?: F | api.Context,\n    arg4?: F\n  ): ReturnType<F> | undefined {\n    let opts: api.SpanOptions | undefined;\n    let ctx: api.Context | undefined;\n    let fn: F;\n\n    if (arguments.length < 2) {\n      return;\n    } else if (arguments.length === 2) {\n      fn = arg2 as F;\n    } else if (arguments.length === 3) {\n      opts = arg2 as api.SpanOptions | undefined;\n      fn = arg3 as F;\n    } else {\n      opts = arg2 as api.SpanOptions | undefined;\n      ctx = arg3 as api.Context | undefined;\n      fn = arg4 as F;\n    }\n\n    const parentContext = ctx ?? api.context.active();\n    const span = this.startSpan(name, opts, parentContext);\n    const contextWithSpanSet = api.trace.setSpan(parentContext, span);\n\n    return api.context.with(contextWithSpanSet, fn, undefined, span);\n  }\n\n  /** Returns the active {@link GeneralLimits}. */\n  getGeneralLimits(): GeneralLimits {\n    return this._generalLimits;\n  }\n\n  /** Returns the active {@link SpanLimits}. */\n  getSpanLimits(): SpanLimits {\n    return this._spanLimits;\n  }\n\n  getActiveSpanProcessor(): SpanProcessor {\n    return this._tracerProvider.getActiveSpanProcessor();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,KAAK,GAAG,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAEL,kBAAkB,EAClB,mBAAmB,GACpB,MAAM,qBAAqB,CAAC;;AAG7B,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAE9B,OAAO,EAAE,WAAW,EAAE,MAAM,WAAW,CAAC;AAIxC,OAAO,EAAE,iBAAiB,EAAE,MAAM,YAAY,CAAC;;;;;;AAE/C;;GAEG,CACH,IAAA,SAAA;IAQE;;OAEG,CACH,SAAA,OACE,sBAA8C,EAC9C,MAAoB,EACZ,eAAoC;QAApC,IAAA,CAAA,eAAe,GAAf,eAAe,CAAqB;QAE5C,IAAM,WAAW,OAAG,0SAAA,AAAW,EAAC,MAAM,CAAC,CAAC;QACxC,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC;QACpC,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,aAAa,CAAC;QAChD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,UAAU,CAAC;QAC1C,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,WAAW,IAAI,8TAAI,oBAAiB,EAAE,CAAC;QAClE,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC;QACzC,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;IACvD,CAAC;IAED;;;OAGG,CACH,OAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAA6B,EAC7B,OAA8B;;QAD9B,IAAA,YAAA,KAAA,GAAA;YAAA,UAAA,CAAA,CAA6B;QAAA;QAC7B,IAAA,YAAA,KAAA,GAAA;YAAA,wMAAU,GAAG,CAAC,MAAO,CAAC,MAAM,EAAE;QAAA;QAE9B,wEAAwE;QACxE,IAAI,OAAO,CAAC,IAAI,EAAE;YAChB,OAAO,iMAAG,GAAG,CAAC,IAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;SACzC;QACD,IAAM,UAAU,iMAAG,GAAG,CAAC,IAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAE9C,KAAI,mUAAA,AAAmB,EAAC,OAAO,CAAC,EAAE;0MAChC,GAAG,CAAC,GAAI,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;YAClE,IAAM,gBAAgB,iMAAG,GAAG,CAAC,IAAK,CAAC,eAAe,+LAChD,GAAG,CAAC,mBAAoB,CACzB,CAAC;YACF,OAAO,gBAAgB,CAAC;SACzB;QAED,IAAM,iBAAiB,GAAG,UAAU,KAAA,QAAV,UAAU,KAAA,KAAA,IAAA,KAAA,IAAV,UAAU,CAAE,WAAW,EAAE,CAAC;QACpD,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;QAClD,IAAI,OAAO,CAAC;QACZ,IAAI,UAAU,CAAC;QACf,IAAI,YAAY,CAAC;QACjB,IACE,CAAC,iBAAiB,IAClB,CAAC,GAAG,CAAC,kMAAK,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,EAChD;YACA,iBAAiB;YACjB,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;SAC/C,MAAM;YACL,kBAAkB;YAClB,OAAO,GAAG,iBAAiB,CAAC,OAAO,CAAC;YACpC,UAAU,GAAG,iBAAiB,CAAC,UAAU,CAAC;YAC1C,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC;SACzC;QAED,IAAM,QAAQ,GAAG,CAAA,KAAA,OAAO,CAAC,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,GAAG,CAAC,qMAAQ,CAAC,QAAQ,CAAC;QACvD,IAAM,KAAK,GAAG,CAAC,CAAA,KAAA,OAAO,CAAC,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,SAAA,IAAI;YAC1C,OAAO;gBACL,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,UAAU,MAAE,uTAAA,AAAkB,EAAC,IAAI,CAAC,UAAU,CAAC;aAChD,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,IAAM,UAAU,ySAAG,qBAAA,AAAkB,EAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC1D,yBAAyB;QACzB,IAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAC/C,OAAO,EACP,OAAO,EACP,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,KAAK,CACN,CAAC;QAEF,UAAU,GAAG,CAAA,KAAA,cAAc,CAAC,UAAU,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,UAAU,CAAC;QAErD,IAAM,UAAU,GACd,cAAc,CAAC,QAAQ,mMAAK,GAAG,CAAC,eAAgB,CAAC,kBAAkB,iMAC/D,GAAG,CAAC,SAAU,CAAC,OAAO,iMACtB,GAAG,CAAC,SAAU,CAAC,IAAI,CAAC;QAC1B,IAAM,WAAW,GAAG;YAAE,OAAO,EAAA,OAAA;YAAE,MAAM,EAAA,MAAA;YAAE,UAAU,EAAA,UAAA;YAAE,UAAU,EAAA,UAAA;QAAA,CAAE,CAAC;QAChE,IAAI,cAAc,CAAC,QAAQ,KAAK,GAAG,CAAC,6MAAgB,CAAC,UAAU,EAAE;0MAC/D,GAAG,CAAC,GAAI,CAAC,KAAK,CACZ,+DAA+D,CAChE,CAAC;YACF,IAAM,gBAAgB,iMAAG,GAAG,CAAC,IAAK,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAChE,OAAO,gBAAgB,CAAC;SACzB;QAED,2EAA2E;QAC3E,4EAA4E;QAC5E,IAAM,cAAc,ySAAG,qBAAA,AAAkB,EACvC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,cAAc,CAAC,UAAU,CAAC,CACrD,CAAC;QAEF,IAAM,IAAI,GAAG,6RAAI,OAAI,CACnB,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,OAAO,CAAC,SAAS,EACjB,SAAS,EACT,cAAc,CACf,CAAC;QACF,OAAO,IAAI,CAAC;IACd,CAAC;IA4DD,OAAA,SAAA,CAAA,eAAe,GAAf,SACE,IAAY,EACZ,IAA0B,EAC1B,IAAsB,EACtB,IAAQ;QAER,IAAI,IAAiC,CAAC;QACtC,IAAI,GAA4B,CAAC;QACjC,IAAI,EAAK,CAAC;QAEV,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,OAAO;SACR,MAAM,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,EAAE,GAAG,IAAS,CAAC;SAChB,MAAM,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,IAAI,GAAG,IAAmC,CAAC;YAC3C,EAAE,GAAG,IAAS,CAAC;SAChB,MAAM;YACL,IAAI,GAAG,IAAmC,CAAC;YAC3C,GAAG,GAAG,IAA+B,CAAC;YACtC,EAAE,GAAG,IAAS,CAAC;SAChB;QAED,IAAM,aAAa,GAAG,GAAG,KAAA,QAAH,GAAG,KAAA,KAAA,IAAH,GAAG,iMAAI,GAAG,CAAC,MAAO,CAAC,MAAM,EAAE,CAAC;QAClD,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;QACvD,IAAM,kBAAkB,iMAAG,GAAG,CAAC,IAAK,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAElE,oMAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IACnE,CAAC;IAED,8CAAA,EAAgD,CAChD,OAAA,SAAA,CAAA,gBAAgB,GAAhB;QACE,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED,2CAAA,EAA6C,CAC7C,OAAA,SAAA,CAAA,aAAa,GAAb;QACE,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,OAAA,SAAA,CAAA,sBAAsB,GAAtB;QACE,OAAO,IAAI,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC;IACvD,CAAC;IACH,OAAA,MAAC;AAAD,CAAC,AA7ND,IA6NC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1080, "column": 0}, "map": {"version": 3, "file": "MultiSpanProcessor.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/MultiSpanProcessor.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '@opentelemetry/api';\nimport { globalErrorHandler } from '@opentelemetry/core';\nimport { ReadableSpan } from './export/ReadableSpan';\nimport { Span } from './Span';\nimport { SpanProcessor } from './SpanProcessor';\n\n/**\n * Implementation of the {@link SpanProcessor} that simply forwards all\n * received events to a list of {@link SpanProcessor}s.\n */\nexport class MultiSpanProcessor implements SpanProcessor {\n  constructor(private readonly _spanProcessors: SpanProcessor[]) {}\n\n  forceFlush(): Promise<void> {\n    const promises: Promise<void>[] = [];\n\n    for (const spanProcessor of this._spanProcessors) {\n      promises.push(spanProcessor.forceFlush());\n    }\n    return new Promise(resolve => {\n      Promise.all(promises)\n        .then(() => {\n          resolve();\n        })\n        .catch(error => {\n          globalErrorHandler(\n            error || new Error('MultiSpanProcessor: forceFlush failed')\n          );\n          resolve();\n        });\n    });\n  }\n\n  onStart(span: Span, context: Context): void {\n    for (const spanProcessor of this._spanProcessors) {\n      spanProcessor.onStart(span, context);\n    }\n  }\n\n  onEnd(span: ReadableSpan): void {\n    for (const spanProcessor of this._spanProcessors) {\n      spanProcessor.onEnd(span);\n    }\n  }\n\n  shutdown(): Promise<void> {\n    const promises: Promise<void>[] = [];\n\n    for (const spanProcessor of this._spanProcessors) {\n      promises.push(spanProcessor.shutdown());\n    }\n    return new Promise((resolve, reject) => {\n      Promise.all(promises).then(() => {\n        resolve();\n      }, reject);\n    });\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,OAAO,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;AAKzD;;;GAGG,CACH,IAAA,qBAAA;IACE,SAAA,mBAA6B,eAAgC;QAAhC,IAAA,CAAA,eAAe,GAAf,eAAe,CAAiB;IAAG,CAAC;IAEjE,mBAAA,SAAA,CAAA,UAAU,GAAV;;QACE,IAAM,QAAQ,GAAoB,EAAE,CAAC;;YAErC,IAA4B,IAAA,KAAA,SAAA,IAAI,CAAC,eAAe,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;gBAA7C,IAAM,aAAa,GAAA,GAAA,KAAA;gBACtB,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC;aAC3C;;;;;;;;;;;;QACD,OAAO,IAAI,OAAO,CAAC,SAAA,OAAO;YACxB,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAClB,IAAI,CAAC;gBACJ,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CACD,KAAK,CAAC,SAAA,KAAK;sUACV,qBAAA,AAAkB,EAChB,KAAK,IAAI,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAC5D,CAAC;gBACF,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAED,mBAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,IAAU,EAAE,OAAgB;;;YAClC,IAA4B,IAAA,KAAA,SAAA,IAAI,CAAC,eAAe,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;gBAA7C,IAAM,aAAa,GAAA,GAAA,KAAA;gBACtB,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;aACtC;;;;;;;;;;;;IACH,CAAC;IAED,mBAAA,SAAA,CAAA,KAAK,GAAL,SAAM,IAAkB;;;YACtB,IAA4B,IAAA,KAAA,SAAA,IAAI,CAAC,eAAe,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;gBAA7C,IAAM,aAAa,GAAA,GAAA,KAAA;gBACtB,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;aAC3B;;;;;;;;;;;;IACH,CAAC;IAED,mBAAA,SAAA,CAAA,QAAQ,GAAR;;QACE,IAAM,QAAQ,GAAoB,EAAE,CAAC;;YAErC,IAA4B,IAAA,KAAA,SAAA,IAAI,CAAC,eAAe,CAAA,EAAA,KAAA,GAAA,IAAA,EAAA,EAAA,CAAA,GAAA,IAAA,EAAA,KAAA,GAAA,IAAA,GAAE;gBAA7C,IAAM,aAAa,GAAA,GAAA,KAAA;gBACtB,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC;aACzC;;;;;;;;;;;;QACD,OAAO,IAAI,OAAO,CAAC,SAAC,OAAO,EAAE,MAAM;YACjC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;gBACzB,OAAO,EAAE,CAAC;YACZ,CAAC,EAAE,MAAM,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;IACL,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AA/CD,IA+CC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1221, "column": 0}, "map": {"version": 3, "file": "NoopSpanProcessor.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/export/NoopSpanProcessor.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '@opentelemetry/api';\nimport { ReadableSpan } from './ReadableSpan';\nimport { Span } from '../Span';\nimport { SpanProcessor } from '../SpanProcessor';\n\n/** No-op implementation of SpanProcessor */\nexport class NoopSpanProcessor implements SpanProcessor {\n  onStart(_span: Span, _context: Context): void {}\n  onEnd(_span: ReadableSpan): void {}\n  shutdown(): Promise<void> {\n    return Promise.resolve();\n  }\n  forceFlush(): Promise<void> {\n    return Promise.resolve();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAOH,0CAAA,EAA4C;;;AAC5C,IAAA,oBAAA;IAAA,SAAA,qBASA,CAAC;IARC,kBAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,KAAW,EAAE,QAAiB,GAAS,CAAC;IAChD,kBAAA,SAAA,CAAA,KAAK,GAAL,SAAM,KAAmB,GAAS,CAAC;IACnC,kBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IACD,kBAAA,SAAA,CAAA,UAAU,GAAV;QACE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IACH,OAAA,iBAAC;AAAD,CAAC,AATD,IASC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1258, "column": 0}, "map": {"version": 3, "file": "BatchSpanProcessorBase.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/export/BatchSpanProcessorBase.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { context, Context, diag, TraceFlags } from '@opentelemetry/api';\nimport {\n  BindOnceFuture,\n  ExportResultCode,\n  getEnv,\n  globalErrorHandler,\n  suppressTracing,\n  unrefTimer,\n} from '@opentelemetry/core';\nimport { Span } from '../Span';\nimport { SpanProcessor } from '../SpanProcessor';\nimport { BufferConfig } from '../types';\nimport { ReadableSpan } from './ReadableSpan';\nimport { SpanExporter } from './SpanExporter';\n\n/**\n * Implementation of the {@link SpanProcessor} that batches spans exported by\n * the SDK then pushes them to the exporter pipeline.\n */\nexport abstract class BatchSpanProcessorBase<T extends BufferConfig>\n  implements SpanProcessor\n{\n  private readonly _maxExportBatchSize: number;\n  private readonly _maxQueueSize: number;\n  private readonly _scheduledDelayMillis: number;\n  private readonly _exportTimeoutMillis: number;\n\n  private _isExporting = false;\n  private _finishedSpans: ReadableSpan[] = [];\n  private _timer: NodeJS.Timeout | undefined;\n  private _shutdownOnce: BindOnceFuture<void>;\n  private _droppedSpansCount: number = 0;\n\n  constructor(\n    private readonly _exporter: SpanExporter,\n    config?: T\n  ) {\n    const env = getEnv();\n    this._maxExportBatchSize =\n      typeof config?.maxExportBatchSize === 'number'\n        ? config.maxExportBatchSize\n        : env.OTEL_BSP_MAX_EXPORT_BATCH_SIZE;\n    this._maxQueueSize =\n      typeof config?.maxQueueSize === 'number'\n        ? config.maxQueueSize\n        : env.OTEL_BSP_MAX_QUEUE_SIZE;\n    this._scheduledDelayMillis =\n      typeof config?.scheduledDelayMillis === 'number'\n        ? config.scheduledDelayMillis\n        : env.OTEL_BSP_SCHEDULE_DELAY;\n    this._exportTimeoutMillis =\n      typeof config?.exportTimeoutMillis === 'number'\n        ? config.exportTimeoutMillis\n        : env.OTEL_BSP_EXPORT_TIMEOUT;\n\n    this._shutdownOnce = new BindOnceFuture(this._shutdown, this);\n\n    if (this._maxExportBatchSize > this._maxQueueSize) {\n      diag.warn(\n        'BatchSpanProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize'\n      );\n      this._maxExportBatchSize = this._maxQueueSize;\n    }\n  }\n\n  forceFlush(): Promise<void> {\n    if (this._shutdownOnce.isCalled) {\n      return this._shutdownOnce.promise;\n    }\n    return this._flushAll();\n  }\n\n  // does nothing.\n  onStart(_span: Span, _parentContext: Context): void {}\n\n  onEnd(span: ReadableSpan): void {\n    if (this._shutdownOnce.isCalled) {\n      return;\n    }\n\n    if ((span.spanContext().traceFlags & TraceFlags.SAMPLED) === 0) {\n      return;\n    }\n\n    this._addToBuffer(span);\n  }\n\n  shutdown(): Promise<void> {\n    return this._shutdownOnce.call();\n  }\n\n  private _shutdown() {\n    return Promise.resolve()\n      .then(() => {\n        return this.onShutdown();\n      })\n      .then(() => {\n        return this._flushAll();\n      })\n      .then(() => {\n        return this._exporter.shutdown();\n      });\n  }\n\n  /** Add a span in the buffer. */\n  private _addToBuffer(span: ReadableSpan) {\n    if (this._finishedSpans.length >= this._maxQueueSize) {\n      // limit reached, drop span\n\n      if (this._droppedSpansCount === 0) {\n        diag.debug('maxQueueSize reached, dropping spans');\n      }\n      this._droppedSpansCount++;\n\n      return;\n    }\n\n    if (this._droppedSpansCount > 0) {\n      // some spans were dropped, log once with count of spans dropped\n      diag.warn(\n        `Dropped ${this._droppedSpansCount} spans because maxQueueSize reached`\n      );\n      this._droppedSpansCount = 0;\n    }\n\n    this._finishedSpans.push(span);\n    this._maybeStartTimer();\n  }\n\n  /**\n   * Send all spans to the exporter respecting the batch size limit\n   * This function is used only on forceFlush or shutdown,\n   * for all other cases _flush should be used\n   * */\n  private _flushAll(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const promises = [];\n      // calculate number of batches\n      const count = Math.ceil(\n        this._finishedSpans.length / this._maxExportBatchSize\n      );\n      for (let i = 0, j = count; i < j; i++) {\n        promises.push(this._flushOneBatch());\n      }\n      Promise.all(promises)\n        .then(() => {\n          resolve();\n        })\n        .catch(reject);\n    });\n  }\n\n  private _flushOneBatch(): Promise<void> {\n    this._clearTimer();\n    if (this._finishedSpans.length === 0) {\n      return Promise.resolve();\n    }\n    return new Promise((resolve, reject) => {\n      const timer = setTimeout(() => {\n        // don't wait anymore for export, this way the next batch can start\n        reject(new Error('Timeout'));\n      }, this._exportTimeoutMillis);\n      // prevent downstream exporter calls from generating spans\n      context.with(suppressTracing(context.active()), () => {\n        // Reset the finished spans buffer here because the next invocations of the _flush method\n        // could pass the same finished spans to the exporter if the buffer is cleared\n        // outside the execution of this callback.\n        let spans: ReadableSpan[];\n        if (this._finishedSpans.length <= this._maxExportBatchSize) {\n          spans = this._finishedSpans;\n          this._finishedSpans = [];\n        } else {\n          spans = this._finishedSpans.splice(0, this._maxExportBatchSize);\n        }\n\n        const doExport = () =>\n          this._exporter.export(spans, result => {\n            clearTimeout(timer);\n            if (result.code === ExportResultCode.SUCCESS) {\n              resolve();\n            } else {\n              reject(\n                result.error ??\n                  new Error('BatchSpanProcessor: span export failed')\n              );\n            }\n          });\n\n        let pendingResources: Array<Promise<void>> | null = null;\n        for (let i = 0, len = spans.length; i < len; i++) {\n          const span = spans[i];\n          if (\n            span.resource.asyncAttributesPending &&\n            span.resource.waitForAsyncAttributes\n          ) {\n            pendingResources ??= [];\n            pendingResources.push(span.resource.waitForAsyncAttributes());\n          }\n        }\n\n        // Avoid scheduling a promise to make the behavior more predictable and easier to test\n        if (pendingResources === null) {\n          doExport();\n        } else {\n          Promise.all(pendingResources).then(doExport, err => {\n            globalErrorHandler(err);\n            reject(err);\n          });\n        }\n      });\n    });\n  }\n\n  private _maybeStartTimer() {\n    if (this._isExporting) return;\n    const flush = () => {\n      this._isExporting = true;\n      this._flushOneBatch()\n        .finally(() => {\n          this._isExporting = false;\n          if (this._finishedSpans.length > 0) {\n            this._clearTimer();\n            this._maybeStartTimer();\n          }\n        })\n        .catch(e => {\n          this._isExporting = false;\n          globalErrorHandler(e);\n        });\n    };\n    // we only wait if the queue doesn't have enough elements yet\n    if (this._finishedSpans.length >= this._maxExportBatchSize) {\n      return flush();\n    }\n    if (this._timer !== undefined) return;\n    this._timer = setTimeout(() => flush(), this._scheduledDelayMillis);\n    unrefTimer(this._timer);\n  }\n\n  private _clearTimer() {\n    if (this._timer !== undefined) {\n      clearTimeout(this._timer);\n      this._timer = undefined;\n    }\n  }\n\n  protected abstract onShutdown(): void;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,OAAO,EAAW,IAAI,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AACxE,OAAO,EACL,cAAc,EACd,gBAAgB,EAChB,MAAM,EACN,kBAAkB,EAClB,eAAe,EACf,UAAU,GACX,MAAM,qBAAqB,CAAC;;;;;;;;AAO7B;;;GAGG,CACH,IAAA,yBAAA;IAcE,SAAA,uBACmB,SAAuB,EACxC,MAAU;QADO,IAAA,CAAA,SAAS,GAAT,SAAS,CAAc;QAPlC,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QACrB,IAAA,CAAA,cAAc,GAAmB,EAAE,CAAC;QAGpC,IAAA,CAAA,kBAAkB,GAAW,CAAC,CAAC;QAMrC,IAAM,GAAG,oTAAG,SAAA,AAAM,EAAE,CAAC;QACrB,IAAI,CAAC,mBAAmB,GACtB,OAAO,CAAA,MAAM,KAAA,QAAN,MAAM,KAAA,KAAA,IAAA,KAAA,IAAN,MAAM,CAAE,kBAAkB,CAAA,KAAK,QAAQ,GAC1C,MAAM,CAAC,kBAAkB,GACzB,GAAG,CAAC,8BAA8B,CAAC;QACzC,IAAI,CAAC,aAAa,GAChB,OAAO,CAAA,MAAM,KAAA,QAAN,MAAM,KAAA,KAAA,IAAA,KAAA,IAAN,MAAM,CAAE,YAAY,CAAA,KAAK,QAAQ,GACpC,MAAM,CAAC,YAAY,GACnB,GAAG,CAAC,uBAAuB,CAAC;QAClC,IAAI,CAAC,qBAAqB,GACxB,OAAO,CAAA,MAAM,KAAA,QAAN,MAAM,KAAA,KAAA,IAAA,KAAA,IAAN,MAAM,CAAE,oBAAoB,CAAA,KAAK,QAAQ,GAC5C,MAAM,CAAC,oBAAoB,GAC3B,GAAG,CAAC,uBAAuB,CAAC;QAClC,IAAI,CAAC,oBAAoB,GACvB,OAAO,CAAA,MAAM,KAAA,QAAN,MAAM,KAAA,KAAA,IAAA,KAAA,IAAN,MAAM,CAAE,mBAAmB,CAAA,KAAK,QAAQ,GAC3C,MAAM,CAAC,mBAAmB,GAC1B,GAAG,CAAC,uBAAuB,CAAC;QAElC,IAAI,CAAC,aAAa,GAAG,mSAAI,iBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,aAAa,EAAE;0MACjD,OAAI,CAAC,IAAI,CACP,mIAAmI,CACpI,CAAC;YACF,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC;SAC/C;IACH,CAAC;IAED,uBAAA,SAAA,CAAA,UAAU,GAAV;QACE,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;SACnC;QACD,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;IAC1B,CAAC;IAED,gBAAgB;IAChB,uBAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,KAAW,EAAE,cAAuB,GAAS,CAAC;IAEtD,uBAAA,SAAA,CAAA,KAAK,GAAL,SAAM,IAAkB;QACtB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,OAAO;SACR;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,iMAAG,aAAU,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC9D,OAAO;SACR;QAED,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED,uBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEO,uBAAA,SAAA,CAAA,SAAS,GAAjB;QAAA,IAAA,QAAA,IAAA,CAWC;QAVC,OAAO,OAAO,CAAC,OAAO,EAAE,CACrB,IAAI,CAAC;YACJ,OAAO,KAAI,CAAC,UAAU,EAAE,CAAC;QAC3B,CAAC,CAAC,CACD,IAAI,CAAC;YACJ,OAAO,KAAI,CAAC,SAAS,EAAE,CAAC;QAC1B,CAAC,CAAC,CACD,IAAI,CAAC;YACJ,OAAO,KAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;IACP,CAAC;IAED,8BAAA,EAAgC,CACxB,uBAAA,SAAA,CAAA,YAAY,GAApB,SAAqB,IAAkB;QACrC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE;YACpD,2BAA2B;YAE3B,IAAI,IAAI,CAAC,kBAAkB,KAAK,CAAC,EAAE;8MACjC,OAAI,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;aACpD;YACD,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAE1B,OAAO;SACR;QAED,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE;YAC/B,gEAAgE;0MAChE,OAAI,CAAC,IAAI,CACP,aAAW,IAAI,CAAC,kBAAkB,GAAA,qCAAqC,CACxE,CAAC;YACF,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;SAC7B;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;;;SAIK,CACG,uBAAA,SAAA,CAAA,SAAS,GAAjB;QAAA,IAAA,QAAA,IAAA,CAgBC;QAfC,OAAO,IAAI,OAAO,CAAC,SAAC,OAAO,EAAE,MAAM;YACjC,IAAM,QAAQ,GAAG,EAAE,CAAC;YACpB,8BAA8B;YAC9B,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CACrB,KAAI,CAAC,cAAc,CAAC,MAAM,GAAG,KAAI,CAAC,mBAAmB,CACtD,CAAC;YACF,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;gBACrC,QAAQ,CAAC,IAAI,CAAC,KAAI,CAAC,cAAc,EAAE,CAAC,CAAC;aACtC;YACD,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAClB,IAAI,CAAC;gBACJ,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CACD,KAAK,CAAC,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,uBAAA,SAAA,CAAA,cAAc,GAAtB;QAAA,IAAA,QAAA,IAAA,CA2DC;QA1DC,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;YACpC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;QACD,OAAO,IAAI,OAAO,CAAC,SAAC,OAAO,EAAE,MAAM;YACjC,IAAM,KAAK,GAAG,UAAU,CAAC;gBACvB,mEAAmE;gBACnE,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;YAC/B,CAAC,EAAE,KAAI,CAAC,oBAAoB,CAAC,CAAC;YAC9B,0DAA0D;0MAC1D,UAAO,CAAC,IAAI,+SAAC,kBAAA,AAAe,gMAAC,UAAO,CAAC,MAAM,EAAE,CAAC,EAAE;gBAC9C,yFAAyF;gBACzF,8EAA8E;gBAC9E,0CAA0C;gBAC1C,IAAI,KAAqB,CAAC;gBAC1B,IAAI,KAAI,CAAC,cAAc,CAAC,MAAM,IAAI,KAAI,CAAC,mBAAmB,EAAE;oBAC1D,KAAK,GAAG,KAAI,CAAC,cAAc,CAAC;oBAC5B,KAAI,CAAC,cAAc,GAAG,EAAE,CAAC;iBAC1B,MAAM;oBACL,KAAK,GAAG,KAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,KAAI,CAAC,mBAAmB,CAAC,CAAC;iBACjE;gBAED,IAAM,QAAQ,GAAG;oBACf,OAAA,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,SAAA,MAAM;;wBACjC,YAAY,CAAC,KAAK,CAAC,CAAC;wBACpB,IAAI,MAAM,CAAC,IAAI,KAAK,6SAAgB,CAAC,OAAO,EAAE;4BAC5C,OAAO,EAAE,CAAC;yBACX,MAAM;4BACL,MAAM,CACJ,CAAA,KAAA,MAAM,CAAC,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KACV,IAAI,KAAK,CAAC,wCAAwC,CAAC,CACtD,CAAC;yBACH;oBACH,CAAC,CAAC;gBAVF,CAUE,CAAC;gBAEL,IAAI,gBAAgB,GAAgC,IAAI,CAAC;gBACzD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAE;oBAChD,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;oBACtB,IACE,IAAI,CAAC,QAAQ,CAAC,sBAAsB,IACpC,IAAI,CAAC,QAAQ,CAAC,sBAAsB,EACpC;wBACA,gBAAgB,KAAA,QAAhB,gBAAgB,KAAA,KAAA,IAAhB,gBAAgB,GAAhB,gBAAgB,GAAK,EAAE,EAAC;wBACxB,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,sBAAsB,EAAE,CAAC,CAAC;qBAC/D;iBACF;gBAED,sFAAsF;gBACtF,IAAI,gBAAgB,KAAK,IAAI,EAAE;oBAC7B,QAAQ,EAAE,CAAC;iBACZ,MAAM;oBACL,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAA,GAAG;8UAC9C,qBAAA,AAAkB,EAAC,GAAG,CAAC,CAAC;wBACxB,MAAM,CAAC,GAAG,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC;iBACJ;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,uBAAA,SAAA,CAAA,gBAAgB,GAAxB;QAAA,IAAA,QAAA,IAAA,CAwBC;QAvBC,IAAI,IAAI,CAAC,YAAY,EAAE,OAAO;QAC9B,IAAM,KAAK,GAAG;YACZ,KAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,KAAI,CAAC,cAAc,EAAE,CAClB,OAAO,CAAC;gBACP,KAAI,CAAC,YAAY,GAAG,KAAK,CAAC;gBAC1B,IAAI,KAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;oBAClC,KAAI,CAAC,WAAW,EAAE,CAAC;oBACnB,KAAI,CAAC,gBAAgB,EAAE,CAAC;iBACzB;YACH,CAAC,CAAC,CACD,KAAK,CAAC,SAAA,CAAC;gBACN,KAAI,CAAC,YAAY,GAAG,KAAK,CAAC;sUAC1B,qBAAA,AAAkB,EAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QACF,6DAA6D;QAC7D,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1D,OAAO,KAAK,EAAE,CAAC;SAChB;QACD,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE,OAAO;QACtC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;YAAM,OAAA,KAAK,EAAE;QAAP,CAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;2TACpE,aAAA,AAAU,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAEO,uBAAA,SAAA,CAAA,WAAW,GAAnB;QACE,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAC7B,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1B,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;SACzB;IACH,CAAC;IAGH,OAAA,sBAAC;AAAD,CAAC,AApOD,IAoOC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1465, "column": 0}, "map": {"version": 3, "file": "BatchSpanProcessor.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/platform/node/export/BatchSpanProcessor.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { BatchSpanProcessorBase } from '../../../export/BatchSpanProcessorBase';\nimport { BufferConfig } from '../../../types';\n\nexport class BatchSpanProcessor extends BatchSpanProcessorBase<BufferConfig> {\n  protected onShutdown(): void {}\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,sBAAsB,EAAE,MAAM,wCAAwC,CAAC;;;;;;;;;;;;;;;;;;;;;;AAGhF,IAAA,qBAAA,SAAA,MAAA;IAAwC,UAAA,oBAAA,QAAoC;IAA5E,SAAA;;IAEA,CAAC;IADW,mBAAA,SAAA,CAAA,UAAU,GAApB,YAA8B,CAAC;IACjC,OAAA,kBAAC;AAAD,CAAC,AAFD,sTAAwC,yBAAsB,GAE7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1520, "column": 0}, "map": {"version": 3, "file": "BasicTracerProvider.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/BasicTracerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  context,\n  diag,\n  propagation,\n  TextMapPropagator,\n  trace,\n  TracerProvider,\n} from '@opentelemetry/api';\nimport {\n  CompositePropagator,\n  W3CBaggagePropagator,\n  W3CTraceContextPropagator,\n  getEnv,\n  merge,\n} from '@opentelemetry/core';\nimport { IResource, Resource } from '@opentelemetry/resources';\nimport { SpanProcessor } from './SpanProcessor';\nimport { Tracer } from './Tracer';\nimport { loadDefaultConfig } from './config';\nimport { MultiSpanProcessor } from './MultiSpanProcessor';\nimport { NoopSpanProcessor } from './export/NoopSpanProcessor';\nimport { SDKRegistrationConfig, TracerConfig } from './types';\nimport { SpanExporter } from './export/SpanExporter';\nimport { BatchSpanProcessor } from './platform';\nimport { reconfigureLimits } from './utility';\n\nexport type PROPAGATOR_FACTORY = () => TextMapPropagator;\nexport type EXPORTER_FACTORY = () => SpanExporter;\n\nexport enum ForceFlushState {\n  'resolved',\n  'timeout',\n  'error',\n  'unresolved',\n}\n\n/**\n * This class represents a basic tracer provider which platform libraries can extend\n */\nexport class BasicTracerProvider implements TracerProvider {\n  protected static readonly _registeredPropagators = new Map<\n    string,\n    PROPAGATOR_FACTORY\n  >([\n    ['tracecontext', () => new W3CTraceContextPropagator()],\n    ['baggage', () => new W3CBaggagePropagator()],\n  ]);\n\n  protected static readonly _registeredExporters = new Map<\n    string,\n    EXPORTER_FACTORY\n  >();\n\n  private readonly _config: TracerConfig;\n  private readonly _registeredSpanProcessors: SpanProcessor[] = [];\n  private readonly _tracers: Map<string, Tracer> = new Map();\n\n  activeSpanProcessor: SpanProcessor;\n  readonly resource: IResource;\n\n  constructor(config: TracerConfig = {}) {\n    const mergedConfig = merge(\n      {},\n      loadDefaultConfig(),\n      reconfigureLimits(config)\n    );\n    this.resource = mergedConfig.resource ?? Resource.empty();\n\n    if (mergedConfig.mergeResourceWithDefaults) {\n      this.resource = Resource.default().merge(this.resource);\n    }\n\n    this._config = Object.assign({}, mergedConfig, {\n      resource: this.resource,\n    });\n\n    if (config.spanProcessors?.length) {\n      this._registeredSpanProcessors = [...config.spanProcessors];\n      this.activeSpanProcessor = new MultiSpanProcessor(\n        this._registeredSpanProcessors\n      );\n    } else {\n      const defaultExporter = this._buildExporterFromEnv();\n      if (defaultExporter !== undefined) {\n        const batchProcessor = new BatchSpanProcessor(defaultExporter);\n        this.activeSpanProcessor = batchProcessor;\n      } else {\n        this.activeSpanProcessor = new NoopSpanProcessor();\n      }\n    }\n  }\n\n  getTracer(\n    name: string,\n    version?: string,\n    options?: { schemaUrl?: string }\n  ): Tracer {\n    const key = `${name}@${version || ''}:${options?.schemaUrl || ''}`;\n    if (!this._tracers.has(key)) {\n      this._tracers.set(\n        key,\n        new Tracer(\n          { name, version, schemaUrl: options?.schemaUrl },\n          this._config,\n          this\n        )\n      );\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    return this._tracers.get(key)!;\n  }\n\n  /**\n   * @deprecated please use {@link TracerConfig} spanProcessors property\n   * Adds a new {@link SpanProcessor} to this tracer.\n   * @param spanProcessor the new SpanProcessor to be added.\n   */\n  addSpanProcessor(spanProcessor: SpanProcessor): void {\n    if (this._registeredSpanProcessors.length === 0) {\n      // since we might have enabled by default a batchProcessor, we disable it\n      // before adding the new one\n      this.activeSpanProcessor\n        .shutdown()\n        .catch(err =>\n          diag.error(\n            'Error while trying to shutdown current span processor',\n            err\n          )\n        );\n    }\n    this._registeredSpanProcessors.push(spanProcessor);\n    this.activeSpanProcessor = new MultiSpanProcessor(\n      this._registeredSpanProcessors\n    );\n  }\n\n  getActiveSpanProcessor(): SpanProcessor {\n    return this.activeSpanProcessor;\n  }\n\n  /**\n   * Register this TracerProvider for use with the OpenTelemetry API.\n   * Undefined values may be replaced with defaults, and\n   * null values will be skipped.\n   *\n   * @param config Configuration object for SDK registration\n   */\n  register(config: SDKRegistrationConfig = {}): void {\n    trace.setGlobalTracerProvider(this);\n    if (config.propagator === undefined) {\n      config.propagator = this._buildPropagatorFromEnv();\n    }\n\n    if (config.contextManager) {\n      context.setGlobalContextManager(config.contextManager);\n    }\n\n    if (config.propagator) {\n      propagation.setGlobalPropagator(config.propagator);\n    }\n  }\n\n  forceFlush(): Promise<void> {\n    const timeout = this._config.forceFlushTimeoutMillis;\n    const promises = this._registeredSpanProcessors.map(\n      (spanProcessor: SpanProcessor) => {\n        return new Promise(resolve => {\n          let state: ForceFlushState;\n          const timeoutInterval = setTimeout(() => {\n            resolve(\n              new Error(\n                `Span processor did not completed within timeout period of ${timeout} ms`\n              )\n            );\n            state = ForceFlushState.timeout;\n          }, timeout);\n\n          spanProcessor\n            .forceFlush()\n            .then(() => {\n              clearTimeout(timeoutInterval);\n              if (state !== ForceFlushState.timeout) {\n                state = ForceFlushState.resolved;\n                resolve(state);\n              }\n            })\n            .catch(error => {\n              clearTimeout(timeoutInterval);\n              state = ForceFlushState.error;\n              resolve(error);\n            });\n        });\n      }\n    );\n\n    return new Promise<void>((resolve, reject) => {\n      Promise.all(promises)\n        .then(results => {\n          const errors = results.filter(\n            result => result !== ForceFlushState.resolved\n          );\n          if (errors.length > 0) {\n            reject(errors);\n          } else {\n            resolve();\n          }\n        })\n        .catch(error => reject([error]));\n    });\n  }\n\n  shutdown(): Promise<void> {\n    return this.activeSpanProcessor.shutdown();\n  }\n\n  /**\n   * TS cannot yet infer the type of this.constructor:\n   * https://github.com/Microsoft/TypeScript/issues/3841#issuecomment-337560146\n   * There is no need to override either of the getters in your child class.\n   * The type of the registered component maps should be the same across all\n   * classes in the inheritance tree.\n   */\n  protected _getPropagator(name: string): TextMapPropagator | undefined {\n    return (\n      this.constructor as typeof BasicTracerProvider\n    )._registeredPropagators.get(name)?.();\n  }\n\n  protected _getSpanExporter(name: string): SpanExporter | undefined {\n    return (\n      this.constructor as typeof BasicTracerProvider\n    )._registeredExporters.get(name)?.();\n  }\n\n  protected _buildPropagatorFromEnv(): TextMapPropagator | undefined {\n    // per spec, propagators from env must be deduplicated\n    const uniquePropagatorNames = Array.from(\n      new Set(getEnv().OTEL_PROPAGATORS)\n    );\n\n    const propagators = uniquePropagatorNames.map(name => {\n      const propagator = this._getPropagator(name);\n      if (!propagator) {\n        diag.warn(\n          `Propagator \"${name}\" requested through environment variable is unavailable.`\n        );\n      }\n\n      return propagator;\n    });\n    const validPropagators = propagators.reduce<TextMapPropagator[]>(\n      (list, item) => {\n        if (item) {\n          list.push(item);\n        }\n        return list;\n      },\n      []\n    );\n\n    if (validPropagators.length === 0) {\n      return;\n    } else if (uniquePropagatorNames.length === 1) {\n      return validPropagators[0];\n    } else {\n      return new CompositePropagator({\n        propagators: validPropagators,\n      });\n    }\n  }\n\n  protected _buildExporterFromEnv(): SpanExporter | undefined {\n    const exporterName = getEnv().OTEL_TRACES_EXPORTER;\n    if (exporterName === 'none' || exporterName === '') return;\n    const exporter = this._getSpanExporter(exporterName);\n    if (!exporter) {\n      diag.error(\n        `Exporter \"${exporterName}\" requested through environment variable is unavailable.`\n      );\n    }\n    return exporter;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,OAAO,EACL,OAAO,EACP,IAAI,EACJ,WAAW,EAEX,KAAK,GAEN,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EACL,mBAAmB,EACnB,oBAAoB,EACpB,yBAAyB,EACzB,MAAM,EACN,KAAK,GACN,MAAM,qBAAqB,CAAC;;;;;AAC7B,OAAO,EAAa,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAE/D,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,iBAAiB,EAAE,MAAM,UAAU,CAAC;AAC7C,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAG/D,OAAO,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAC;AAChD,OAAO,EAAE,iBAAiB,EAAE,MAAM,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAK9C,IAAY,eAKX;AALD,CAAA,SAAY,eAAe;IACzB,eAAA,CAAA,eAAA,CAAA,WAAA,GAAA,EAAA,GAAA,UAAU,CAAA;IACV,eAAA,CAAA,eAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAS,CAAA;IACT,eAAA,CAAA,eAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAO,CAAA;IACP,eAAA,CAAA,eAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAY,CAAA;AACd,CAAC,EALW,eAAe,IAAA,CAAf,eAAe,GAAA,CAAA,CAAA,GAK1B;AAED;;GAEG,CACH,IAAA,sBAAA;IAqBE,SAAA,oBAAY,MAAyB;QAAzB,IAAA,WAAA,KAAA,GAAA;YAAA,SAAA,CAAA,CAAyB;QAAA;;QANpB,IAAA,CAAA,yBAAyB,GAAoB,EAAE,CAAC;QAChD,IAAA,CAAA,QAAQ,GAAwB,IAAI,GAAG,EAAE,CAAC;QAMzD,IAAM,YAAY,mSAAG,QAAA,AAAK,EACxB,CAAA,CAAE,iSACF,oBAAA,AAAiB,EAAE,IACnB,mTAAA,AAAiB,EAAC,MAAM,CAAC,CAC1B,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,CAAA,KAAA,YAAY,CAAC,QAAQ,MAAA,QAAA,OAAA,KAAA,IAAA,qSAAI,WAAQ,CAAC,KAAK,EAAE,CAAC;QAE1D,IAAI,YAAY,CAAC,yBAAyB,EAAE;YAC1C,IAAI,CAAC,QAAQ,mSAAG,WAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACzD;QAED,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,YAAY,EAAE;YAC7C,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;QAEH,IAAI,CAAA,KAAA,MAAM,CAAC,cAAc,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,MAAM,EAAE;YACjC,IAAI,CAAC,yBAAyB,GAAA,cAAA,EAAA,EAAA,OAAO,MAAM,CAAC,cAAc,GAAA,MAAC,CAAC;YAC5D,IAAI,CAAC,mBAAmB,GAAG,2SAAI,qBAAkB,CAC/C,IAAI,CAAC,yBAAyB,CAC/B,CAAC;SACH,MAAM;YACL,IAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACrD,IAAI,eAAe,KAAK,SAAS,EAAE;gBACjC,IAAM,cAAc,GAAG,IAAI,0VAAkB,CAAC,eAAe,CAAC,CAAC;gBAC/D,IAAI,CAAC,mBAAmB,GAAG,cAAc,CAAC;aAC3C,MAAM;gBACL,IAAI,CAAC,mBAAmB,GAAG,mTAAI,qBAAiB,EAAE,CAAC;aACpD;SACF;IACH,CAAC;IAED,oBAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAAgB,EAChB,OAAgC;QAEhC,IAAM,GAAG,GAAM,IAAI,GAAA,MAAA,CAAI,OAAO,IAAI,EAAE,IAAA,MAAA,CAAI,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,SAAS,KAAI,EAAE,CAAE,CAAC;QACnE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YAC3B,IAAI,CAAC,QAAQ,CAAC,GAAG,CACf,GAAG,EACH,+RAAI,SAAM,CACR;gBAAE,IAAI,EAAA,IAAA;gBAAE,OAAO,EAAA,OAAA;gBAAE,SAAS,EAAE,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,SAAS;YAAA,CAAE,EAChD,IAAI,CAAC,OAAO,EACZ,IAAI,CACL,CACF,CAAC;SACH;QAED,oEAAoE;QACpE,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;IACjC,CAAC;IAED;;;;OAIG,CACH,oBAAA,SAAA,CAAA,gBAAgB,GAAhB,SAAiB,aAA4B;QAC3C,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/C,yEAAyE;YACzE,4BAA4B;YAC5B,IAAI,CAAC,mBAAmB,CACrB,QAAQ,EAAE,CACV,KAAK,CAAC,SAAA,GAAG;gBACR,OAAA,qMAAI,CAAC,KAAK,CACR,uDAAuD,EACvD,GAAG,CACJ;YAHD,CAGC,CACF,CAAC;SACL;QACD,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACnD,IAAI,CAAC,mBAAmB,GAAG,0SAAI,sBAAkB,CAC/C,IAAI,CAAC,yBAAyB,CAC/B,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,sBAAsB,GAAtB;QACE,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC,CAAC;IAED;;;;;;OAMG,CACH,oBAAA,SAAA,CAAA,QAAQ,GAAR,SAAS,MAAkC;QAAlC,IAAA,WAAA,KAAA,GAAA;YAAA,SAAA,CAAA,CAAkC;QAAA;sMACzC,QAAK,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,EAAE;YACnC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;SACpD;QAED,IAAI,MAAM,CAAC,cAAc,EAAE;0MACzB,UAAO,CAAC,uBAAuB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;SACxD;QAED,IAAI,MAAM,CAAC,UAAU,EAAE;YACrB,4MAAW,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;SACpD;IACH,CAAC;IAED,oBAAA,SAAA,CAAA,UAAU,GAAV;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC;QACrD,IAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC,GAAG,CACjD,SAAC,aAA4B;YAC3B,OAAO,IAAI,OAAO,CAAC,SAAA,OAAO;gBACxB,IAAI,KAAsB,CAAC;gBAC3B,IAAM,eAAe,GAAG,UAAU,CAAC;oBACjC,OAAO,CACL,IAAI,KAAK,CACP,+DAA6D,OAAO,GAAA,KAAK,CAC1E,CACF,CAAC;oBACF,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC;gBAClC,CAAC,EAAE,OAAO,CAAC,CAAC;gBAEZ,aAAa,CACV,UAAU,EAAE,CACZ,IAAI,CAAC;oBACJ,YAAY,CAAC,eAAe,CAAC,CAAC;oBAC9B,IAAI,KAAK,KAAK,eAAe,CAAC,OAAO,EAAE;wBACrC,KAAK,GAAG,eAAe,CAAC,QAAQ,CAAC;wBACjC,OAAO,CAAC,KAAK,CAAC,CAAC;qBAChB;gBACH,CAAC,CAAC,CACD,KAAK,CAAC,SAAA,KAAK;oBACV,YAAY,CAAC,eAAe,CAAC,CAAC;oBAC9B,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC;oBAC9B,OAAO,CAAC,KAAK,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;QACL,CAAC,CACF,CAAC;QAEF,OAAO,IAAI,OAAO,CAAO,SAAC,OAAO,EAAE,MAAM;YACvC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAClB,IAAI,CAAC,SAAA,OAAO;gBACX,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAC3B,SAAA,MAAM;oBAAI,OAAA,MAAM,KAAK,eAAe,CAAC,QAAQ;gBAAnC,CAAmC,CAC9C,CAAC;gBACF,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;oBACrB,MAAM,CAAC,MAAM,CAAC,CAAC;iBAChB,MAAM;oBACL,OAAO,EAAE,CAAC;iBACX;YACH,CAAC,CAAC,CACD,KAAK,CAAC,SAAA,KAAK;gBAAI,OAAA,MAAM,CAAC;oBAAC,KAAK;iBAAC,CAAC;YAAf,CAAe,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,oBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC;IAC7C,CAAC;IAED;;;;;;OAMG,CACO,oBAAA,SAAA,CAAA,cAAc,GAAxB,SAAyB,IAAY;;QACnC,OAAO,CAAA,KACL,IAAI,CAAC,WACN,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,IAAI,CAAC;IACzC,CAAC;IAES,oBAAA,SAAA,CAAA,gBAAgB,GAA1B,SAA2B,IAAY;;QACrC,OAAO,CAAA,KACL,IAAI,CAAC,WACN,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,IAAI,CAAC;IACvC,CAAC;IAES,oBAAA,SAAA,CAAA,uBAAuB,GAAjC;QAAA,IAAA,QAAA,IAAA,CAmCC;QAlCC,sDAAsD;QACtD,IAAM,qBAAqB,GAAG,KAAK,CAAC,IAAI,CACtC,IAAI,GAAG,kTAAC,SAAA,AAAM,EAAE,EAAC,gBAAgB,CAAC,CACnC,CAAC;QAEF,IAAM,WAAW,GAAG,qBAAqB,CAAC,GAAG,CAAC,SAAA,IAAI;YAChD,IAAM,UAAU,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,CAAC,UAAU,EAAE;gBACf,qMAAI,CAAC,IAAI,CACP,kBAAe,IAAI,GAAA,2DAA0D,CAC9E,CAAC;aACH;YAED,OAAO,UAAU,CAAC;QACpB,CAAC,CAAC,CAAC;QACH,IAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,CACzC,SAAC,IAAI,EAAE,IAAI;YACT,IAAI,IAAI,EAAE;gBACR,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACjB;YACD,OAAO,IAAI,CAAC;QACd,CAAC,EACD,EAAE,CACH,CAAC;QAEF,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,OAAO;SACR,MAAM,IAAI,qBAAqB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7C,OAAO,gBAAgB,CAAC,CAAC,CAAC,CAAC;SAC5B,MAAM;YACL,OAAO,0SAAI,sBAAmB,CAAC;gBAC7B,WAAW,EAAE,gBAAgB;aAC9B,CAAC,CAAC;SACJ;IACH,CAAC;IAES,oBAAA,SAAA,CAAA,qBAAqB,GAA/B;QACE,IAAM,YAAY,oTAAG,SAAA,AAAM,EAAE,EAAC,oBAAoB,CAAC;QACnD,IAAI,YAAY,KAAK,MAAM,IAAI,YAAY,KAAK,EAAE,EAAE,OAAO;QAC3D,IAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QACrD,IAAI,CAAC,QAAQ,EAAE;0MACb,OAAI,CAAC,KAAK,CACR,gBAAa,YAAY,GAAA,2DAA0D,CACpF,CAAC;SACH;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAlPyB,oBAAA,sBAAsB,GAAG,IAAI,GAAG,CAGxD;QACA;YAAC,cAAc;YAAE;gBAAM,OAAA,oTAAI,4BAAyB,EAAE;YAA/B,CAA+B;SAAC;QACvD;YAAC,SAAS;YAAE;gBAAM,OAAA,gUAAI,uBAAoB,EAAE;YAA1B,CAA0B;SAAC;KAC9C,CAAC,CAAC;IAEuB,oBAAA,oBAAoB,GAAG,IAAI,GAAG,EAGrD,CAAC;IAwON,OAAA,mBAAC;CAAA,AApPD,IAoPC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1794, "column": 0}, "map": {"version": 3, "file": "ConsoleSpanExporter.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/export/ConsoleSpanExporter.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SpanExporter } from './SpanExporter';\nimport { ReadableSpan } from './ReadableSpan';\nimport {\n  ExportResult,\n  ExportResultCode,\n  hrTimeToMicroseconds,\n} from '@opentelemetry/core';\n\n/**\n * This is implementation of {@link SpanExporter} that prints spans to the\n * console. This class can be used for diagnostic purposes.\n *\n * NOTE: This {@link SpanExporter} is intended for diagnostics use only, output rendered to the console may change at any time.\n */\n\n/* eslint-disable no-console */\nexport class ConsoleSpanExporter implements SpanExporter {\n  /**\n   * Export spans.\n   * @param spans\n   * @param resultCallback\n   */\n  export(\n    spans: ReadableSpan[],\n    resultCallback: (result: ExportResult) => void\n  ): void {\n    return this._sendSpans(spans, resultCallback);\n  }\n\n  /**\n   * Shutdown the exporter.\n   */\n  shutdown(): Promise<void> {\n    this._sendSpans([]);\n    return this.forceFlush();\n  }\n\n  /**\n   * Exports any pending spans in exporter\n   */\n  forceFlush(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  /**\n   * converts span info into more readable format\n   * @param span\n   */\n  private _exportInfo(span: ReadableSpan) {\n    return {\n      resource: {\n        attributes: span.resource.attributes,\n      },\n      instrumentationScope: span.instrumentationLibrary,\n      traceId: span.spanContext().traceId,\n      parentId: span.parentSpanId,\n      traceState: span.spanContext().traceState?.serialize(),\n      name: span.name,\n      id: span.spanContext().spanId,\n      kind: span.kind,\n      timestamp: hrTimeToMicroseconds(span.startTime),\n      duration: hrTimeToMicroseconds(span.duration),\n      attributes: span.attributes,\n      status: span.status,\n      events: span.events,\n      links: span.links,\n    };\n  }\n\n  /**\n   * Showing spans in console\n   * @param spans\n   * @param done\n   */\n  private _sendSpans(\n    spans: ReadableSpan[],\n    done?: (result: ExportResult) => void\n  ): void {\n    for (const span of spans) {\n      console.dir(this._exportInfo(span), { depth: 3 });\n    }\n    if (done) {\n      return done({ code: ExportResultCode.SUCCESS });\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,OAAO,EAEL,gBAAgB,EAChB,oBAAoB,GACrB,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;AAE7B;;;;;GAKG,CAEH,6BAAA,EAA+B,CAC/B,IAAA,sBAAA;IAAA,SAAA,uBAqEA,CAAC;IApEC;;;;OAIG,CACH,oBAAA,SAAA,CAAA,MAAM,GAAN,SACE,KAAqB,EACrB,cAA8C;QAE9C,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACpB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,UAAU,GAAV;QACE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED;;;OAGG,CACK,oBAAA,SAAA,CAAA,WAAW,GAAnB,SAAoB,IAAkB;;QACpC,OAAO;YACL,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU;aACrC;YACD,oBAAoB,EAAE,IAAI,CAAC,sBAAsB;YACjD,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO;YACnC,QAAQ,EAAE,IAAI,CAAC,YAAY;YAC3B,UAAU,EAAE,CAAA,KAAA,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,EAAE;YACtD,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM;YAC7B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,SAAS,kSAAE,uBAAA,AAAoB,EAAC,IAAI,CAAC,SAAS,CAAC;YAC/C,QAAQ,MAAE,mTAAA,AAAoB,EAAC,IAAI,CAAC,QAAQ,CAAC;YAC7C,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACK,oBAAA,SAAA,CAAA,UAAU,GAAlB,SACE,KAAqB,EACrB,IAAqC;;;YAErC,IAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,EAAA,YAAA,QAAA,IAAA,EAAA,EAAA,CAAA,UAAA,IAAA,EAAA,YAAA,QAAA,IAAA,GAAE;gBAArB,IAAM,IAAI,GAAA,UAAA,KAAA;gBACb,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;oBAAE,KAAK,EAAE,CAAC;gBAAA,CAAE,CAAC,CAAC;aACnD;;;;;;;;;;;;QACD,IAAI,IAAI,EAAE;YACR,OAAO,IAAI,CAAC;gBAAE,IAAI,4RAAE,mBAAgB,CAAC,OAAO;YAAA,CAAE,CAAC,CAAC;SACjD;IACH,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AArED,IAqEC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1917, "column": 0}, "map": {"version": 3, "file": "InMemorySpanExporter.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/export/InMemorySpanExporter.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SpanExporter } from './SpanExporter';\nimport { ReadableSpan } from './ReadableSpan';\nimport { ExportResult, ExportResultCode } from '@opentelemetry/core';\n\n/**\n * This class can be used for testing purposes. It stores the exported spans\n * in a list in memory that can be retrieved using the `getFinishedSpans()`\n * method.\n */\nexport class InMemorySpanExporter implements SpanExporter {\n  private _finishedSpans: ReadableSpan[] = [];\n  /**\n   * Indicates if the exporter has been \"shutdown.\"\n   * When false, exported spans will not be stored in-memory.\n   */\n  protected _stopped = false;\n\n  export(\n    spans: ReadableSpan[],\n    resultCallback: (result: ExportResult) => void\n  ): void {\n    if (this._stopped)\n      return resultCallback({\n        code: ExportResultCode.FAILED,\n        error: new Error('Exporter has been stopped'),\n      });\n    this._finishedSpans.push(...spans);\n\n    setTimeout(() => resultCallback({ code: ExportResultCode.SUCCESS }), 0);\n  }\n\n  shutdown(): Promise<void> {\n    this._stopped = true;\n    this._finishedSpans = [];\n    return this.forceFlush();\n  }\n\n  /**\n   * Exports any pending spans in the exporter\n   */\n  forceFlush(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  reset(): void {\n    this._finishedSpans = [];\n  }\n\n  getFinishedSpans(): ReadableSpan[] {\n    return this._finishedSpans;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,OAAO,EAAgB,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErE;;;;GAIG,CACH,IAAA,uBAAA;IAAA,SAAA;QACU,IAAA,CAAA,cAAc,GAAmB,EAAE,CAAC;QAC5C;;;WAGG,CACO,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;IAoC7B,CAAC;IAlCC,qBAAA,SAAA,CAAA,MAAM,GAAN,SACE,KAAqB,EACrB,cAA8C;;QAE9C,IAAI,IAAI,CAAC,QAAQ,EACf,OAAO,cAAc,CAAC;YACpB,IAAI,4RAAE,mBAAgB,CAAC,MAAM;YAC7B,KAAK,EAAE,IAAI,KAAK,CAAC,2BAA2B,CAAC;SAC9C,CAAC,CAAC;QACL,CAAA,KAAA,IAAI,CAAC,cAAc,CAAA,CAAC,IAAI,CAAA,KAAA,CAAA,IAAA,cAAA,EAAA,EAAA,OAAI,KAAK,GAAA,QAAE;QAEnC,UAAU,CAAC;YAAM,OAAA,cAAc,CAAC;gBAAE,IAAI,4RAAE,mBAAgB,CAAC,OAAO;YAAA,CAAE,CAAC;QAAlD,CAAkD,EAAE,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED,qBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,qBAAA,SAAA,CAAA,UAAU,GAAV;QACE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,qBAAA,SAAA,CAAA,KAAK,GAAL;QACE,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;IAC3B,CAAC;IAED,qBAAA,SAAA,CAAA,gBAAgB,GAAhB;QACE,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IACH,OAAA,oBAAC;AAAD,CAAC,AA1CD,IA0CC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2015, "column": 0}, "map": {"version": 3, "file": "SimpleSpanProcessor.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/node_modules/.pnpm/%40opentelemetry%2Bsdk-trace-ba_e6e7b6b6d817ce552ed74ad5b04c04c8/node_modules/%40opentelemetry/sdk-trace-base/src/export/SimpleSpanProcessor.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, TraceFlags } from '@opentelemetry/api';\nimport {\n  internal,\n  ExportResultCode,\n  globalErrorHandler,\n  BindOnceFuture,\n  ExportResult,\n} from '@opentelemetry/core';\nimport { Span } from '../Span';\nimport { SpanProcessor } from '../SpanProcessor';\nimport { ReadableSpan } from './ReadableSpan';\nimport { SpanExporter } from './SpanExporter';\nimport { Resource } from '@opentelemetry/resources';\n\n/**\n * An implementation of the {@link SpanProcessor} that converts the {@link Span}\n * to {@link ReadableSpan} and passes it to the configured exporter.\n *\n * Only spans that are sampled are converted.\n *\n * NOTE: This {@link SpanProcessor} exports every ended span individually instead of batching spans together, which causes significant performance overhead with most exporters. For production use, please consider using the {@link BatchSpanProcessor} instead.\n */\nexport class SimpleSpanProcessor implements SpanProcessor {\n  private _shutdownOnce: BindOnceFuture<void>;\n  private _unresolvedExports: Set<Promise<void>>;\n\n  constructor(private readonly _exporter: SpanExporter) {\n    this._shutdownOnce = new BindOnceFuture(this._shutdown, this);\n    this._unresolvedExports = new Set<Promise<void>>();\n  }\n\n  async forceFlush(): Promise<void> {\n    // await unresolved resources before resolving\n    await Promise.all(Array.from(this._unresolvedExports));\n    if (this._exporter.forceFlush) {\n      await this._exporter.forceFlush();\n    }\n  }\n\n  onStart(_span: Span, _parentContext: Context): void {}\n\n  onEnd(span: ReadableSpan): void {\n    if (this._shutdownOnce.isCalled) {\n      return;\n    }\n\n    if ((span.spanContext().traceFlags & TraceFlags.SAMPLED) === 0) {\n      return;\n    }\n\n    const doExport = () =>\n      internal\n        ._export(this._exporter, [span])\n        .then((result: ExportResult) => {\n          if (result.code !== ExportResultCode.SUCCESS) {\n            globalErrorHandler(\n              result.error ??\n                new Error(\n                  `SimpleSpanProcessor: span export failed (status ${result})`\n                )\n            );\n          }\n        })\n        .catch(error => {\n          globalErrorHandler(error);\n        });\n\n    // Avoid scheduling a promise to make the behavior more predictable and easier to test\n    if (span.resource.asyncAttributesPending) {\n      const exportPromise = (span.resource as Resource)\n        .waitForAsyncAttributes?.()\n        .then(\n          () => {\n            if (exportPromise != null) {\n              this._unresolvedExports.delete(exportPromise);\n            }\n            return doExport();\n          },\n          err => globalErrorHandler(err)\n        );\n\n      // store the unresolved exports\n      if (exportPromise != null) {\n        this._unresolvedExports.add(exportPromise);\n      }\n    } else {\n      void doExport();\n    }\n  }\n\n  shutdown(): Promise<void> {\n    return this._shutdownOnce.call();\n  }\n\n  private _shutdown(): Promise<void> {\n    return this._exporter.shutdown();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAW,UAAU,EAAE,MAAM,oBAAoB,CAAC;;;AACzD,OAAO,EACL,QAAQ,EACR,gBAAgB,EAChB,kBAAkB,EAClB,cAAc,GAEf,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B;;;;;;;GAOG,CACH,IAAA,sBAAA;IAIE,SAAA,oBAA6B,SAAuB;QAAvB,IAAA,CAAA,SAAS,GAAT,SAAS,CAAc;QAClD,IAAI,CAAC,aAAa,GAAG,mSAAI,iBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC9D,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,EAAiB,CAAC;IACrD,CAAC;IAEK,oBAAA,SAAA,CAAA,UAAU,GAAhB;;;;;wBACE,8CAA8C;wBAC9C,OAAA;4BAAA,EAAA,OAAA;4BAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;yBAAA,CAAA;;wBADtD,8CAA8C;wBAC9C,GAAA,IAAA,EAAsD,CAAC;6BACnD,IAAI,CAAC,SAAS,CAAC,UAAU,EAAzB,OAAA;4BAAA,EAAA,OAAA;4BAAA;yBAAA,CAAyB;wBAC3B,OAAA;4BAAA,EAAA,OAAA;4BAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;yBAAA,CAAA;;wBAAjC,GAAA,IAAA,EAAiC,CAAC;;;;;;;;;KAErC;IAED,oBAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,KAAW,EAAE,cAAuB,GAAS,CAAC;IAEtD,oBAAA,SAAA,CAAA,KAAK,GAAL,SAAM,IAAkB;QAAxB,IAAA,QAAA,IAAA,CA+CC;;QA9CC,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,OAAO;SACR;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,iMAAG,aAAU,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC9D,OAAO;SACR;QAED,IAAM,QAAQ,GAAG;YACf,OAAA,8SAAQ,CACL,OAAO,CAAC,KAAI,CAAC,SAAS,EAAE;gBAAC,IAAI;aAAC,CAAC,CAC/B,IAAI,CAAC,SAAC,MAAoB;;gBACzB,IAAI,MAAM,CAAC,IAAI,+RAAK,mBAAgB,CAAC,OAAO,EAAE;0UAC5C,qBAAA,AAAkB,EAChB,CAAA,KAAA,MAAM,CAAC,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KACV,IAAI,KAAK,CACP,qDAAmD,MAAM,GAAA,GAAG,CAC7D,CACJ,CAAC;iBACH;YACH,CAAC,CAAC,CACD,KAAK,CAAC,SAAA,KAAK;sUACV,qBAAA,AAAkB,EAAC,KAAK,CAAC,CAAC;YAC5B,CAAC,CAAC;QAdJ,CAcI,CAAC;QAEP,sFAAsF;QACtF,IAAI,IAAI,CAAC,QAAQ,CAAC,sBAAsB,EAAE;YACxC,IAAM,eAAa,GAAG,CAAA,KAAA,CAAA,KAAC,IAAI,CAAC,QAAqB,EAC9C,sBAAsB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IACtB,IAAI,CACH;gBACE,IAAI,eAAa,IAAI,IAAI,EAAE;oBACzB,KAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,eAAa,CAAC,CAAC;iBAC/C;gBACD,OAAO,QAAQ,EAAE,CAAC;YACpB,CAAC,EACD,SAAA,GAAG;gBAAI,QAAA,0UAAA,AAAkB,EAAC,GAAG,CAAC;YAAvB,CAAuB,CAC/B,CAAC;YAEJ,+BAA+B;YAC/B,IAAI,eAAa,IAAI,IAAI,EAAE;gBACzB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,eAAa,CAAC,CAAC;aAC5C;SACF,MAAM;YACL,KAAK,QAAQ,EAAE,CAAC;SACjB;IACH,CAAC;IAED,oBAAA,SAAA,CAAA,QAAQ,GAAR;QACE,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEO,oBAAA,SAAA,CAAA,SAAS,GAAjB;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;IACnC,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AA3ED,IA2EC", "ignoreList": [0], "debugId": null}}]}