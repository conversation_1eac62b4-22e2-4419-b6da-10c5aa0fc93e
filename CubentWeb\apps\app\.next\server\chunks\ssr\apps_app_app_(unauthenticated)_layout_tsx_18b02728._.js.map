{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/app/app/%28unauthenticated%29/layout.tsx"], "sourcesContent": ["\nimport type { ReactNode } from 'react';\n\ntype AuthLayoutProps = {\n  readonly children: ReactNode;\n};\n\nconst AuthLayout = ({ children }: AuthLayoutProps) => (\n  <div className=\"relative min-h-dvh w-full\" style={{backgroundColor: '#161616'}}>\n    {/* Vertical lines */}\n    <div className=\"absolute inset-0 pointer-events-none\">\n      <div className=\"absolute top-0 bottom-0 w-px bg-white/5 left-1/4\"></div>\n      <div className=\"absolute top-0 bottom-0 w-px bg-white/5 left-2/4\"></div>\n      <div className=\"absolute top-0 bottom-0 w-px bg-white/5 left-3/4\"></div>\n    </div>\n\n    {/* Horizontal lines */}\n    <div className=\"absolute inset-0 pointer-events-none\">\n      <div className=\"absolute left-0 right-0 h-px bg-white/5 top-1/4\"></div>\n      <div className=\"absolute left-0 right-0 h-px bg-white/5 top-2/4\"></div>\n      <div className=\"absolute left-0 right-0 h-px bg-white/5 top-3/4\"></div>\n    </div>\n\n    <div className=\"relative z-10 flex min-h-dvh items-center justify-center p-4\">\n      <div className=\"w-full max-w-lg mx-auto\">\n        <div className=\"space-y-6 flex flex-col items-center\">\n          {children}\n        </div>\n      </div>\n    </div>\n  </div>\n);\n\nexport default AuthLayout;\n"], "names": [], "mappings": ";;;;;AAOA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAmB,iBAC/C,6VAAC;QAAI,WAAU;QAA4B,OAAO;YAAC,iBAAiB;QAAS;;0BAE3E,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;;;;;kCACf,6VAAC;wBAAI,WAAU;;;;;;kCACf,6VAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;;;;;kCACf,6VAAC;wBAAI,WAAU;;;;;;kCACf,6VAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;uCAOI", "debugId": null}}]}